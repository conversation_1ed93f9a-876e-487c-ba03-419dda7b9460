"use client";

import React from 'react';
import { Shield, Users, Award, Clock, CheckCircle, ArrowRight } from 'lucide-react';

export default function PricingCTA() {
  const benefits = [
    {
      icon: <Shield className="w-6 h-6" />,
      title: "Enterprise-Grade Security",
      description: "CISSP, CEH, and OSCP certified professionals with 10+ years of experience"
    },
    {
      icon: <Users className="w-6 h-6" />,
      title: "Dedicated Security Team",
      description: "Assigned security consultants who understand your business and industry"
    },
    {
      icon: <Award className="w-6 h-6" />,
      title: "Compliance Expertise",
      description: "ISO 27001, SOC 2, PCI DSS, and industry-specific compliance guidance"
    },
    {
      icon: <Clock className="w-6 h-6" />,
      title: "Rapid Response",
      description: "24/7 incident response and emergency security consultation available"
    }
  ];

  const guarantees = [
    "Comprehensive security reports with actionable recommendations",
    "Executive summary for leadership and technical details for IT teams",
    "30-day post-assessment support for remediation guidance",
    "Compliance with industry standards and regulatory requirements",
    "Transparent pricing with no hidden fees or surprise costs"
  ];

  return (
    <section className="py-20 lg:py-28 bg-gradient-to-br from-[var(--color-dark-blue)] to-[var(--color-blue)] relative overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 bg-cover bg-center bg-no-repeat opacity-10"
           style={{ backgroundImage: 'url(/images/hexagon.svg)' }}>
      </div>
      
      <div className="relative z-10 container mx-auto px-4 max-w-7xl">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
          {/* Left Column - Main CTA */}
          <div className="text-white">
            <h2 className="text-4xl lg:text-5xl font-bold mb-6 leading-tight">
              Ready to Secure Your
              <span className="text-[var(--color-yellow)]"> Digital Assets?</span>
            </h2>
            
            <p className="text-xl text-white/90 mb-8 leading-relaxed">
              Join 200+ enterprises who trust SecurityLit for their cybersecurity needs. 
              Get a personalized consultation and detailed security roadmap for your organization.
            </p>

            {/* Benefits Grid */}
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-6 mb-10">
              {benefits.map((benefit, index) => (
                <div key={index} className="flex items-start gap-4">
                  <div className="w-12 h-12 bg-[var(--color-yellow)] rounded-lg flex items-center justify-center flex-shrink-0">
                    <div className="text-[var(--color-dark-blue)]">
                      {benefit.icon}
                    </div>
                  </div>
                  <div>
                    <h3 className="font-semibold text-white mb-2">{benefit.title}</h3>
                    <p className="text-white/80 text-sm leading-relaxed">{benefit.description}</p>
                  </div>
                </div>
              ))}
            </div>

            {/* CTA Buttons */}
            <div className="flex flex-col sm:flex-row gap-4">
              <a
                href="#pricing-contact"
                className="bg-[var(--color-yellow)] hover:bg-[var(--color-yellow-hover)] text-[var(--color-dark-blue)] px-8 py-4 rounded-xl font-bold text-lg transition-all duration-300 shadow-lg hover:shadow-xl flex items-center justify-center gap-2 group"
              >
                Get Custom Quote
                <ArrowRight className="w-5 h-5 group-hover:translate-x-1 transition-transform" />
              </a>
              
              <a
                href="/contact"
                className="border-2 border-white text-white hover:bg-white hover:text-[var(--color-dark-blue)] px-8 py-4 rounded-xl font-bold text-lg transition-all duration-300 flex items-center justify-center gap-2"
              >
                Schedule Consultation
              </a>
            </div>
          </div>

          {/* Right Column - Guarantees */}
          <div className="bg-white/10 backdrop-blur-sm rounded-3xl p-8 lg:p-10 border border-white/20">
            <h3 className="text-2xl lg:text-3xl font-bold text-white mb-6">
              What You Get with SecurityLit
            </h3>
            
            <div className="space-y-4">
              {guarantees.map((guarantee, index) => (
                <div key={index} className="flex items-start gap-4">
                  <div className="w-6 h-6 bg-[var(--color-yellow)] rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                    <CheckCircle className="w-4 h-4 text-[var(--color-dark-blue)]" />
                  </div>
                  <p className="text-white/90 leading-relaxed">{guarantee}</p>
                </div>
              ))}
            </div>

            {/* Trust Indicators */}
            <div className="mt-8 pt-8 border-t border-white/20">
              <div className="grid grid-cols-3 gap-6 text-center">
                <div>
                  <div className="text-3xl font-bold text-[var(--color-yellow)] mb-1">200+</div>
                  <div className="text-white/80 text-sm">Clients Secured</div>
                </div>
                <div>
                  <div className="text-3xl font-bold text-[var(--color-yellow)] mb-1">99.9%</div>
                  <div className="text-white/80 text-sm">Client Satisfaction</div>
                </div>
                <div>
                  <div className="text-3xl font-bold text-[var(--color-yellow)] mb-1">24/7</div>
                  <div className="text-white/80 text-sm">Support Available</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Bottom Section - Industry Recognition */}
        <div className="mt-16 pt-16 border-t border-white/20">
          <div className="text-center mb-12">
            <h3 className="text-2xl lg:text-3xl font-bold text-white mb-4">
              Trusted by Industry Leaders
            </h3>
            <p className="text-white/80 text-lg max-w-3xl mx-auto">
              SecurityLit has helped organizations across finance, healthcare, technology, and government 
              sectors strengthen their cybersecurity posture and achieve compliance.
            </p>
          </div>

          {/* Industry Sectors */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
            {[
              { name: "Financial Services", icon: "🏦" },
              { name: "Healthcare", icon: "🏥" },
              { name: "Technology", icon: "💻" },
              { name: "Government", icon: "🏛️" }
            ].map((sector, index) => (
              <div key={index} className="bg-white/10 backdrop-blur-sm rounded-xl p-6 text-center border border-white/20">
                <div className="text-4xl mb-3">{sector.icon}</div>
                <div className="text-white font-semibold">{sector.name}</div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}
