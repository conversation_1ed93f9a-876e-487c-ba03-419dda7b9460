"use client";

import React from 'react';
import SecurityCalculator from './SecurityCalculator';
import BreadcrumbNavigation from "../../common/components/BreadcrumbNavigation";

export default function PricingCalculatorSection() {
  const breadcrumbItems = [
    {
      name: "Home",
      url: "/",
      iconKey: "home",
      description: "Return to homepage"
    },
    {
      name: "Pricing",
      url: "/pricing",
      current: true,
      iconKey: "pricing",
      description: "Cybersecurity services pricing calculator and cost estimation"
    }
  ];

  return (
    <div className="relative bg-gradient-to-br from-[var(--bg-light-blue)] to-white py-16 lg:py-24">
      {/* Background Pattern */}
      <div className="absolute inset-0 bg-cover bg-center bg-no-repeat opacity-5"
           style={{ backgroundImage: 'url(/images/hexagon.svg)' }}>
      </div>
      
      <div className="relative z-10 container mx-auto px-4 max-w-7xl">
        {/* Breadcrumb Navigation */}
        <div className="mb-8">
          <BreadcrumbNavigation items={breadcrumbItems} />
        </div>

        {/* Header Section */}
        <div className="text-center mb-16">
          <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold text-[var(--color-dark-blue)] mb-6 leading-tight">
            Transparent Cybersecurity
            <span className="text-[var(--color-blue)]"> Pricing</span>
          </h1>
          <p className="text-xl lg:text-2xl text-gray-600 max-w-4xl mx-auto mb-8 leading-relaxed">
            Get instant pricing estimates for our enterprise-grade cybersecurity services. 
            Compare costs and see how SecurityLit delivers exceptional value for your security investments.
          </p>
          
          {/* Key Benefits */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto mt-12">
            <div className="bg-white/80 backdrop-blur-sm rounded-xl p-6 border border-[var(--color-blue)]/20">
              <div className="w-12 h-12 bg-[var(--color-blue)] rounded-lg flex items-center justify-center mx-auto mb-4">
                <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <h3 className="text-lg font-semibold text-[var(--color-dark-blue)] mb-2">Transparent Pricing</h3>
              <p className="text-gray-600 text-sm">No hidden fees or surprise costs. Get clear, upfront pricing for all services.</p>
            </div>
            
            <div className="bg-white/80 backdrop-blur-sm rounded-xl p-6 border border-[var(--color-blue)]/20">
              <div className="w-12 h-12 bg-[var(--color-blue)] rounded-lg flex items-center justify-center mx-auto mb-4">
                <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
              </div>
              <h3 className="text-lg font-semibold text-[var(--color-dark-blue)] mb-2">Instant Estimates</h3>
              <p className="text-gray-600 text-sm">Get immediate cost calculations based on your specific requirements.</p>
            </div>
            
            <div className="bg-white/80 backdrop-blur-sm rounded-xl p-6 border border-[var(--color-blue)]/20">
              <div className="w-12 h-12 bg-[var(--color-blue)] rounded-lg flex items-center justify-center mx-auto mb-4">
                <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                </svg>
              </div>
              <h3 className="text-lg font-semibold text-[var(--color-dark-blue)] mb-2">Competitive Rates</h3>
              <p className="text-gray-600 text-sm">Compare our pricing with other vendors and see the SecurityLit advantage.</p>
            </div>
          </div>
        </div>

        {/* Calculator Container */}
        <div className="bg-white rounded-3xl shadow-2xl border border-[var(--color-blue)]/10 overflow-hidden">
          <div className="bg-gradient-to-r from-[var(--color-dark-blue)] to-[var(--color-blue)] p-1">
            <div className="bg-white rounded-3xl">
              <SecurityCalculator />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
