{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/src/app/common/components/BreadcrumbNavigation.jsx"], "sourcesContent": ["\"use client\";\r\nimport React, { useState } from 'react';\r\nimport Link from 'next/link';\r\nimport { ChevronRight, Home, FileText, Users, Settings, MapPin, Shield, Code, Smartphone, Globe, DollarSign, Building, Phone, Download } from 'lucide-react';\r\nimport { motion } from 'framer-motion';\r\n\r\n/**\r\n * Truncated Text Component for Mobile Breadcrumbs\r\n * Shows truncated text on mobile with click to expand functionality\r\n */\r\nconst TruncatedText = ({ text, maxLength = 25, onExpansionChange }) => {\r\n  const [isExpanded, setIsExpanded] = useState(false);\r\n\r\n  const handleToggle = () => {\r\n    const newExpanded = !isExpanded;\r\n    setIsExpanded(newExpanded);\r\n    if (onExpansionChange) {\r\n      onExpansionChange(newExpanded);\r\n    }\r\n  };\r\n\r\n  if (text.length <= maxLength) {\r\n    return <span>{text}</span>;\r\n  }\r\n\r\n  return (\r\n    <span\r\n      className=\"cursor-pointer\"\r\n      onClick={handleToggle}\r\n      title={isExpanded ? \"Click to collapse\" : \"Click to expand\"}\r\n    >\r\n      <span className=\"sm:hidden\">\r\n        {isExpanded ? text : `${text.substring(0, maxLength)}...`}\r\n      </span>\r\n      <span className=\"hidden sm:inline\">\r\n        {text}\r\n      </span>\r\n    </span>\r\n  );\r\n};\r\n\r\n/**\r\n * Structured Data Component for SEO\r\n */\r\nconst BreadcrumbStructuredData = ({ items }) => {\r\n  const structuredData = {\r\n    \"@context\": \"https://schema.org\",\r\n    \"@type\": \"BreadcrumbList\",\r\n    \"itemListElement\": items.map((item, index) => ({\r\n      \"@type\": \"ListItem\",\r\n      \"position\": index + 1,\r\n      \"name\": item.name,\r\n      \"item\": item.current ? undefined : `https://securitylit.com${item.url}`\r\n    }))\r\n  };\r\n\r\n  return (\r\n    <script\r\n      type=\"application/ld+json\"\r\n      dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}\r\n    />\r\n  );\r\n};\r\n\r\n/**\r\n * Universal Breadcrumb Navigation Component with SEO structured data\r\n * Provides clear navigation hierarchy and improves SEO across all pages\r\n */\r\nconst BreadcrumbNavigation = ({ items, className = \"\" }) => {\r\n  const [hasExpandedText, setHasExpandedText] = useState(false);\r\n\r\n  // Check if this is a blog page for conditional padding\r\n  const isBlogPage = items?.some(item =>\r\n    item.url?.includes('/Blogs') ||\r\n    item.iconKey === 'blogs' ||\r\n    className?.includes('blog')\r\n  );\r\n\r\n  return (\r\n    <>\r\n      {/* Structured Data for SEO */}\r\n      <BreadcrumbStructuredData items={items} />\r\n      \r\n      {/* Simple Breadcrumb Navigation */}\r\n      <nav\r\n        className={`bg-transparent py-2 sm:py-3 ${\r\n          isBlogPage\r\n            ? hasExpandedText\r\n              ? 'pb-6 sm:pb-4'\r\n              : 'pb-3 sm:pb-4'\r\n            : 'pb-2 sm:pb-3'\r\n        } ${className}`}\r\n        aria-label=\"Breadcrumb navigation\"\r\n      >\r\n        <div className=\"max-w-7xl mx-auto px-4\">\r\n          <motion.ol\r\n            className={`flex items-center space-x-1 text-xs sm:text-sm ${\r\n              className?.includes('text-white')\r\n                ? 'text-white'\r\n                : className?.includes('text-blue-600')\r\n                ? 'text-blue-600'\r\n                : 'text-gray-600'\r\n            }`}\r\n            initial={{ opacity: 0 }}\r\n            animate={{ opacity: 1 }}\r\n            transition={{ duration: 0.4 }}\r\n          >\r\n            {items.map((item, index) => (\r\n              <motion.li\r\n                key={index}\r\n                className=\"flex items-center\"\r\n                initial={{ opacity: 0 }}\r\n                animate={{ opacity: 1 }}\r\n                transition={{ duration: 0.3, delay: index * 0.1 }}\r\n              >\r\n                {index > 0 && (\r\n                  <ChevronRight\r\n                    className={`w-3 h-3 mx-1 ${\r\n                      className?.includes('text-white')\r\n                        ? 'text-white/70'\r\n                        : className?.includes('text-blue-600')\r\n                        ? 'text-blue-400'\r\n                        : 'text-gray-400'\r\n                    }`}\r\n                    aria-hidden=\"true\"\r\n                  />\r\n                )}\r\n\r\n                {item.current ? (\r\n                  <span\r\n                    className={`font-medium flex items-center gap-1 ${\r\n                      className?.includes('text-white')\r\n                        ? 'text-white'\r\n                        : className?.includes('text-blue-600')\r\n                        ? 'text-blue-600'\r\n                        : 'text-gray-900'\r\n                    }`}\r\n                    aria-current=\"page\"\r\n                    title={item.description}\r\n                  >\r\n                    {item.iconKey === 'home' && (\r\n                      <Home className={`w-3 h-3 ${\r\n                        className?.includes('text-white')\r\n                          ? 'text-white'\r\n                          : className?.includes('text-blue-600')\r\n                          ? 'text-blue-600'\r\n                          : 'text-blue-600'\r\n                      }`} />\r\n                    )}\r\n                    {item.iconKey === 'blogs' && (\r\n                      <FileText className={`w-3 h-3 ${\r\n                        className?.includes('text-white')\r\n                          ? 'text-white'\r\n                          : className?.includes('text-blue-600')\r\n                          ? 'text-blue-600'\r\n                          : 'text-blue-600'\r\n                      }`} />\r\n                    )}\r\n                    {item.iconKey === 'contact-us' && (\r\n                      <Phone className={`w-3 h-3 ${\r\n                        className?.includes('text-white')\r\n                          ? 'text-white'\r\n                          : className?.includes('text-blue-600')\r\n                          ? 'text-blue-600'\r\n                          : 'text-blue-600'\r\n                      }`} />\r\n                    )}\r\n                    <TruncatedText\r\n                      text={item.name}\r\n                      onExpansionChange={setHasExpandedText}\r\n                    />\r\n                  </span>\r\n                ) : (\r\n                  <Link\r\n                    href={item.url}\r\n                    className={`transition-colors duration-200 flex items-center gap-1 hover:underline ${\r\n                      className?.includes('text-white')\r\n                        ? 'text-white hover:text-white/80'\r\n                        : className?.includes('text-blue-600')\r\n                        ? 'text-blue-600 hover:text-blue-800'\r\n                        : 'text-blue-600 hover:text-blue-800'\r\n                    }`}\r\n                    title={item.description}\r\n                  >\r\n                    {item.iconKey === 'home' && (\r\n                      <Home className={`w-3 h-3 ${\r\n                        className?.includes('text-white')\r\n                          ? 'text-white'\r\n                          : className?.includes('text-blue-600')\r\n                          ? 'text-blue-600'\r\n                          : 'text-current'\r\n                      }`} />\r\n                    )}\r\n                    {item.iconKey === 'blogs' && (\r\n                      <FileText className={`w-3 h-3 ${\r\n                        className?.includes('text-white')\r\n                          ? 'text-white'\r\n                          : className?.includes('text-blue-600')\r\n                          ? 'text-blue-600'\r\n                          : 'text-current'\r\n                      }`} />\r\n                    )}\r\n                    {item.iconKey === 'contact-us' && (\r\n                      <Phone className={`w-3 h-3 ${\r\n                        className?.includes('text-white')\r\n                          ? 'text-white'\r\n                          : className?.includes('text-blue-600')\r\n                          ? 'text-blue-600'\r\n                          : 'text-current'\r\n                      }`} />\r\n                    )}\r\n                    <TruncatedText\r\n                      text={item.name}\r\n                      onExpansionChange={setHasExpandedText}\r\n                    />\r\n                  </Link>\r\n                )}\r\n              </motion.li>\r\n            ))}\r\n          </motion.ol>\r\n        </div>\r\n      </nav>\r\n    </>\r\n  );\r\n};\r\n\r\n// Client-side helper function for generating breadcrumb items\r\nexport const generateBreadcrumbs = (pageType, params = {}) => {\r\n  const baseBreadcrumb = {\r\n    name: \"Home\",\r\n    url: \"/\",\r\n    iconKey: \"home\",\r\n    description: \"Return to homepage\"\r\n  };\r\n\r\n  const breadcrumbConfigs = {\r\n    blog: (slug) => [\r\n      baseBreadcrumb,\r\n      {\r\n        name: \"Blog\",\r\n        url: \"/Blogs\",\r\n        iconKey: \"blogs\",\r\n        description: \"View all blog posts\"\r\n      },\r\n      {\r\n        name: params.title || \"Blog Post\",\r\n        url: `/Blogs/${slug}`,\r\n        current: true,\r\n        description: `${params.title || 'Blog post'} - Cybersecurity insights and penetration testing guidance`\r\n      }\r\n    ],\r\n    \r\n    service: (service) => [\r\n      baseBreadcrumb,\r\n      {\r\n        name: \"Services\",\r\n        url: \"/Services\",\r\n        iconKey: \"services\",\r\n        description: \"View all cybersecurity services\"\r\n      },\r\n      {\r\n        name: params.title || service,\r\n        url: `/Services/${service}`,\r\n        current: true,\r\n        iconKey: service,\r\n        description: `${params.title || service} penetration testing services`\r\n      }\r\n    ],\r\n\r\n    location: (country) => [\r\n      baseBreadcrumb,\r\n      {\r\n        name: \"Locations\",\r\n        url: \"/Locations\",\r\n        iconKey: \"locations\",\r\n        description: \"View all locations\"\r\n      },\r\n      {\r\n        name: params.countryName || country,\r\n        url: `/Locations/${country}`,\r\n        current: true,\r\n        description: `${params.countryName || country} cybersecurity services`\r\n      }\r\n    ],\r\n\r\n    company: (page) => [\r\n      baseBreadcrumb,\r\n      {\r\n        name: \"Company\",\r\n        url: \"/Company\",\r\n        iconKey: \"company\",\r\n        description: \"Learn about SecurityLit\"\r\n      },\r\n      {\r\n        name: params.title || page,\r\n        url: `/Company/${page}`,\r\n        current: true,\r\n        description: params.description || `${page} information`\r\n      }\r\n    ],\r\n\r\n    'company-size': (size) => [\r\n      baseBreadcrumb,\r\n      {\r\n        name: \"Company Size\",\r\n        url: \"/Company-size\",\r\n        iconKey: \"company\",\r\n        description: \"Solutions for different company sizes\"\r\n      },\r\n      {\r\n        name: params.title || size,\r\n        url: `/Company-size/${size}`,\r\n        current: true,\r\n        description: params.description || `${size} cybersecurity solutions`\r\n      }\r\n    ],\r\n\r\n    industry: (industry) => [\r\n      baseBreadcrumb,\r\n      {\r\n        name: \"Industries\",\r\n        url: \"/Industries\",\r\n        iconKey: \"company\",\r\n        description: \"Industry-specific cybersecurity solutions\"\r\n      },\r\n      {\r\n        name: params.title || industry,\r\n        url: `/Industries/${industry}`,\r\n        current: true,\r\n        description: params.description || `${industry} cybersecurity solutions`\r\n      }\r\n    ],\r\n\r\n    product: (product) => [\r\n      baseBreadcrumb,\r\n      {\r\n        name: \"Product\",\r\n        url: \"/Product\",\r\n        iconKey: \"product\",\r\n        description: \"Explore our cybersecurity products\"\r\n      },\r\n      {\r\n        name: params.title || product,\r\n        url: `/Product/${product}`,\r\n        current: true,\r\n        description: params.description || `${product} product information`\r\n      }\r\n    ],\r\n\r\n    simple: (pageName, url) => [\r\n      baseBreadcrumb,\r\n      {\r\n        name: pageName,\r\n        url: url,\r\n        current: true,\r\n        iconKey: params.iconKey,\r\n        description: params.description || `${pageName} page`\r\n      }\r\n    ]\r\n  };\r\n\r\n  return breadcrumbConfigs[pageType] || (() => [baseBreadcrumb]);\r\n};\r\n\r\nexport default BreadcrumbNavigation; "], "names": [], "mappings": ";;;;;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AAJA;;;;;;AAMA;;;CAGC,GACD,MAAM,gBAAgB,CAAC,EAAE,IAAI,EAAE,YAAY,EAAE,EAAE,iBAAiB,EAAE;IAChE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,eAAe;QACnB,MAAM,cAAc,CAAC;QACrB,cAAc;QACd,IAAI,mBAAmB;YACrB,kBAAkB;QACpB;IACF;IAEA,IAAI,KAAK,MAAM,IAAI,WAAW;QAC5B,qBAAO,8OAAC;sBAAM;;;;;;IAChB;IAEA,qBACE,8OAAC;QACC,WAAU;QACV,SAAS;QACT,OAAO,aAAa,sBAAsB;;0BAE1C,8OAAC;gBAAK,WAAU;0BACb,aAAa,OAAO,GAAG,KAAK,SAAS,CAAC,GAAG,WAAW,GAAG,CAAC;;;;;;0BAE3D,8OAAC;gBAAK,WAAU;0BACb;;;;;;;;;;;;AAIT;AAEA;;CAEC,GACD,MAAM,2BAA2B,CAAC,EAAE,KAAK,EAAE;IACzC,MAAM,iBAAiB;QACrB,YAAY;QACZ,SAAS;QACT,mBAAmB,MAAM,GAAG,CAAC,CAAC,MAAM,QAAU,CAAC;gBAC7C,SAAS;gBACT,YAAY,QAAQ;gBACpB,QAAQ,KAAK,IAAI;gBACjB,QAAQ,KAAK,OAAO,GAAG,YAAY,CAAC,uBAAuB,EAAE,KAAK,GAAG,EAAE;YACzE,CAAC;IACH;IAEA,qBACE,8OAAC;QACC,MAAK;QACL,yBAAyB;YAAE,QAAQ,KAAK,SAAS,CAAC;QAAgB;;;;;;AAGxE;AAEA;;;CAGC,GACD,MAAM,uBAAuB,CAAC,EAAE,KAAK,EAAE,YAAY,EAAE,EAAE;IACrD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvD,uDAAuD;IACvD,MAAM,aAAa,OAAO,KAAK,CAAA,OAC7B,KAAK,GAAG,EAAE,SAAS,aACnB,KAAK,OAAO,KAAK,WACjB,WAAW,SAAS;IAGtB,qBACE;;0BAEE,8OAAC;gBAAyB,OAAO;;;;;;0BAGjC,8OAAC;gBACC,WAAW,CAAC,4BAA4B,EACtC,aACI,kBACE,iBACA,iBACF,eACL,CAAC,EAAE,WAAW;gBACf,cAAW;0BAEX,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;wBACR,WAAW,CAAC,+CAA+C,EACzD,WAAW,SAAS,gBAChB,eACA,WAAW,SAAS,mBACpB,kBACA,iBACJ;wBACF,SAAS;4BAAE,SAAS;wBAAE;wBACtB,SAAS;4BAAE,SAAS;wBAAE;wBACtB,YAAY;4BAAE,UAAU;wBAAI;kCAE3B,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;gCAER,WAAU;gCACV,SAAS;oCAAE,SAAS;gCAAE;gCACtB,SAAS;oCAAE,SAAS;gCAAE;gCACtB,YAAY;oCAAE,UAAU;oCAAK,OAAO,QAAQ;gCAAI;;oCAE/C,QAAQ,mBACP,8OAAC,sNAAA,CAAA,eAAY;wCACX,WAAW,CAAC,aAAa,EACvB,WAAW,SAAS,gBAChB,kBACA,WAAW,SAAS,mBACpB,kBACA,iBACJ;wCACF,eAAY;;;;;;oCAIf,KAAK,OAAO,iBACX,8OAAC;wCACC,WAAW,CAAC,oCAAoC,EAC9C,WAAW,SAAS,gBAChB,eACA,WAAW,SAAS,mBACpB,kBACA,iBACJ;wCACF,gBAAa;wCACb,OAAO,KAAK,WAAW;;4CAEtB,KAAK,OAAO,KAAK,wBAChB,8OAAC,mMAAA,CAAA,OAAI;gDAAC,WAAW,CAAC,QAAQ,EACxB,WAAW,SAAS,gBAChB,eACA,WAAW,SAAS,mBACpB,kBACA,iBACJ;;;;;;4CAEH,KAAK,OAAO,KAAK,yBAChB,8OAAC,8MAAA,CAAA,WAAQ;gDAAC,WAAW,CAAC,QAAQ,EAC5B,WAAW,SAAS,gBAChB,eACA,WAAW,SAAS,mBACpB,kBACA,iBACJ;;;;;;4CAEH,KAAK,OAAO,KAAK,8BAChB,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAW,CAAC,QAAQ,EACzB,WAAW,SAAS,gBAChB,eACA,WAAW,SAAS,mBACpB,kBACA,iBACJ;;;;;;0DAEJ,8OAAC;gDACC,MAAM,KAAK,IAAI;gDACf,mBAAmB;;;;;;;;;;;iGAIvB,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAM,KAAK,GAAG;wCACd,WAAW,CAAC,uEAAuE,EACjF,WAAW,SAAS,gBAChB,mCACA,WAAW,SAAS,mBACpB,sCACA,qCACJ;wCACF,OAAO,KAAK,WAAW;;4CAEtB,KAAK,OAAO,KAAK,wBAChB,8OAAC,mMAAA,CAAA,OAAI;gDAAC,WAAW,CAAC,QAAQ,EACxB,WAAW,SAAS,gBAChB,eACA,WAAW,SAAS,mBACpB,kBACA,gBACJ;;;;;;4CAEH,KAAK,OAAO,KAAK,yBAChB,8OAAC,8MAAA,CAAA,WAAQ;gDAAC,WAAW,CAAC,QAAQ,EAC5B,WAAW,SAAS,gBAChB,eACA,WAAW,SAAS,mBACpB,kBACA,gBACJ;;;;;;4CAEH,KAAK,OAAO,KAAK,8BAChB,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAW,CAAC,QAAQ,EACzB,WAAW,SAAS,gBAChB,eACA,WAAW,SAAS,mBACpB,kBACA,gBACJ;;;;;;0DAEJ,8OAAC;gDACC,MAAM,KAAK,IAAI;gDACf,mBAAmB;;;;;;;;;;;;;+BAxGpB;;;;;;;;;;;;;;;;;;;;;;AAmHrB;AAGO,MAAM,sBAAsB,CAAC,UAAU,SAAS,CAAC,CAAC;IACvD,MAAM,iBAAiB;QACrB,MAAM;QACN,KAAK;QACL,SAAS;QACT,aAAa;IACf;IAEA,MAAM,oBAAoB;QACxB,MAAM,CAAC,OAAS;gBACd;gBACA;oBACE,MAAM;oBACN,KAAK;oBACL,SAAS;oBACT,aAAa;gBACf;gBACA;oBACE,MAAM,OAAO,KAAK,IAAI;oBACtB,KAAK,CAAC,OAAO,EAAE,MAAM;oBACrB,SAAS;oBACT,aAAa,GAAG,OAAO,KAAK,IAAI,YAAY,0DAA0D,CAAC;gBACzG;aACD;QAED,SAAS,CAAC,UAAY;gBACpB;gBACA;oBACE,MAAM;oBACN,KAAK;oBACL,SAAS;oBACT,aAAa;gBACf;gBACA;oBACE,MAAM,OAAO,KAAK,IAAI;oBACtB,KAAK,CAAC,UAAU,EAAE,SAAS;oBAC3B,SAAS;oBACT,SAAS;oBACT,aAAa,GAAG,OAAO,KAAK,IAAI,QAAQ,6BAA6B,CAAC;gBACxE;aACD;QAED,UAAU,CAAC,UAAY;gBACrB;gBACA;oBACE,MAAM;oBACN,KAAK;oBACL,SAAS;oBACT,aAAa;gBACf;gBACA;oBACE,MAAM,OAAO,WAAW,IAAI;oBAC5B,KAAK,CAAC,WAAW,EAAE,SAAS;oBAC5B,SAAS;oBACT,aAAa,GAAG,OAAO,WAAW,IAAI,QAAQ,uBAAuB,CAAC;gBACxE;aACD;QAED,SAAS,CAAC,OAAS;gBACjB;gBACA;oBACE,MAAM;oBACN,KAAK;oBACL,SAAS;oBACT,aAAa;gBACf;gBACA;oBACE,MAAM,OAAO,KAAK,IAAI;oBACtB,KAAK,CAAC,SAAS,EAAE,MAAM;oBACvB,SAAS;oBACT,aAAa,OAAO,WAAW,IAAI,GAAG,KAAK,YAAY,CAAC;gBAC1D;aACD;QAED,gBAAgB,CAAC,OAAS;gBACxB;gBACA;oBACE,MAAM;oBACN,KAAK;oBACL,SAAS;oBACT,aAAa;gBACf;gBACA;oBACE,MAAM,OAAO,KAAK,IAAI;oBACtB,KAAK,CAAC,cAAc,EAAE,MAAM;oBAC5B,SAAS;oBACT,aAAa,OAAO,WAAW,IAAI,GAAG,KAAK,wBAAwB,CAAC;gBACtE;aACD;QAED,UAAU,CAAC,WAAa;gBACtB;gBACA;oBACE,MAAM;oBACN,KAAK;oBACL,SAAS;oBACT,aAAa;gBACf;gBACA;oBACE,MAAM,OAAO,KAAK,IAAI;oBACtB,KAAK,CAAC,YAAY,EAAE,UAAU;oBAC9B,SAAS;oBACT,aAAa,OAAO,WAAW,IAAI,GAAG,SAAS,wBAAwB,CAAC;gBAC1E;aACD;QAED,SAAS,CAAC,UAAY;gBACpB;gBACA;oBACE,MAAM;oBACN,KAAK;oBACL,SAAS;oBACT,aAAa;gBACf;gBACA;oBACE,MAAM,OAAO,KAAK,IAAI;oBACtB,KAAK,CAAC,SAAS,EAAE,SAAS;oBAC1B,SAAS;oBACT,aAAa,OAAO,WAAW,IAAI,GAAG,QAAQ,oBAAoB,CAAC;gBACrE;aACD;QAED,QAAQ,CAAC,UAAU,MAAQ;gBACzB;gBACA;oBACE,MAAM;oBACN,KAAK;oBACL,SAAS;oBACT,SAAS,OAAO,OAAO;oBACvB,aAAa,OAAO,WAAW,IAAI,GAAG,SAAS,KAAK,CAAC;gBACvD;aACD;IACH;IAEA,OAAO,iBAAiB,CAAC,SAAS,IAAI,CAAC,IAAM;YAAC;SAAe;AAC/D;uCAEe", "debugId": null}}, {"offset": {"line": 386, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/src/app/training/components/HeroBento.jsx"], "sourcesContent": ["\"use client\";\n\nimport React from 'react';\nimport { Shield, Play, ArrowRight, Mail } from 'lucide-react';\nimport BreadcrumbNavigation from '../../common/components/BreadcrumbNavigation';\n\nexport default function HeroBento() {\n  const breadcrumbItems = [\n    {\n      name: \"Home\",\n      url: \"/\",\n      iconKey: \"home\",\n      description: \"Return to homepage\"\n    },\n    {\n      name: \"Training\",\n      url: \"/training\",\n      current: true,\n      iconKey: \"graduation-cap\",\n      description: \"Explore SecurityLit's cybersecurity training programs\"\n    }\n  ];\n\n  return (\n    <div className=\"min-h-screen bg-white\">\n      {/* Hero Section - Light Color */}\n      <section className=\"py-12 sm:py-16 lg:py-24 bg-gradient-to-br from-[var(--color-blue)]/10 to-[var(--color-blue-secondary)]/10 relative overflow-hidden\">\n        <div className=\"absolute inset-0 bg-cover bg-center bg-no-repeat opacity-5\"\n             style={{ backgroundImage: 'url(/images/hexagon.svg)' }}>\n        </div>\n\n        <div className=\"relative z-10 container mx-auto px-4 max-w-7xl\">\n          <div className=\"flex flex-col lg:flex-row items-center gap-12\">\n            <div className=\"flex-1 text-center lg:text-left\">\n              {/* Breadcrumb */}\n              <div className=\"mb-6\">\n                <BreadcrumbNavigation items={breadcrumbItems} className=\"text-[var(--color-dark-blue)]\" />\n              </div>\n\n              <div className=\"inline-flex items-center bg-gradient-to-r from-[var(--color-blue)]/10 to-[var(--color-blue-secondary)]/10 px-6 py-3 rounded-full mb-8 border border-[var(--color-blue)]/20\">\n                <Shield className=\"w-5 h-5 text-[var(--color-blue)] mr-3\" />\n                <span className=\"text-sm font-semibold text-[var(--color-blue)]\">Security Lit Presents</span>\n              </div>\n\n              <h1 className=\"text-4xl lg:text-5xl font-bold text-[var(--color-dark-blue)] mb-6 leading-tight\">\n                Are you searching for training in\n                <span className=\"block bg-gradient-to-r from-[var(--color-blue)] to-[var(--color-blue-secondary)] bg-clip-text text-transparent\">\n                  Cyber Security field?\n                </span>\n              </h1>\n\n              <p className=\"text-lg text-[var(--foreground-secondary)] mb-8 leading-relaxed max-w-3xl\">\n                We are among the few companies in India offering internships across different sectors of Cyber Security. Check out real-life Cyber Security projects, get awesome experience to kickstart your career in cyber security and totally change your life!\n              </p>\n\n              <div className=\"flex flex-col sm:flex-row gap-4 justify-center lg:justify-start\">\n                <button className=\"group bg-gradient-to-r from-[var(--color-blue)] to-[var(--color-blue-secondary)] text-white px-6 py-3 rounded-lg font-semibold text-lg transition-all duration-300 shadow-lg hover:shadow-xl flex items-center justify-center gap-2\">\n                  <Play className=\"w-5 h-5\" />\n                  Enroll Now\n                  <ArrowRight className=\"w-5 h-5 group-hover:translate-x-1 transition-transform\" />\n                </button>\n\n                <button className=\"group bg-white text-[var(--color-dark-blue)] px-6 py-3 rounded-lg font-semibold text-lg transition-all duration-300 border-2 border-[var(--color-blue)]/20 hover:bg-[var(--color-blue)]/5 flex items-center justify-center gap-2\">\n                  <Mail className=\"w-5 h-5\" />\n                  Contact Us\n                </button>\n              </div>\n            </div>\n\n            <div className=\"flex-1\">\n              <div className=\"relative\">\n                <img\n                  src=\"https://securitylit.com/images/p1s1.png\"\n                  alt=\"Cybersecurity Training\"\n                  className=\"w-full h-[500px] rounded-2xl shadow-2xl\"\n                />\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n    </div>\n  );\n} "], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AACA;AAJA;;;;;AAMe,SAAS;IACtB,MAAM,kBAAkB;QACtB;YACE,MAAM;YACN,KAAK;YACL,SAAS;YACT,aAAa;QACf;QACA;YACE,MAAM;YACN,KAAK;YACL,SAAS;YACT,SAAS;YACT,aAAa;QACf;KACD;IAED,qBACE,8OAAC;QAAI,WAAU;kBAEb,cAAA,8OAAC;YAAQ,WAAU;;8BACjB,8OAAC;oBAAI,WAAU;oBACV,OAAO;wBAAE,iBAAiB;oBAA2B;;;;;;8BAG1D,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,2JAAA,CAAA,UAAoB;4CAAC,OAAO;4CAAiB,WAAU;;;;;;;;;;;kDAG1D,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,sMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,8OAAC;gDAAK,WAAU;0DAAiD;;;;;;;;;;;;kDAGnE,8OAAC;wCAAG,WAAU;;4CAAkF;0DAE9F,8OAAC;gDAAK,WAAU;0DAAiH;;;;;;;;;;;;kDAKnI,8OAAC;wCAAE,WAAU;kDAA4E;;;;;;kDAIzF,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAO,WAAU;;kEAChB,8OAAC,kMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDAAY;kEAE5B,8OAAC,kNAAA,CAAA,aAAU;wDAAC,WAAU;;;;;;;;;;;;0DAGxB,8OAAC;gDAAO,WAAU;;kEAChB,8OAAC,kMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDAAY;;;;;;;;;;;;;;;;;;;0CAMlC,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCACC,KAAI;wCACJ,KAAI;wCACJ,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS5B", "debugId": null}}, {"offset": {"line": 611, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/src/app/training/components/CurriculumHighlights.jsx"], "sourcesContent": ["\"use client\";\n\nimport React from 'react';\nimport { Shield, Users, TrendingUp, ArrowRight } from 'lucide-react';\n\nexport default function CurriculumHighlights() {\n  const cards = [\n    {\n      icon: Shield,\n      title: \"Conduct Advanced Security Assessments\",\n      description: \"In the free tier, you'll gain the skills to perform comprehensive security assessments, vulnerability analyses, and penetration testing on various systems and networks, applying both theoretical knowledge and practical techniques learned during the training.\",\n      label: \"Hands On Training\"\n    },\n    {\n      icon: Users,\n      title: \"Join Elite Cybersecurity Teams\",\n      description: \"As a premium member, you'll be prepared to join similar high-level security teams, armed with industry-relevant skills and insider knowledge.\",\n      label: \"Professional Internship\"\n    },\n    {\n      icon: TrendingUp,\n      title: \"Pursue Diverse Cybersecurity Careers\",\n      description: \"Whether you choose the free or premium path, you'll explore various cybersecurity career options, understanding the roles and responsibilities in different organizations. This knowledge will help you make informed decisions about your career trajectory in the cybersecurity field.\",\n      label: \"Hands On Training\"\n    }\n  ];\n\n  return (\n    <section className=\"py-16 lg:py-24 bg-gradient-to-br from-[var(--color-dark-blue)] via-[var(--color-dark-blue-hover)] to-[var(--color-dark-blue-bg)] relative overflow-hidden\">\n      {/* Background Elements */}\n      <div className=\"absolute inset-0\">\n        <div className=\"absolute top-0 left-0 w-full h-full bg-[url('data:image/svg+xml,%3Csvg%20width%3D%2260%22%20height%3D%2260%22%20viewBox%3D%220%200%2060%2060%22%20xmlns%3D%22http%3A//www.w3.org/2000/svg%22%3E%3Cg%20fill%3D%22none%22%20fill-rule%3D%22evenodd%22%3E%3Cg%20fill%3D%22%23ffffff%22%20fill-opacity%3D%220.03%22%3E%3Ccircle%20cx%3D%2230%22%20cy%3D%2230%22%20r%3D%221%22/%3E%3C/g%3E%3C/g%3E%3C/svg%3E')] opacity-50\"></div>\n        <div className=\"absolute top-20 left-10 w-72 h-72 bg-[var(--color-blue)]/20 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob\"></div>\n        <div className=\"absolute bottom-20 right-10 w-72 h-72 bg-[var(--color-blue-secondary)]/20 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob animation-delay-2000\"></div>\n      </div>\n\n      <div className=\"relative z-10 container mx-auto px-4 max-w-7xl\">\n        {/* Header Section */}\n        <div className=\"text-center mb-20\">\n          <div className=\"text-[var(--color-blue)] text-sm font-medium mb-4\">\n            Security Lit Presents\n          </div>\n          \n          <h2 className=\"text-4xl lg:text-5xl font-bold text-white mb-8 leading-tight\">\n            Explore Our Comprehensive Curriculum\n          </h2>\n          \n          <p className=\"text-lg text-white/90 max-w-4xl mx-auto leading-relaxed px-4\">\n            Dive into essential areas such as web application security, network penetration testing, cloud security, API security,\n            and ethical hacking fundamentals.\n          </p>\n\n          {/* CTA Button */}\n          <div className=\"mt-12\">\n            <button className=\"inline-flex items-center gap-3 px-8 py-4 bg-[var(--color-blue)] text-white font-semibold rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 hover:bg-[var(--color-blue-secondary)]\">\n              <Shield className=\"w-5 h-5\" />\n              Get the Curriculum Brochure\n            </button>\n                  </div>\n                </div>\n                \n        {/* Three Cards Grid */}\n        <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-8 lg:gap-12\">\n          {cards.map((card, index) => (\n            <div key={index} className=\"group relative\">\n              <div className=\"relative h-full bg-white/10 backdrop-blur-xl rounded-3xl p-8 border border-white/20 shadow-2xl hover:shadow-3xl transition-all duration-300\">\n                {/* Card Background Pattern */}\n                <div className=\"absolute inset-0 bg-gradient-to-br from-[var(--color-blue)]/5 to-transparent rounded-3xl\"></div>\n                \n                <div className=\"relative z-10 h-full flex flex-col\">\n                  {/* Icon */}\n                  <div className=\"flex items-start justify-between mb-6\">\n                    <div className=\"w-14 h-14 bg-[var(--color-blue)]/20 backdrop-blur-sm rounded-2xl flex items-center justify-center shadow-lg\">\n                      <card.icon className=\"w-7 h-7 text-[var(--color-blue)]\" />\n                    </div>\n                  </div>\n                  \n                  {/* Content */}\n                <div className=\"flex-1\">\n                    <h3 className=\"text-xl lg:text-2xl font-bold text-white mb-4 leading-tight\">\n                      {card.title}\n                    </h3>\n                    <p className=\"text-lg text-white/80 leading-relaxed\">\n                      {card.description}\n                    </p>\n                  </div>\n\n                  {/* Arrow Indicator */}\n                  <div className=\"mt-6 pt-6 border-t border-[var(--color-blue)]/20\">\n                    <ArrowRight className=\"w-6 h-6 text-[var(--color-blue)] group-hover:translate-x-2 transition-transform\" />\n                  </div>\n                </div>\n              </div>\n            </div>\n          ))}\n        </div>\n      </div>\n    </section>\n  );\n} "], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAHA;;;;AAKe,SAAS;IACtB,MAAM,QAAQ;QACZ;YACE,MAAM,sMAAA,CAAA,SAAM;YACZ,OAAO;YACP,aAAa;YACb,OAAO;QACT;QACA;YACE,MAAM,oMAAA,CAAA,QAAK;YACX,OAAO;YACP,aAAa;YACb,OAAO;QACT;QACA;YACE,MAAM,kNAAA,CAAA,aAAU;YAChB,OAAO;YACP,aAAa;YACb,OAAO;QACT;KACD;IAED,qBACE,8OAAC;QAAQ,WAAU;;0BAEjB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;;;;;;;0BAGjB,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CAAoD;;;;;;0CAInE,8OAAC;gCAAG,WAAU;0CAA+D;;;;;;0CAI7E,8OAAC;gCAAE,WAAU;0CAA+D;;;;;;0CAM5E,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAO,WAAU;;sDAChB,8OAAC,sMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;wCAAY;;;;;;;;;;;;;;;;;;kCAOpC,8OAAC;wBAAI,WAAU;kCACZ,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,8OAAC;gCAAgB,WAAU;0CACzB,cAAA,8OAAC;oCAAI,WAAU;;sDAEb,8OAAC;4CAAI,WAAU;;;;;;sDAEf,8OAAC;4CAAI,WAAU;;8DAEb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC,KAAK,IAAI;4DAAC,WAAU;;;;;;;;;;;;;;;;8DAK3B,8OAAC;oDAAI,WAAU;;sEACX,8OAAC;4DAAG,WAAU;sEACX,KAAK,KAAK;;;;;;sEAEb,8OAAC;4DAAE,WAAU;sEACV,KAAK,WAAW;;;;;;;;;;;;8DAKrB,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,kNAAA,CAAA,aAAU;wDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;+BAzBpB;;;;;;;;;;;;;;;;;;;;;;AAmCtB", "debugId": null}}, {"offset": {"line": 854, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/src/app/training/components/WhoCanJoin.jsx"], "sourcesContent": ["\"use client\";\n\nimport React from 'react';\nimport { motion } from 'framer-motion';\nimport { User, GraduationCap, Briefcase, Star, ArrowRight, Shield, Zap } from 'lucide-react';\n\nconst audienceTypes = [\n  {\n    icon: User,\n    title: \"Cybersecurity Enthusiasts\",\n    description: \"Whether you're new to the field or have some experience, our program caters to learners of all levels. Start with the free tier to build a strong foundation, then upgrade to the premium tier for a more immersive, mentor-guided experience.\",\n    color: \"from-[var(--color-blue)] to-[var(--color-blue-secondary)]\",\n    badge: \"All Levels\",\n    delay: 0.1\n  },\n  {\n    icon: GraduationCap,\n    title: \"Information Security Professionals\",\n    description: \"If you have been practicing cybersecurity topics, you can join the premium tier to take your skills to the next level through hands-on projects and industry-relevant training.\",\n    color: \"from-[var(--color-dark-blue)] to-[var(--color-dark-blue-hover)]\",\n    badge: \"Advanced\",\n    delay: 0.3\n  },\n  {\n    icon: Briefcase,\n    title: \"Career Switchers\",\n    description: \"Looking to transition into the exciting world of cybersecurity? Our program provides the knowledge and practical experience you need to kickstart your career in this high-demand field.\",\n    color: \"from-[var(--color-yellow)] to-[var(--color-yellow-hover)]\",\n    badge: \"Career Change\",\n    delay: 0.5\n  }\n];\n\nexport default function WhoCanJoin() {\n  return (\n    <section className=\"py-12 sm:py-16 lg:py-24 bg-white relative overflow-hidden\">\n      {/* Background Elements */}\n      <div className=\"absolute inset-0\">\n        <div className=\"absolute top-0 left-1/4 w-96 h-96 bg-gradient-to-br from-[var(--color-blue)]/10 to-[var(--color-blue-secondary)]/10 rounded-full blur-3xl\"></div>\n        <div className=\"absolute bottom-0 right-1/4 w-96 h-96 bg-gradient-to-br from-[var(--color-yellow)]/10 to-[var(--color-yellow-hover)]/10 rounded-full blur-3xl\"></div>\n      </div>\n\n      <div className=\"relative z-10 container mx-auto px-4 max-w-7xl\">\n        {/* Header */}\n        <motion.div \n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          viewport={{ once: true }}\n          className=\"text-center mb-16\"\n        >\n          <motion.div \n            initial={{ opacity: 0, scale: 0.8 }}\n            whileInView={{ opacity: 1, scale: 1 }}\n            transition={{ delay: 0.2, duration: 0.6 }}\n            viewport={{ once: true }}\n            className=\"inline-flex items-center bg-gradient-to-r from-[var(--color-blue)]/10 to-[var(--color-blue-secondary)]/10 px-4 py-2 rounded-full mb-6\"\n          >\n            <Star className=\"w-4 h-4 text-[var(--color-blue)] mr-2\" />\n            <span className=\"text-sm font-medium text-[var(--color-blue)]\">Who Can Join</span>\n          </motion.div>\n          \n          <motion.h2 \n            initial={{ opacity: 0, y: 20 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ delay: 0.4, duration: 0.6 }}\n            viewport={{ once: true }}\n            className=\"text-4xl lg:text-5xl font-bold text-[var(--color-dark-blue)] mb-6\"\n          >\n            So Who Can Take Up\n            <span className=\"block bg-gradient-to-r from-[var(--color-blue)] to-[var(--color-blue-secondary)] bg-clip-text text-transparent\">\n              This Training?\n            </span>\n          </motion.h2>\n          \n          <motion.p \n            initial={{ opacity: 0, y: 20 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ delay: 0.6, duration: 0.6 }}\n            viewport={{ once: true }}\n            className=\"text-lg text-[var(--foreground-secondary)] max-w-4xl mx-auto leading-relaxed px-4\"\n          >\n            Our program is designed to be accessible and beneficial for a wide range of learners, from beginners to experienced professionals. Whether you're looking to start your cybersecurity journey or take it to new heights, we've got you covered.\n          </motion.p>\n        </motion.div>\n\n        {/* Cards with Equal Heights */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 sm:gap-8 lg:gap-12\">\n          {audienceTypes.map((audience, index) => (\n            <motion.div\n              key={index}\n              initial={{ opacity: 0, y: 50 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ delay: audience.delay, duration: 0.8 }}\n              viewport={{ once: true }}\n              whileHover={{ \n                y: -10,\n                transition: { duration: 0.3 }\n              }}\n              className=\"group relative h-full\"\n            >\n              {/* Card with Equal Height */}\n              <div className=\"relative bg-white rounded-3xl p-8 shadow-[0_20px_40px_rgb(0,0,0,0.08)] hover:shadow-[0_30px_60px_rgb(0,0,0,0.12)] transition-all duration-500 border border-gray-100 h-full flex flex-col\">\n                \n                {/* Fixed Badge - No Transparency Issues */}\n                <motion.div \n                  initial={{ opacity: 0, scale: 0 }}\n                  whileInView={{ opacity: 1, scale: 1 }}\n                  transition={{ delay: audience.delay + 0.2, duration: 0.5 }}\n                  viewport={{ once: true }}\n                  className={`absolute -top-4 left-8 bg-gradient-to-r ${audience.color} text-white px-4 py-2 rounded-full text-sm font-semibold shadow-lg z-10 border-2 border-white`}\n                >\n                  {audience.badge}\n                </motion.div>\n\n                {/* Icon Container */}\n                <motion.div \n                  whileHover={{ \n                    rotate: 360,\n                    scale: 1.1\n                  }}\n                  transition={{ duration: 0.6 }}\n                  className={`w-20 h-20 bg-gradient-to-br ${audience.color} rounded-3xl flex items-center justify-center mb-6 shadow-xl group-hover:shadow-2xl transition-all duration-300`}\n                >\n                  <audience.icon className=\"w-10 h-10 text-white\" />\n                </motion.div>\n\n                {/* Content - Flex Grow to Fill Space */}\n                <div className=\"space-y-4 flex-1 flex flex-col\">\n                  <div>\n                    <h3 className=\"text-xl lg:text-2xl font-bold text-[var(--color-dark-blue)] mb-4\">\n                      {audience.title}\n                    </h3>\n                  </div>\n                  \n                  <p className=\"text-lg text-[var(--foreground-secondary)] leading-relaxed flex-1\">\n                    {audience.description}\n                  </p>\n\n                  {/* CTA Button - Fixed at Bottom */}\n                  <motion.button\n                    whileHover={{ scale: 1.05 }}\n                    whileTap={{ scale: 0.95 }}\n                    className={`w-full bg-gradient-to-r ${audience.color} text-white py-3 px-6 rounded-2xl font-semibold shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center gap-2 group/btn mt-auto`}\n                  >\n                    Get Started\n                    <ArrowRight className=\"w-4 h-4 group-hover/btn:translate-x-1 transition-transform\" />\n                  </motion.button>\n                </div>\n\n                {/* Enhanced Claymorphic Effects */}\n                <div className=\"absolute inset-0 bg-gradient-to-br from-white/30 to-transparent rounded-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none\" />\n                <div className={`absolute inset-0 bg-gradient-to-br ${audience.color} rounded-3xl opacity-0 group-hover:opacity-5 transition-opacity duration-300 blur-xl pointer-events-none`} />\n              </div>\n            </motion.div>\n          ))}\n        </div>\n      </div>\n    </section>\n  );\n} "], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAJA;;;;;AAMA,MAAM,gBAAgB;IACpB;QACE,MAAM,kMAAA,CAAA,OAAI;QACV,OAAO;QACP,aAAa;QACb,OAAO;QACP,OAAO;QACP,OAAO;IACT;IACA;QACE,MAAM,wNAAA,CAAA,gBAAa;QACnB,OAAO;QACP,aAAa;QACb,OAAO;QACP,OAAO;QACP,OAAO;IACT;IACA;QACE,MAAM,4MAAA,CAAA,YAAS;QACf,OAAO;QACP,aAAa;QACb,OAAO;QACP,OAAO;QACP,OAAO;IACT;CACD;AAEc,SAAS;IACtB,qBACE,8OAAC;QAAQ,WAAU;;0BAEjB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;;;;;;;0BAGjB,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,YAAY;4BAAE,UAAU;wBAAI;wBAC5B,UAAU;4BAAE,MAAM;wBAAK;wBACvB,WAAU;;0CAEV,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,OAAO;gCAAI;gCAClC,aAAa;oCAAE,SAAS;oCAAG,OAAO;gCAAE;gCACpC,YAAY;oCAAE,OAAO;oCAAK,UAAU;gCAAI;gCACxC,UAAU;oCAAE,MAAM;gCAAK;gCACvB,WAAU;;kDAEV,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;kDAChB,8OAAC;wCAAK,WAAU;kDAA+C;;;;;;;;;;;;0CAGjE,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;gCACR,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,YAAY;oCAAE,OAAO;oCAAK,UAAU;gCAAI;gCACxC,UAAU;oCAAE,MAAM;gCAAK;gCACvB,WAAU;;oCACX;kDAEC,8OAAC;wCAAK,WAAU;kDAAiH;;;;;;;;;;;;0CAKnI,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;gCACP,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,YAAY;oCAAE,OAAO;oCAAK,UAAU;gCAAI;gCACxC,UAAU;oCAAE,MAAM;gCAAK;gCACvB,WAAU;0CACX;;;;;;;;;;;;kCAMH,8OAAC;wBAAI,WAAU;kCACZ,cAAc,GAAG,CAAC,CAAC,UAAU,sBAC5B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCAET,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,YAAY;oCAAE,OAAO,SAAS,KAAK;oCAAE,UAAU;gCAAI;gCACnD,UAAU;oCAAE,MAAM;gCAAK;gCACvB,YAAY;oCACV,GAAG,CAAC;oCACJ,YAAY;wCAAE,UAAU;oCAAI;gCAC9B;gCACA,WAAU;0CAGV,cAAA,8OAAC;oCAAI,WAAU;;sDAGb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4CACT,SAAS;gDAAE,SAAS;gDAAG,OAAO;4CAAE;4CAChC,aAAa;gDAAE,SAAS;gDAAG,OAAO;4CAAE;4CACpC,YAAY;gDAAE,OAAO,SAAS,KAAK,GAAG;gDAAK,UAAU;4CAAI;4CACzD,UAAU;gDAAE,MAAM;4CAAK;4CACvB,WAAW,CAAC,wCAAwC,EAAE,SAAS,KAAK,CAAC,6FAA6F,CAAC;sDAElK,SAAS,KAAK;;;;;;sDAIjB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4CACT,YAAY;gDACV,QAAQ;gDACR,OAAO;4CACT;4CACA,YAAY;gDAAE,UAAU;4CAAI;4CAC5B,WAAW,CAAC,4BAA4B,EAAE,SAAS,KAAK,CAAC,+GAA+G,CAAC;sDAEzK,cAAA,8OAAC,SAAS,IAAI;gDAAC,WAAU;;;;;;;;;;;sDAI3B,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;8DACC,cAAA,8OAAC;wDAAG,WAAU;kEACX,SAAS,KAAK;;;;;;;;;;;8DAInB,8OAAC;oDAAE,WAAU;8DACV,SAAS,WAAW;;;;;;8DAIvB,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;oDACZ,YAAY;wDAAE,OAAO;oDAAK;oDAC1B,UAAU;wDAAE,OAAO;oDAAK;oDACxB,WAAW,CAAC,wBAAwB,EAAE,SAAS,KAAK,CAAC,8JAA8J,CAAC;;wDACrN;sEAEC,8OAAC,kNAAA,CAAA,aAAU;4DAAC,WAAU;;;;;;;;;;;;;;;;;;sDAK1B,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;4CAAI,WAAW,CAAC,mCAAmC,EAAE,SAAS,KAAK,CAAC,wGAAwG,CAAC;;;;;;;;;;;;+BA9D3K;;;;;;;;;;;;;;;;;;;;;;AAsEnB", "debugId": null}}, {"offset": {"line": 1217, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/src/app/training/components/StudentLogos.jsx"], "sourcesContent": ["\"use client\";\n\nimport React from 'react';\nimport { CheckCircle } from 'lucide-react';\n\nconst premiumBenefits = [\n  \"Access to advance Lab Subscription (TryHackMe, HackTheBox)\",\n  \"Personalized mentorship and guided learning\",\n  \"3 months of hands-on experience with SecurityLit\",\n  \"Professional report writing training\",\n  \"Experience letter upon completion\"\n];\n\nexport default function StudentLogos() {\n  return (\n    <section className=\"py-24 bg-white\">\n      <div className=\"container mx-auto px-4 max-w-7xl\">\n        {/* Header Section */}\n        <div className=\"text-center mb-16\">\n          <h2 className=\"text-4xl lg:text-5xl font-bold text-[var(--color-dark-blue)] mb-8\">\n            Our Trained Students works at\n          </h2>\n          \n          {/* Company Logos */}\n          <div className=\"flex justify-center items-center gap-8 mb-16\">\n            <div className=\"flex items-center justify-center\">\n              <img \n                src=\"https://securitylit.com/images/aia_nz_logo.jpg\" \n                alt=\"AIA NZ\" \n                className=\"h-16 w-auto object-contain\"\n              />\n            </div>\n            <div className=\"flex items-center justify-center\">\n              <img \n                src=\"https://securitylit.com/images/data_torque_ltd_logo.jpg\" \n                alt=\"Data Torque\" \n                className=\"h-16 w-auto object-contain\"\n              />\n            </div>\n          </div>\n        </div>\n\n        {/* Content Grid */}\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-12 items-start\">\n          {/* Left Section - Premium Tier Benefits */}\n          <div className=\"bg-gradient-to-br from-[var(--color-dark-blue)] to-[var(--color-dark-blue-hover)] rounded-3xl p-8 lg:p-12 text-white\">\n            <h3 className=\"text-2xl lg:text-3xl font-bold text-white mb-6\">\n              Premium Tier Benefits\n            </h3>\n            \n            <p className=\"text-lg text-white/90 leading-relaxed mb-8\">\n              As a Premium member, you'll work directly with SecurityLit on live penetration testing projects. This hands-on experience will immerse you in real-time cybersecurity challenges, teaching you how professional pentests are conducted. You'll learn the intricacies of report writing, client communication, and industry best practices. Upon completion, you'll receive an experience letter, validating your practical skills and boosting your career prospects in the cybersecurity field.\n            </p>\n          </div>\n\n          {/* Right Section - Benefits List */}\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n            {premiumBenefits.map((benefit, index) => (\n              <div \n                key={index} \n                className=\"bg-gradient-to-br from-[var(--color-blue)]/10 to-[var(--color-blue-secondary)]/10 rounded-2xl p-6 border border-[var(--color-blue)]/20 hover:border-[var(--color-blue)]/30 transition-all duration-300\"\n              >\n                <div className=\"flex items-start gap-4\">\n                  <div className=\"w-8 h-8 bg-[var(--color-blue)] rounded-lg flex items-center justify-center flex-shrink-0 mt-1\">\n                    <img \n                      src=\"https://securitylit.com/images/icon.png\" \n                      alt=\"Benefit Icon\" \n                      className=\"w-5 h-5 object-contain\"\n                    />\n                  </div>\n                  <p className=\"text-[var(--color-dark-blue)] font-medium leading-relaxed\">\n                    {benefit}\n                  </p>\n                </div>\n              </div>\n            ))}\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n} "], "names": [], "mappings": ";;;;AAEA;AAFA;;;;AAKA,MAAM,kBAAkB;IACtB;IACA;IACA;IACA;IACA;CACD;AAEc,SAAS;IACtB,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAoE;;;;;;sCAKlF,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCACC,KAAI;wCACJ,KAAI;wCACJ,WAAU;;;;;;;;;;;8CAGd,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCACC,KAAI;wCACJ,KAAI;wCACJ,WAAU;;;;;;;;;;;;;;;;;;;;;;;8BAOlB,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAiD;;;;;;8CAI/D,8OAAC;oCAAE,WAAU;8CAA6C;;;;;;;;;;;;sCAM5D,8OAAC;4BAAI,WAAU;sCACZ,gBAAgB,GAAG,CAAC,CAAC,SAAS,sBAC7B,8OAAC;oCAEC,WAAU;8CAEV,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDACC,KAAI;oDACJ,KAAI;oDACJ,WAAU;;;;;;;;;;;0DAGd,8OAAC;gDAAE,WAAU;0DACV;;;;;;;;;;;;mCAZA;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsBrB", "debugId": null}}, {"offset": {"line": 1394, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/src/app/training/components/LearningModules.jsx"], "sourcesContent": ["\"use client\";\n\nimport React from 'react';\n\nexport default function LearningModules({ content }) {\n  // Use content if provided, otherwise use default modules\n  const moduleContent = content || {\n    title: \"Here's A Short Teaser Of What You Will Learn\",\n    modules: [\n      {\n        title: \"Web Application Security\",\n        description: \"Fortifying the digital frontline against cyber threats.\"\n      },\n      {\n        title: \"API & Network Security\", \n        description: \"Safeguarding the backbone of modern interconnected systems.\"\n      },\n      {\n        title: \"Practical Skills Development\",\n        description: \"Honing real-world cybersecurity expertise through hands-on learning.\"\n      },\n      {\n        title: \"Soft Skill & Professional Growth\",\n        description: \"Cultivating the human element in technical cybersecurity roles.\"\n      },\n      {\n        title: \"Real World Environment Navigation\",\n        description: \"Mastering the art of securing complex, live digital ecosystems.\"\n      },\n      {\n        title: \"Active Directory & Cloud Security\",\n        description: \"Protecting the nerve centers of enterprise and cloud infrastructures.\"\n      },\n      {\n        title: \"Continuous Learning & Adaption\",\n        description: \"Staying ahead in the ever-evolving cybersecurity landscape.\"\n      },\n      {\n        title: \"Report Writing Skills\",\n        description: \"Crafting clear, concise, and impactful cybersecurity documentation for stakeholders at all levels.\"\n      }\n    ]\n  };\n\n  return (\n    <section className=\"py-16 lg:py-24 bg-gradient-to-br from-gray-50 to-white\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        \n        {/* Header */}\n        <div className=\"text-center mb-16\">\n          <h2 className=\"text-3xl sm:text-4xl lg:text-5xl font-bold text-[var(--color-dark-blue)] mb-6 font-poppins\">\n            {moduleContent.title}\n          </h2>\n        </div>\n\n        {/* Learning Areas Grid */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 lg:gap-8\">\n          {moduleContent.modules.map((module, index) => (\n            <div key={index} className=\"group\">\n              <div className=\"bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100 group-hover:border-[var(--color-blue)]/20 h-full flex flex-col\">\n                \n                {/* Icon */}\n                <div className=\"w-12 h-12 bg-gradient-to-br from-[var(--color-blue)]/10 to-[var(--color-blue-secondary)]/10 rounded-lg flex items-center justify-center mb-4 group-hover:scale-110 transition-transform\">\n                  <img \n                    src=\"/images/web.png\" \n                    alt=\"Web Security Icon\" \n                    className=\"w-6 h-6\"\n                  />\n                </div>\n\n                {/* Content */}\n                <h5 className=\"text-lg font-bold text-[var(--color-dark-blue)] mb-3 font-poppins\">\n                  {module.title}\n                </h5>\n                \n                <p className=\"text-[var(--foreground-secondary)] text-sm leading-relaxed flex-grow font-roboto\">\n                  {module.description}\n                </p>\n              </div>\n            </div>\n          ))}\n        </div>\n\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIe,SAAS,gBAAgB,EAAE,OAAO,EAAE;IACjD,yDAAyD;IACzD,MAAM,gBAAgB,WAAW;QAC/B,OAAO;QACP,SAAS;YACP;gBACE,OAAO;gBACP,aAAa;YACf;YACA;gBACE,OAAO;gBACP,aAAa;YACf;YACA;gBACE,OAAO;gBACP,aAAa;YACf;YACA;gBACE,OAAO;gBACP,aAAa;YACf;YACA;gBACE,OAAO;gBACP,aAAa;YACf;YACA;gBACE,OAAO;gBACP,aAAa;YACf;YACA;gBACE,OAAO;gBACP,aAAa;YACf;YACA;gBACE,OAAO;gBACP,aAAa;YACf;SACD;IACH;IAEA,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;;8BAGb,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAG,WAAU;kCACX,cAAc,KAAK;;;;;;;;;;;8BAKxB,8OAAC;oBAAI,WAAU;8BACZ,cAAc,OAAO,CAAC,GAAG,CAAC,CAAC,QAAQ,sBAClC,8OAAC;4BAAgB,WAAU;sCACzB,cAAA,8OAAC;gCAAI,WAAU;;kDAGb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CACC,KAAI;4CACJ,KAAI;4CACJ,WAAU;;;;;;;;;;;kDAKd,8OAAC;wCAAG,WAAU;kDACX,OAAO,KAAK;;;;;;kDAGf,8OAAC;wCAAE,WAAU;kDACV,OAAO,WAAW;;;;;;;;;;;;2BAlBf;;;;;;;;;;;;;;;;;;;;;AA4BtB", "debugId": null}}, {"offset": {"line": 1532, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/src/app/training/components/PremiumCTA.jsx"], "sourcesContent": ["\"use client\";\n\nimport React, { useState } from 'react';\nimport { motion } from 'framer-motion';\nimport { Crown, ArrowRight, CheckCircle, Star, Zap, Shield, Mail } from 'lucide-react';\n\nconst premiumFeatures = [\n  \"Access to advance Lab Subscription (TryHackMe, HackTheBox)\",\n  \"Personalized mentorship and guided learning\",\n  \"3 months of hands-on experience with SecurityLit\",\n  \"Professional report writing training\",\n  \"Experience letter upon completion\"\n];\n\nexport default function PremiumCTA() {\n  const [showForm, setShowForm] = useState(false);\n\n  const handleEmailContact = () => {\n    window.location.href = 'mailto:<EMAIL>';\n  };\n\n  return (\n    <>\n      {/* Main Premium Section */}\n      <section className=\"py-24 bg-gradient-to-br from-[var(--color-dark-blue)] via-[var(--color-dark-blue-hover)] to-[var(--color-dark-blue-bg)] relative overflow-hidden\">\n        {/* Background Elements */}\n        <div className=\"absolute inset-0\">\n          <div className=\"absolute top-0 left-0 w-full h-full bg-[url('data:image/svg+xml,%3Csvg%20width%3D%2260%22%20height%3D%2260%22%20viewBox%3D%220%200%2060%2060%22%20xmlns%3D%22http%3A//www.w3.org/2000/svg%22%3E%3Cg%20fill%3D%22none%22%20fill-rule%3D%22evenodd%22%3E%3Cg%20fill%3D%22%23ffffff%22%20fill-opacity%3D%220.03%22%3E%3Ccircle%20cx%3D%2230%22%20cy%3D%2230%22%20r%3D%221%22/%3E%3C/g%3E%3C/g%3E%3C/svg%3E')] opacity-50\"></div>\n          <div className=\"absolute top-20 left-10 w-72 h-72 bg-[var(--color-blue)]/20 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob\"></div>\n          <div className=\"absolute bottom-20 right-10 w-72 h-72 bg-[var(--color-blue-secondary)]/20 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob animation-delay-2000\"></div>\n        </div>\n\n        <div className=\"relative z-10 container mx-auto px-4 max-w-7xl\">\n          {/* Header */}\n          <motion.div \n            initial={{ opacity: 0, y: 30 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8 }}\n            viewport={{ once: true }}\n            className=\"text-center mb-16\"\n          >\n            <motion.div \n              initial={{ opacity: 0, scale: 0.8 }}\n              whileInView={{ opacity: 1, scale: 1 }}\n              transition={{ delay: 0.2, duration: 0.6 }}\n              viewport={{ once: true }}\n              className=\"inline-flex items-center bg-[var(--color-yellow)]/20 px-4 py-2 rounded-full mb-6\"\n            >\n              <Crown className=\"w-4 h-4 text-[var(--color-yellow)] mr-2\" />\n              <span className=\"text-sm font-medium text-[var(--color-yellow)]\">Premium Tier Benefits</span>\n            </motion.div>\n            \n            <motion.h2 \n              initial={{ opacity: 0, y: 20 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ delay: 0.4, duration: 0.6 }}\n              viewport={{ once: true }}\n              className=\"text-4xl lg:text-5xl font-bold text-white mb-6\"\n            >\n              As a Premium member, you'll work directly with\n              <span className=\"block bg-gradient-to-r from-[var(--color-blue)] to-[var(--color-blue-secondary)] bg-clip-text text-transparent\">\n                SecurityLit\n              </span>\n            </motion.h2>\n            \n            <motion.p \n              initial={{ opacity: 0, y: 20 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ delay: 0.6, duration: 0.6 }}\n              viewport={{ once: true }}\n              className=\"text-lg text-white/80 max-w-4xl mx-auto leading-relaxed\"\n            >\n              on live penetration testing projects. This hands-on experience will immerse you in real-time cybersecurity challenges, teaching you how professional pentests are conducted. You'll learn the intricacies of report writing, client communication, and industry best practices. Upon completion, you'll receive an experience letter, validating your practical skills and boosting your career prospects in the cybersecurity field.\n            </motion.p>\n          </motion.div>\n\n          {/* Premium Features Grid */}\n          <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center mb-16\">\n            {/* Features List */}\n            <motion.div \n              initial={{ opacity: 0, x: -50 }}\n              whileInView={{ opacity: 1, x: 0 }}\n              transition={{ duration: 0.8 }}\n              viewport={{ once: true }}\n              className=\"space-y-6\"\n            >\n              {premiumFeatures.map((feature, index) => (\n                <motion.div\n                  key={index}\n                  initial={{ opacity: 0, x: -30 }}\n                  whileInView={{ opacity: 1, x: 0 }}\n                  transition={{ delay: index * 0.1, duration: 0.6 }}\n                  viewport={{ once: true }}\n                  className=\"flex items-center gap-4\"\n                >\n                  <div className=\"w-12 h-12 bg-gradient-to-br from-[var(--color-blue)] to-[var(--color-blue-secondary)] rounded-2xl flex items-center justify-center shadow-lg\">\n                    <CheckCircle className=\"w-6 h-6 text-[var(--color-yellow)]\" />\n                  </div>\n                  <div>\n                    <h3 className=\"text-lg font-semibold text-white\">{feature}</h3>\n                  </div>\n                </motion.div>\n              ))}\n            </motion.div>\n\n            {/* Contact Card */}\n            <motion.div \n              initial={{ opacity: 0, x: 50 }}\n              whileInView={{ opacity: 1, x: 0 }}\n              transition={{ duration: 0.8 }}\n              viewport={{ once: true }}\n              className=\"relative\"\n            >\n              <div className=\"bg-white/10 backdrop-blur-xl rounded-3xl p-8 border border-white/20 shadow-2xl\">\n                <div className=\"text-center space-y-6\">\n                  {/* Premium Badge */}\n                  <div className=\"inline-flex items-center gap-2 bg-gradient-to-r from-[var(--color-yellow)] to-[var(--color-yellow-hover)] text-white px-6 py-3 rounded-full\">\n                    <Crown className=\"w-5 h-5\" />\n                    <span className=\"font-semibold\">Premium Plan</span>\n                  </div>\n\n                  {/* Contact Information */}\n                  <div>\n                    <div className=\"text-2xl font-bold text-white mb-2\">\n                      Get Custom Pricing\n                    </div>\n                    <p className=\"text-white/70\">Contact our team for personalized pricing and consultation</p>\n                  </div>\n\n                  {/* Contact Email */}\n                  <div className=\"bg-white/10 rounded-2xl p-4\">\n                    <div className=\"flex items-center justify-center gap-3\">\n                      <Mail className=\"w-5 h-5 text-[var(--color-blue)]\" />\n                      <span className=\"text-white font-medium\"><EMAIL></span>\n                    </div>\n                  </div>\n\n                  {/* Features */}\n                  <div className=\"space-y-3\">\n                    <div className=\"flex items-center justify-between text-sm\">\n                      <span className=\"text-white/70\">All Basic Features</span>\n                      <CheckCircle className=\"w-4 h-4 text-green-400\" />\n                    </div>\n                    <div className=\"flex items-center justify-between text-sm\">\n                      <span className=\"text-white/70\">Advanced Modules</span>\n                      <CheckCircle className=\"w-4 h-4 text-green-400\" />\n                    </div>\n                    <div className=\"flex items-center justify-between text-sm\">\n                      <span className=\"text-white/70\">1-on-1 Mentoring</span>\n                      <CheckCircle className=\"w-4 h-4 text-green-400\" />\n                    </div>\n                    <div className=\"flex items-center justify-between text-sm\">\n                      <span className=\"text-white/70\">Certification</span>\n                      <CheckCircle className=\"w-4 h-4 text-green-400\" />\n                    </div>\n                    <div className=\"flex items-center justify-between text-sm\">\n                      <span className=\"text-white/70\">Priority Support</span>\n                      <CheckCircle className=\"w-4 h-4 text-green-400\" />\n                    </div>\n                  </div>\n\n                  {/* CTA Button */}\n                  <motion.button\n                    whileHover={{ scale: 1.05 }}\n                    whileTap={{ scale: 0.95 }}\n                    onClick={handleEmailContact}\n                    className=\"w-full bg-gradient-to-r from-[var(--color-yellow)] to-[var(--color-yellow-hover)] hover:from-[var(--color-yellow-hover)] hover:to-[var(--color-yellow)] text-white py-4 px-8 rounded-2xl font-bold text-lg shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center gap-2\"\n                  >\n                    <Mail className=\"w-5 h-5\" />\n                    Contact for Premium Pricing\n                    <ArrowRight className=\"w-5 h-5\" />\n                  </motion.button>\n\n                  <p className=\"text-xs text-white/50\">\n                    Get personalized pricing and consultation\n                  </p>\n                </div>\n              </div>\n\n              {/* Floating Elements */}\n              <motion.div\n                animate={{ y: [-10, 10, -10] }}\n                transition={{ duration: 3, repeat: Infinity, ease: \"easeInOut\" }}\n                className=\"absolute -top-4 -right-4 w-8 h-8 bg-[var(--color-blue)] rounded-full opacity-80\"\n              />\n              <motion.div\n                animate={{ y: [10, -10, 10] }}\n                transition={{ duration: 4, repeat: Infinity, ease: \"easeInOut\" }}\n                className=\"absolute -bottom-4 -left-4 w-6 h-6 bg-[var(--color-blue-secondary)] rounded-full opacity-80\"\n              />\n            </motion.div>\n          </div>\n\n          {/* Bottom CTA */}\n          <motion.div \n            initial={{ opacity: 0, y: 30 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ delay: 0.8, duration: 0.6 }}\n            viewport={{ once: true }}\n            className=\"text-center mt-16\"\n          >\n            <p className=\"text-lg text-white/70 mb-4\">\n              Take your cybersecurity training to the next level with our premium tier. Contact our team for personalized pricing and consultation to unlock exclusive access to advanced labs, personalized mentorship, and hands-on projects.\n            </p>\n          </motion.div>\n        </div>\n      </section>\n\n      {/* Contact Form Modal */}\n      {showForm && (\n        <motion.div\n          initial={{ opacity: 0 }}\n          animate={{ opacity: 1 }}\n          exit={{ opacity: 0 }}\n          className=\"fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/50 backdrop-blur-sm\"\n          onClick={() => setShowForm(false)}\n        >\n          <motion.div\n            initial={{ opacity: 0, scale: 0.9, y: 20 }}\n            animate={{ opacity: 1, scale: 1, y: 0 }}\n            exit={{ opacity: 0, scale: 0.9, y: 20 }}\n            transition={{ type: \"spring\", damping: 25, stiffness: 300 }}\n            className=\"relative bg-white rounded-3xl shadow-2xl max-w-md w-full p-8\"\n            onClick={(e) => e.stopPropagation()}\n          >\n            <div className=\"text-center space-y-6\">\n              <div className=\"w-16 h-16 bg-gradient-to-br from-[var(--color-blue)] to-[var(--color-blue-secondary)] rounded-3xl flex items-center justify-center mx-auto\">\n                <Mail className=\"w-8 h-8 text-[var(--color-yellow)]\" />\n              </div>\n              \n              <div>\n                <h3 className=\"text-2xl font-bold text-[var(--color-dark-blue)] mb-2\">Contact Our Team</h3>\n                <p className=\"text-[var(--foreground-secondary)]\">Get personalized pricing and consultation for premium tier</p>\n              </div>\n\n              <form className=\"space-y-4\">\n                <input\n                  type=\"email\"\n                  placeholder=\"Enter your email\"\n                  className=\"w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:border-[var(--color-blue)]\"\n                />\n                <input\n                  type=\"text\"\n                  placeholder=\"Full name\"\n                  className=\"w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:border-[var(--color-blue)]\"\n                />\n                <textarea\n                  placeholder=\"Tell us about your requirements\"\n                  rows=\"3\"\n                  className=\"w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:border-[var(--color-blue)] resize-none\"\n                />\n                <motion.button\n                  whileHover={{ scale: 1.05 }}\n                  whileTap={{ scale: 0.95 }}\n                  className=\"w-full bg-gradient-to-r from-[var(--color-blue)] to-[var(--color-blue-secondary)] text-white py-3 px-6 rounded-xl font-semibold shadow-lg hover:shadow-xl transition-all duration-300\"\n                >\n                  Send Message\n                </motion.button>\n              </form>\n\n              <div className=\"text-sm text-[var(--foreground-secondary)]\">\n                Or email directly to: <a href=\"mailto:<EMAIL>\" className=\"text-[var(--color-blue)] hover:underline\"><EMAIL></a>\n              </div>\n\n              <button\n                onClick={() => setShowForm(false)}\n                className=\"text-[var(--foreground-secondary)] hover:text-[var(--color-dark-blue)] transition-colors\"\n              >\n                Close\n              </button>\n            </div>\n          </motion.div>\n        </motion.div>\n      )}\n    </>\n  );\n} "], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAJA;;;;;AAMA,MAAM,kBAAkB;IACtB;IACA;IACA;IACA;IACA;CACD;AAEc,SAAS;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,MAAM,qBAAqB;QACzB,OAAO,QAAQ,CAAC,IAAI,GAAG;IACzB;IAEA,qBACE;;0BAEE,8OAAC;gBAAQ,WAAU;;kCAEjB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAI,WAAU;;;;;;;;;;;;kCAGjB,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,YAAY;oCAAE,UAAU;gCAAI;gCAC5B,UAAU;oCAAE,MAAM;gCAAK;gCACvB,WAAU;;kDAEV,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,SAAS;4CAAE,SAAS;4CAAG,OAAO;wCAAI;wCAClC,aAAa;4CAAE,SAAS;4CAAG,OAAO;wCAAE;wCACpC,YAAY;4CAAE,OAAO;4CAAK,UAAU;wCAAI;wCACxC,UAAU;4CAAE,MAAM;wCAAK;wCACvB,WAAU;;0DAEV,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;0DACjB,8OAAC;gDAAK,WAAU;0DAAiD;;;;;;;;;;;;kDAGnE,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;wCACR,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,aAAa;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAChC,YAAY;4CAAE,OAAO;4CAAK,UAAU;wCAAI;wCACxC,UAAU;4CAAE,MAAM;wCAAK;wCACvB,WAAU;;4CACX;0DAEC,8OAAC;gDAAK,WAAU;0DAAiH;;;;;;;;;;;;kDAKnI,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;wCACP,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,aAAa;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAChC,YAAY;4CAAE,OAAO;4CAAK,UAAU;wCAAI;wCACxC,UAAU;4CAAE,MAAM;wCAAK;wCACvB,WAAU;kDACX;;;;;;;;;;;;0CAMH,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,SAAS;4CAAE,SAAS;4CAAG,GAAG,CAAC;wCAAG;wCAC9B,aAAa;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAChC,YAAY;4CAAE,UAAU;wCAAI;wCAC5B,UAAU;4CAAE,MAAM;wCAAK;wCACvB,WAAU;kDAET,gBAAgB,GAAG,CAAC,CAAC,SAAS,sBAC7B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gDAET,SAAS;oDAAE,SAAS;oDAAG,GAAG,CAAC;gDAAG;gDAC9B,aAAa;oDAAE,SAAS;oDAAG,GAAG;gDAAE;gDAChC,YAAY;oDAAE,OAAO,QAAQ;oDAAK,UAAU;gDAAI;gDAChD,UAAU;oDAAE,MAAM;gDAAK;gDACvB,WAAU;;kEAEV,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC,2NAAA,CAAA,cAAW;4DAAC,WAAU;;;;;;;;;;;kEAEzB,8OAAC;kEACC,cAAA,8OAAC;4DAAG,WAAU;sEAAoC;;;;;;;;;;;;+CAX/C;;;;;;;;;;kDAkBX,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,aAAa;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAChC,YAAY;4CAAE,UAAU;wCAAI;wCAC5B,UAAU;4CAAE,MAAM;wCAAK;wCACvB,WAAU;;0DAEV,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;;sEAEb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,oMAAA,CAAA,QAAK;oEAAC,WAAU;;;;;;8EACjB,8OAAC;oEAAK,WAAU;8EAAgB;;;;;;;;;;;;sEAIlC,8OAAC;;8EACC,8OAAC;oEAAI,WAAU;8EAAqC;;;;;;8EAGpD,8OAAC;oEAAE,WAAU;8EAAgB;;;;;;;;;;;;sEAI/B,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,kMAAA,CAAA,OAAI;wEAAC,WAAU;;;;;;kFAChB,8OAAC;wEAAK,WAAU;kFAAyB;;;;;;;;;;;;;;;;;sEAK7C,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAK,WAAU;sFAAgB;;;;;;sFAChC,8OAAC,2NAAA,CAAA,cAAW;4EAAC,WAAU;;;;;;;;;;;;8EAEzB,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAK,WAAU;sFAAgB;;;;;;sFAChC,8OAAC,2NAAA,CAAA,cAAW;4EAAC,WAAU;;;;;;;;;;;;8EAEzB,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAK,WAAU;sFAAgB;;;;;;sFAChC,8OAAC,2NAAA,CAAA,cAAW;4EAAC,WAAU;;;;;;;;;;;;8EAEzB,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAK,WAAU;sFAAgB;;;;;;sFAChC,8OAAC,2NAAA,CAAA,cAAW;4EAAC,WAAU;;;;;;;;;;;;8EAEzB,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAK,WAAU;sFAAgB;;;;;;sFAChC,8OAAC,2NAAA,CAAA,cAAW;4EAAC,WAAU;;;;;;;;;;;;;;;;;;sEAK3B,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;4DACZ,YAAY;gEAAE,OAAO;4DAAK;4DAC1B,UAAU;gEAAE,OAAO;4DAAK;4DACxB,SAAS;4DACT,WAAU;;8EAEV,8OAAC,kMAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;gEAAY;8EAE5B,8OAAC,kNAAA,CAAA,aAAU;oEAAC,WAAU;;;;;;;;;;;;sEAGxB,8OAAC;4DAAE,WAAU;sEAAwB;;;;;;;;;;;;;;;;;0DAOzC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gDACT,SAAS;oDAAE,GAAG;wDAAC,CAAC;wDAAI;wDAAI,CAAC;qDAAG;gDAAC;gDAC7B,YAAY;oDAAE,UAAU;oDAAG,QAAQ;oDAAU,MAAM;gDAAY;gDAC/D,WAAU;;;;;;0DAEZ,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gDACT,SAAS;oDAAE,GAAG;wDAAC;wDAAI,CAAC;wDAAI;qDAAG;gDAAC;gDAC5B,YAAY;oDAAE,UAAU;oDAAG,QAAQ;oDAAU,MAAM;gDAAY;gDAC/D,WAAU;;;;;;;;;;;;;;;;;;0CAMhB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,YAAY;oCAAE,OAAO;oCAAK,UAAU;gCAAI;gCACxC,UAAU;oCAAE,MAAM;gCAAK;gCACvB,WAAU;0CAEV,cAAA,8OAAC;oCAAE,WAAU;8CAA6B;;;;;;;;;;;;;;;;;;;;;;;YAQ/C,0BACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;gBAAE;gBACtB,SAAS;oBAAE,SAAS;gBAAE;gBACtB,MAAM;oBAAE,SAAS;gBAAE;gBACnB,WAAU;gBACV,SAAS,IAAM,YAAY;0BAE3B,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,OAAO;wBAAK,GAAG;oBAAG;oBACzC,SAAS;wBAAE,SAAS;wBAAG,OAAO;wBAAG,GAAG;oBAAE;oBACtC,MAAM;wBAAE,SAAS;wBAAG,OAAO;wBAAK,GAAG;oBAAG;oBACtC,YAAY;wBAAE,MAAM;wBAAU,SAAS;wBAAI,WAAW;oBAAI;oBAC1D,WAAU;oBACV,SAAS,CAAC,IAAM,EAAE,eAAe;8BAEjC,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;;;;;;0CAGlB,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAAwD;;;;;;kDACtE,8OAAC;wCAAE,WAAU;kDAAqC;;;;;;;;;;;;0CAGpD,8OAAC;gCAAK,WAAU;;kDACd,8OAAC;wCACC,MAAK;wCACL,aAAY;wCACZ,WAAU;;;;;;kDAEZ,8OAAC;wCACC,MAAK;wCACL,aAAY;wCACZ,WAAU;;;;;;kDAEZ,8OAAC;wCACC,aAAY;wCACZ,MAAK;wCACL,WAAU;;;;;;kDAEZ,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;wCACZ,YAAY;4CAAE,OAAO;wCAAK;wCAC1B,UAAU;4CAAE,OAAO;wCAAK;wCACxB,WAAU;kDACX;;;;;;;;;;;;0CAKH,8OAAC;gCAAI,WAAU;;oCAA6C;kDACpC,8OAAC;wCAAE,MAAK;wCAAgC,WAAU;kDAA2C;;;;;;;;;;;;0CAGrH,8OAAC;gCACC,SAAS,IAAM,YAAY;gCAC3B,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;;;AASf", "debugId": null}}, {"offset": {"line": 2340, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/src/app/training/components/TrainingAccessForm.jsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useState } from 'react';\r\nimport { motion, AnimatePresence } from 'framer-motion';\r\nimport { Shield, User, Mail, Phone, Building, GraduationCap, ArrowRight, CheckCircle, ChevronLeft, ChevronRight, Linkedin, Briefcase, BookOpen, MessageSquare, ExternalLink } from 'lucide-react';\r\n\r\nexport default function TrainingAccessForm() {\r\n  const [currentStep, setCurrentStep] = useState(1);\r\n  const [formData, setFormData] = useState({\r\n    disclaimer: false,\r\n    fullName: '',\r\n    email: '',\r\n    phone: '',\r\n    linkedin: '',\r\n    occupation: '',\r\n    experience: '',\r\n    education: '',\r\n    interest: '',\r\n    source: '',\r\n    securitySkills: 'no',\r\n    certification: false,\r\n    terms: false\r\n  });\r\n\r\n  const [isSubmitting, setIsSubmitting] = useState(false);\r\n  const [apiError, setApiError] = useState(\"\");\r\n  const [success, setSuccess] = useState(\"\");\r\n\r\n  const totalSteps = 3;\r\n\r\n  const handleInputChange = (e) => {\r\n    const { name, value, type, checked } = e.target;\r\n    setFormData(prev => ({\r\n      ...prev,\r\n      [name]: type === 'checkbox' ? checked : value\r\n    }));\r\n  };\r\n\r\n  const nextStep = () => {\r\n    if (currentStep < totalSteps) {\r\n      setCurrentStep(currentStep + 1);\r\n    }\r\n  };\r\n\r\n  const prevStep = () => {\r\n    if (currentStep > 1) {\r\n      setCurrentStep(currentStep - 1);\r\n    }\r\n  };\r\n\r\n  const handleSubmit = async (e) => {\r\n    e.preventDefault();\r\n    if (isSubmitting) return;\r\n    setIsSubmitting(true);\r\n    setApiError(\"\");\r\n    setSuccess(\"\");\r\n\r\n    try {\r\n      // Make the API request to store submission and send email\r\n      const response = await fetch(\"/api/submissions\", {\r\n        method: \"POST\",\r\n        headers: { \"Content-Type\": \"application/json\" },\r\n        body: JSON.stringify({\r\n          fullName: formData.fullName,\r\n          email: formData.email,\r\n          phone: formData.phone,\r\n          linkedin: formData.linkedin,\r\n          occupation: formData.occupation,\r\n          experience: formData.experience,\r\n          education: formData.education,\r\n          interest: formData.interest,\r\n          source: formData.source,\r\n          securitySkills: formData.securitySkills,\r\n          certification: formData.certification,\r\n          disclaimer: formData.disclaimer,\r\n          terms: formData.terms,\r\n          formType: 'training-enrollment',\r\n          subject: 'New Training Enrollment Application'\r\n        }),\r\n      });\r\n\r\n      const result = await response.json();\r\n      if (response.ok && result.success) {\r\n        // Clear form fields on success\r\n        setFormData({\r\n          disclaimer: false,\r\n          fullName: '',\r\n          email: '',\r\n          phone: '',\r\n          linkedin: '',\r\n          occupation: '',\r\n          experience: '',\r\n          education: '',\r\n          interest: '',\r\n          source: '',\r\n          securitySkills: 'no',\r\n          certification: false,\r\n          terms: false\r\n        });\r\n        setCurrentStep(1); // Reset to first step\r\n        setSuccess(\"Thank you! Your training application has been submitted successfully. We'll review your application and get back to you within 24 hours.\");\r\n      } else {\r\n        setApiError(result.error || \"Something went wrong. Please try again.\");\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error submitting form:\", error);\r\n      setApiError(\"Failed to submit application. Please try again.\");\r\n    } finally {\r\n      setIsSubmitting(false);\r\n    }\r\n  };\r\n\r\n  const renderStep = () => {\r\n    switch (currentStep) {\r\n      case 1:\r\n        return (\r\n          <div className=\"space-y-6\">\r\n            <div className=\"text-center mb-8\">\r\n              <div className=\"w-16 h-16 bg-gradient-to-br from-[var(--color-blue)] to-[var(--color-blue-secondary)] rounded-3xl flex items-center justify-center mx-auto mb-4\">\r\n                <Shield className=\"w-8 h-8 text-white\" />\r\n              </div>\r\n              <h3 className=\"text-2xl font-bold text-[var(--color-dark-blue)] mb-2\">Step 1 of {totalSteps}</h3>\r\n              <p className=\"text-[var(--foreground-secondary)]\">Personal Information & Disclaimer</p>\r\n            </div>\r\n            \r\n            {/* Disclaimer */}\r\n            <div className=\"bg-gradient-to-br from-[var(--color-blue)]/10 to-[var(--color-blue-secondary)]/10 rounded-2xl p-6 border border-[var(--color-blue)]/20\">\r\n              <p className=\"text-[var(--color-dark-blue)] leading-relaxed mb-4\">\r\n                By submitting this form, you consent to our use of the provided information to contact you regarding the security training program. We respect your privacy and will only use this information for program-related communications.\r\n              </p>\r\n              <label className=\"flex items-center gap-3 cursor-pointer\">\r\n                <input\r\n                  type=\"checkbox\"\r\n                  name=\"disclaimer\"\r\n                  checked={formData.disclaimer}\r\n                  onChange={handleInputChange}\r\n                  required\r\n                  className=\"w-4 h-4 text-[var(--color-blue)] border-gray-300 rounded focus:ring-[var(--color-blue)]\"\r\n                />\r\n                <span className=\"text-[var(--color-dark-blue)] font-medium\">Yes, I agree to the disclaimer</span>\r\n              </label>\r\n            </div>\r\n\r\n            {/* Personal Information */}\r\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\r\n              <div>\r\n                <label className=\"block text-[var(--color-dark-blue)] font-medium mb-2\">Full Name *</label>\r\n                <div className=\"relative\">\r\n                  <User className=\"absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-[var(--color-blue)]\" />\r\n                  <input\r\n                    type=\"text\"\r\n                    name=\"fullName\"\r\n                    value={formData.fullName}\r\n                    onChange={handleInputChange}\r\n                    required\r\n                    className=\"w-full pl-10 pr-4 py-3 bg-white border border-gray-200 rounded-xl text-[var(--color-dark-blue)] placeholder-gray-400 focus:outline-none focus:border-[var(--color-blue)] focus:ring-2 focus:ring-[var(--color-blue)]/20 transition-all\"\r\n                    placeholder=\"Enter your full name\"\r\n                  />\r\n                </div>\r\n              </div>\r\n\r\n              <div>\r\n                <label className=\"block text-[var(--color-dark-blue)] font-medium mb-2\">Email Address *</label>\r\n                <div className=\"relative\">\r\n                  <Mail className=\"absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-[var(--color-blue)]\" />\r\n                  <input\r\n                    type=\"email\"\r\n                    name=\"email\"\r\n                    value={formData.email}\r\n                    onChange={handleInputChange}\r\n                    required\r\n                    className=\"w-full pl-10 pr-4 py-3 bg-white border border-gray-200 rounded-xl text-[var(--color-dark-blue)] placeholder-gray-400 focus:outline-none focus:border-[var(--color-blue)] focus:ring-2 focus:ring-[var(--color-blue)]/20 transition-all\"\r\n                    placeholder=\"Enter your email address\"\r\n                  />\r\n                </div>\r\n              </div>\r\n\r\n              <div>\r\n                <label className=\"block text-[var(--color-dark-blue)] font-medium mb-2\">Phone Number</label>\r\n                <div className=\"relative\">\r\n                  <Phone className=\"absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-[var(--color-blue)]\" />\r\n                  <input\r\n                    type=\"tel\"\r\n                    name=\"phone\"\r\n                    value={formData.phone}\r\n                    onChange={handleInputChange}\r\n                    className=\"w-full pl-10 pr-4 py-3 bg-white border border-gray-200 rounded-xl text-[var(--color-dark-blue)] placeholder-gray-400 focus:outline-none focus:border-[var(--color-blue)] focus:ring-2 focus:ring-[var(--color-blue)]/20 transition-all\"\r\n                    placeholder=\"Enter your phone number\"\r\n                  />\r\n                </div>\r\n              </div>\r\n\r\n              <div>\r\n                <label className=\"block text-[var(--color-dark-blue)] font-medium mb-2\">LinkedIn Profile</label>\r\n                <div className=\"relative\">\r\n                  <Linkedin className=\"absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-[var(--color-blue)]\" />\r\n                  <input\r\n                    type=\"url\"\r\n                    name=\"linkedin\"\r\n                    value={formData.linkedin}\r\n                    onChange={handleInputChange}\r\n                    className=\"w-full pl-10 pr-4 py-3 bg-white border border-gray-200 rounded-xl text-[var(--color-dark-blue)] placeholder-gray-400 focus:outline-none focus:border-[var(--color-blue)] focus:ring-2 focus:ring-[var(--color-blue)]/20 transition-all\"\r\n                    placeholder=\"Enter your LinkedIn profile URL\"\r\n                  />\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        );\r\n\r\n      case 2:\r\n        return (\r\n          <div className=\"space-y-6\">\r\n            <div className=\"text-center mb-8\">\r\n              <h3 className=\"text-2xl font-bold text-[var(--color-dark-blue)] mb-2\">Step 2 of {totalSteps}</h3>\r\n              <p className=\"text-[var(--foreground-secondary)]\">Professional Background & Interest</p>\r\n            </div>\r\n            \r\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\r\n              <div>\r\n                <label className=\"block text-[var(--color-dark-blue)] font-medium mb-2\">Current Occupation</label>\r\n                <div className=\"relative\">\r\n                  <Briefcase className=\"absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-[var(--color-blue)]\" />\r\n                  <input\r\n                    type=\"text\"\r\n                    name=\"occupation\"\r\n                    value={formData.occupation}\r\n                    onChange={handleInputChange}\r\n                    className=\"w-full pl-10 pr-4 py-3 bg-white border border-gray-200 rounded-xl text-[var(--color-dark-blue)] placeholder-gray-400 focus:outline-none focus:border-[var(--color-blue)] focus:ring-2 focus:ring-[var(--color-blue)]/20 transition-all\"\r\n                    placeholder=\"Enter your current occupation\"\r\n                  />\r\n                </div>\r\n              </div>\r\n\r\n              <div>\r\n                <label className=\"block text-[var(--color-dark-blue)] font-medium mb-2\">Years of Experience</label>\r\n                <div className=\"relative\">\r\n                  <BookOpen className=\"absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-[var(--color-blue)]\" />\r\n                  <input\r\n                    type=\"text\"\r\n                    name=\"experience\"\r\n                    value={formData.experience}\r\n                    onChange={handleInputChange}\r\n                    className=\"w-full pl-10 pr-4 py-3 bg-white border border-gray-200 rounded-xl text-[var(--color-dark-blue)] placeholder-gray-400 focus:outline-none focus:border-[var(--color-blue)] focus:ring-2 focus:ring-[var(--color-blue)]/20 transition-all\"\r\n                    placeholder=\"e.g., 2 years in IT, 1 year in cybersecurity\"\r\n                  />\r\n                </div>\r\n              </div>\r\n\r\n              <div className=\"md:col-span-2\">\r\n                <label className=\"block text-[var(--color-dark-blue)] font-medium mb-2\">Highest Educational Qualification</label>\r\n                <div className=\"relative\">\r\n                  <GraduationCap className=\"absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-[var(--color-blue)]\" />\r\n                  <input\r\n                    type=\"text\"\r\n                    name=\"education\"\r\n                    value={formData.education}\r\n                    onChange={handleInputChange}\r\n                    className=\"w-full pl-10 pr-4 py-3 bg-white border border-gray-200 rounded-xl text-[var(--color-dark-blue)] placeholder-gray-400 focus:outline-none focus:border-[var(--color-blue)] focus:ring-2 focus:ring-[var(--color-blue)]/20 transition-all\"\r\n                    placeholder=\"e.g., Bachelor's in Computer Science\"\r\n                  />\r\n                </div>\r\n              </div>\r\n\r\n              <div className=\"md:col-span-2\">\r\n                <label className=\"block text-[var(--color-dark-blue)] font-medium mb-2\">Why are you interested in this security training program?</label>\r\n                <textarea\r\n                  name=\"interest\"\r\n                  value={formData.interest}\r\n                  onChange={handleInputChange}\r\n                  rows={4}\r\n                  className=\"w-full px-4 py-3 bg-white border border-gray-200 rounded-xl text-[var(--color-dark-blue)] placeholder-gray-400 focus:outline-none focus:border-[var(--color-blue)] focus:ring-2 focus:ring-[var(--color-blue)]/20 transition-all resize-none\"\r\n                  placeholder=\"Tell us about your interest in cybersecurity training...\"\r\n                />\r\n              </div>\r\n\r\n              <div>\r\n                <label className=\"block text-[var(--color-dark-blue)] font-medium mb-2\">How did you hear about us?</label>\r\n                <div className=\"relative\">\r\n                  <MessageSquare className=\"absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-[var(--color-blue)]\" />\r\n                  <input\r\n                    type=\"text\"\r\n                    name=\"source\"\r\n                    value={formData.source}\r\n                    onChange={handleInputChange}\r\n                    className=\"w-full pl-10 pr-4 py-3 bg-white border border-gray-200 rounded-xl text-[var(--color-dark-blue)] placeholder-gray-400 focus:outline-none focus:border-[var(--color-blue)] focus:ring-2 focus:ring-[var(--color-blue)]/20 transition-all\"\r\n                    placeholder=\"e.g., Social media, referral, search engine\"\r\n                  />\r\n                </div>\r\n              </div>\r\n\r\n              <div>\r\n                <label className=\"block text-[var(--color-dark-blue)] font-medium mb-2\">Do you have security skills?</label>\r\n                <div className=\"space-y-3\">\r\n                  <label className=\"flex items-center gap-3 cursor-pointer\">\r\n                    <input\r\n                      type=\"radio\"\r\n                      name=\"securitySkills\"\r\n                      value=\"yes\"\r\n                      checked={formData.securitySkills === 'yes'}\r\n                      onChange={handleInputChange}\r\n                      className=\"w-4 h-4 text-[var(--color-blue)] border-gray-300 focus:ring-[var(--color-blue)]\"\r\n                    />\r\n                    <span className=\"text-[var(--color-dark-blue)]\">Yes</span>\r\n                  </label>\r\n                  <label className=\"flex items-center gap-3 cursor-pointer\">\r\n                    <input\r\n                      type=\"radio\"\r\n                      name=\"securitySkills\"\r\n                      value=\"no\"\r\n                      checked={formData.securitySkills === 'no'}\r\n                      onChange={handleInputChange}\r\n                      className=\"w-4 h-4 text-[var(--color-blue)] border-gray-300 focus:ring-[var(--color-blue)]\"\r\n                    />\r\n                    <span className=\"text-[var(--color-dark-blue)]\">No</span>\r\n                  </label>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        );\r\n\r\n      case 3:\r\n        return (\r\n          <div className=\"space-y-6\">\r\n            <div className=\"text-center mb-8\">\r\n              <h3 className=\"text-2xl font-bold text-[var(--color-dark-blue)] mb-2\">Step 3 of {totalSteps}</h3>\r\n              <p className=\"text-[var(--foreground-secondary)]\">Certification & Terms</p>\r\n            </div>\r\n            \r\n            {/* Certification */}\r\n            <div className=\"bg-gradient-to-br from-[var(--color-blue)]/10 to-[var(--color-blue-secondary)]/10 rounded-2xl p-6 border border-[var(--color-blue)]/20\">\r\n              <p className=\"text-[var(--color-dark-blue)] leading-relaxed mb-4\">\r\n                I hereby certify that all the information provided in this form is true and correct to the best of my knowledge. I understand that any false or misleading information may result in the rejection of my application.\r\n              </p>\r\n              \r\n              <label className=\"flex items-center gap-3 cursor-pointer\">\r\n                <input\r\n                  type=\"checkbox\"\r\n                  name=\"certification\"\r\n                  checked={formData.certification}\r\n                  onChange={handleInputChange}\r\n                  required\r\n                  className=\"w-4 h-4 text-[var(--color-blue)] border-gray-300 rounded focus:ring-[var(--color-blue)]\"\r\n                />\r\n                <span className=\"text-[var(--color-dark-blue)] font-medium\">Yes, I certify</span>\r\n              </label>\r\n            </div>\r\n\r\n            {/* Terms and Conditions */}\r\n            <div className=\"bg-gradient-to-br from-[var(--color-blue)]/10 to-[var(--color-blue-secondary)]/10 rounded-2xl p-6 border border-[var(--color-blue)]/20\">\r\n              <p className=\"text-[var(--color-dark-blue)] leading-relaxed mb-4\">\r\n                By checking this box, you agree to our terms and conditions and privacy policy regarding course access and communications.\r\n              </p>\r\n              <a \r\n                href=\"https://securitylit.com/TrainingTermsAndCondition.pdf\" \r\n                target=\"_blank\" \r\n                rel=\"noopener noreferrer\"\r\n                className=\"inline-flex items-center gap-2 text-[var(--color-blue)] hover:text-[var(--color-blue-secondary)] transition-colors mb-4\"\r\n              >\r\n                <span>View Terms and Conditions</span>\r\n                <ExternalLink className=\"w-4 h-4\" />\r\n              </a>\r\n              \r\n              <label className=\"flex items-center gap-3 cursor-pointer\">\r\n                <input\r\n                  type=\"checkbox\"\r\n                  name=\"terms\"\r\n                  checked={formData.terms}\r\n                  onChange={handleInputChange}\r\n                  required\r\n                  className=\"w-4 h-4 text-[var(--color-blue)] border-gray-300 rounded focus:ring-[var(--color-blue)]\"\r\n                />\r\n                <span className=\"text-[var(--color-dark-blue)] font-medium\">Yes, I agree to the terms and conditions</span>\r\n              </label>\r\n            </div>\r\n          </div>\r\n        );\r\n\r\n      default:\r\n        return null;\r\n    }\r\n  };\r\n\r\n  return (\r\n    <section className=\"py-24 bg-gradient-to-br from-[var(--color-blue)]/10 to-[var(--color-blue-secondary)]/10 relative overflow-hidden\">\r\n      {/* Background Pattern */}\r\n      <div className=\"absolute inset-0\">\r\n        <div className=\"absolute top-0 left-0 w-full h-full bg-[url('data:image/svg+xml,%3Csvg%20width%3D%2260%22%20height%3D%2260%22%20viewBox%3D%220%200%2060%2060%22%20xmlns%3D%22http%3A//www.w3.org/2000/svg%22%3E%3Cg%20fill%3D%22none%22%20fill-rule%3D%22evenodd%22%3E%3Cg%20fill%3D%22%23000000%22%20fill-opacity%3D%220.03%22%3E%3Ccircle%20cx%3D%2230%22%20cy%3D%2230%22%20r%3D%221%22/%3E%3C/g%3E%3C/g%3E%3C/svg%3E')] opacity-50\"></div>\r\n      </div>\r\n\r\n      <div className=\"relative z-10 container mx-auto px-4 max-w-4xl\">\r\n        {/* Header */}\r\n        <motion.div \r\n          initial={{ opacity: 0, y: 30 }}\r\n          whileInView={{ opacity: 1, y: 0 }}\r\n          transition={{ duration: 0.8 }}\r\n          viewport={{ once: true }}\r\n          className=\"text-center mb-16\"\r\n        >\r\n          <motion.div \r\n            initial={{ opacity: 0, scale: 0.8 }}\r\n            whileInView={{ opacity: 1, scale: 1 }}\r\n            transition={{ delay: 0.2, duration: 0.6 }}\r\n            viewport={{ once: true }}\r\n            className=\"inline-flex items-center bg-gradient-to-r from-[var(--color-blue)]/10 to-[var(--color-blue-secondary)]/10 px-6 py-3 rounded-full mb-8 border border-[var(--color-blue)]/20\"\r\n          >\r\n            <Shield className=\"w-5 h-5 text-[var(--color-blue)] mr-3\" />\r\n            <span className=\"text-sm font-semibold text-[var(--color-blue)]\">Training Application</span>\r\n          </motion.div>\r\n          \r\n          <motion.h2 \r\n            initial={{ opacity: 0, y: 20 }}\r\n            whileInView={{ opacity: 1, y: 0 }}\r\n            transition={{ delay: 0.4, duration: 0.6 }}\r\n            viewport={{ once: true }}\r\n            className=\"text-4xl lg:text-5xl font-bold text-[var(--color-dark-blue)] mb-8\"\r\n          >\r\n            Complete Your\r\n            <span className=\"block bg-gradient-to-r from-[var(--color-blue)] to-[var(--color-blue-secondary)] bg-clip-text text-transparent\">\r\n              Training Application\r\n            </span>\r\n          </motion.h2>\r\n          \r\n          <motion.p \r\n            initial={{ opacity: 0, y: 20 }}\r\n            whileInView={{ opacity: 1, y: 0 }}\r\n            transition={{ delay: 0.6, duration: 0.6 }}\r\n            viewport={{ once: true }}\r\n            className=\"text-lg text-[var(--foreground-secondary)] max-w-3xl mx-auto leading-relaxed\"\r\n          >\r\n            Join our comprehensive cybersecurity training program. Complete this 3-step application to get started on your cybersecurity journey.\r\n          </motion.p>\r\n        </motion.div>\r\n\r\n        {/* Progress Bar */}\r\n        <div className=\"mb-12\">\r\n          <div className=\"flex justify-between items-center mb-4\">\r\n            <span className=\"text-sm font-medium text-[var(--color-dark-blue)]\">Progress</span>\r\n            <span className=\"text-sm font-medium text-[var(--color-blue)]\">{currentStep} of {totalSteps}</span>\r\n          </div>\r\n          <div className=\"w-full bg-gray-200 rounded-full h-2\">\r\n            <motion.div \r\n              className=\"bg-gradient-to-r from-[var(--color-blue)] to-[var(--color-blue-secondary)] h-2 rounded-full\"\r\n              initial={{ width: 0 }}\r\n              animate={{ width: `${(currentStep / totalSteps) * 100}%` }}\r\n              transition={{ duration: 0.5 }}\r\n            />\r\n          </div>\r\n        </div>\r\n\r\n        {/* Form Container */}\r\n        <motion.div \r\n          initial={{ opacity: 0, y: 30 }}\r\n          whileInView={{ opacity: 1, y: 0 }}\r\n          transition={{ duration: 0.8 }}\r\n          viewport={{ once: true }}\r\n          className=\"bg-white rounded-3xl p-8 lg:p-12 shadow-2xl border border-gray-100\"\r\n        >\r\n          <form onSubmit={handleSubmit}>\r\n            {/* Error Message */}\r\n            {apiError && (\r\n              <div className=\"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg mb-6\">\r\n                {apiError}\r\n              </div>\r\n            )}\r\n\r\n            {/* Success Message */}\r\n            {success && (\r\n              <div className=\"bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg mb-6\">\r\n                {success}\r\n              </div>\r\n            )}\r\n\r\n            <AnimatePresence mode=\"wait\">\r\n              <motion.div\r\n                key={currentStep}\r\n                initial={{ opacity: 0, x: 20 }}\r\n                animate={{ opacity: 1, x: 0 }}\r\n                exit={{ opacity: 0, x: -20 }}\r\n                transition={{ duration: 0.3 }}\r\n              >\r\n                {renderStep()}\r\n              </motion.div>\r\n            </AnimatePresence>\r\n\r\n            {/* Navigation Buttons */}\r\n            <div className=\"flex justify-between items-center mt-12 pt-8 border-t border-gray-200\">\r\n              <motion.button\r\n                type=\"button\"\r\n                onClick={prevStep}\r\n                disabled={currentStep === 1}\r\n                className={`flex items-center gap-2 px-6 py-3 rounded-xl font-semibold transition-all duration-300 ${\r\n                  currentStep === 1\r\n                    ? 'text-gray-400 cursor-not-allowed'\r\n                    : 'text-[var(--color-dark-blue)] hover:bg-gray-100'\r\n                }`}\r\n                whileHover={currentStep !== 1 ? { scale: 1.02 } : {}}\r\n                whileTap={currentStep !== 1 ? { scale: 0.98 } : {}}\r\n              >\r\n                <ChevronLeft className=\"w-5 h-5\" />\r\n                Previous\r\n              </motion.button>\r\n\r\n              {currentStep < totalSteps ? (\r\n                <motion.button\r\n                  type=\"button\"\r\n                  onClick={nextStep}\r\n                  className=\"flex items-center gap-2 bg-gradient-to-r from-[var(--color-blue)] to-[var(--color-blue-secondary)] text-white px-8 py-3 rounded-xl font-semibold hover:shadow-lg transition-all duration-300\"\r\n                  whileHover={{ scale: 1.02 }}\r\n                  whileTap={{ scale: 0.98 }}\r\n                >\r\n                  Next\r\n                  <ChevronRight className=\"w-5 h-5\" />\r\n                </motion.button>\r\n              ) : (\r\n                <motion.button\r\n                  type=\"submit\"\r\n                  disabled={isSubmitting}\r\n                  className=\"flex items-center gap-2 bg-gradient-to-r from-[var(--color-blue)] to-[var(--color-blue-secondary)] disabled:from-gray-400 disabled:to-gray-500 text-white px-8 py-3 rounded-xl font-semibold hover:shadow-lg transition-all duration-300\"\r\n                  whileHover={{ scale: isSubmitting ? 1 : 1.02 }}\r\n                  whileTap={{ scale: isSubmitting ? 1 : 0.98 }}\r\n                >\r\n                  {isSubmitting ? (\r\n                    <>\r\n                      <div className=\"animate-spin rounded-full h-5 w-5 border-b-2 border-white\"></div>\r\n                      Submitting Application...\r\n                    </>\r\n                  ) : (\r\n                    <>\r\n                      <Shield className=\"w-5 h-5\" />\r\n                      Submit Application\r\n                      <ArrowRight className=\"w-5 h-5\" />\r\n                    </>\r\n                  )}\r\n                </motion.button>\r\n              )}\r\n            </div>\r\n          </form>\r\n        </motion.div>\r\n      </div>\r\n    </section>\r\n  );\r\n} "], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAJA;;;;;AAMe,SAAS;IACtB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,YAAY;QACZ,UAAU;QACV,OAAO;QACP,OAAO;QACP,UAAU;QACV,YAAY;QACZ,YAAY;QACZ,WAAW;QACX,UAAU;QACV,QAAQ;QACR,gBAAgB;QAChB,eAAe;QACf,OAAO;IACT;IAEA,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,MAAM,aAAa;IAEnB,MAAM,oBAAoB,CAAC;QACzB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,EAAE,MAAM;QAC/C,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,CAAC,KAAK,EAAE,SAAS,aAAa,UAAU;YAC1C,CAAC;IACH;IAEA,MAAM,WAAW;QACf,IAAI,cAAc,YAAY;YAC5B,eAAe,cAAc;QAC/B;IACF;IAEA,MAAM,WAAW;QACf,IAAI,cAAc,GAAG;YACnB,eAAe,cAAc;QAC/B;IACF;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,IAAI,cAAc;QAClB,gBAAgB;QAChB,YAAY;QACZ,WAAW;QAEX,IAAI;YACF,0DAA0D;YAC1D,MAAM,WAAW,MAAM,MAAM,oBAAoB;gBAC/C,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;oBACnB,UAAU,SAAS,QAAQ;oBAC3B,OAAO,SAAS,KAAK;oBACrB,OAAO,SAAS,KAAK;oBACrB,UAAU,SAAS,QAAQ;oBAC3B,YAAY,SAAS,UAAU;oBAC/B,YAAY,SAAS,UAAU;oBAC/B,WAAW,SAAS,SAAS;oBAC7B,UAAU,SAAS,QAAQ;oBAC3B,QAAQ,SAAS,MAAM;oBACvB,gBAAgB,SAAS,cAAc;oBACvC,eAAe,SAAS,aAAa;oBACrC,YAAY,SAAS,UAAU;oBAC/B,OAAO,SAAS,KAAK;oBACrB,UAAU;oBACV,SAAS;gBACX;YACF;YAEA,MAAM,SAAS,MAAM,SAAS,IAAI;YAClC,IAAI,SAAS,EAAE,IAAI,OAAO,OAAO,EAAE;gBACjC,+BAA+B;gBAC/B,YAAY;oBACV,YAAY;oBACZ,UAAU;oBACV,OAAO;oBACP,OAAO;oBACP,UAAU;oBACV,YAAY;oBACZ,YAAY;oBACZ,WAAW;oBACX,UAAU;oBACV,QAAQ;oBACR,gBAAgB;oBAChB,eAAe;oBACf,OAAO;gBACT;gBACA,eAAe,IAAI,sBAAsB;gBACzC,WAAW;YACb,OAAO;gBACL,YAAY,OAAO,KAAK,IAAI;YAC9B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,YAAY;QACd,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,MAAM,aAAa;QACjB,OAAQ;YACN,KAAK;gBACH,qBACE,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,sMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;;;;;;8CAEpB,8OAAC;oCAAG,WAAU;;wCAAwD;wCAAW;;;;;;;8CACjF,8OAAC;oCAAE,WAAU;8CAAqC;;;;;;;;;;;;sCAIpD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAE,WAAU;8CAAqD;;;;;;8CAGlE,8OAAC;oCAAM,WAAU;;sDACf,8OAAC;4CACC,MAAK;4CACL,MAAK;4CACL,SAAS,SAAS,UAAU;4CAC5B,UAAU;4CACV,QAAQ;4CACR,WAAU;;;;;;sDAEZ,8OAAC;4CAAK,WAAU;sDAA4C;;;;;;;;;;;;;;;;;;sCAKhE,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAAuD;;;;;;sDACxE,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;8DAChB,8OAAC;oDACC,MAAK;oDACL,MAAK;oDACL,OAAO,SAAS,QAAQ;oDACxB,UAAU;oDACV,QAAQ;oDACR,WAAU;oDACV,aAAY;;;;;;;;;;;;;;;;;;8CAKlB,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAAuD;;;;;;sDACxE,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;8DAChB,8OAAC;oDACC,MAAK;oDACL,MAAK;oDACL,OAAO,SAAS,KAAK;oDACrB,UAAU;oDACV,QAAQ;oDACR,WAAU;oDACV,aAAY;;;;;;;;;;;;;;;;;;8CAKlB,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAAuD;;;;;;sDACxE,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,oMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;8DACjB,8OAAC;oDACC,MAAK;oDACL,MAAK;oDACL,OAAO,SAAS,KAAK;oDACrB,UAAU;oDACV,WAAU;oDACV,aAAY;;;;;;;;;;;;;;;;;;8CAKlB,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAAuD;;;;;;sDACxE,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,0MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;8DACpB,8OAAC;oDACC,MAAK;oDACL,MAAK;oDACL,OAAO,SAAS,QAAQ;oDACxB,UAAU;oDACV,WAAU;oDACV,aAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAQ1B,KAAK;gBACH,qBACE,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;;wCAAwD;wCAAW;;;;;;;8CACjF,8OAAC;oCAAE,WAAU;8CAAqC;;;;;;;;;;;;sCAGpD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAAuD;;;;;;sDACxE,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,4MAAA,CAAA,YAAS;oDAAC,WAAU;;;;;;8DACrB,8OAAC;oDACC,MAAK;oDACL,MAAK;oDACL,OAAO,SAAS,UAAU;oDAC1B,UAAU;oDACV,WAAU;oDACV,aAAY;;;;;;;;;;;;;;;;;;8CAKlB,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAAuD;;;;;;sDACxE,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,8MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;8DACpB,8OAAC;oDACC,MAAK;oDACL,MAAK;oDACL,OAAO,SAAS,UAAU;oDAC1B,UAAU;oDACV,WAAU;oDACV,aAAY;;;;;;;;;;;;;;;;;;8CAKlB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAM,WAAU;sDAAuD;;;;;;sDACxE,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,wNAAA,CAAA,gBAAa;oDAAC,WAAU;;;;;;8DACzB,8OAAC;oDACC,MAAK;oDACL,MAAK;oDACL,OAAO,SAAS,SAAS;oDACzB,UAAU;oDACV,WAAU;oDACV,aAAY;;;;;;;;;;;;;;;;;;8CAKlB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAM,WAAU;sDAAuD;;;;;;sDACxE,8OAAC;4CACC,MAAK;4CACL,OAAO,SAAS,QAAQ;4CACxB,UAAU;4CACV,MAAM;4CACN,WAAU;4CACV,aAAY;;;;;;;;;;;;8CAIhB,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAAuD;;;;;;sDACxE,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,wNAAA,CAAA,gBAAa;oDAAC,WAAU;;;;;;8DACzB,8OAAC;oDACC,MAAK;oDACL,MAAK;oDACL,OAAO,SAAS,MAAM;oDACtB,UAAU;oDACV,WAAU;oDACV,aAAY;;;;;;;;;;;;;;;;;;8CAKlB,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAAuD;;;;;;sDACxE,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAM,WAAU;;sEACf,8OAAC;4DACC,MAAK;4DACL,MAAK;4DACL,OAAM;4DACN,SAAS,SAAS,cAAc,KAAK;4DACrC,UAAU;4DACV,WAAU;;;;;;sEAEZ,8OAAC;4DAAK,WAAU;sEAAgC;;;;;;;;;;;;8DAElD,8OAAC;oDAAM,WAAU;;sEACf,8OAAC;4DACC,MAAK;4DACL,MAAK;4DACL,OAAM;4DACN,SAAS,SAAS,cAAc,KAAK;4DACrC,UAAU;4DACV,WAAU;;;;;;sEAEZ,8OAAC;4DAAK,WAAU;sEAAgC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAQ9D,KAAK;gBACH,qBACE,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;;wCAAwD;wCAAW;;;;;;;8CACjF,8OAAC;oCAAE,WAAU;8CAAqC;;;;;;;;;;;;sCAIpD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAE,WAAU;8CAAqD;;;;;;8CAIlE,8OAAC;oCAAM,WAAU;;sDACf,8OAAC;4CACC,MAAK;4CACL,MAAK;4CACL,SAAS,SAAS,aAAa;4CAC/B,UAAU;4CACV,QAAQ;4CACR,WAAU;;;;;;sDAEZ,8OAAC;4CAAK,WAAU;sDAA4C;;;;;;;;;;;;;;;;;;sCAKhE,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAE,WAAU;8CAAqD;;;;;;8CAGlE,8OAAC;oCACC,MAAK;oCACL,QAAO;oCACP,KAAI;oCACJ,WAAU;;sDAEV,8OAAC;sDAAK;;;;;;sDACN,8OAAC,sNAAA,CAAA,eAAY;4CAAC,WAAU;;;;;;;;;;;;8CAG1B,8OAAC;oCAAM,WAAU;;sDACf,8OAAC;4CACC,MAAK;4CACL,MAAK;4CACL,SAAS,SAAS,KAAK;4CACvB,UAAU;4CACV,QAAQ;4CACR,WAAU;;;;;;sDAEZ,8OAAC;4CAAK,WAAU;sDAA4C;;;;;;;;;;;;;;;;;;;;;;;;YAMtE;gBACE,OAAO;QACX;IACF;IAEA,qBACE,8OAAC;QAAQ,WAAU;;0BAEjB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;;;;;;;;;;0BAGjB,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,YAAY;4BAAE,UAAU;wBAAI;wBAC5B,UAAU;4BAAE,MAAM;wBAAK;wBACvB,WAAU;;0CAEV,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,OAAO;gCAAI;gCAClC,aAAa;oCAAE,SAAS;oCAAG,OAAO;gCAAE;gCACpC,YAAY;oCAAE,OAAO;oCAAK,UAAU;gCAAI;gCACxC,UAAU;oCAAE,MAAM;gCAAK;gCACvB,WAAU;;kDAEV,8OAAC,sMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,8OAAC;wCAAK,WAAU;kDAAiD;;;;;;;;;;;;0CAGnE,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;gCACR,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,YAAY;oCAAE,OAAO;oCAAK,UAAU;gCAAI;gCACxC,UAAU;oCAAE,MAAM;gCAAK;gCACvB,WAAU;;oCACX;kDAEC,8OAAC;wCAAK,WAAU;kDAAiH;;;;;;;;;;;;0CAKnI,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;gCACP,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,YAAY;oCAAE,OAAO;oCAAK,UAAU;gCAAI;gCACxC,UAAU;oCAAE,MAAM;gCAAK;gCACvB,WAAU;0CACX;;;;;;;;;;;;kCAMH,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;kDAAoD;;;;;;kDACpE,8OAAC;wCAAK,WAAU;;4CAAgD;4CAAY;4CAAK;;;;;;;;;;;;;0CAEnF,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,WAAU;oCACV,SAAS;wCAAE,OAAO;oCAAE;oCACpB,SAAS;wCAAE,OAAO,GAAG,AAAC,cAAc,aAAc,IAAI,CAAC,CAAC;oCAAC;oCACzD,YAAY;wCAAE,UAAU;oCAAI;;;;;;;;;;;;;;;;;kCAMlC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,YAAY;4BAAE,UAAU;wBAAI;wBAC5B,UAAU;4BAAE,MAAM;wBAAK;wBACvB,WAAU;kCAEV,cAAA,8OAAC;4BAAK,UAAU;;gCAEb,0BACC,8OAAC;oCAAI,WAAU;8CACZ;;;;;;gCAKJ,yBACC,8OAAC;oCAAI,WAAU;8CACZ;;;;;;8CAIL,8OAAC,yLAAA,CAAA,kBAAe;oCAAC,MAAK;8CACpB,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCAET,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,MAAM;4CAAE,SAAS;4CAAG,GAAG,CAAC;wCAAG;wCAC3B,YAAY;4CAAE,UAAU;wCAAI;kDAE3B;uCANI;;;;;;;;;;8CAWT,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;4CACZ,MAAK;4CACL,SAAS;4CACT,UAAU,gBAAgB;4CAC1B,WAAW,CAAC,uFAAuF,EACjG,gBAAgB,IACZ,qCACA,mDACJ;4CACF,YAAY,gBAAgB,IAAI;gDAAE,OAAO;4CAAK,IAAI,CAAC;4CACnD,UAAU,gBAAgB,IAAI;gDAAE,OAAO;4CAAK,IAAI,CAAC;;8DAEjD,8OAAC,oNAAA,CAAA,cAAW;oDAAC,WAAU;;;;;;gDAAY;;;;;;;wCAIpC,cAAc,2BACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;4CACZ,MAAK;4CACL,SAAS;4CACT,WAAU;4CACV,YAAY;gDAAE,OAAO;4CAAK;4CAC1B,UAAU;gDAAE,OAAO;4CAAK;;gDACzB;8DAEC,8OAAC,sNAAA,CAAA,eAAY;oDAAC,WAAU;;;;;;;;;;;iEAG1B,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;4CACZ,MAAK;4CACL,UAAU;4CACV,WAAU;4CACV,YAAY;gDAAE,OAAO,eAAe,IAAI;4CAAK;4CAC7C,UAAU;gDAAE,OAAO,eAAe,IAAI;4CAAK;sDAE1C,6BACC;;kEACE,8OAAC;wDAAI,WAAU;;;;;;oDAAkE;;6EAInF;;kEACE,8OAAC,sMAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;oDAAY;kEAE9B,8OAAC,kNAAA,CAAA,aAAU;wDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAW5C", "debugId": null}}, {"offset": {"line": 3642, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/src/app/CybersecurityTraining/page.jsx"], "sourcesContent": ["\"use client\";\n\nimport React from 'react';\nimport HeroBento from '../training/components/HeroBento';\nimport CurriculumHighlights from '../training/components/CurriculumHighlights';\nimport WhoCanJoin from '../training/components/WhoCanJoin';\nimport StudentLogos from '../training/components/StudentLogos';\nimport LearningModules from '../training/components/LearningModules';\nimport PremiumCTA from '../training/components/PremiumCTA';\nimport TrainingAccessForm from '../training/components/TrainingAccessForm';\n\nexport default function CybersecurityTrainingPage() {\n  return (\n    <div className=\"min-h-screen bg-white\">\n      {/* Hero Bento Section */}\n      <HeroBento />\n      \n      {/* Curriculum Highlights */}\n      <CurriculumHighlights />\n      \n      {/* Who Can Join */}\n      <WhoCanJoin />\n      \n      {/* Student Logos */}\n      <StudentLogos />\n      \n      {/* Learning Modules */}\n      <LearningModules />\n      \n      {/* Premium CTA */}\n      <PremiumCTA />\n      \n      {/* Training Access Form */}\n      <div id=\"training-form\"><TrainingAccessForm /></div>\n      \n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AATA;;;;;;;;;;AAWe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC,kJAAA,CAAA,UAAS;;;;;0BAGV,8OAAC,6JAAA,CAAA,UAAoB;;;;;0BAGrB,8OAAC,mJAAA,CAAA,UAAU;;;;;0BAGX,8OAAC,qJAAA,CAAA,UAAY;;;;;0BAGb,8OAAC,wJAAA,CAAA,UAAe;;;;;0BAGhB,8OAAC,mJAAA,CAAA,UAAU;;;;;0BAGX,8OAAC;gBAAI,IAAG;0BAAgB,cAAA,8OAAC,2JAAA,CAAA,UAAkB;;;;;;;;;;;;;;;;AAIjD", "debugId": null}}]}