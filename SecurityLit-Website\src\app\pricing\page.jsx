import React from 'react';
import PricingCalculatorSection from './components/PricingCalculatorSection';
import PricingCTA from './components/PricingCTA';
import ContactHero from '../contact/components/contactHero';

export const metadata = {
  title: "Cybersecurity Services Pricing | SecurityLit - Enterprise Security Consulting",
  description: "Get transparent pricing for SecurityLit's cybersecurity services including penetration testing, security assessments, and cloud security consulting. Calculate your project cost instantly.",
  keywords: "cybersecurity pricing, penetration testing cost, security assessment pricing, cloud security consulting rates, SecurityLit pricing",
  openGraph: {
    title: "Cybersecurity Services Pricing | SecurityLit",
    description: "Get transparent pricing for enterprise cybersecurity services. Calculate your project cost instantly with our pricing calculator.",
    url: "https://securitylit.com/pricing",
    siteName: "SecurityLit",
    images: [
      {
        url: "https://securitylit.com/images/pricing-og.jpg",
        width: 1200,
        height: 630,
        alt: "SecurityLit Cybersecurity Services Pricing",
      },
    ],
    locale: "en_US",
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "Cybersecurity Services Pricing | SecurityLit",
    description: "Get transparent pricing for enterprise cybersecurity services. Calculate your project cost instantly.",
    images: ["https://securitylit.com/images/pricing-twitter.jpg"],
  },
  alternates: {
    canonical: "https://securitylit.com/pricing",
  },
};

export default function PricingPage() {
  const pricingBreadcrumbItems = [
    {
      name: "Home",
      url: "/",
      iconKey: "home",
      description: "Return to homepage"
    },
    {
      name: "Pricing",
      url: "/pricing",
      current: true,
      iconKey: "pricing",
      description: "Get transparent pricing for cybersecurity services and calculate your project cost"
    }
  ];

  return (
    <div className="min-h-screen bg-white">
      {/* Main Calculator Section */}
      <PricingCalculatorSection />

      {/* Call to Action Section */}
      <PricingCTA />

      {/* Contact Hero Section with Custom Props */}
      <ContactHero
        title="Get Your Custom Security Quote"
        subtitle="Transparent pricing for enterprise cybersecurity"
        description="Ready to secure your organization? Fill out our form and our security experts will provide you with a detailed proposal tailored to your specific requirements. Get expert consultation and discover how we can protect your digital assets."
        breadcrumbItems={pricingBreadcrumbItems}
      />
    </div>
  );
}
