{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/src/app/common/components/BreadcrumbNavigation.jsx"], "sourcesContent": ["\"use client\";\r\nimport React, { useState } from 'react';\r\nimport Link from 'next/link';\r\nimport { ChevronRight, Home, FileText, Users, Settings, MapPin, Shield, Code, Smartphone, Globe, DollarSign, Building, Phone, Download } from 'lucide-react';\r\nimport { motion } from 'framer-motion';\r\n\r\n/**\r\n * Truncated Text Component for Mobile Breadcrumbs\r\n * Shows truncated text on mobile with click to expand functionality\r\n */\r\nconst TruncatedText = ({ text, maxLength = 25, onExpansionChange }) => {\r\n  const [isExpanded, setIsExpanded] = useState(false);\r\n\r\n  const handleToggle = () => {\r\n    const newExpanded = !isExpanded;\r\n    setIsExpanded(newExpanded);\r\n    if (onExpansionChange) {\r\n      onExpansionChange(newExpanded);\r\n    }\r\n  };\r\n\r\n  if (text.length <= maxLength) {\r\n    return <span>{text}</span>;\r\n  }\r\n\r\n  return (\r\n    <span\r\n      className=\"cursor-pointer\"\r\n      onClick={handleToggle}\r\n      title={isExpanded ? \"Click to collapse\" : \"Click to expand\"}\r\n    >\r\n      <span className=\"sm:hidden\">\r\n        {isExpanded ? text : `${text.substring(0, maxLength)}...`}\r\n      </span>\r\n      <span className=\"hidden sm:inline\">\r\n        {text}\r\n      </span>\r\n    </span>\r\n  );\r\n};\r\n\r\n/**\r\n * Structured Data Component for SEO\r\n */\r\nconst BreadcrumbStructuredData = ({ items }) => {\r\n  const structuredData = {\r\n    \"@context\": \"https://schema.org\",\r\n    \"@type\": \"BreadcrumbList\",\r\n    \"itemListElement\": items.map((item, index) => ({\r\n      \"@type\": \"ListItem\",\r\n      \"position\": index + 1,\r\n      \"name\": item.name,\r\n      \"item\": item.current ? undefined : `https://securitylit.com${item.url}`\r\n    }))\r\n  };\r\n\r\n  return (\r\n    <script\r\n      type=\"application/ld+json\"\r\n      dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}\r\n    />\r\n  );\r\n};\r\n\r\n/**\r\n * Universal Breadcrumb Navigation Component with SEO structured data\r\n * Provides clear navigation hierarchy and improves SEO across all pages\r\n */\r\nconst BreadcrumbNavigation = ({ items, className = \"\" }) => {\r\n  const [hasExpandedText, setHasExpandedText] = useState(false);\r\n\r\n  // Check if this is a blog page for conditional padding\r\n  const isBlogPage = items?.some(item =>\r\n    item.url?.includes('/Blogs') ||\r\n    item.iconKey === 'blogs' ||\r\n    className?.includes('blog')\r\n  );\r\n\r\n  return (\r\n    <>\r\n      {/* Structured Data for SEO */}\r\n      <BreadcrumbStructuredData items={items} />\r\n      \r\n      {/* Simple Breadcrumb Navigation */}\r\n      <nav\r\n        className={`bg-transparent py-2 sm:py-3 ${\r\n          isBlogPage\r\n            ? hasExpandedText\r\n              ? 'pb-6 sm:pb-4'\r\n              : 'pb-3 sm:pb-4'\r\n            : 'pb-2 sm:pb-3'\r\n        } ${className}`}\r\n        aria-label=\"Breadcrumb navigation\"\r\n      >\r\n        <div className=\"max-w-7xl mx-auto px-4\">\r\n          <motion.ol\r\n            className={`flex items-center space-x-1 text-xs sm:text-sm ${\r\n              className?.includes('text-white')\r\n                ? 'text-white'\r\n                : className?.includes('text-blue-600')\r\n                ? 'text-blue-600'\r\n                : 'text-gray-600'\r\n            }`}\r\n            initial={{ opacity: 0 }}\r\n            animate={{ opacity: 1 }}\r\n            transition={{ duration: 0.4 }}\r\n          >\r\n            {items.map((item, index) => (\r\n              <motion.li\r\n                key={index}\r\n                className=\"flex items-center\"\r\n                initial={{ opacity: 0 }}\r\n                animate={{ opacity: 1 }}\r\n                transition={{ duration: 0.3, delay: index * 0.1 }}\r\n              >\r\n                {index > 0 && (\r\n                  <ChevronRight\r\n                    className={`w-3 h-3 mx-1 ${\r\n                      className?.includes('text-white')\r\n                        ? 'text-white/70'\r\n                        : className?.includes('text-blue-600')\r\n                        ? 'text-blue-400'\r\n                        : 'text-gray-400'\r\n                    }`}\r\n                    aria-hidden=\"true\"\r\n                  />\r\n                )}\r\n\r\n                {item.current ? (\r\n                  <span\r\n                    className={`font-medium flex items-center gap-1 ${\r\n                      className?.includes('text-white')\r\n                        ? 'text-white'\r\n                        : className?.includes('text-blue-600')\r\n                        ? 'text-blue-600'\r\n                        : 'text-gray-900'\r\n                    }`}\r\n                    aria-current=\"page\"\r\n                    title={item.description}\r\n                  >\r\n                    {item.iconKey === 'home' && (\r\n                      <Home className={`w-3 h-3 ${\r\n                        className?.includes('text-white')\r\n                          ? 'text-white'\r\n                          : className?.includes('text-blue-600')\r\n                          ? 'text-blue-600'\r\n                          : 'text-blue-600'\r\n                      }`} />\r\n                    )}\r\n                    {item.iconKey === 'blogs' && (\r\n                      <FileText className={`w-3 h-3 ${\r\n                        className?.includes('text-white')\r\n                          ? 'text-white'\r\n                          : className?.includes('text-blue-600')\r\n                          ? 'text-blue-600'\r\n                          : 'text-blue-600'\r\n                      }`} />\r\n                    )}\r\n                    {item.iconKey === 'contact-us' && (\r\n                      <Phone className={`w-3 h-3 ${\r\n                        className?.includes('text-white')\r\n                          ? 'text-white'\r\n                          : className?.includes('text-blue-600')\r\n                          ? 'text-blue-600'\r\n                          : 'text-blue-600'\r\n                      }`} />\r\n                    )}\r\n                    <TruncatedText\r\n                      text={item.name}\r\n                      onExpansionChange={setHasExpandedText}\r\n                    />\r\n                  </span>\r\n                ) : (\r\n                  <Link\r\n                    href={item.url}\r\n                    className={`transition-colors duration-200 flex items-center gap-1 hover:underline ${\r\n                      className?.includes('text-white')\r\n                        ? 'text-white hover:text-white/80'\r\n                        : className?.includes('text-blue-600')\r\n                        ? 'text-blue-600 hover:text-blue-800'\r\n                        : 'text-blue-600 hover:text-blue-800'\r\n                    }`}\r\n                    title={item.description}\r\n                  >\r\n                    {item.iconKey === 'home' && (\r\n                      <Home className={`w-3 h-3 ${\r\n                        className?.includes('text-white')\r\n                          ? 'text-white'\r\n                          : className?.includes('text-blue-600')\r\n                          ? 'text-blue-600'\r\n                          : 'text-current'\r\n                      }`} />\r\n                    )}\r\n                    {item.iconKey === 'blogs' && (\r\n                      <FileText className={`w-3 h-3 ${\r\n                        className?.includes('text-white')\r\n                          ? 'text-white'\r\n                          : className?.includes('text-blue-600')\r\n                          ? 'text-blue-600'\r\n                          : 'text-current'\r\n                      }`} />\r\n                    )}\r\n                    {item.iconKey === 'contact-us' && (\r\n                      <Phone className={`w-3 h-3 ${\r\n                        className?.includes('text-white')\r\n                          ? 'text-white'\r\n                          : className?.includes('text-blue-600')\r\n                          ? 'text-blue-600'\r\n                          : 'text-current'\r\n                      }`} />\r\n                    )}\r\n                    <TruncatedText\r\n                      text={item.name}\r\n                      onExpansionChange={setHasExpandedText}\r\n                    />\r\n                  </Link>\r\n                )}\r\n              </motion.li>\r\n            ))}\r\n          </motion.ol>\r\n        </div>\r\n      </nav>\r\n    </>\r\n  );\r\n};\r\n\r\n// Client-side helper function for generating breadcrumb items\r\nexport const generateBreadcrumbs = (pageType, params = {}) => {\r\n  const baseBreadcrumb = {\r\n    name: \"Home\",\r\n    url: \"/\",\r\n    iconKey: \"home\",\r\n    description: \"Return to homepage\"\r\n  };\r\n\r\n  const breadcrumbConfigs = {\r\n    blog: (slug) => [\r\n      baseBreadcrumb,\r\n      {\r\n        name: \"Blog\",\r\n        url: \"/Blogs\",\r\n        iconKey: \"blogs\",\r\n        description: \"View all blog posts\"\r\n      },\r\n      {\r\n        name: params.title || \"Blog Post\",\r\n        url: `/Blogs/${slug}`,\r\n        current: true,\r\n        description: `${params.title || 'Blog post'} - Cybersecurity insights and penetration testing guidance`\r\n      }\r\n    ],\r\n    \r\n    service: (service) => [\r\n      baseBreadcrumb,\r\n      {\r\n        name: \"Services\",\r\n        url: \"/Services\",\r\n        iconKey: \"services\",\r\n        description: \"View all cybersecurity services\"\r\n      },\r\n      {\r\n        name: params.title || service,\r\n        url: `/Services/${service}`,\r\n        current: true,\r\n        iconKey: service,\r\n        description: `${params.title || service} penetration testing services`\r\n      }\r\n    ],\r\n\r\n    location: (country) => [\r\n      baseBreadcrumb,\r\n      {\r\n        name: \"Locations\",\r\n        url: \"/Locations\",\r\n        iconKey: \"locations\",\r\n        description: \"View all locations\"\r\n      },\r\n      {\r\n        name: params.countryName || country,\r\n        url: `/Locations/${country}`,\r\n        current: true,\r\n        description: `${params.countryName || country} cybersecurity services`\r\n      }\r\n    ],\r\n\r\n    company: (page) => [\r\n      baseBreadcrumb,\r\n      {\r\n        name: \"Company\",\r\n        url: \"/Company\",\r\n        iconKey: \"company\",\r\n        description: \"Learn about SecurityLit\"\r\n      },\r\n      {\r\n        name: params.title || page,\r\n        url: `/Company/${page}`,\r\n        current: true,\r\n        description: params.description || `${page} information`\r\n      }\r\n    ],\r\n\r\n    'company-size': (size) => [\r\n      baseBreadcrumb,\r\n      {\r\n        name: \"Company Size\",\r\n        url: \"/Company-size\",\r\n        iconKey: \"company\",\r\n        description: \"Solutions for different company sizes\"\r\n      },\r\n      {\r\n        name: params.title || size,\r\n        url: `/Company-size/${size}`,\r\n        current: true,\r\n        description: params.description || `${size} cybersecurity solutions`\r\n      }\r\n    ],\r\n\r\n    industry: (industry) => [\r\n      baseBreadcrumb,\r\n      {\r\n        name: \"Industries\",\r\n        url: \"/Industries\",\r\n        iconKey: \"company\",\r\n        description: \"Industry-specific cybersecurity solutions\"\r\n      },\r\n      {\r\n        name: params.title || industry,\r\n        url: `/Industries/${industry}`,\r\n        current: true,\r\n        description: params.description || `${industry} cybersecurity solutions`\r\n      }\r\n    ],\r\n\r\n    product: (product) => [\r\n      baseBreadcrumb,\r\n      {\r\n        name: \"Product\",\r\n        url: \"/Product\",\r\n        iconKey: \"product\",\r\n        description: \"Explore our cybersecurity products\"\r\n      },\r\n      {\r\n        name: params.title || product,\r\n        url: `/Product/${product}`,\r\n        current: true,\r\n        description: params.description || `${product} product information`\r\n      }\r\n    ],\r\n\r\n    simple: (pageName, url) => [\r\n      baseBreadcrumb,\r\n      {\r\n        name: pageName,\r\n        url: url,\r\n        current: true,\r\n        iconKey: params.iconKey,\r\n        description: params.description || `${pageName} page`\r\n      }\r\n    ]\r\n  };\r\n\r\n  return breadcrumbConfigs[pageType] || (() => [baseBreadcrumb]);\r\n};\r\n\r\nexport default BreadcrumbNavigation; "], "names": [], "mappings": ";;;;;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;;;AAJA;;;;;AAMA;;;CAGC,GACD,MAAM,gBAAgB;QAAC,EAAE,IAAI,EAAE,YAAY,EAAE,EAAE,iBAAiB,EAAE;;IAChE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,eAAe;QACnB,MAAM,cAAc,CAAC;QACrB,cAAc;QACd,IAAI,mBAAmB;YACrB,kBAAkB;QACpB;IACF;IAEA,IAAI,KAAK,MAAM,IAAI,WAAW;QAC5B,qBAAO,6LAAC;sBAAM;;;;;;IAChB;IAEA,qBACE,6LAAC;QACC,WAAU;QACV,SAAS;QACT,OAAO,aAAa,sBAAsB;;0BAE1C,6LAAC;gBAAK,WAAU;0BACb,aAAa,OAAO,AAAC,GAA+B,OAA7B,KAAK,SAAS,CAAC,GAAG,YAAW;;;;;;0BAEvD,6LAAC;gBAAK,WAAU;0BACb;;;;;;;;;;;;AAIT;GA7BM;KAAA;AA+BN;;CAEC,GACD,MAAM,2BAA2B;QAAC,EAAE,KAAK,EAAE;IACzC,MAAM,iBAAiB;QACrB,YAAY;QACZ,SAAS;QACT,mBAAmB,MAAM,GAAG,CAAC,CAAC,MAAM,QAAU,CAAC;gBAC7C,SAAS;gBACT,YAAY,QAAQ;gBACpB,QAAQ,KAAK,IAAI;gBACjB,QAAQ,KAAK,OAAO,GAAG,YAAY,AAAC,0BAAkC,OAAT,KAAK,GAAG;YACvE,CAAC;IACH;IAEA,qBACE,6LAAC;QACC,MAAK;QACL,yBAAyB;YAAE,QAAQ,KAAK,SAAS,CAAC;QAAgB;;;;;;AAGxE;MAlBM;AAoBN;;;CAGC,GACD,MAAM,uBAAuB;QAAC,EAAE,KAAK,EAAE,YAAY,EAAE,EAAE;;IACrD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvD,uDAAuD;IACvD,MAAM,aAAa,kBAAA,4BAAA,MAAO,IAAI,CAAC,CAAA;YAC7B;eAAA,EAAA,YAAA,KAAK,GAAG,cAAR,gCAAA,UAAU,QAAQ,CAAC,cACnB,KAAK,OAAO,KAAK,YACjB,sBAAA,gCAAA,UAAW,QAAQ,CAAC;;IAGtB,qBACE;;0BAEE,6LAAC;gBAAyB,OAAO;;;;;;0BAGjC,6LAAC;gBACC,WAAW,AAAC,+BAMR,OALF,aACI,kBACE,iBACA,iBACF,gBACL,KAAa,OAAV;gBACJ,cAAW;0BAEX,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,EAAE;wBACR,WAAW,AAAC,kDAMX,OALC,CAAA,sBAAA,gCAAA,UAAW,QAAQ,CAAC,iBAChB,eACA,CAAA,sBAAA,gCAAA,UAAW,QAAQ,CAAC,oBACpB,kBACA;wBAEN,SAAS;4BAAE,SAAS;wBAAE;wBACtB,SAAS;4BAAE,SAAS;wBAAE;wBACtB,YAAY;4BAAE,UAAU;wBAAI;kCAE3B,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,6LAAC,6LAAA,CAAA,SAAM,CAAC,EAAE;gCAER,WAAU;gCACV,SAAS;oCAAE,SAAS;gCAAE;gCACtB,SAAS;oCAAE,SAAS;gCAAE;gCACtB,YAAY;oCAAE,UAAU;oCAAK,OAAO,QAAQ;gCAAI;;oCAE/C,QAAQ,mBACP,6LAAC,yNAAA,CAAA,eAAY;wCACX,WAAW,AAAC,gBAMX,OALC,CAAA,sBAAA,gCAAA,UAAW,QAAQ,CAAC,iBAChB,kBACA,CAAA,sBAAA,gCAAA,UAAW,QAAQ,CAAC,oBACpB,kBACA;wCAEN,eAAY;;;;;;oCAIf,KAAK,OAAO,iBACX,6LAAC;wCACC,WAAW,AAAC,uCAMX,OALC,CAAA,sBAAA,gCAAA,UAAW,QAAQ,CAAC,iBAChB,eACA,CAAA,sBAAA,gCAAA,UAAW,QAAQ,CAAC,oBACpB,kBACA;wCAEN,gBAAa;wCACb,OAAO,KAAK,WAAW;;4CAEtB,KAAK,OAAO,KAAK,wBAChB,6LAAC,sMAAA,CAAA,OAAI;gDAAC,WAAW,AAAC,WAMjB,OALC,CAAA,sBAAA,gCAAA,UAAW,QAAQ,CAAC,iBAChB,eACA,CAAA,sBAAA,gCAAA,UAAW,QAAQ,CAAC,oBACpB,kBACA;;;;;;4CAGP,KAAK,OAAO,KAAK,yBAChB,6LAAC,iNAAA,CAAA,WAAQ;gDAAC,WAAW,AAAC,WAMrB,OALC,CAAA,sBAAA,gCAAA,UAAW,QAAQ,CAAC,iBAChB,eACA,CAAA,sBAAA,gCAAA,UAAW,QAAQ,CAAC,oBACpB,kBACA;;;;;;4CAGP,KAAK,OAAO,KAAK,8BAChB,6LAAC,uMAAA,CAAA,QAAK;gDAAC,WAAW,AAAC,WAMlB,OALC,CAAA,sBAAA,gCAAA,UAAW,QAAQ,CAAC,iBAChB,eACA,CAAA,sBAAA,gCAAA,UAAW,QAAQ,CAAC,oBACpB,kBACA;;;;;;0DAGR,6LAAC;gDACC,MAAM,KAAK,IAAI;gDACf,mBAAmB;;;;;;;;;;;iGAIvB,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAM,KAAK,GAAG;wCACd,WAAW,AAAC,0EAMX,OALC,CAAA,sBAAA,gCAAA,UAAW,QAAQ,CAAC,iBAChB,mCACA,CAAA,sBAAA,gCAAA,UAAW,QAAQ,CAAC,oBACpB,sCACA;wCAEN,OAAO,KAAK,WAAW;;4CAEtB,KAAK,OAAO,KAAK,wBAChB,6LAAC,sMAAA,CAAA,OAAI;gDAAC,WAAW,AAAC,WAMjB,OALC,CAAA,sBAAA,gCAAA,UAAW,QAAQ,CAAC,iBAChB,eACA,CAAA,sBAAA,gCAAA,UAAW,QAAQ,CAAC,oBACpB,kBACA;;;;;;4CAGP,KAAK,OAAO,KAAK,yBAChB,6LAAC,iNAAA,CAAA,WAAQ;gDAAC,WAAW,AAAC,WAMrB,OALC,CAAA,sBAAA,gCAAA,UAAW,QAAQ,CAAC,iBAChB,eACA,CAAA,sBAAA,gCAAA,UAAW,QAAQ,CAAC,oBACpB,kBACA;;;;;;4CAGP,KAAK,OAAO,KAAK,8BAChB,6LAAC,uMAAA,CAAA,QAAK;gDAAC,WAAW,AAAC,WAMlB,OALC,CAAA,sBAAA,gCAAA,UAAW,QAAQ,CAAC,iBAChB,eACA,CAAA,sBAAA,gCAAA,UAAW,QAAQ,CAAC,oBACpB,kBACA;;;;;;0DAGR,6LAAC;gDACC,MAAM,KAAK,IAAI;gDACf,mBAAmB;;;;;;;;;;;;;+BAxGpB;;;;;;;;;;;;;;;;;;;;;;AAmHrB;IA5JM;MAAA;AA+JC,MAAM,sBAAsB,SAAC;QAAU,0EAAS,CAAC;IACtD,MAAM,iBAAiB;QACrB,MAAM;QACN,KAAK;QACL,SAAS;QACT,aAAa;IACf;IAEA,MAAM,oBAAoB;QACxB,MAAM,CAAC,OAAS;gBACd;gBACA;oBACE,MAAM;oBACN,KAAK;oBACL,SAAS;oBACT,aAAa;gBACf;gBACA;oBACE,MAAM,OAAO,KAAK,IAAI;oBACtB,KAAK,AAAC,UAAc,OAAL;oBACf,SAAS;oBACT,aAAa,AAAC,GAA8B,OAA5B,OAAO,KAAK,IAAI,aAAY;gBAC9C;aACD;QAED,SAAS,CAAC,UAAY;gBACpB;gBACA;oBACE,MAAM;oBACN,KAAK;oBACL,SAAS;oBACT,aAAa;gBACf;gBACA;oBACE,MAAM,OAAO,KAAK,IAAI;oBACtB,KAAK,AAAC,aAAoB,OAAR;oBAClB,SAAS;oBACT,SAAS;oBACT,aAAa,AAAC,GAA0B,OAAxB,OAAO,KAAK,IAAI,SAAQ;gBAC1C;aACD;QAED,UAAU,CAAC,UAAY;gBACrB;gBACA;oBACE,MAAM;oBACN,KAAK;oBACL,SAAS;oBACT,aAAa;gBACf;gBACA;oBACE,MAAM,OAAO,WAAW,IAAI;oBAC5B,KAAK,AAAC,cAAqB,OAAR;oBACnB,SAAS;oBACT,aAAa,AAAC,GAAgC,OAA9B,OAAO,WAAW,IAAI,SAAQ;gBAChD;aACD;QAED,SAAS,CAAC,OAAS;gBACjB;gBACA;oBACE,MAAM;oBACN,KAAK;oBACL,SAAS;oBACT,aAAa;gBACf;gBACA;oBACE,MAAM,OAAO,KAAK,IAAI;oBACtB,KAAK,AAAC,YAAgB,OAAL;oBACjB,SAAS;oBACT,aAAa,OAAO,WAAW,IAAI,AAAC,GAAO,OAAL,MAAK;gBAC7C;aACD;QAED,gBAAgB,CAAC,OAAS;gBACxB;gBACA;oBACE,MAAM;oBACN,KAAK;oBACL,SAAS;oBACT,aAAa;gBACf;gBACA;oBACE,MAAM,OAAO,KAAK,IAAI;oBACtB,KAAK,AAAC,iBAAqB,OAAL;oBACtB,SAAS;oBACT,aAAa,OAAO,WAAW,IAAI,AAAC,GAAO,OAAL,MAAK;gBAC7C;aACD;QAED,UAAU,CAAC,WAAa;gBACtB;gBACA;oBACE,MAAM;oBACN,KAAK;oBACL,SAAS;oBACT,aAAa;gBACf;gBACA;oBACE,MAAM,OAAO,KAAK,IAAI;oBACtB,KAAK,AAAC,eAAuB,OAAT;oBACpB,SAAS;oBACT,aAAa,OAAO,WAAW,IAAI,AAAC,GAAW,OAAT,UAAS;gBACjD;aACD;QAED,SAAS,CAAC,UAAY;gBACpB;gBACA;oBACE,MAAM;oBACN,KAAK;oBACL,SAAS;oBACT,aAAa;gBACf;gBACA;oBACE,MAAM,OAAO,KAAK,IAAI;oBACtB,KAAK,AAAC,YAAmB,OAAR;oBACjB,SAAS;oBACT,aAAa,OAAO,WAAW,IAAI,AAAC,GAAU,OAAR,SAAQ;gBAChD;aACD;QAED,QAAQ,CAAC,UAAU,MAAQ;gBACzB;gBACA;oBACE,MAAM;oBACN,KAAK;oBACL,SAAS;oBACT,SAAS,OAAO,OAAO;oBACvB,aAAa,OAAO,WAAW,IAAI,AAAC,GAAW,OAAT,UAAS;gBACjD;aACD;IACH;IAEA,OAAO,iBAAiB,CAAC,SAAS,IAAI,CAAC,IAAM;YAAC;SAAe;AAC/D;uCAEe", "debugId": null}}, {"offset": {"line": 412, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/src/app/contact/components/ContactForm.jsx"], "sourcesContent": ["\"use client\";\n\nimport React, { useState } from \"react\";\n\nexport default function ContactForm() {\n  const [formData, setFormData] = useState({\n    firstName: '',\n    lastName: '',\n    email: '',\n    company: '',\n    organizationSize: '',\n    industry: ''\n  });\n\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const [apiError, setApiError] = useState(\"\");\n  const [success, setSuccess] = useState(\"\");\n\n  const handleInputChange = (e) => {\n    const { name, value } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    if (isSubmitting) return;\n    setIsSubmitting(true);\n    setApiError(\"\");\n    setSuccess(\"\");\n\n    try {\n      // Make the API request to store submission and send email\n      const response = await fetch(\"/api/submissions\", {\n        method: \"POST\",\n        headers: { \"Content-Type\": \"application/json\" },\n        body: JSON.stringify({\n          name: `${formData.firstName} ${formData.lastName}`,\n          email: formData.email,\n          company: formData.company,\n          organizationSize: formData.organizationSize,\n          industry: formData.industry,\n          message: `Contact inquiry from ${formData.firstName} ${formData.lastName} at ${formData.company}. Organization size: ${formData.organizationSize}, Industry: ${formData.industry}`,\n          formType: 'contact-form',\n          subject: 'New Contact Form Submission from SecurityLit Contact Page'\n        }),\n      });\n\n      const result = await response.json();\n      if (response.ok && result.success) {\n        // Clear form fields on success\n        setFormData({\n          firstName: '',\n          lastName: '',\n          email: '',\n          company: '',\n          organizationSize: '',\n          industry: ''\n        });\n        setSuccess(\"Thank you! We'll get back to you within 24 hours.\");\n      } else {\n        setApiError(result.error || \"Something went wrong. Please try again.\");\n      }\n    } catch (error) {\n      console.error(\"Error submitting form:\", error);\n      setApiError(\"Failed to send message. Please try again.\");\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n\n  return (\n    <div className=\"lg:w-1/2 bg-white\">\n      <div className=\"max-w-lg mx-auto p-8 pt-28 pb-16 lg:p-12 lg:pt-28 lg:pb-16\">\n        <div className=\"mb-6\">\n          <h2 className=\"text-3xl lg:text-4xl font-bold text-[var(--color-dark-blue)] mb-4\">\n            Complete the form to talk to an expert.\n          </h2>\n          <p className=\"text-lg text-gray-600\">\n            Get in touch for a free consultation and discover how we can protect your organization.\n          </p>\n        </div>\n\n        <form onSubmit={handleSubmit} className=\"space-y-5\">\n          {/* Error Message */}\n          {apiError && (\n            <div className=\"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg\">\n              {apiError}\n            </div>\n          )}\n\n          {/* Success Message */}\n          {success && (\n            <div className=\"bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg\">\n              {success}\n            </div>\n          )}\n\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-5\">\n            <div>\n              <label htmlFor=\"firstName\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                First name <span className=\"text-red-500\">*</span>\n              </label>\n              <input \n                type=\"text\" \n                id=\"firstName\" \n                name=\"firstName\" \n                value={formData.firstName} \n                onChange={handleInputChange} \n                required \n                className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[var(--color-blue)] transition-all\"\n              />\n            </div>\n            <div>\n              <label htmlFor=\"lastName\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Last name <span className=\"text-red-500\">*</span>\n              </label>\n              <input \n                type=\"text\" \n                id=\"lastName\" \n                name=\"lastName\" \n                value={formData.lastName} \n                onChange={handleInputChange} \n                required \n                className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[var(--color-blue)] transition-all\"\n              />\n            </div>\n          </div>\n          \n          <div>\n            <label htmlFor=\"email\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n              Email <span className=\"text-red-500\">*</span>\n            </label>\n            <input \n              type=\"email\" \n              id=\"email\" \n              name=\"email\" \n              value={formData.email} \n              onChange={handleInputChange} \n              required \n              className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[var(--color-blue)] transition-all\"\n            />\n          </div>\n          \n          <div>\n            <label htmlFor=\"company\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n              Company <span className=\"text-red-500\">*</span>\n            </label>\n            <input \n              type=\"text\" \n              id=\"company\" \n              name=\"company\" \n              value={formData.company} \n              onChange={handleInputChange} \n              required \n              className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[var(--color-blue)] transition-all\"\n            />\n          </div>\n          \n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-5\">\n            <div>\n              <label htmlFor=\"organizationSize\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Company size <span className=\"text-red-500\">*</span>\n              </label>\n              <select \n                id=\"organizationSize\" \n                name=\"organizationSize\" \n                value={formData.organizationSize} \n                onChange={handleInputChange} \n                required \n                className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[var(--color-blue)] transition-all bg-white\"\n              >\n                <option value=\"\">Choose size</option>\n                <option value=\"startup\">Startup (1-50)</option>\n                <option value=\"smb\">SMB (51-500)</option>\n                <option value=\"enterprise\">Enterprise (500+)</option>\n                <option value=\"government\">Government</option>\n              </select>\n            </div>\n            <div>\n              <label htmlFor=\"industry\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Company industry <span className=\"text-red-500\">*</span>\n              </label>\n              <select \n                id=\"industry\" \n                name=\"industry\" \n                value={formData.industry} \n                onChange={handleInputChange} \n                required \n                className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[var(--color-blue)] transition-all bg-white\"\n              >\n                <option value=\"\">Choose industry</option>\n                <option value=\"technology\">Technology</option>\n                <option value=\"finance\">Finance</option>\n                <option value=\"healthcare\">Healthcare</option>\n                <option value=\"manufacturing\">Manufacturing</option>\n                <option value=\"retail\">Retail</option>\n                <option value=\"education\">Education</option>\n                <option value=\"government\">Government</option>\n                <option value=\"other\">Other</option>\n              </select>\n            </div>\n          </div>\n          \n          <button\n            type=\"submit\"\n            disabled={isSubmitting}\n            className=\"w-full bg-[var(--color-blue)] hover:bg-[var(--color-blue-secondary)] disabled:bg-gray-400 text-white px-8 py-4 rounded-lg font-bold text-lg transition-all duration-300 shadow-lg hover:shadow-xl flex items-center justify-center gap-2\"\n          >\n            {isSubmitting ? (\n              <>\n                <div className=\"animate-spin rounded-full h-5 w-5 border-b-2 border-white\"></div>\n                Submitting...\n              </>\n            ) : (\n              'Submit Inquiry'\n            )}\n          </button>\n        </form>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;;;AAFA;;AAIe,SAAS;;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,WAAW;QACX,UAAU;QACV,OAAO;QACP,SAAS;QACT,kBAAkB;QAClB,UAAU;IACZ;IAEA,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,MAAM,oBAAoB,CAAC;QACzB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM;QAChC,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,CAAC,KAAK,EAAE;YACV,CAAC;IACH;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,IAAI,cAAc;QAClB,gBAAgB;QAChB,YAAY;QACZ,WAAW;QAEX,IAAI;YACF,0DAA0D;YAC1D,MAAM,WAAW,MAAM,MAAM,oBAAoB;gBAC/C,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;oBACnB,MAAM,AAAC,GAAwB,OAAtB,SAAS,SAAS,EAAC,KAAqB,OAAlB,SAAS,QAAQ;oBAChD,OAAO,SAAS,KAAK;oBACrB,SAAS,SAAS,OAAO;oBACzB,kBAAkB,SAAS,gBAAgB;oBAC3C,UAAU,SAAS,QAAQ;oBAC3B,SAAS,AAAC,wBAA6C,OAAtB,SAAS,SAAS,EAAC,KAA2B,OAAxB,SAAS,QAAQ,EAAC,QAA8C,OAAxC,SAAS,OAAO,EAAC,yBAA+D,OAAxC,SAAS,gBAAgB,EAAC,gBAAgC,OAAlB,SAAS,QAAQ;oBAChL,UAAU;oBACV,SAAS;gBACX;YACF;YAEA,MAAM,SAAS,MAAM,SAAS,IAAI;YAClC,IAAI,SAAS,EAAE,IAAI,OAAO,OAAO,EAAE;gBACjC,+BAA+B;gBAC/B,YAAY;oBACV,WAAW;oBACX,UAAU;oBACV,OAAO;oBACP,SAAS;oBACT,kBAAkB;oBAClB,UAAU;gBACZ;gBACA,WAAW;YACb,OAAO;gBACL,YAAY,OAAO,KAAK,IAAI;YAC9B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,YAAY;QACd,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAoE;;;;;;sCAGlF,6LAAC;4BAAE,WAAU;sCAAwB;;;;;;;;;;;;8BAKvC,6LAAC;oBAAK,UAAU;oBAAc,WAAU;;wBAErC,0BACC,6LAAC;4BAAI,WAAU;sCACZ;;;;;;wBAKJ,yBACC,6LAAC;4BAAI,WAAU;sCACZ;;;;;;sCAIL,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAM,SAAQ;4CAAY,WAAU;;gDAA+C;8DACvE,6LAAC;oDAAK,WAAU;8DAAe;;;;;;;;;;;;sDAE5C,6LAAC;4CACC,MAAK;4CACL,IAAG;4CACH,MAAK;4CACL,OAAO,SAAS,SAAS;4CACzB,UAAU;4CACV,QAAQ;4CACR,WAAU;;;;;;;;;;;;8CAGd,6LAAC;;sDACC,6LAAC;4CAAM,SAAQ;4CAAW,WAAU;;gDAA+C;8DACvE,6LAAC;oDAAK,WAAU;8DAAe;;;;;;;;;;;;sDAE3C,6LAAC;4CACC,MAAK;4CACL,IAAG;4CACH,MAAK;4CACL,OAAO,SAAS,QAAQ;4CACxB,UAAU;4CACV,QAAQ;4CACR,WAAU;;;;;;;;;;;;;;;;;;sCAKhB,6LAAC;;8CACC,6LAAC;oCAAM,SAAQ;oCAAQ,WAAU;;wCAA+C;sDACxE,6LAAC;4CAAK,WAAU;sDAAe;;;;;;;;;;;;8CAEvC,6LAAC;oCACC,MAAK;oCACL,IAAG;oCACH,MAAK;oCACL,OAAO,SAAS,KAAK;oCACrB,UAAU;oCACV,QAAQ;oCACR,WAAU;;;;;;;;;;;;sCAId,6LAAC;;8CACC,6LAAC;oCAAM,SAAQ;oCAAU,WAAU;;wCAA+C;sDACxE,6LAAC;4CAAK,WAAU;sDAAe;;;;;;;;;;;;8CAEzC,6LAAC;oCACC,MAAK;oCACL,IAAG;oCACH,MAAK;oCACL,OAAO,SAAS,OAAO;oCACvB,UAAU;oCACV,QAAQ;oCACR,WAAU;;;;;;;;;;;;sCAId,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAM,SAAQ;4CAAmB,WAAU;;gDAA+C;8DAC5E,6LAAC;oDAAK,WAAU;8DAAe;;;;;;;;;;;;sDAE9C,6LAAC;4CACC,IAAG;4CACH,MAAK;4CACL,OAAO,SAAS,gBAAgB;4CAChC,UAAU;4CACV,QAAQ;4CACR,WAAU;;8DAEV,6LAAC;oDAAO,OAAM;8DAAG;;;;;;8DACjB,6LAAC;oDAAO,OAAM;8DAAU;;;;;;8DACxB,6LAAC;oDAAO,OAAM;8DAAM;;;;;;8DACpB,6LAAC;oDAAO,OAAM;8DAAa;;;;;;8DAC3B,6LAAC;oDAAO,OAAM;8DAAa;;;;;;;;;;;;;;;;;;8CAG/B,6LAAC;;sDACC,6LAAC;4CAAM,SAAQ;4CAAW,WAAU;;gDAA+C;8DAChE,6LAAC;oDAAK,WAAU;8DAAe;;;;;;;;;;;;sDAElD,6LAAC;4CACC,IAAG;4CACH,MAAK;4CACL,OAAO,SAAS,QAAQ;4CACxB,UAAU;4CACV,QAAQ;4CACR,WAAU;;8DAEV,6LAAC;oDAAO,OAAM;8DAAG;;;;;;8DACjB,6LAAC;oDAAO,OAAM;8DAAa;;;;;;8DAC3B,6LAAC;oDAAO,OAAM;8DAAU;;;;;;8DACxB,6LAAC;oDAAO,OAAM;8DAAa;;;;;;8DAC3B,6LAAC;oDAAO,OAAM;8DAAgB;;;;;;8DAC9B,6LAAC;oDAAO,OAAM;8DAAS;;;;;;8DACvB,6LAAC;oDAAO,OAAM;8DAAY;;;;;;8DAC1B,6LAAC;oDAAO,OAAM;8DAAa;;;;;;8DAC3B,6LAAC;oDAAO,OAAM;8DAAQ;;;;;;;;;;;;;;;;;;;;;;;;sCAK5B,6LAAC;4BACC,MAAK;4BACL,UAAU;4BACV,WAAU;sCAET,6BACC;;kDACE,6LAAC;wCAAI,WAAU;;;;;;oCAAkE;;+CAInF;;;;;;;;;;;;;;;;;;;;;;;AAOd;GA5NwB;KAAA", "debugId": null}}, {"offset": {"line": 961, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/src/app/contact/components/contactHero.jsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React from \"react\";\r\nimport { CheckCircle, Shield, Users, Clock, Award, Globe } from \"lucide-react\";\r\nimport BreadcrumbNavigation from \"../../common/components/BreadcrumbNavigation\";\r\nimport ContactForm from \"./ContactForm\";\r\n\r\nexport default function ContactHero() {\r\n\r\n  const breadcrumbItems = [\r\n    {\r\n      name: \"Home\",\r\n      url: \"/\",\r\n      iconKey: \"home\",\r\n      description: \"Return to homepage\"\r\n    },\r\n    {\r\n      name: \"Contact Us\",\r\n      url: \"/contact\",\r\n      current: true,\r\n      iconKey: \"contact-us\",\r\n      description: \"Get in touch with SecurityLit for cybersecurity consultation and services\"\r\n    }\r\n  ];\r\n\r\n  const features = [\r\n    { icon: <Shield className=\"w-5 h-5\" />, title: \"Free Security Consultation\", description: \"Expert cybersecurity guidance at no cost\" },\r\n    { icon: <Users className=\"w-5 h-5\" />, title: \"Certified Security Experts\", description: \"CISSP, CEH, and OSCP certified professionals\" },\r\n    { icon: <Clock className=\"w-5 h-5\" />, title: \"24/7 Incident Response\", description: \"Round-the-clock security support\" },\r\n    { icon: <Award className=\"w-5 h-5\" />, title: \"200+ Clients Served\", description: \"Trusted by enterprises globally\" },\r\n    { icon: <Globe className=\"w-5 h-5\" />, title: \"Global Presence\", description: \"Services across India, USA, Singapore, Australia\" },\r\n    { icon: <CheckCircle className=\"w-5 h-5\" />, title: \"Compliance Excellence\", description: \"ISO 27001 certified solutions\" }\r\n  ];\r\n  return (\r\n    <div className=\"relative min-h-screen bg-white\">\r\n      <div className=\"flex flex-col lg:flex-row min-h-screen\">\r\n        {/* Left Section - Dark Hero Panel */}\r\n        <div className=\"lg:w-1/2 bg-gradient-to-br from-[var(--color-dark-blue)] to-[var(--color-dark-blue-hover)] relative overflow-hidden\">\r\n          <div className=\"absolute inset-0 bg-cover bg-center bg-no-repeat opacity-20\"\r\n               style={{ backgroundImage: 'url(/images/hexagon.svg)' }}>\r\n          </div>\r\n          <div className=\"absolute inset-0 bg-[var(--color-dark-blue)]/80\"></div>\r\n          \r\n          {/* CHANGED: Added flex and justify-center to center the content block */}\r\n          <div className=\"relative z-10 flex justify-center px-6 pt-28 pb-16 lg:px-12 lg:pt-28 lg:pb-16\">\r\n            <div className=\"max-w-lg w-full\"> {/* Added w-full to ensure max-w-lg is effective */}\r\n              <div className=\"mb-6\">\r\n                <BreadcrumbNavigation items={breadcrumbItems} className=\"text-white\" />\r\n              </div>\r\n              \r\n              <h1 className=\"text-3xl lg:text-4xl font-bold text-white mb-4\">\r\n                Contact SecurityLit\r\n              </h1>\r\n              <p className=\"text-lg lg:text-xl text-[var(--color-blue)] font-semibold mb-4\">\r\n              Your global cybersecurity consulting partner\r\n              </p>\r\n              <p className=\"text-base lg:text-lg text-white/90 mb-8\">\r\n              We partner with organizations worldwide to strengthen their cybersecurity posture through strategic consulting, advanced threat testing, and comprehensive security solutions.\r\n              </p>\r\n\r\n              <div className=\"grid grid-cols-1 sm:grid-cols-2 gap-4 mb-8\">\r\n                {features.map((feature, index) => (\r\n                  <div key={index} className=\"bg-white/10 backdrop-blur-sm rounded-xl p-4 border border-white/20 hover:bg-white/15 transition-all duration-300 group\">\r\n                    <div className=\"flex items-center gap-3 mb-2\">\r\n                      <div className=\"text-[var(--color-yellow)] group-hover:scale-110 transition-transform\">\r\n                        {feature.icon}\r\n                      </div>\r\n                      <h3 className=\"text-white font-semibold text-sm\">{feature.title}</h3>\r\n                    </div>\r\n                    <p className=\"text-white/70 text-xs leading-relaxed\">\r\n                      {feature.description}\r\n                    </p>\r\n                  </div>\r\n                ))}\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Right Section - Contact Form */}\r\n        <ContactForm />\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AALA;;;;;;AAOe,SAAS;IAEtB,MAAM,kBAAkB;QACtB;YACE,MAAM;YACN,KAAK;YACL,SAAS;YACT,aAAa;QACf;QACA;YACE,MAAM;YACN,KAAK;YACL,SAAS;YACT,SAAS;YACT,aAAa;QACf;KACD;IAED,MAAM,WAAW;QACf;YAAE,oBAAM,6LAAC,yMAAA,CAAA,SAAM;gBAAC,WAAU;;;;;;YAAc,OAAO;YAA8B,aAAa;QAA2C;QACrI;YAAE,oBAAM,6LAAC,uMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;YAAc,OAAO;YAA8B,aAAa;QAA+C;QACxI;YAAE,oBAAM,6LAAC,uMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;YAAc,OAAO;YAA0B,aAAa;QAAmC;QACxH;YAAE,oBAAM,6LAAC,uMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;YAAc,OAAO;YAAuB,aAAa;QAAkC;QACpH;YAAE,oBAAM,6LAAC,uMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;YAAc,OAAO;YAAmB,aAAa;QAAmD;QACjI;YAAE,oBAAM,6LAAC,8NAAA,CAAA,cAAW;gBAAC,WAAU;;;;;;YAAc,OAAO;YAAyB,aAAa;QAAgC;KAC3H;IACD,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;4BACV,OAAO;gCAAE,iBAAiB;4BAA2B;;;;;;sCAE1D,6LAAC;4BAAI,WAAU;;;;;;sCAGf,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;oCAAkB;kDAC/B,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,8JAAA,CAAA,UAAoB;4CAAC,OAAO;4CAAiB,WAAU;;;;;;;;;;;kDAG1D,6LAAC;wCAAG,WAAU;kDAAiD;;;;;;kDAG/D,6LAAC;wCAAE,WAAU;kDAAiE;;;;;;kDAG9E,6LAAC;wCAAE,WAAU;kDAA0C;;;;;;kDAIvD,6LAAC;wCAAI,WAAU;kDACZ,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,6LAAC;gDAAgB,WAAU;;kEACzB,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;0EACZ,QAAQ,IAAI;;;;;;0EAEf,6LAAC;gEAAG,WAAU;0EAAoC,QAAQ,KAAK;;;;;;;;;;;;kEAEjE,6LAAC;wDAAE,WAAU;kEACV,QAAQ,WAAW;;;;;;;+CARd;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAkBpB,6LAAC,sJAAA,CAAA,UAAW;;;;;;;;;;;;;;;;AAIpB;KA7EwB", "debugId": null}}, {"offset": {"line": 1229, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/src/app/common/components/EmailCallCTA.jsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React from \"react\";\r\nimport { LinkButton, OutlinedLinkButton } from \"../buttons/BrandButtons\";\r\n\r\nexport default function EmailCallCTA({ \r\n  title = \"Ready to Secure Your Organization?\",\r\n  description = \"Contact us today for a free consultation and discover how we can protect your business.\",\r\n  phoneNumber = \"+918527800769\",\r\n  emailAddress = \"<EMAIL>\",\r\n  callButtonText = \"Call Now\",\r\n  emailButtonText = \"Send Email\",\r\n  className = \"\"\r\n}) {\r\n  return (\r\n    <div className={`text-center ${className}`}>\r\n      <div className=\"bg-gradient-to-r from-[var(--color-blue)] to-[var(--color-blue-secondary)] rounded-2xl p-8 text-white\">\r\n        <h3 className=\"text-2xl font-bold mb-4\">{title}</h3>\r\n        <p className=\"text-lg mb-6 opacity-90\">\r\n          {description}\r\n        </p>\r\n        <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\r\n          <LinkButton href={`tel:${phoneNumber}`}>\r\n            {callButtonText}\r\n          </LinkButton>\r\n          <OutlinedLinkButton href={`mailto:${emailAddress}`}>\r\n            {emailButtonText}\r\n          </OutlinedLinkButton>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n} "], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKe,SAAS,aAAa,KAQpC;QARoC,EACnC,QAAQ,oCAAoC,EAC5C,cAAc,yFAAyF,EACvG,cAAc,eAAe,EAC7B,eAAe,yBAAyB,EACxC,iBAAiB,UAAU,EAC3B,kBAAkB,YAAY,EAC9B,YAAY,EAAE,EACf,GARoC;IASnC,qBACE,6LAAC;QAAI,WAAW,AAAC,eAAwB,OAAV;kBAC7B,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAG,WAAU;8BAA2B;;;;;;8BACzC,6LAAC;oBAAE,WAAU;8BACV;;;;;;8BAEH,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,mJAAA,CAAA,aAAU;4BAAC,MAAM,AAAC,OAAkB,OAAZ;sCACtB;;;;;;sCAEH,6LAAC,mJAAA,CAAA,qBAAkB;4BAAC,MAAM,AAAC,UAAsB,OAAb;sCACjC;;;;;;;;;;;;;;;;;;;;;;;AAMb;KA3BwB", "debugId": null}}, {"offset": {"line": 1311, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/src/app/contact/components/contactOfficeInfo.jsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React from \"react\";\r\nimport { MapPin, Phone, Mail, Clock, Globe, Building, Users, Award, Shield, Target } from \"lucide-react\";\r\nimport { LinkButton, OutlinedLinkButton } from \"../../common/buttons/BrandButtons\";\r\nimport EmailCallCTA from \"../../common/components/EmailCallCTA\";\r\n\r\nexport default function ContactOfficeInfo() {\r\n  const officeDetails = {\r\n    address: {\r\n      street: \"5th Floor, DLF Two Horizon Centre\",\r\n      city: \"DLF Phase 5, Gurugram\",\r\n      country: \"India\"\r\n    },\r\n    contact: {\r\n      phone: \"+91 8527 800769\",\r\n      email: \"<EMAIL>\",\r\n      website: \"www.securitylit.com\"\r\n    },\r\n    team: {\r\n      experts: \"OSCP, CEH, EJPT, eWPTX V2 certified experts\",\r\n      experience: \"10+ years average experience\",\r\n      specializations: \"Penetration testing, Red teaming, vCISO\"\r\n    },\r\n    certifications: [\r\n      {\r\n        name: \"OSCP\",\r\n        fullName: \"Offensive Security Certified Professional\",\r\n        logo: \"/images/oscp-logo.png\",\r\n        description: \"Advanced penetration testing certification\"\r\n      },\r\n      {\r\n        name: \"EJPT\",\r\n        fullName: \"eLearnSecurity Junior Penetration Tester\",\r\n        logo: \"/images/ejpt-logo.png\", \r\n        description: \"Entry-level penetration testing certification\"\r\n      },\r\n      {\r\n        name: \"CEH\",\r\n        fullName: \"Certified Ethical Hacker\",\r\n        logo: \"/images/ceh-logo.png\",\r\n        description: \"Ethical hacking and security assessment\"\r\n      },\r\n      {\r\n        name: \"eWPTX V2\",\r\n        fullName: \"eLearnSecurity Web Application Penetration Tester\",\r\n        logo: \"/images/ewptx-logo.png\",\r\n        description: \"Advanced security management certification\"\r\n      }\r\n    ]\r\n  };\r\n\r\n  return (\r\n    <section className=\"py-20 bg-gradient-to-br from-gray-50 to-white\">\r\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\r\n        <div className=\"text-center mb-16\">\r\n          <h2 className=\"text-4xl lg:text-5xl font-bold text-[var(--color-dark-blue)] mb-6\">\r\n            Reach Out to Us\r\n          </h2>\r\n          <p className=\"text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed\">\r\n            Get in touch with our cybersecurity experts. We're here to help secure your organization.\r\n          </p>\r\n        </div>\r\n\r\n        <div className=\"grid grid-cols-1 xl:grid-cols-3 gap-8 lg:gap-12\">\r\n          {/* Contact Information */}\r\n          <div className=\"bg-white rounded-3xl shadow-xl border border-gray-100 p-8 hover:shadow-2xl transition-all duration-300\">\r\n            <div className=\"text-center mb-6\">\r\n              <div className=\"w-16 h-16 bg-[var(--color-blue)]/10 rounded-2xl flex items-center justify-center mx-auto mb-4\">\r\n                <Building className=\"w-8 h-8 text-[var(--color-blue)]\" />\r\n              </div>\r\n              <h3 className=\"text-2xl font-bold text-[var(--color-dark-blue)]\">Contact Details</h3>\r\n            </div>\r\n            \r\n            <div className=\"space-y-6\">\r\n              {/* Email */}\r\n              <div className=\"bg-gray-50 rounded-xl p-4 hover:bg-[var(--color-blue)]/5 transition-colors\">\r\n                <div className=\"flex items-center gap-4\">\r\n                  <div className=\"flex-shrink-0 w-10 h-10 bg-[var(--color-blue)]/10 rounded-xl flex items-center justify-center\">\r\n                    <Mail className=\"w-5 h-5 text-[var(--color-blue)]\" />\r\n                  </div>\r\n                  <div>\r\n                    <h4 className=\"font-semibold text-gray-900 mb-1\">Email Address</h4>\r\n                    <a \r\n                      href={`mailto:${officeDetails.contact.email}`}\r\n                      className=\"text-[var(--color-blue)] hover:text-[var(--color-blue-secondary)] transition-colors font-medium\"\r\n                    >\r\n                      {officeDetails.contact.email}\r\n                    </a>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              {/* Website */}\r\n              <div className=\"bg-gray-50 rounded-xl p-4 hover:bg-[var(--color-blue)]/5 transition-colors\">\r\n                <div className=\"flex items-center gap-4\">\r\n                  <div className=\"flex-shrink-0 w-10 h-10 bg-[var(--color-blue)]/10 rounded-xl flex items-center justify-center\">\r\n                    <Globe className=\"w-5 h-5 text-[var(--color-blue)]\" />\r\n                  </div>\r\n                  <div>\r\n                    <h4 className=\"font-semibold text-gray-900 mb-1\">Website</h4>\r\n                    <a \r\n                      href={`https://${officeDetails.contact.website}`}\r\n                      target=\"_blank\"\r\n                      rel=\"noopener noreferrer\"\r\n                      className=\"text-[var(--color-blue)] hover:text-[var(--color-blue-secondary)] transition-colors font-medium\"\r\n                    >\r\n                      {officeDetails.contact.website}\r\n                    </a>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Team Information */}\r\n          <div className=\"bg-white rounded-3xl shadow-xl border border-gray-100 p-8 hover:shadow-2xl transition-all duration-300\">\r\n            <div className=\"text-center mb-6\">\r\n              <div className=\"w-16 h-16 bg-[var(--color-blue)]/10 rounded-2xl flex items-center justify-center mx-auto mb-4\">\r\n                <Users className=\"w-8 h-8 text-[var(--color-blue)]\" />\r\n              </div>\r\n              <h3 className=\"text-2xl font-bold text-[var(--color-dark-blue)]\">Our Expert Team</h3>\r\n            </div>\r\n            \r\n            <div className=\"space-y-6\">\r\n              <div className=\"bg-gray-50 rounded-xl p-4 hover:bg-[var(--color-blue)]/5 transition-colors\">\r\n                <div className=\"flex items-start gap-4\">\r\n                  <div className=\"flex-shrink-0 w-10 h-10 bg-[var(--color-blue)]/10 rounded-xl flex items-center justify-center\">\r\n                    <Shield className=\"w-5 h-5 text-[var(--color-blue)]\" />\r\n                  </div>\r\n                  <div>\r\n                    <h4 className=\"font-semibold text-gray-900 mb-2\">Certified Professionals</h4>\r\n                    <p className=\"text-gray-600 text-sm leading-relaxed\">{officeDetails.team.experts}</p>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n              \r\n              <div className=\"bg-gray-50 rounded-xl p-4 hover:bg-[var(--color-blue)]/5 transition-colors\">\r\n                <div className=\"flex items-start gap-4\">\r\n                  <div className=\"flex-shrink-0 w-10 h-10 bg-[var(--color-blue)]/10 rounded-xl flex items-center justify-center\">\r\n                    <Target className=\"w-5 h-5 text-[var(--color-blue)]\" />\r\n                  </div>\r\n                  <div>\r\n                    <h4 className=\"font-semibold text-gray-900 mb-2\">Experience</h4>\r\n                    <p className=\"text-gray-600 text-sm leading-relaxed\">{officeDetails.team.experience}</p>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n              \r\n              <div className=\"bg-gray-50 rounded-xl p-4 hover:bg-[var(--color-blue)]/5 transition-colors\">\r\n                <div className=\"flex items-start gap-4\">\r\n                  <div className=\"flex-shrink-0 w-10 h-10 bg-[var(--color-blue)]/10 rounded-xl flex items-center justify-center\">\r\n                    <Award className=\"w-5 h-5 text-[var(--color-blue)]\" />\r\n                  </div>\r\n                  <div>\r\n                    <h4 className=\"font-semibold text-gray-900 mb-2\">Specializations</h4>\r\n                    <p className=\"text-gray-600 text-sm leading-relaxed\">{officeDetails.team.specializations}</p>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Certifications */}\r\n          <div className=\"bg-white rounded-3xl shadow-xl border border-gray-100 p-8 hover:shadow-2xl transition-all duration-300\">\r\n            <div className=\"text-center mb-6\">\r\n              <div className=\"w-16 h-16 bg-[var(--color-blue)]/10 rounded-2xl flex items-center justify-center mx-auto mb-4\">\r\n                <Award className=\"w-8 h-8 text-[var(--color-blue)]\" />\r\n              </div>\r\n              <h3 className=\"text-2xl font-bold text-[var(--color-dark-blue)]\">Certifications & Credentials</h3>\r\n            </div>\r\n            \r\n            <div className=\"space-y-4\">\r\n              {officeDetails.certifications.map((cert, index) => (\r\n                <div key={index} className=\"bg-gray-50 rounded-xl p-4 hover:bg-[var(--color-blue)]/5 transition-colors\">\r\n                  <div className=\"flex items-center gap-4\">\r\n                    <div className=\"flex-shrink-0 w-12 h-12 bg-white rounded-xl flex items-center justify-center shadow-sm\">\r\n                      <img \r\n                        src={cert.logo} \r\n                        alt={`${cert.name} logo`}\r\n                        className=\"w-8 h-8 object-contain\"\r\n                      />\r\n                    </div>\r\n                    <div className=\"flex-1 min-w-0\">\r\n                      <h4 className=\"font-semibold text-gray-900 text-sm mb-1\">{cert.name}</h4>\r\n                      <p className=\"text-gray-600 text-xs leading-tight\">{cert.description}</p>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              ))}\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Call to Action */}\r\n        <div className=\"mt-16\">\r\n          <EmailCallCTA />\r\n        </div>\r\n      </div>\r\n    </section>\r\n  );\r\n} "], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AALA;;;;;;AAOe,SAAS;IACtB,MAAM,gBAAgB;QACpB,SAAS;YACP,QAAQ;YACR,MAAM;YACN,SAAS;QACX;QACA,SAAS;YACP,OAAO;YACP,OAAO;YACP,SAAS;QACX;QACA,MAAM;YACJ,SAAS;YACT,YAAY;YACZ,iBAAiB;QACnB;QACA,gBAAgB;YACd;gBACE,MAAM;gBACN,UAAU;gBACV,MAAM;gBACN,aAAa;YACf;YACA;gBACE,MAAM;gBACN,UAAU;gBACV,MAAM;gBACN,aAAa;YACf;YACA;gBACE,MAAM;gBACN,UAAU;gBACV,MAAM;gBACN,aAAa;YACf;YACA;gBACE,MAAM;gBACN,UAAU;gBACV,MAAM;gBACN,aAAa;YACf;SACD;IACH;IAEA,qBACE,6LAAC;QAAQ,WAAU;kBACjB,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAoE;;;;;;sCAGlF,6LAAC;4BAAE,WAAU;sCAA0D;;;;;;;;;;;;8BAKzE,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,6MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;;;;;;sDAEtB,6LAAC;4CAAG,WAAU;sDAAmD;;;;;;;;;;;;8CAGnE,6LAAC;oCAAI,WAAU;;sDAEb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;;;;;;kEAElB,6LAAC;;0EACC,6LAAC;gEAAG,WAAU;0EAAmC;;;;;;0EACjD,6LAAC;gEACC,MAAM,AAAC,UAAqC,OAA5B,cAAc,OAAO,CAAC,KAAK;gEAC3C,WAAU;0EAET,cAAc,OAAO,CAAC,KAAK;;;;;;;;;;;;;;;;;;;;;;;sDAOpC,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;4DAAC,WAAU;;;;;;;;;;;kEAEnB,6LAAC;;0EACC,6LAAC;gEAAG,WAAU;0EAAmC;;;;;;0EACjD,6LAAC;gEACC,MAAM,AAAC,WAAwC,OAA9B,cAAc,OAAO,CAAC,OAAO;gEAC9C,QAAO;gEACP,KAAI;gEACJ,WAAU;0EAET,cAAc,OAAO,CAAC,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAS1C,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;;;;;;sDAEnB,6LAAC;4CAAG,WAAU;sDAAmD;;;;;;;;;;;;8CAGnE,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC,yMAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;;;;;;kEAEpB,6LAAC;;0EACC,6LAAC;gEAAG,WAAU;0EAAmC;;;;;;0EACjD,6LAAC;gEAAE,WAAU;0EAAyC,cAAc,IAAI,CAAC,OAAO;;;;;;;;;;;;;;;;;;;;;;;sDAKtF,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC,yMAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;;;;;;kEAEpB,6LAAC;;0EACC,6LAAC;gEAAG,WAAU;0EAAmC;;;;;;0EACjD,6LAAC;gEAAE,WAAU;0EAAyC,cAAc,IAAI,CAAC,UAAU;;;;;;;;;;;;;;;;;;;;;;;sDAKzF,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;4DAAC,WAAU;;;;;;;;;;;kEAEnB,6LAAC;;0EACC,6LAAC;gEAAG,WAAU;0EAAmC;;;;;;0EACjD,6LAAC;gEAAE,WAAU;0EAAyC,cAAc,IAAI,CAAC,eAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAQlG,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;;;;;;sDAEnB,6LAAC;4CAAG,WAAU;sDAAmD;;;;;;;;;;;;8CAGnE,6LAAC;oCAAI,WAAU;8CACZ,cAAc,cAAc,CAAC,GAAG,CAAC,CAAC,MAAM,sBACvC,6LAAC;4CAAgB,WAAU;sDACzB,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DACC,KAAK,KAAK,IAAI;4DACd,KAAK,AAAC,GAAY,OAAV,KAAK,IAAI,EAAC;4DAClB,WAAU;;;;;;;;;;;kEAGd,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAG,WAAU;0EAA4C,KAAK,IAAI;;;;;;0EACnE,6LAAC;gEAAE,WAAU;0EAAuC,KAAK,WAAW;;;;;;;;;;;;;;;;;;2CAXhE;;;;;;;;;;;;;;;;;;;;;;8BAqBlB,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,sJAAA,CAAA,UAAY;;;;;;;;;;;;;;;;;;;;;AAKvB;KAlMwB", "debugId": null}}, {"offset": {"line": 1926, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/src/app/contact/components/contactTestimonial.jsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React from \"react\";\r\n\r\nexport default function ContactTestimonial() {\r\n  const partners = [\r\n    { name: \"Fortune 500\", logo: \"/seclit-logo-white.png\" },\r\n    { name: \"Government of India\", logo: \"/seclit-logo-white.png\" },\r\n    { name: \"Leading Healthcare Provider\", logo: \"/seclit-logo-white.png\" },\r\n    { name: \"Major Financial Institution\", logo: \"/seclit-logo-white.png\" },\r\n  ];\r\n\r\n  return (\r\n    // CHANGED: The background is now set to your light brand yellow.\r\n    <section \r\n      className=\"py-20 lg:py-28\" \r\n      style={{ backgroundColor: 'var(--color-yellow)' }}\r\n    >\r\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\r\n        <div className=\"grid grid-cols-1 lg:grid-cols-12 gap-y-12 items-center\">\r\n          \r\n          {/* Left Section - Heading */}\r\n          <div className=\"lg:col-span-5 text-center lg:text-left\">\r\n            <h2 className=\"text-3xl md:text-4xl font-bold text-[var(--color-white)] leading-snug\">\r\n              Trusted cyber security partner to leading Indian organisations.\r\n            </h2>\r\n          </div>\r\n\r\n          {/* Divider (visible on large screens only) */}\r\n          <div className=\"hidden lg:block lg:col-span-1\">\r\n             {/* CHANGED: Divider color is slightly darkened for better contrast on the yellow background */}\r\n             <div className=\"w-px h-24 bg-gray-300 mx-auto\"></div>\r\n          </div>\r\n\r\n          {/* Right Section - Partner Logos */}\r\n          <div className=\"lg:col-span-6\">\r\n            <div className=\"grid grid-cols-2 gap-x-8 gap-y-10 sm:gap-12\">\r\n              {partners.map((partner) => (\r\n                <div key={partner.name} className=\"flex justify-center items-center\">\r\n                  <img\r\n                    className=\"max-h-12 w-full object-contain transition-all duration-300 filter grayscale hover:grayscale-0\"\r\n                    src={partner.logo}\r\n                    alt={partner.name}\r\n                    onError={(e) => { e.currentTarget.style.display = 'none'; }}\r\n                  />\r\n                </div>\r\n              ))}\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </section>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIe,SAAS;IACtB,MAAM,WAAW;QACf;YAAE,MAAM;YAAe,MAAM;QAAyB;QACtD;YAAE,MAAM;YAAuB,MAAM;QAAyB;QAC9D;YAAE,MAAM;YAA+B,MAAM;QAAyB;QACtE;YAAE,MAAM;YAA+B,MAAM;QAAyB;KACvE;IAED,OACE,iEAAiE;kBACjE,6LAAC;QACC,WAAU;QACV,OAAO;YAAE,iBAAiB;QAAsB;kBAEhD,cAAA,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCAGb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAG,WAAU;sCAAwE;;;;;;;;;;;kCAMxF,6LAAC;wBAAI,WAAU;kCAEZ,cAAA,6LAAC;4BAAI,WAAU;;;;;;;;;;;kCAIlB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;sCACZ,SAAS,GAAG,CAAC,CAAC,wBACb,6LAAC;oCAAuB,WAAU;8CAChC,cAAA,6LAAC;wCACC,WAAU;wCACV,KAAK,QAAQ,IAAI;wCACjB,KAAK,QAAQ,IAAI;wCACjB,SAAS,CAAC;4CAAQ,EAAE,aAAa,CAAC,KAAK,CAAC,OAAO,GAAG;wCAAQ;;;;;;mCALpD,QAAQ,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAetC;KAjDwB", "debugId": null}}, {"offset": {"line": 2055, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/src/app/Home/components/TrustedTestimonials.jsx"], "sourcesContent": ["\"use client\";\r\nimport React from \"react\";\r\nimport Image from \"next/image\";\r\n\r\nconst TrustedTestimonials = () => {\r\n  const trustedCompanies = [\r\n    {\r\n      name: \"Microsoft\",\r\n      logo: \"/images/iso27001-logo.png\", // Using existing logo as placeholder\r\n      alt: \"Microsoft Logo\"\r\n    },\r\n    {\r\n      name: \"Amazon\", \r\n      logo: \"/images/pci-dss-logo.png\",\r\n      alt: \"Amazon Logo\"\r\n    },\r\n    {\r\n      name: \"Google\",\r\n      logo: \"/images/crest-logo.png\", \r\n      alt: \"Google Logo\"\r\n    },\r\n    {\r\n      name: \"Apple\",\r\n      logo: \"/images/hipaa-logo.png\",\r\n      alt: \"Apple Logo\"\r\n    }\r\n  ];\r\n\r\n  const testimonials = [\r\n    {\r\n      name: \"<PERSON>\",\r\n      title: \"CTO at TechCorp\",\r\n      photo: \"/images/ankita-dhakar.jpeg\", // Using existing team photo\r\n      quote: \"SecurityLit's comprehensive security solutions have transformed our approach to cybersecurity. Their real-time monitoring and incident response capabilities are unmatched.\"\r\n    },\r\n    {\r\n      name: \"<PERSON>\", \r\n      title: \"CISO at DataFlow\",\r\n      photo: \"/images/akash-verma.jpeg\", // Using existing team photo\r\n      quote: \"The level of expertise and dedication from the SecurityLit team is exceptional. They've helped us maintain the highest security standards while scaling our operations.\"\r\n    },\r\n    {\r\n      name: \"<PERSON>\",\r\n      title: \"VP Security at CloudNet\", \r\n      photo: \"/images/archana-verma.jpeg\", // Using existing team photo\r\n      quote: \"Implementing SecurityLit's PTaaS solution was a game-changer for our organization. The continuous testing and real-time alerts have significantly improved our security posture.\"\r\n    }\r\n  ];\r\n\r\n  return (\r\n    <section className=\"w-full\">\r\n             {/* Dark Section - Trusted by Industry Leaders */}\r\n       <div className=\"bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 py-20\">\r\n         <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\r\n           <div className=\"text-center mb-10\">\r\n                         <h2 className=\"text-3xl md:text-4xl font-bold text-white mb-3\">\r\n               Trusted by Industry Leaders\r\n             </h2>\r\n             <p className=\"text-lg text-gray-300 max-w-3xl mx-auto\">\r\n               Join hundreds of leading organizations that trust SecurityLit for their cybersecurity needs\r\n             </p>\r\n          </div>\r\n          \r\n                     {/* Company Logos */}\r\n           <div className=\"flex flex-wrap justify-center items-center gap-6 md:gap-8\">\r\n             {trustedCompanies.map((company, index) => (\r\n               <div \r\n                 key={index}\r\n                 className=\"bg-gray-800/50 backdrop-blur-sm border border-gray-700/50 rounded-xl p-4 md:p-6 hover:bg-gray-800/70 transition-all duration-300 group\"\r\n               >\r\n                                   <div className=\"w-20 h-20 md:w-24 md:h-24 mx-auto mb-3 bg-white rounded-lg flex items-center justify-center p-3 group-hover:scale-110 transition-transform duration-300\">\r\n                    <Image\r\n                      src={company.logo}\r\n                      alt={company.alt}\r\n                      width={64}\r\n                      height={64}\r\n                      className=\"w-12 h-12 md:w-16 md:h-16 object-contain\"\r\n                    />\r\n                  </div>\r\n                                   <p className=\"text-white font-medium text-center text-sm md:text-base\">\r\n                    {company.name}\r\n                  </p>\r\n              </div>\r\n            ))}\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n             {/* White Section - Testimonials */}\r\n       <div className=\"bg-white py-20 border-t-4 border-purple-500\">\r\n         <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\r\n           <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\r\n            {testimonials.map((testimonial, index) => (\r\n                                            <div \r\n                 key={index}\r\n                 className=\"bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100\"\r\n               >\r\n                 {/* Testimonial Photo */}\r\n                 <div className=\"flex items-center mb-6\">\r\n                   <div className=\"w-14 h-14 rounded-full overflow-hidden mr-3\">\r\n                     <Image\r\n                       src={testimonial.photo}\r\n                       alt={testimonial.name}\r\n                       width={56}\r\n                       height={56}\r\n                       className=\"w-full h-full object-cover\"\r\n                     />\r\n                   </div>\r\n                   <div>\r\n                     <h3 className=\"font-bold text-gray-900 text-base\">\r\n                       {testimonial.name}\r\n                     </h3>\r\n                     <p className=\"text-orange-500 font-medium text-sm\">\r\n                       {testimonial.title}\r\n                     </p>\r\n                   </div>\r\n                 </div>\r\n                 \r\n                                   {/* Testimonial Quote */}\r\n                  <blockquote className=\"text-gray-600 leading-relaxed text-sm pt-4\">\r\n                    \"{testimonial.quote}\"\r\n                  </blockquote>\r\n              </div>\r\n            ))}\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </section>\r\n  );\r\n};\r\n\r\nexport default TrustedTestimonials; "], "names": [], "mappings": ";;;;AACA;AACA;AAFA;;;;AAIA,MAAM,sBAAsB;IAC1B,MAAM,mBAAmB;QACvB;YACE,MAAM;YACN,MAAM;YACN,KAAK;QACP;QACA;YACE,MAAM;YACN,MAAM;YACN,KAAK;QACP;QACA;YACE,MAAM;YACN,MAAM;YACN,KAAK;QACP;QACA;YACE,MAAM;YACN,MAAM;YACN,KAAK;QACP;KACD;IAED,MAAM,eAAe;QACnB;YACE,MAAM;YACN,OAAO;YACP,OAAO;YACP,OAAO;QACT;QACA;YACE,MAAM;YACN,OAAO;YACP,OAAO;YACP,OAAO;QACT;QACA;YACE,MAAM;YACN,OAAO;YACP,OAAO;YACP,OAAO;QACT;KACD;IAED,qBACE,6LAAC;QAAQ,WAAU;;0BAEhB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACD,6LAAC;oCAAG,WAAU;8CAAiD;;;;;;8CAG3E,6LAAC;oCAAE,WAAU;8CAA0C;;;;;;;;;;;;sCAMzD,6LAAC;4BAAI,WAAU;sCACZ,iBAAiB,GAAG,CAAC,CAAC,SAAS,sBAC9B,6LAAC;oCAEC,WAAU;;sDAEQ,6LAAC;4CAAI,WAAU;sDAC9B,cAAA,6LAAC,gIAAA,CAAA,UAAK;gDACJ,KAAK,QAAQ,IAAI;gDACjB,KAAK,QAAQ,GAAG;gDAChB,OAAO;gDACP,QAAQ;gDACR,WAAU;;;;;;;;;;;sDAGG,6LAAC;4CAAE,WAAU;sDAC3B,QAAQ,IAAI;;;;;;;mCAbX;;;;;;;;;;;;;;;;;;;;;0BAsBf,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;kCACb,aAAa,GAAG,CAAC,CAAC,aAAa,sBACA,6LAAC;gCAE5B,WAAU;;kDAGV,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;oDACJ,KAAK,YAAY,KAAK;oDACtB,KAAK,YAAY,IAAI;oDACrB,OAAO;oDACP,QAAQ;oDACR,WAAU;;;;;;;;;;;0DAGd,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEACX,YAAY,IAAI;;;;;;kEAEnB,6LAAC;wDAAE,WAAU;kEACV,YAAY,KAAK;;;;;;;;;;;;;;;;;;kDAMvB,6LAAC;wCAAW,WAAU;;4CAA6C;4CAC/D,YAAY,KAAK;4CAAC;;;;;;;;+BA1BlB;;;;;;;;;;;;;;;;;;;;;;;;;;AAmCtB;KA7HM;uCA+HS", "debugId": null}}]}