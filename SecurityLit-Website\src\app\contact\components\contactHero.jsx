"use client";

import React from "react";
import { CheckCircle, Shield, Users, Clock, Award, Globe } from "lucide-react";
import BreadcrumbNavigation from "../../common/components/BreadcrumbNavigation";
import ContactForm from "./ContactForm";

export default function ContactHero({
  title = "Contact SecurityLit",
  subtitle = "Your global cybersecurity consulting partner",
  description = "We partner with organizations worldwide to strengthen their cybersecurity posture through strategic consulting, advanced threat testing, and comprehensive security solutions.",
  breadcrumbItems = [
    {
      name: "Home",
      url: "/",
      iconKey: "home",
      description: "Return to homepage"
    },
    {
      name: "Contact Us",
      url: "/contact",
      current: true,
      iconKey: "contact-us",
      description: "Get in touch with SecurityLit for cybersecurity consultation and services"
    }
  ]
}) {

  const features = [
    { icon: <Shield className="w-5 h-5" />, title: "Free Security Consultation", description: "Expert cybersecurity guidance at no cost" },
    { icon: <Users className="w-5 h-5" />, title: "Certified Security Experts", description: "CISSP, CEH, and OSCP certified professionals" },
    { icon: <Clock className="w-5 h-5" />, title: "24/7 Incident Response", description: "Round-the-clock security support" },
    { icon: <Award className="w-5 h-5" />, title: "200+ Clients Served", description: "Trusted by enterprises globally" },
    { icon: <Globe className="w-5 h-5" />, title: "Global Presence", description: "Services across India, USA, Singapore, Australia" },
    { icon: <CheckCircle className="w-5 h-5" />, title: "Compliance Excellence", description: "ISO 27001 certified solutions" }
  ];
  return (
    <div className="relative min-h-screen bg-white">
      <div className="flex flex-col lg:flex-row min-h-screen">
        {/* Left Section - Dark Hero Panel */}
        <div className="lg:w-1/2 bg-gradient-to-br from-[var(--color-dark-blue)] to-[var(--color-dark-blue-hover)] relative overflow-hidden">
          <div className="absolute inset-0 bg-cover bg-center bg-no-repeat opacity-20"
               style={{ backgroundImage: 'url(/images/hexagon.svg)' }}>
          </div>
          <div className="absolute inset-0 bg-[var(--color-dark-blue)]/80"></div>
          
          {/* CHANGED: Added flex and justify-center to center the content block */}
          <div className="relative z-10 flex justify-center px-6 pt-28 pb-16 lg:px-12 lg:pt-28 lg:pb-16">
            <div className="max-w-lg w-full"> {/* Added w-full to ensure max-w-lg is effective */}
              <div className="mb-6">
                <BreadcrumbNavigation items={breadcrumbItems} className="text-white" />
              </div>
              
              <h1 className="text-3xl lg:text-4xl font-bold text-white mb-4">
                {title}
              </h1>
              <p className="text-lg lg:text-xl text-[var(--color-blue)] font-semibold mb-4">
                {subtitle}
              </p>
              <p className="text-base lg:text-lg text-white/90 mb-8">
                {description}
              </p>

              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-8">
                {features.map((feature, index) => (
                  <div key={index} className="bg-white/10 backdrop-blur-sm rounded-xl p-4 border border-white/20 hover:bg-white/15 transition-all duration-300 group">
                    <div className="flex items-center gap-3 mb-2">
                      <div className="text-[var(--color-yellow)] group-hover:scale-110 transition-transform">
                        {feature.icon}
                      </div>
                      <h3 className="text-white font-semibold text-sm">{feature.title}</h3>
                    </div>
                    <p className="text-white/70 text-xs leading-relaxed">
                      {feature.description}
                    </p>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Right Section - Contact Form */}
        <ContactForm />
      </div>
    </div>
  );
}
