{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/src/app/pricing/components/SecurityCalculator.jsx"], "sourcesContent": ["\"use client\";\n\nimport React, { useState, useMemo } from \"react\";\n\n// SecurityLit Cybersecurity Services Pricing Data\nconst securityServicesData = [\n  // Web Application Security Testing\n  {\n    target: \"Web Application\",\n    userRole: \"1 user role\",\n    pages: \"40 pages\",\n    hours: 40,\n    comprehensive: 6500,\n    standard: 4800,\n  },\n  {\n    target: \"Web Application\",\n    userRole: \"1 user role\", \n    pages: \"50 pages\",\n    hours: 56,\n    comprehensive: 9200,\n    standard: 6900,\n  },\n  {\n    target: \"Web Application\",\n    userRole: \"2 user roles\",\n    pages: \"60 pages\",\n    hours: 80,\n    comprehensive: 13500,\n    standard: 10200,\n  },\n  {\n    target: \"Web Application\",\n    userRole: \"3 user roles\",\n    pages: \"70 pages\",\n    hours: 96,\n    comprehensive: 16800,\n    standard: 12600,\n  },\n  {\n    target: \"Web Application\",\n    userRole: \"4 user roles\",\n    pages: \"80 pages\",\n    hours: 112,\n    comprehensive: 20100,\n    standard: 15000,\n  },\n  {\n    target: \"Web Application\",\n    userRole: \"5+ user roles\",\n    pages: \"100+ pages\",\n    hours: 128,\n    comprehensive: 23400,\n    standard: 17500,\n  },\n\n  // API Security Testing\n  {\n    target: \"API Security\",\n    userRole: \"1 service\",\n    endpoints: \"75 endpoints\",\n    hours: 40,\n    comprehensive: 6500,\n    standard: 4800,\n  },\n  {\n    target: \"API Security\",\n    userRole: \"1 service\",\n    endpoints: \"90 endpoints\", \n    hours: 56,\n    comprehensive: 9200,\n    standard: 6900,\n  },\n  {\n    target: \"API Security\",\n    userRole: \"2 services\",\n    endpoints: \"120 endpoints\",\n    hours: 80,\n    comprehensive: 13500,\n    standard: 10200,\n  },\n  {\n    target: \"API Security\",\n    userRole: \"3 services\",\n    endpoints: \"150 endpoints\",\n    hours: 96,\n    comprehensive: 16800,\n    standard: 12600,\n  },\n  {\n    target: \"API Security\",\n    userRole: \"4+ services\",\n    endpoints: \"180+ endpoints\",\n    hours: 112,\n    comprehensive: 20100,\n    standard: 15000,\n  },\n\n  // Mobile Application Security\n  {\n    target: \"Mobile Application\",\n    userRole: \"iOS or Android\",\n    screens: \"25 screens\",\n    hours: 40,\n    comprehensive: 6500,\n    standard: 4800,\n  },\n  {\n    target: \"Mobile Application\",\n    userRole: \"iOS and Android\",\n    screens: \"35 screens\",\n    hours: 56,\n    comprehensive: 9200,\n    standard: 6900,\n  },\n  {\n    target: \"Mobile Application\",\n    userRole: \"Cross-platform + Backend\",\n    screens: \"50 screens\",\n    hours: 80,\n    comprehensive: 13500,\n    standard: 10200,\n  },\n  {\n    target: \"Mobile Application\",\n    userRole: \"Enterprise Mobile Suite\",\n    screens: \"75+ screens\",\n    hours: 96,\n    comprehensive: 16800,\n    standard: 12600,\n  },\n\n  // Network Security Assessment\n  {\n    target: \"Network Security\",\n    userRole: \"Small Network\",\n    scope: \"50 IPs\",\n    hours: 40,\n    comprehensive: 6500,\n    standard: 4800,\n  },\n  {\n    target: \"Network Security\",\n    userRole: \"Medium Network\",\n    scope: \"100 IPs\",\n    hours: 56,\n    comprehensive: 9200,\n    standard: 6900,\n  },\n  {\n    target: \"Network Security\",\n    userRole: \"Large Network\",\n    scope: \"200 IPs\",\n    hours: 80,\n    comprehensive: 13500,\n    standard: 10200,\n  },\n  {\n    target: \"Network Security\",\n    userRole: \"Enterprise Network\",\n    scope: \"300+ IPs\",\n    hours: 96,\n    comprehensive: 16800,\n    standard: 12600,\n  },\n\n  // Cloud Security Assessment\n  {\n    target: \"Cloud Security\",\n    userRole: \"Single Cloud Provider\",\n    scope: \"Basic Configuration\",\n    hours: 40,\n    comprehensive: 6500,\n    standard: 4800,\n  },\n  {\n    target: \"Cloud Security\",\n    userRole: \"Multi-Cloud Setup\",\n    scope: \"Standard Configuration\",\n    hours: 56,\n    comprehensive: 9200,\n    standard: 6900,\n  },\n  {\n    target: \"Cloud Security\",\n    userRole: \"Enterprise Cloud\",\n    scope: \"Complex Configuration\",\n    hours: 80,\n    comprehensive: 13500,\n    standard: 10200,\n  },\n  {\n    target: \"Cloud Security\",\n    userRole: \"Global Cloud Infrastructure\",\n    scope: \"Enterprise+ Configuration\",\n    hours: 96,\n    comprehensive: 16800,\n    standard: 12600,\n  },\n];\n\nconst serviceDescriptions = {\n  \"Web Application\": \"Comprehensive security testing of your web applications to identify vulnerabilities, secure coding issues, and potential attack vectors that could compromise your business data.\",\n  \"API Security\": \"In-depth security assessment of your APIs, including authentication, authorization, data validation, and protection against common API vulnerabilities like injection attacks.\",\n  \"Mobile Application\": \"Complete mobile security testing covering both client-side and server-side components, including data storage, communication security, and platform-specific vulnerabilities.\",\n  \"Network Security\": \"Thorough network penetration testing to identify security weaknesses in your network infrastructure, including firewalls, routers, and network segmentation.\",\n  \"Cloud Security\": \"Comprehensive cloud security assessment covering configuration reviews, access controls, data protection, and compliance with cloud security best practices.\",\n};\n\nconst SecurityDropdown = ({ label, value, onChange, options, placeholder, icon }) => (\n  <div className=\"space-y-3\">\n    <div className=\"flex items-center gap-3\">\n      {icon && (\n        <div className=\"w-8 h-8 bg-gradient-to-br from-[var(--color-blue)] to-[var(--color-blue-secondary)] rounded-lg flex items-center justify-center\">\n          <div className=\"text-white text-sm\">\n            {icon}\n          </div>\n        </div>\n      )}\n      <label className=\"text-[var(--color-dark-blue)] font-semibold text-lg\">\n        {label}\n      </label>\n    </div>\n    <div className=\"relative\">\n      <select\n        value={value}\n        onChange={onChange}\n        className={`w-full p-5 pr-14 border-2 rounded-2xl appearance-none transition-all duration-300 font-medium text-lg\n          ${value\n            ? 'border-[var(--color-blue)] bg-white text-[var(--color-dark-blue)] shadow-xl shadow-[var(--color-blue)]/10'\n            : 'border-gray-300 bg-white text-gray-500 hover:border-[var(--color-blue)]/60 hover:shadow-lg'\n          }\n          focus:outline-none focus:ring-4 focus:ring-[var(--color-blue)]/25 focus:border-[var(--color-blue)] focus:shadow-xl`}\n      >\n        <option value=\"\" className=\"text-gray-500\">{placeholder}</option>\n        {options.map((option) => (\n          <option key={option} value={option} className=\"text-[var(--color-dark-blue)] font-medium py-2\">\n            {option}\n          </option>\n        ))}\n      </select>\n      <div className=\"pointer-events-none absolute inset-y-0 right-0 flex items-center pr-5\">\n        <div className={`w-8 h-8 rounded-full flex items-center justify-center transition-all duration-300 ${\n          value ? 'bg-[var(--color-blue)]' : 'bg-gray-300'\n        }`}>\n          <svg\n            className=\"h-4 w-4 text-white\"\n            xmlns=\"http://www.w3.org/2000/svg\"\n            viewBox=\"0 0 20 20\"\n            fill=\"currentColor\"\n          >\n            <path fillRule=\"evenodd\" d=\"M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z\" clipRule=\"evenodd\" />\n          </svg>\n        </div>\n      </div>\n    </div>\n  </div>\n);\n\nconst StyledDropdown = ({ label, value, onChange, options, placeholder }) => (\n  <div className=\"relative group\">\n    <label className=\"block mb-2 font-medium text-[var(--color-dark-blue)] pl-2\">{label}:</label>\n    <div className=\"relative flex items-center pl-2\">\n      <select\n        value={value}\n        onChange={onChange}\n        className={`w-full p-3 pr-10 border rounded-lg appearance-none transition-all duration-200 ease-in-out\n          ${value ? 'border-[var(--color-blue)] bg-white' : 'border-gray-300 bg-gray-50'}\n          focus:outline-none focus:ring-2 focus:ring-[var(--color-blue)] focus:border-transparent\n          hover:border-[var(--color-blue)]`}\n      >\n        <option value=\"\">{placeholder}</option>\n        {options.map((option) => (\n          <option key={option} value={option}>\n            {option}\n          </option>\n        ))}\n      </select>\n      <div className=\"pointer-events-none absolute inset-y-0 right-0 flex items-center pr-3\">\n        <div className=\"h-6 border-r border-gray-300 mr-2\"></div>\n        <svg\n          className=\"fill-current text-gray-500 h-4 w-4 transition-transform duration-200 group-hover:scale-110\"\n          xmlns=\"http://www.w3.org/2000/svg\"\n          viewBox=\"0 0 20 20\"\n        >\n          <path d=\"M9.293 12.95l.707.707L15.657 8l-1.414-1.414L10 10.828 5.757 6.586 4.343 8z\" />\n        </svg>\n      </div>\n    </div>\n    {value && (\n      <div className=\"w-1 h-full bg-[var(--color-blue)] absolute -left-1 top-0 bottom-0 rounded-full\"></div>\n    )}\n  </div>\n);\n\nconst TargetDescription = ({ target, assessmentType, userRole, scope, competitorQuote, result }) => {\n  const serviceDescriptions = {\n    \"Web Application\": \"Comprehensive security testing of web applications including authentication, authorization, input validation, and business logic vulnerabilities.\",\n    \"API Security\": \"Thorough testing of REST and GraphQL APIs for security vulnerabilities, authentication flaws, and data exposure risks.\",\n    \"Mobile Application\": \"Complete security assessment of mobile applications covering both client-side and server-side vulnerabilities.\",\n    \"Network Infrastructure\": \"In-depth penetration testing of network infrastructure, including firewalls, routers, and network segmentation.\",\n    \"Cloud Security\": \"Comprehensive security assessment of cloud environments, configurations, and access controls.\",\n    \"Social Engineering\": \"Simulated social engineering attacks to test human security awareness and organizational vulnerabilities.\"\n  };\n\n  return (\n    <div className=\"mb-6 md:mt-0 mt-6 p-4 bg-gray-50 rounded-lg border border-gray-200\">\n      <div className=\"font-bold text-xl text-gray-800 mb-2\">{target}</div>\n      <div className=\"text-gray-600 mb-4\">{serviceDescriptions[target]}</div>\n\n      <div className=\"space-y-3\">\n        {assessmentType && (\n          <div className=\"flex items-center text-sm text-gray-700\">\n            <span className=\"font-semibold w-40\">Assessment Type:</span>\n            <span className=\"bg-[var(--color-blue)]/10 text-[var(--color-blue)] px-3 py-1 rounded-full text-sm\">\n              {assessmentType}\n            </span>\n          </div>\n        )}\n\n        {userRole && (\n          <div className=\"flex items-center text-sm text-gray-700\">\n            <span className=\"font-semibold w-40\">User Role / Scope:</span>\n            <span className=\"bg-gray-100 text-gray-800 px-3 py-1 rounded-full text-sm\">\n              {userRole}\n            </span>\n          </div>\n        )}\n\n        {scope && (\n          <div className=\"flex items-center text-sm text-gray-700\">\n            <span className=\"font-semibold w-40\">Scope:</span>\n            <span className=\"bg-gray-100 text-gray-800 px-3 py-1 rounded-full text-sm\">\n              {scope}\n            </span>\n          </div>\n        )}\n\n        {competitorQuote && (\n          <div className=\"flex items-center text-sm text-gray-700\">\n            <span className=\"font-semibold w-40\">Other Vendor Quote:</span>\n            <span className=\"bg-gray-100 text-gray-800 px-3 py-1 rounded-full text-sm\">\n              USD ${competitorQuote}\n            </span>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default function SecurityCalculator() {\n  const [assessmentType, setAssessmentType] = useState(\"\");\n  const [target, setTarget] = useState(\"\");\n  const [userRole, setUserRole] = useState(\"\");\n  const [scope, setScope] = useState(\"\");\n  const [competitorQuote, setCompetitorQuote] = useState(\"\");\n\n  const targets = useMemo(\n    () => [...new Set(securityServicesData.map((item) => item.target))],\n    []\n  );\n\n  const userRoles = useMemo(() => {\n    if (!target) return [];\n    return [\n      ...new Set(\n        securityServicesData\n          .filter((item) => item.target === target)\n          .map((item) => item.userRole)\n      ),\n    ];\n  }, [target]);\n\n  const scopes = useMemo(() => {\n    if (!target || !userRole) return [];\n    return [\n      ...new Set(\n        securityServicesData\n          .filter(\n            (item) => item.target === target && item.userRole === userRole\n          )\n          .map((item) => item.pages || item.endpoints || item.screens || item.scope)\n      ),\n    ].filter(Boolean);\n  }, [target, userRole]);\n\n  const getUserRoleLabel = () => {\n    switch (target) {\n      case \"Web Application\":\n      case \"API Security\":\n        return \"User Roles\";\n      case \"Mobile Application\":\n        return \"Mobile Suite\";\n      case \"Network Infrastructure\":\n        return \"Network Scope\";\n      case \"Cloud Security\":\n        return \"Cloud Environment\";\n      default:\n        return \"User Role / Scope\";\n    }\n  };\n\n  const getScopeLabel = () => {\n    switch (target) {\n      case \"Web Application\":\n        return \"Pages\";\n      case \"API Security\":\n        return \"Endpoints\";\n      case \"Mobile Application\":\n        return \"Screens\";\n      case \"Network Infrastructure\":\n        return \"IP Addresses\";\n      case \"Cloud Security\":\n        return \"Resources\";\n      default:\n        return \"Scope\";\n    }\n  };\n\n  const result = useMemo(() => {\n    if (!assessmentType || !target || !userRole) return null;\n\n    let item;\n    if ([\"Web Application\", \"API Security\", \"Mobile Application\"].includes(target)) {\n      if (!scope) return null;\n      item = securityServicesData.find(\n        (item) =>\n          item.target === target &&\n          item.userRole === userRole &&\n          (item.pages === scope ||\n            item.endpoints === scope ||\n            item.screens === scope ||\n            item.scope === scope)\n      );\n    } else {\n      item = securityServicesData.find(\n        (item) => item.target === target && item.userRole === userRole\n      );\n    }\n\n    if (!item) return null;\n    const cost = assessmentType === \"Comprehensive Assessment\" ? item.comprehensive : item.standard;\n    const savings =\n      competitorQuote && competitorQuote !== \"\"\n        ? parseFloat(competitorQuote) - cost\n        : 0;\n    const percentage =\n      savings && savings !== 0\n        ? ((savings / parseFloat(competitorQuote)) * 100).toFixed(1)\n        : 0;\n    return { hours: item.hours, cost, savings, percentage };\n  }, [assessmentType, target, userRole, scope, competitorQuote]);\n\n  return (\n    <div className=\"w-full max-w-8xl mx-auto p-4 lg:p-8\">\n      {/* Header Section */}\n      <div className=\"text-center mb-8\">\n        <h2 className=\"text-3xl lg:text-4xl font-bold text-[var(--color-dark-blue)] mb-4\">\n          Security Assessment Calculator\n        </h2>\n        <p className=\"text-lg text-gray-600 max-w-3xl mx-auto leading-relaxed\">\n          Configure your security assessment requirements to get an instant pricing estimate.\n          Our transparent pricing helps you plan your cybersecurity investments effectively.\n        </p>\n      </div>\n\n      {/* Main Calculator Grid */}\n      <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-6\">\n        {/* Left Panel - Configuration */}\n        <div className=\"lg:col-span-1 space-y-6\">\n          <div className=\"bg-gradient-to-br from-[var(--color-dark-blue)] to-[var(--color-blue)] rounded-2xl p-6 text-white\">\n            <div className=\"flex items-center gap-3 mb-4\">\n              <div className=\"w-10 h-10 bg-[var(--color-yellow)] rounded-lg flex items-center justify-center\">\n                <svg className=\"w-5 h-5 text-[var(--color-dark-blue)]\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z\" />\n                </svg>\n              </div>\n              <h3 className=\"text-xl font-bold\">Configure Your Assessment</h3>\n            </div>\n            <p className=\"text-white/90 leading-relaxed\">\n              Select your requirements below to receive an accurate pricing estimate for your cybersecurity assessment.\n            </p>\n          </div>\n\n          <div className=\"bg-white rounded-2xl p-6 shadow-lg border border-gray-100 space-y-6\">\n            <StyledDropdown\n              label=\"Assessment Type\"\n              value={assessmentType}\n              onChange={(e) => {\n                setAssessmentType(e.target.value);\n                setTarget(\"\");\n                setUserRole(\"\");\n                setScope(\"\");\n                setCompetitorQuote(\"\");\n              }}\n              options={[\"Grey Box\", \"Black Box\"]}\n              placeholder=\"Choose Type\"\n            />\n\n            <StyledDropdown\n              label=\"Target Security Testing\"\n              value={target}\n              onChange={(e) => {\n                setTarget(e.target.value);\n                setUserRole(\"\");\n                setScope(\"\");\n                setCompetitorQuote(\"\");\n              }}\n              options={targets}\n              placeholder=\"Choose Target\"\n            />\n\n            {target && (\n              <StyledDropdown\n                label={getUserRoleLabel()}\n                value={userRole}\n                onChange={(e) => {\n                  setUserRole(e.target.value);\n                  setScope(\"\");\n                  setCompetitorQuote(\"\");\n                }}\n                options={userRoles}\n                placeholder={`Select ${getUserRoleLabel()}`}\n              />\n            )}\n          </div>\n        </div>\n\n        {/* Right Panel - Results and Details */}\n        <div className=\"lg:col-span-2 space-y-6\">\n          {target && (\n            <TargetDescription\n              target={target}\n              assessmentType={assessmentType}\n              userRole={userRole}\n              scope={scope}\n              competitorQuote={competitorQuote}\n              result={result}\n            />\n          )}\n\n          {userRole &&\n            [\"Web Application\", \"API Security\", \"Mobile Application\"].includes(target) &&\n            scopes.length > 0 && (\n              <StyledDropdown\n                label={getScopeLabel()}\n                value={scope}\n                onChange={(e) => {\n                  setScope(e.target.value);\n                  setCompetitorQuote(\"\");\n                }}\n                options={scopes}\n                placeholder={`Select ${getScopeLabel()}`}\n              />\n            )}\n\n          <div className=\"bg-gray-50 rounded-2xl p-6 border border-gray-200\">\n            <label className=\"block mb-3 font-semibold text-[var(--color-dark-blue)] text-sm uppercase tracking-wide\">\n              Other Vendor Quote:\n            </label>\n            <input\n              type=\"number\"\n              value={competitorQuote}\n              onChange={(e) => setCompetitorQuote(e.target.value)}\n              className=\"w-full p-4 border-2 border-gray-200 rounded-xl focus:outline-none focus:ring-4 focus:ring-[var(--color-blue)]/20 focus:border-[var(--color-blue)] transition-all duration-300 font-medium\"\n              placeholder=\"Enter other vendor quote in USD\"\n            />\n          </div>\n\n          {/* Pricing Results */}\n          <div className=\"bg-gradient-to-br from-[var(--color-dark-blue)] to-[var(--color-blue)] rounded-2xl p-6 text-white\">\n            <h3 className=\"text-2xl font-bold mb-6\">Investment Summary</h3>\n\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n              <div className=\"bg-white/10 backdrop-blur-sm rounded-xl p-6\">\n                <p className=\"text-white/80 font-medium mb-2\">Estimated Investment</p>\n                <p className=\"text-4xl font-bold text-white\">\n                  ${result ? result.cost.toLocaleString() : '0'}\n                </p>\n                {result && (\n                  <p className=\"text-white/80 text-sm mt-2\">\n                    ~{result.hours} hours of expert assessment\n                  </p>\n                )}\n              </div>\n\n              {result && result.savings > 0 && (\n                <div className=\"bg-[var(--color-yellow)]/20 backdrop-blur-sm rounded-xl p-6\">\n                  <p className=\"text-white/80 font-medium mb-2\">Your Savings</p>\n                  <p className=\"text-4xl font-bold text-[var(--color-yellow)]\">\n                    ${result.savings.toLocaleString()}\n                  </p>\n                  <p className=\"text-white/80 text-sm mt-2\">\n                    {result.percentage}% less than competitor\n                  </p>\n                </div>\n              )}\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Disclaimer */}\n      <div className=\"mt-8 p-6 bg-gray-50 rounded-2xl border border-gray-200\">\n        <p className=\"text-sm text-gray-600 leading-relaxed\">\n          <strong className=\"text-[var(--color-dark-blue)]\">Important:</strong>\n          These estimates are for planning purposes and may vary based on specific requirements, complexity, and timeline.\n          SecurityLit provides customized cybersecurity solutions tailored to your unique environment.\n          For a detailed proposal, please{\" \"}\n          <a href=\"/contact\" className=\"text-[var(--color-blue)] hover:text-[var(--color-blue-secondary)] font-semibold underline\">\n            contact our security experts\n          </a>{\" \"}\n          for a comprehensive consultation and accurate quote.\n        </p>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;;;AAFA;;AAIA,kDAAkD;AAClD,MAAM,uBAAuB;IAC3B,mCAAmC;IACnC;QACE,QAAQ;QACR,UAAU;QACV,OAAO;QACP,OAAO;QACP,eAAe;QACf,UAAU;IACZ;IACA;QACE,QAAQ;QACR,UAAU;QACV,OAAO;QACP,OAAO;QACP,eAAe;QACf,UAAU;IACZ;IACA;QACE,QAAQ;QACR,UAAU;QACV,OAAO;QACP,OAAO;QACP,eAAe;QACf,UAAU;IACZ;IACA;QACE,QAAQ;QACR,UAAU;QACV,OAAO;QACP,OAAO;QACP,eAAe;QACf,UAAU;IACZ;IACA;QACE,QAAQ;QACR,UAAU;QACV,OAAO;QACP,OAAO;QACP,eAAe;QACf,UAAU;IACZ;IACA;QACE,QAAQ;QACR,UAAU;QACV,OAAO;QACP,OAAO;QACP,eAAe;QACf,UAAU;IACZ;IAEA,uBAAuB;IACvB;QACE,QAAQ;QACR,UAAU;QACV,WAAW;QACX,OAAO;QACP,eAAe;QACf,UAAU;IACZ;IACA;QACE,QAAQ;QACR,UAAU;QACV,WAAW;QACX,OAAO;QACP,eAAe;QACf,UAAU;IACZ;IACA;QACE,QAAQ;QACR,UAAU;QACV,WAAW;QACX,OAAO;QACP,eAAe;QACf,UAAU;IACZ;IACA;QACE,QAAQ;QACR,UAAU;QACV,WAAW;QACX,OAAO;QACP,eAAe;QACf,UAAU;IACZ;IACA;QACE,QAAQ;QACR,UAAU;QACV,WAAW;QACX,OAAO;QACP,eAAe;QACf,UAAU;IACZ;IAEA,8BAA8B;IAC9B;QACE,QAAQ;QACR,UAAU;QACV,SAAS;QACT,OAAO;QACP,eAAe;QACf,UAAU;IACZ;IACA;QACE,QAAQ;QACR,UAAU;QACV,SAAS;QACT,OAAO;QACP,eAAe;QACf,UAAU;IACZ;IACA;QACE,QAAQ;QACR,UAAU;QACV,SAAS;QACT,OAAO;QACP,eAAe;QACf,UAAU;IACZ;IACA;QACE,QAAQ;QACR,UAAU;QACV,SAAS;QACT,OAAO;QACP,eAAe;QACf,UAAU;IACZ;IAEA,8BAA8B;IAC9B;QACE,QAAQ;QACR,UAAU;QACV,OAAO;QACP,OAAO;QACP,eAAe;QACf,UAAU;IACZ;IACA;QACE,QAAQ;QACR,UAAU;QACV,OAAO;QACP,OAAO;QACP,eAAe;QACf,UAAU;IACZ;IACA;QACE,QAAQ;QACR,UAAU;QACV,OAAO;QACP,OAAO;QACP,eAAe;QACf,UAAU;IACZ;IACA;QACE,QAAQ;QACR,UAAU;QACV,OAAO;QACP,OAAO;QACP,eAAe;QACf,UAAU;IACZ;IAEA,4BAA4B;IAC5B;QACE,QAAQ;QACR,UAAU;QACV,OAAO;QACP,OAAO;QACP,eAAe;QACf,UAAU;IACZ;IACA;QACE,QAAQ;QACR,UAAU;QACV,OAAO;QACP,OAAO;QACP,eAAe;QACf,UAAU;IACZ;IACA;QACE,QAAQ;QACR,UAAU;QACV,OAAO;QACP,OAAO;QACP,eAAe;QACf,UAAU;IACZ;IACA;QACE,QAAQ;QACR,UAAU;QACV,OAAO;QACP,OAAO;QACP,eAAe;QACf,UAAU;IACZ;CACD;AAED,MAAM,sBAAsB;IAC1B,mBAAmB;IACnB,gBAAgB;IAChB,sBAAsB;IACtB,oBAAoB;IACpB,kBAAkB;AACpB;AAEA,MAAM,mBAAmB;QAAC,EAAE,KAAK,EAAE,KAAK,EAAE,QAAQ,EAAE,OAAO,EAAE,WAAW,EAAE,IAAI,EAAE;yBAC9E,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;oBACZ,sBACC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;sCACZ;;;;;;;;;;;kCAIP,6LAAC;wBAAM,WAAU;kCACd;;;;;;;;;;;;0BAGL,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBACC,OAAO;wBACP,UAAU;wBACV,WAAW,AAAC,oHAIT,OAHC,QACE,8GACA,8FACH;;0CAGH,6LAAC;gCAAO,OAAM;gCAAG,WAAU;0CAAiB;;;;;;4BAC3C,QAAQ,GAAG,CAAC,CAAC,uBACZ,6LAAC;oCAAoB,OAAO;oCAAQ,WAAU;8CAC3C;mCADU;;;;;;;;;;;kCAKjB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAW,AAAC,qFAEhB,OADC,QAAQ,2BAA2B;sCAEnC,cAAA,6LAAC;gCACC,WAAU;gCACV,OAAM;gCACN,SAAQ;gCACR,MAAK;0CAEL,cAAA,6LAAC;oCAAK,UAAS;oCAAU,GAAE;oCAAqH,UAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KA1C/J;AAkDN,MAAM,iBAAiB;QAAC,EAAE,KAAK,EAAE,KAAK,EAAE,QAAQ,EAAE,OAAO,EAAE,WAAW,EAAE;yBACtE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAM,WAAU;;oBAA6D;oBAAM;;;;;;;0BACpF,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBACC,OAAO;wBACP,UAAU;wBACV,WAAW,AAAC,yGACqE,OAA7E,QAAQ,wCAAwC,8BAA6B;;0CAIjF,6LAAC;gCAAO,OAAM;0CAAI;;;;;;4BACjB,QAAQ,GAAG,CAAC,CAAC,uBACZ,6LAAC;oCAAoB,OAAO;8CACzB;mCADU;;;;;;;;;;;kCAKjB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;;;;;0CACf,6LAAC;gCACC,WAAU;gCACV,OAAM;gCACN,SAAQ;0CAER,cAAA,6LAAC;oCAAK,GAAE;;;;;;;;;;;;;;;;;;;;;;;YAIb,uBACC,6LAAC;gBAAI,WAAU;;;;;;;;;;;;;MA/Bf;AAoCN,MAAM,oBAAoB;QAAC,EAAE,MAAM,EAAE,cAAc,EAAE,QAAQ,EAAE,KAAK,EAAE,eAAe,EAAE,MAAM,EAAE;IAC7F,MAAM,sBAAsB;QAC1B,mBAAmB;QACnB,gBAAgB;QAChB,sBAAsB;QACtB,0BAA0B;QAC1B,kBAAkB;QAClB,sBAAsB;IACxB;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;0BAAwC;;;;;;0BACvD,6LAAC;gBAAI,WAAU;0BAAsB,mBAAmB,CAAC,OAAO;;;;;;0BAEhE,6LAAC;gBAAI,WAAU;;oBACZ,gCACC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAK,WAAU;0CAAqB;;;;;;0CACrC,6LAAC;gCAAK,WAAU;0CACb;;;;;;;;;;;;oBAKN,0BACC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAK,WAAU;0CAAqB;;;;;;0CACrC,6LAAC;gCAAK,WAAU;0CACb;;;;;;;;;;;;oBAKN,uBACC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAK,WAAU;0CAAqB;;;;;;0CACrC,6LAAC;gCAAK,WAAU;0CACb;;;;;;;;;;;;oBAKN,iCACC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAK,WAAU;0CAAqB;;;;;;0CACrC,6LAAC;gCAAK,WAAU;;oCAA2D;oCACnE;;;;;;;;;;;;;;;;;;;;;;;;;AAOpB;MAtDM;AAwDS,SAAS;;IACtB,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvD,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;+CACpB,IAAM;mBAAI,IAAI,IAAI,qBAAqB,GAAG;2DAAC,CAAC,OAAS,KAAK,MAAM;;aAAG;8CACnE,EAAE;IAGJ,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;iDAAE;YACxB,IAAI,CAAC,QAAQ,OAAO,EAAE;YACtB,OAAO;mBACF,IAAI,IACL,qBACG,MAAM;6DAAC,CAAC,OAAS,KAAK,MAAM,KAAK;4DACjC,GAAG;6DAAC,CAAC,OAAS,KAAK,QAAQ;;aAEjC;QACH;gDAAG;QAAC;KAAO;IAEX,MAAM,SAAS,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;8CAAE;YACrB,IAAI,CAAC,UAAU,CAAC,UAAU,OAAO,EAAE;YACnC,OAAO;mBACF,IAAI,IACL,qBACG,MAAM;0DACL,CAAC,OAAS,KAAK,MAAM,KAAK,UAAU,KAAK,QAAQ,KAAK;yDAEvD,GAAG;0DAAC,CAAC,OAAS,KAAK,KAAK,IAAI,KAAK,SAAS,IAAI,KAAK,OAAO,IAAI,KAAK,KAAK;;aAE9E,CAAC,MAAM,CAAC;QACX;6CAAG;QAAC;QAAQ;KAAS;IAErB,MAAM,mBAAmB;QACvB,OAAQ;YACN,KAAK;YACL,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,gBAAgB;QACpB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,SAAS,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;8CAAE;YACrB,IAAI,CAAC,kBAAkB,CAAC,UAAU,CAAC,UAAU,OAAO;YAEpD,IAAI;YACJ,IAAI;gBAAC;gBAAmB;gBAAgB;aAAqB,CAAC,QAAQ,CAAC,SAAS;gBAC9E,IAAI,CAAC,OAAO,OAAO;gBACnB,OAAO,qBAAqB,IAAI;0DAC9B,CAAC,OACC,KAAK,MAAM,KAAK,UAChB,KAAK,QAAQ,KAAK,YAClB,CAAC,KAAK,KAAK,KAAK,SACd,KAAK,SAAS,KAAK,SACnB,KAAK,OAAO,KAAK,SACjB,KAAK,KAAK,KAAK,KAAK;;YAE5B,OAAO;gBACL,OAAO,qBAAqB,IAAI;0DAC9B,CAAC,OAAS,KAAK,MAAM,KAAK,UAAU,KAAK,QAAQ,KAAK;;YAE1D;YAEA,IAAI,CAAC,MAAM,OAAO;YAClB,MAAM,OAAO,mBAAmB,6BAA6B,KAAK,aAAa,GAAG,KAAK,QAAQ;YAC/F,MAAM,UACJ,mBAAmB,oBAAoB,KACnC,WAAW,mBAAmB,OAC9B;YACN,MAAM,aACJ,WAAW,YAAY,IACnB,CAAC,AAAC,UAAU,WAAW,mBAAoB,GAAG,EAAE,OAAO,CAAC,KACxD;YACN,OAAO;gBAAE,OAAO,KAAK,KAAK;gBAAE;gBAAM;gBAAS;YAAW;QACxD;6CAAG;QAAC;QAAgB;QAAQ;QAAU;QAAO;KAAgB;IAE7D,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAoE;;;;;;kCAGlF,6LAAC;wBAAE,WAAU;kCAA0D;;;;;;;;;;;;0BAOzE,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;oDAAwC,MAAK;oDAAO,QAAO;oDAAe,SAAQ;8DAC/F,cAAA,6LAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAa;wDAAG,GAAE;;;;;;;;;;;;;;;;0DAGzE,6LAAC;gDAAG,WAAU;0DAAoB;;;;;;;;;;;;kDAEpC,6LAAC;wCAAE,WAAU;kDAAgC;;;;;;;;;;;;0CAK/C,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,OAAM;wCACN,OAAO;wCACP,UAAU,CAAC;4CACT,kBAAkB,EAAE,MAAM,CAAC,KAAK;4CAChC,UAAU;4CACV,YAAY;4CACZ,SAAS;4CACT,mBAAmB;wCACrB;wCACA,SAAS;4CAAC;4CAAY;yCAAY;wCAClC,aAAY;;;;;;kDAGd,6LAAC;wCACC,OAAM;wCACN,OAAO;wCACP,UAAU,CAAC;4CACT,UAAU,EAAE,MAAM,CAAC,KAAK;4CACxB,YAAY;4CACZ,SAAS;4CACT,mBAAmB;wCACrB;wCACA,SAAS;wCACT,aAAY;;;;;;oCAGb,wBACC,6LAAC;wCACC,OAAO;wCACP,OAAO;wCACP,UAAU,CAAC;4CACT,YAAY,EAAE,MAAM,CAAC,KAAK;4CAC1B,SAAS;4CACT,mBAAmB;wCACrB;wCACA,SAAS;wCACT,aAAa,AAAC,UAA4B,OAAnB;;;;;;;;;;;;;;;;;;kCAO/B,6LAAC;wBAAI,WAAU;;4BACZ,wBACC,6LAAC;gCACC,QAAQ;gCACR,gBAAgB;gCAChB,UAAU;gCACV,OAAO;gCACP,iBAAiB;gCACjB,QAAQ;;;;;;4BAIX,YACC;gCAAC;gCAAmB;gCAAgB;6BAAqB,CAAC,QAAQ,CAAC,WACnE,OAAO,MAAM,GAAG,mBACd,6LAAC;gCACC,OAAO;gCACP,OAAO;gCACP,UAAU,CAAC;oCACT,SAAS,EAAE,MAAM,CAAC,KAAK;oCACvB,mBAAmB;gCACrB;gCACA,SAAS;gCACT,aAAa,AAAC,UAAyB,OAAhB;;;;;;0CAI7B,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAM,WAAU;kDAAyF;;;;;;kDAG1G,6LAAC;wCACC,MAAK;wCACL,OAAO;wCACP,UAAU,CAAC,IAAM,mBAAmB,EAAE,MAAM,CAAC,KAAK;wCAClD,WAAU;wCACV,aAAY;;;;;;;;;;;;0CAKhB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAA0B;;;;;;kDAExC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,WAAU;kEAAiC;;;;;;kEAC9C,6LAAC;wDAAE,WAAU;;4DAAgC;4DACzC,SAAS,OAAO,IAAI,CAAC,cAAc,KAAK;;;;;;;oDAE3C,wBACC,6LAAC;wDAAE,WAAU;;4DAA6B;4DACtC,OAAO,KAAK;4DAAC;;;;;;;;;;;;;4CAKpB,UAAU,OAAO,OAAO,GAAG,mBAC1B,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,WAAU;kEAAiC;;;;;;kEAC9C,6LAAC;wDAAE,WAAU;;4DAAgD;4DACzD,OAAO,OAAO,CAAC,cAAc;;;;;;;kEAEjC,6LAAC;wDAAE,WAAU;;4DACV,OAAO,UAAU;4DAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUjC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAE,WAAU;;sCACX,6LAAC;4BAAO,WAAU;sCAAgC;;;;;;wBAAmB;wBAGrC;sCAChC,6LAAC;4BAAE,MAAK;4BAAW,WAAU;sCAA4F;;;;;;wBAEpH;wBAAI;;;;;;;;;;;;;;;;;;AAMnB;GA3QwB;MAAA", "debugId": null}}, {"offset": {"line": 1088, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/src/app/common/components/BreadcrumbNavigation.jsx"], "sourcesContent": ["\"use client\";\r\nimport React, { useState } from 'react';\r\nimport Link from 'next/link';\r\nimport { ChevronRight, Home, FileText, Users, Settings, MapPin, Shield, Code, Smartphone, Globe, DollarSign, Building, Phone, Download } from 'lucide-react';\r\nimport { motion } from 'framer-motion';\r\n\r\n/**\r\n * Truncated Text Component for Mobile Breadcrumbs\r\n * Shows truncated text on mobile with click to expand functionality\r\n */\r\nconst TruncatedText = ({ text, maxLength = 25, onExpansionChange }) => {\r\n  const [isExpanded, setIsExpanded] = useState(false);\r\n\r\n  const handleToggle = () => {\r\n    const newExpanded = !isExpanded;\r\n    setIsExpanded(newExpanded);\r\n    if (onExpansionChange) {\r\n      onExpansionChange(newExpanded);\r\n    }\r\n  };\r\n\r\n  if (text.length <= maxLength) {\r\n    return <span>{text}</span>;\r\n  }\r\n\r\n  return (\r\n    <span\r\n      className=\"cursor-pointer\"\r\n      onClick={handleToggle}\r\n      title={isExpanded ? \"Click to collapse\" : \"Click to expand\"}\r\n    >\r\n      <span className=\"sm:hidden\">\r\n        {isExpanded ? text : `${text.substring(0, maxLength)}...`}\r\n      </span>\r\n      <span className=\"hidden sm:inline\">\r\n        {text}\r\n      </span>\r\n    </span>\r\n  );\r\n};\r\n\r\n/**\r\n * Structured Data Component for SEO\r\n */\r\nconst BreadcrumbStructuredData = ({ items }) => {\r\n  const structuredData = {\r\n    \"@context\": \"https://schema.org\",\r\n    \"@type\": \"BreadcrumbList\",\r\n    \"itemListElement\": items.map((item, index) => ({\r\n      \"@type\": \"ListItem\",\r\n      \"position\": index + 1,\r\n      \"name\": item.name,\r\n      \"item\": item.current ? undefined : `https://securitylit.com${item.url}`\r\n    }))\r\n  };\r\n\r\n  return (\r\n    <script\r\n      type=\"application/ld+json\"\r\n      dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}\r\n    />\r\n  );\r\n};\r\n\r\n/**\r\n * Universal Breadcrumb Navigation Component with SEO structured data\r\n * Provides clear navigation hierarchy and improves SEO across all pages\r\n */\r\nconst BreadcrumbNavigation = ({ items, className = \"\" }) => {\r\n  const [hasExpandedText, setHasExpandedText] = useState(false);\r\n\r\n  // Check if this is a blog page for conditional padding\r\n  const isBlogPage = items?.some(item =>\r\n    item.url?.includes('/Blogs') ||\r\n    item.iconKey === 'blogs' ||\r\n    className?.includes('blog')\r\n  );\r\n\r\n  return (\r\n    <>\r\n      {/* Structured Data for SEO */}\r\n      <BreadcrumbStructuredData items={items} />\r\n      \r\n      {/* Simple Breadcrumb Navigation */}\r\n      <nav\r\n        className={`bg-transparent py-2 sm:py-3 ${\r\n          isBlogPage\r\n            ? hasExpandedText\r\n              ? 'pb-6 sm:pb-4'\r\n              : 'pb-3 sm:pb-4'\r\n            : 'pb-2 sm:pb-3'\r\n        } ${className}`}\r\n        aria-label=\"Breadcrumb navigation\"\r\n      >\r\n        <div className=\"max-w-7xl mx-auto px-4\">\r\n          <motion.ol\r\n            className={`flex items-center space-x-1 text-xs sm:text-sm ${\r\n              className?.includes('text-white')\r\n                ? 'text-white'\r\n                : className?.includes('text-blue-600')\r\n                ? 'text-blue-600'\r\n                : 'text-gray-600'\r\n            }`}\r\n            initial={{ opacity: 0 }}\r\n            animate={{ opacity: 1 }}\r\n            transition={{ duration: 0.4 }}\r\n          >\r\n            {items.map((item, index) => (\r\n              <motion.li\r\n                key={index}\r\n                className=\"flex items-center\"\r\n                initial={{ opacity: 0 }}\r\n                animate={{ opacity: 1 }}\r\n                transition={{ duration: 0.3, delay: index * 0.1 }}\r\n              >\r\n                {index > 0 && (\r\n                  <ChevronRight\r\n                    className={`w-3 h-3 mx-1 ${\r\n                      className?.includes('text-white')\r\n                        ? 'text-white/70'\r\n                        : className?.includes('text-blue-600')\r\n                        ? 'text-blue-400'\r\n                        : 'text-gray-400'\r\n                    }`}\r\n                    aria-hidden=\"true\"\r\n                  />\r\n                )}\r\n\r\n                {item.current ? (\r\n                  <span\r\n                    className={`font-medium flex items-center gap-1 ${\r\n                      className?.includes('text-white')\r\n                        ? 'text-white'\r\n                        : className?.includes('text-blue-600')\r\n                        ? 'text-blue-600'\r\n                        : 'text-gray-900'\r\n                    }`}\r\n                    aria-current=\"page\"\r\n                    title={item.description}\r\n                  >\r\n                    {item.iconKey === 'home' && (\r\n                      <Home className={`w-3 h-3 ${\r\n                        className?.includes('text-white')\r\n                          ? 'text-white'\r\n                          : className?.includes('text-blue-600')\r\n                          ? 'text-blue-600'\r\n                          : 'text-blue-600'\r\n                      }`} />\r\n                    )}\r\n                    {item.iconKey === 'blogs' && (\r\n                      <FileText className={`w-3 h-3 ${\r\n                        className?.includes('text-white')\r\n                          ? 'text-white'\r\n                          : className?.includes('text-blue-600')\r\n                          ? 'text-blue-600'\r\n                          : 'text-blue-600'\r\n                      }`} />\r\n                    )}\r\n                    {item.iconKey === 'contact-us' && (\r\n                      <Phone className={`w-3 h-3 ${\r\n                        className?.includes('text-white')\r\n                          ? 'text-white'\r\n                          : className?.includes('text-blue-600')\r\n                          ? 'text-blue-600'\r\n                          : 'text-blue-600'\r\n                      }`} />\r\n                    )}\r\n                    <TruncatedText\r\n                      text={item.name}\r\n                      onExpansionChange={setHasExpandedText}\r\n                    />\r\n                  </span>\r\n                ) : (\r\n                  <Link\r\n                    href={item.url}\r\n                    className={`transition-colors duration-200 flex items-center gap-1 hover:underline ${\r\n                      className?.includes('text-white')\r\n                        ? 'text-white hover:text-white/80'\r\n                        : className?.includes('text-blue-600')\r\n                        ? 'text-blue-600 hover:text-blue-800'\r\n                        : 'text-blue-600 hover:text-blue-800'\r\n                    }`}\r\n                    title={item.description}\r\n                  >\r\n                    {item.iconKey === 'home' && (\r\n                      <Home className={`w-3 h-3 ${\r\n                        className?.includes('text-white')\r\n                          ? 'text-white'\r\n                          : className?.includes('text-blue-600')\r\n                          ? 'text-blue-600'\r\n                          : 'text-current'\r\n                      }`} />\r\n                    )}\r\n                    {item.iconKey === 'blogs' && (\r\n                      <FileText className={`w-3 h-3 ${\r\n                        className?.includes('text-white')\r\n                          ? 'text-white'\r\n                          : className?.includes('text-blue-600')\r\n                          ? 'text-blue-600'\r\n                          : 'text-current'\r\n                      }`} />\r\n                    )}\r\n                    {item.iconKey === 'contact-us' && (\r\n                      <Phone className={`w-3 h-3 ${\r\n                        className?.includes('text-white')\r\n                          ? 'text-white'\r\n                          : className?.includes('text-blue-600')\r\n                          ? 'text-blue-600'\r\n                          : 'text-current'\r\n                      }`} />\r\n                    )}\r\n                    <TruncatedText\r\n                      text={item.name}\r\n                      onExpansionChange={setHasExpandedText}\r\n                    />\r\n                  </Link>\r\n                )}\r\n              </motion.li>\r\n            ))}\r\n          </motion.ol>\r\n        </div>\r\n      </nav>\r\n    </>\r\n  );\r\n};\r\n\r\n// Client-side helper function for generating breadcrumb items\r\nexport const generateBreadcrumbs = (pageType, params = {}) => {\r\n  const baseBreadcrumb = {\r\n    name: \"Home\",\r\n    url: \"/\",\r\n    iconKey: \"home\",\r\n    description: \"Return to homepage\"\r\n  };\r\n\r\n  const breadcrumbConfigs = {\r\n    blog: (slug) => [\r\n      baseBreadcrumb,\r\n      {\r\n        name: \"Blog\",\r\n        url: \"/Blogs\",\r\n        iconKey: \"blogs\",\r\n        description: \"View all blog posts\"\r\n      },\r\n      {\r\n        name: params.title || \"Blog Post\",\r\n        url: `/Blogs/${slug}`,\r\n        current: true,\r\n        description: `${params.title || 'Blog post'} - Cybersecurity insights and penetration testing guidance`\r\n      }\r\n    ],\r\n    \r\n    service: (service) => [\r\n      baseBreadcrumb,\r\n      {\r\n        name: \"Services\",\r\n        url: \"/Services\",\r\n        iconKey: \"services\",\r\n        description: \"View all cybersecurity services\"\r\n      },\r\n      {\r\n        name: params.title || service,\r\n        url: `/Services/${service}`,\r\n        current: true,\r\n        iconKey: service,\r\n        description: `${params.title || service} penetration testing services`\r\n      }\r\n    ],\r\n\r\n    location: (country) => [\r\n      baseBreadcrumb,\r\n      {\r\n        name: \"Locations\",\r\n        url: \"/Locations\",\r\n        iconKey: \"locations\",\r\n        description: \"View all locations\"\r\n      },\r\n      {\r\n        name: params.countryName || country,\r\n        url: `/Locations/${country}`,\r\n        current: true,\r\n        description: `${params.countryName || country} cybersecurity services`\r\n      }\r\n    ],\r\n\r\n    company: (page) => [\r\n      baseBreadcrumb,\r\n      {\r\n        name: \"Company\",\r\n        url: \"/Company\",\r\n        iconKey: \"company\",\r\n        description: \"Learn about SecurityLit\"\r\n      },\r\n      {\r\n        name: params.title || page,\r\n        url: `/Company/${page}`,\r\n        current: true,\r\n        description: params.description || `${page} information`\r\n      }\r\n    ],\r\n\r\n    'company-size': (size) => [\r\n      baseBreadcrumb,\r\n      {\r\n        name: \"Company Size\",\r\n        url: \"/Company-size\",\r\n        iconKey: \"company\",\r\n        description: \"Solutions for different company sizes\"\r\n      },\r\n      {\r\n        name: params.title || size,\r\n        url: `/Company-size/${size}`,\r\n        current: true,\r\n        description: params.description || `${size} cybersecurity solutions`\r\n      }\r\n    ],\r\n\r\n    industry: (industry) => [\r\n      baseBreadcrumb,\r\n      {\r\n        name: \"Industries\",\r\n        url: \"/Industries\",\r\n        iconKey: \"company\",\r\n        description: \"Industry-specific cybersecurity solutions\"\r\n      },\r\n      {\r\n        name: params.title || industry,\r\n        url: `/Industries/${industry}`,\r\n        current: true,\r\n        description: params.description || `${industry} cybersecurity solutions`\r\n      }\r\n    ],\r\n\r\n    product: (product) => [\r\n      baseBreadcrumb,\r\n      {\r\n        name: \"Product\",\r\n        url: \"/Product\",\r\n        iconKey: \"product\",\r\n        description: \"Explore our cybersecurity products\"\r\n      },\r\n      {\r\n        name: params.title || product,\r\n        url: `/Product/${product}`,\r\n        current: true,\r\n        description: params.description || `${product} product information`\r\n      }\r\n    ],\r\n\r\n    simple: (pageName, url) => [\r\n      baseBreadcrumb,\r\n      {\r\n        name: pageName,\r\n        url: url,\r\n        current: true,\r\n        iconKey: params.iconKey,\r\n        description: params.description || `${pageName} page`\r\n      }\r\n    ]\r\n  };\r\n\r\n  return breadcrumbConfigs[pageType] || (() => [baseBreadcrumb]);\r\n};\r\n\r\nexport default BreadcrumbNavigation; "], "names": [], "mappings": ";;;;;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;;;AAJA;;;;;AAMA;;;CAGC,GACD,MAAM,gBAAgB;QAAC,EAAE,IAAI,EAAE,YAAY,EAAE,EAAE,iBAAiB,EAAE;;IAChE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,eAAe;QACnB,MAAM,cAAc,CAAC;QACrB,cAAc;QACd,IAAI,mBAAmB;YACrB,kBAAkB;QACpB;IACF;IAEA,IAAI,KAAK,MAAM,IAAI,WAAW;QAC5B,qBAAO,6LAAC;sBAAM;;;;;;IAChB;IAEA,qBACE,6LAAC;QACC,WAAU;QACV,SAAS;QACT,OAAO,aAAa,sBAAsB;;0BAE1C,6LAAC;gBAAK,WAAU;0BACb,aAAa,OAAO,AAAC,GAA+B,OAA7B,KAAK,SAAS,CAAC,GAAG,YAAW;;;;;;0BAEvD,6LAAC;gBAAK,WAAU;0BACb;;;;;;;;;;;;AAIT;GA7BM;KAAA;AA+BN;;CAEC,GACD,MAAM,2BAA2B;QAAC,EAAE,KAAK,EAAE;IACzC,MAAM,iBAAiB;QACrB,YAAY;QACZ,SAAS;QACT,mBAAmB,MAAM,GAAG,CAAC,CAAC,MAAM,QAAU,CAAC;gBAC7C,SAAS;gBACT,YAAY,QAAQ;gBACpB,QAAQ,KAAK,IAAI;gBACjB,QAAQ,KAAK,OAAO,GAAG,YAAY,AAAC,0BAAkC,OAAT,KAAK,GAAG;YACvE,CAAC;IACH;IAEA,qBACE,6LAAC;QACC,MAAK;QACL,yBAAyB;YAAE,QAAQ,KAAK,SAAS,CAAC;QAAgB;;;;;;AAGxE;MAlBM;AAoBN;;;CAGC,GACD,MAAM,uBAAuB;QAAC,EAAE,KAAK,EAAE,YAAY,EAAE,EAAE;;IACrD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvD,uDAAuD;IACvD,MAAM,aAAa,kBAAA,4BAAA,MAAO,IAAI,CAAC,CAAA;YAC7B;eAAA,EAAA,YAAA,KAAK,GAAG,cAAR,gCAAA,UAAU,QAAQ,CAAC,cACnB,KAAK,OAAO,KAAK,YACjB,sBAAA,gCAAA,UAAW,QAAQ,CAAC;;IAGtB,qBACE;;0BAEE,6LAAC;gBAAyB,OAAO;;;;;;0BAGjC,6LAAC;gBACC,WAAW,AAAC,+BAMR,OALF,aACI,kBACE,iBACA,iBACF,gBACL,KAAa,OAAV;gBACJ,cAAW;0BAEX,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,EAAE;wBACR,WAAW,AAAC,kDAMX,OALC,CAAA,sBAAA,gCAAA,UAAW,QAAQ,CAAC,iBAChB,eACA,CAAA,sBAAA,gCAAA,UAAW,QAAQ,CAAC,oBACpB,kBACA;wBAEN,SAAS;4BAAE,SAAS;wBAAE;wBACtB,SAAS;4BAAE,SAAS;wBAAE;wBACtB,YAAY;4BAAE,UAAU;wBAAI;kCAE3B,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,6LAAC,6LAAA,CAAA,SAAM,CAAC,EAAE;gCAER,WAAU;gCACV,SAAS;oCAAE,SAAS;gCAAE;gCACtB,SAAS;oCAAE,SAAS;gCAAE;gCACtB,YAAY;oCAAE,UAAU;oCAAK,OAAO,QAAQ;gCAAI;;oCAE/C,QAAQ,mBACP,6LAAC,yNAAA,CAAA,eAAY;wCACX,WAAW,AAAC,gBAMX,OALC,CAAA,sBAAA,gCAAA,UAAW,QAAQ,CAAC,iBAChB,kBACA,CAAA,sBAAA,gCAAA,UAAW,QAAQ,CAAC,oBACpB,kBACA;wCAEN,eAAY;;;;;;oCAIf,KAAK,OAAO,iBACX,6LAAC;wCACC,WAAW,AAAC,uCAMX,OALC,CAAA,sBAAA,gCAAA,UAAW,QAAQ,CAAC,iBAChB,eACA,CAAA,sBAAA,gCAAA,UAAW,QAAQ,CAAC,oBACpB,kBACA;wCAEN,gBAAa;wCACb,OAAO,KAAK,WAAW;;4CAEtB,KAAK,OAAO,KAAK,wBAChB,6LAAC,sMAAA,CAAA,OAAI;gDAAC,WAAW,AAAC,WAMjB,OALC,CAAA,sBAAA,gCAAA,UAAW,QAAQ,CAAC,iBAChB,eACA,CAAA,sBAAA,gCAAA,UAAW,QAAQ,CAAC,oBACpB,kBACA;;;;;;4CAGP,KAAK,OAAO,KAAK,yBAChB,6LAAC,iNAAA,CAAA,WAAQ;gDAAC,WAAW,AAAC,WAMrB,OALC,CAAA,sBAAA,gCAAA,UAAW,QAAQ,CAAC,iBAChB,eACA,CAAA,sBAAA,gCAAA,UAAW,QAAQ,CAAC,oBACpB,kBACA;;;;;;4CAGP,KAAK,OAAO,KAAK,8BAChB,6LAAC,uMAAA,CAAA,QAAK;gDAAC,WAAW,AAAC,WAMlB,OALC,CAAA,sBAAA,gCAAA,UAAW,QAAQ,CAAC,iBAChB,eACA,CAAA,sBAAA,gCAAA,UAAW,QAAQ,CAAC,oBACpB,kBACA;;;;;;0DAGR,6LAAC;gDACC,MAAM,KAAK,IAAI;gDACf,mBAAmB;;;;;;;;;;;iGAIvB,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAM,KAAK,GAAG;wCACd,WAAW,AAAC,0EAMX,OALC,CAAA,sBAAA,gCAAA,UAAW,QAAQ,CAAC,iBAChB,mCACA,CAAA,sBAAA,gCAAA,UAAW,QAAQ,CAAC,oBACpB,sCACA;wCAEN,OAAO,KAAK,WAAW;;4CAEtB,KAAK,OAAO,KAAK,wBAChB,6LAAC,sMAAA,CAAA,OAAI;gDAAC,WAAW,AAAC,WAMjB,OALC,CAAA,sBAAA,gCAAA,UAAW,QAAQ,CAAC,iBAChB,eACA,CAAA,sBAAA,gCAAA,UAAW,QAAQ,CAAC,oBACpB,kBACA;;;;;;4CAGP,KAAK,OAAO,KAAK,yBAChB,6LAAC,iNAAA,CAAA,WAAQ;gDAAC,WAAW,AAAC,WAMrB,OALC,CAAA,sBAAA,gCAAA,UAAW,QAAQ,CAAC,iBAChB,eACA,CAAA,sBAAA,gCAAA,UAAW,QAAQ,CAAC,oBACpB,kBACA;;;;;;4CAGP,KAAK,OAAO,KAAK,8BAChB,6LAAC,uMAAA,CAAA,QAAK;gDAAC,WAAW,AAAC,WAMlB,OALC,CAAA,sBAAA,gCAAA,UAAW,QAAQ,CAAC,iBAChB,eACA,CAAA,sBAAA,gCAAA,UAAW,QAAQ,CAAC,oBACpB,kBACA;;;;;;0DAGR,6LAAC;gDACC,MAAM,KAAK,IAAI;gDACf,mBAAmB;;;;;;;;;;;;;+BAxGpB;;;;;;;;;;;;;;;;;;;;;;AAmHrB;IA5JM;MAAA;AA+JC,MAAM,sBAAsB,SAAC;QAAU,0EAAS,CAAC;IACtD,MAAM,iBAAiB;QACrB,MAAM;QACN,KAAK;QACL,SAAS;QACT,aAAa;IACf;IAEA,MAAM,oBAAoB;QACxB,MAAM,CAAC,OAAS;gBACd;gBACA;oBACE,MAAM;oBACN,KAAK;oBACL,SAAS;oBACT,aAAa;gBACf;gBACA;oBACE,MAAM,OAAO,KAAK,IAAI;oBACtB,KAAK,AAAC,UAAc,OAAL;oBACf,SAAS;oBACT,aAAa,AAAC,GAA8B,OAA5B,OAAO,KAAK,IAAI,aAAY;gBAC9C;aACD;QAED,SAAS,CAAC,UAAY;gBACpB;gBACA;oBACE,MAAM;oBACN,KAAK;oBACL,SAAS;oBACT,aAAa;gBACf;gBACA;oBACE,MAAM,OAAO,KAAK,IAAI;oBACtB,KAAK,AAAC,aAAoB,OAAR;oBAClB,SAAS;oBACT,SAAS;oBACT,aAAa,AAAC,GAA0B,OAAxB,OAAO,KAAK,IAAI,SAAQ;gBAC1C;aACD;QAED,UAAU,CAAC,UAAY;gBACrB;gBACA;oBACE,MAAM;oBACN,KAAK;oBACL,SAAS;oBACT,aAAa;gBACf;gBACA;oBACE,MAAM,OAAO,WAAW,IAAI;oBAC5B,KAAK,AAAC,cAAqB,OAAR;oBACnB,SAAS;oBACT,aAAa,AAAC,GAAgC,OAA9B,OAAO,WAAW,IAAI,SAAQ;gBAChD;aACD;QAED,SAAS,CAAC,OAAS;gBACjB;gBACA;oBACE,MAAM;oBACN,KAAK;oBACL,SAAS;oBACT,aAAa;gBACf;gBACA;oBACE,MAAM,OAAO,KAAK,IAAI;oBACtB,KAAK,AAAC,YAAgB,OAAL;oBACjB,SAAS;oBACT,aAAa,OAAO,WAAW,IAAI,AAAC,GAAO,OAAL,MAAK;gBAC7C;aACD;QAED,gBAAgB,CAAC,OAAS;gBACxB;gBACA;oBACE,MAAM;oBACN,KAAK;oBACL,SAAS;oBACT,aAAa;gBACf;gBACA;oBACE,MAAM,OAAO,KAAK,IAAI;oBACtB,KAAK,AAAC,iBAAqB,OAAL;oBACtB,SAAS;oBACT,aAAa,OAAO,WAAW,IAAI,AAAC,GAAO,OAAL,MAAK;gBAC7C;aACD;QAED,UAAU,CAAC,WAAa;gBACtB;gBACA;oBACE,MAAM;oBACN,KAAK;oBACL,SAAS;oBACT,aAAa;gBACf;gBACA;oBACE,MAAM,OAAO,KAAK,IAAI;oBACtB,KAAK,AAAC,eAAuB,OAAT;oBACpB,SAAS;oBACT,aAAa,OAAO,WAAW,IAAI,AAAC,GAAW,OAAT,UAAS;gBACjD;aACD;QAED,SAAS,CAAC,UAAY;gBACpB;gBACA;oBACE,MAAM;oBACN,KAAK;oBACL,SAAS;oBACT,aAAa;gBACf;gBACA;oBACE,MAAM,OAAO,KAAK,IAAI;oBACtB,KAAK,AAAC,YAAmB,OAAR;oBACjB,SAAS;oBACT,aAAa,OAAO,WAAW,IAAI,AAAC,GAAU,OAAR,SAAQ;gBAChD;aACD;QAED,QAAQ,CAAC,UAAU,MAAQ;gBACzB;gBACA;oBACE,MAAM;oBACN,KAAK;oBACL,SAAS;oBACT,SAAS,OAAO,OAAO;oBACvB,aAAa,OAAO,WAAW,IAAI,AAAC,GAAW,OAAT,UAAS;gBACjD;aACD;IACH;IAEA,OAAO,iBAAiB,CAAC,SAAS,IAAI,CAAC,IAAM;YAAC;SAAe;AAC/D;uCAEe", "debugId": null}}, {"offset": {"line": 1493, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/src/app/pricing/components/PricingCalculatorSection.jsx"], "sourcesContent": ["\"use client\";\n\nimport React from 'react';\nimport SecurityCalculator from './SecurityCalculator';\nimport BreadcrumbNavigation from \"../../common/components/BreadcrumbNavigation\";\n\nexport default function PricingCalculatorSection() {\n  const breadcrumbItems = [\n    {\n      name: \"Home\",\n      url: \"/\",\n      iconKey: \"home\",\n      description: \"Return to homepage\"\n    },\n    {\n      name: \"Pricing\",\n      url: \"/pricing\",\n      current: true,\n      iconKey: \"pricing\",\n      description: \"Cybersecurity services pricing calculator and cost estimation\"\n    }\n  ];\n\n  return (\n    <div className=\"relative bg-gradient-to-br from-[var(--bg-light-blue)] to-white py-16 lg:py-24\">\n      {/* Background Pattern */}\n      <div className=\"absolute inset-0 bg-cover bg-center bg-no-repeat opacity-5\"\n           style={{ backgroundImage: 'url(/images/hexagon.svg)' }}>\n      </div>\n      \n      <div className=\"relative z-10 container mx-auto px-4 max-w-7xl\">\n        {/* Breadcrumb Navigation */}\n        <div className=\"mb-8\">\n          <BreadcrumbNavigation items={breadcrumbItems} />\n        </div>\n\n        {/* Header Section */}\n        <div className=\"text-center mb-16\">\n          <h1 className=\"text-4xl sm:text-5xl lg:text-6xl font-bold text-[var(--color-dark-blue)] mb-6 leading-tight\">\n            Transparent Cybersecurity\n            <span className=\"text-[var(--color-blue)]\"> Pricing</span>\n          </h1>\n          <p className=\"text-xl lg:text-2xl text-gray-600 max-w-4xl mx-auto mb-8 leading-relaxed\">\n            Get instant pricing estimates for our enterprise-grade cybersecurity services. \n            Compare costs and see how SecurityLit delivers exceptional value for your security investments.\n          </p>\n          \n          {/* Key Benefits */}\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto mt-12\">\n            <div className=\"bg-white/80 backdrop-blur-sm rounded-xl p-6 border border-[var(--color-blue)]/20\">\n              <div className=\"w-12 h-12 bg-[var(--color-blue)] rounded-lg flex items-center justify-center mx-auto mb-4\">\n                <svg className=\"w-6 h-6 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                </svg>\n              </div>\n              <h3 className=\"text-lg font-semibold text-[var(--color-dark-blue)] mb-2\">Transparent Pricing</h3>\n              <p className=\"text-gray-600 text-sm\">No hidden fees or surprise costs. Get clear, upfront pricing for all services.</p>\n            </div>\n            \n            <div className=\"bg-white/80 backdrop-blur-sm rounded-xl p-6 border border-[var(--color-blue)]/20\">\n              <div className=\"w-12 h-12 bg-[var(--color-blue)] rounded-lg flex items-center justify-center mx-auto mb-4\">\n                <svg className=\"w-6 h-6 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M13 10V3L4 14h7v7l9-11h-7z\" />\n                </svg>\n              </div>\n              <h3 className=\"text-lg font-semibold text-[var(--color-dark-blue)] mb-2\">Instant Estimates</h3>\n              <p className=\"text-gray-600 text-sm\">Get immediate cost calculations based on your specific requirements.</p>\n            </div>\n            \n            <div className=\"bg-white/80 backdrop-blur-sm rounded-xl p-6 border border-[var(--color-blue)]/20\">\n              <div className=\"w-12 h-12 bg-[var(--color-blue)] rounded-lg flex items-center justify-center mx-auto mb-4\">\n                <svg className=\"w-6 h-6 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1\" />\n                </svg>\n              </div>\n              <h3 className=\"text-lg font-semibold text-[var(--color-dark-blue)] mb-2\">Competitive Rates</h3>\n              <p className=\"text-gray-600 text-sm\">Compare our pricing with other vendors and see the SecurityLit advantage.</p>\n            </div>\n          </div>\n        </div>\n\n        {/* Calculator Container */}\n        <div className=\"bg-white rounded-3xl shadow-2xl border border-[var(--color-blue)]/10 overflow-hidden\">\n          <div className=\"bg-gradient-to-r from-[var(--color-dark-blue)] to-[var(--color-blue)] p-1\">\n            <div className=\"bg-white rounded-3xl\">\n              <SecurityCalculator />\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAMe,SAAS;IACtB,MAAM,kBAAkB;QACtB;YACE,MAAM;YACN,KAAK;YACL,SAAS;YACT,aAAa;QACf;QACA;YACE,MAAM;YACN,KAAK;YACL,SAAS;YACT,SAAS;YACT,aAAa;QACf;KACD;IAED,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;gBACV,OAAO;oBAAE,iBAAiB;gBAA2B;;;;;;0BAG1D,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,8JAAA,CAAA,UAAoB;4BAAC,OAAO;;;;;;;;;;;kCAI/B,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;;oCAA8F;kDAE1G,6LAAC;wCAAK,WAAU;kDAA2B;;;;;;;;;;;;0CAE7C,6LAAC;gCAAE,WAAU;0CAA2E;;;;;;0CAMxF,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;oDAAqB,MAAK;oDAAO,QAAO;oDAAe,SAAQ;8DAC5E,cAAA,6LAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAa;wDAAG,GAAE;;;;;;;;;;;;;;;;0DAGzE,6LAAC;gDAAG,WAAU;0DAA2D;;;;;;0DACzE,6LAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;kDAGvC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;oDAAqB,MAAK;oDAAO,QAAO;oDAAe,SAAQ;8DAC5E,cAAA,6LAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAa;wDAAG,GAAE;;;;;;;;;;;;;;;;0DAGzE,6LAAC;gDAAG,WAAU;0DAA2D;;;;;;0DACzE,6LAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;kDAGvC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;oDAAqB,MAAK;oDAAO,QAAO;oDAAe,SAAQ;8DAC5E,cAAA,6LAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAa;wDAAG,GAAE;;;;;;;;;;;;;;;;0DAGzE,6LAAC;gDAAG,WAAU;0DAA2D;;;;;;0DACzE,6LAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;;;;;;;;;;;;;kCAM3C,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,6JAAA,CAAA,UAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOjC;KAtFwB", "debugId": null}}, {"offset": {"line": 1802, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/src/app/pricing/components/PricingCTA.jsx"], "sourcesContent": ["\"use client\";\n\nimport React from 'react';\nimport { Shield, Users, Award, Clock, CheckCircle, ArrowRight } from 'lucide-react';\n\nexport default function PricingCTA() {\n  const benefits = [\n    {\n      icon: <Shield className=\"w-6 h-6\" />,\n      title: \"Enterprise-Grade Security\",\n      description: \"CISSP, CEH, and OSCP certified professionals with 10+ years of experience\"\n    },\n    {\n      icon: <Users className=\"w-6 h-6\" />,\n      title: \"Dedicated Security Team\",\n      description: \"Assigned security consultants who understand your business and industry\"\n    },\n    {\n      icon: <Award className=\"w-6 h-6\" />,\n      title: \"Compliance Expertise\",\n      description: \"ISO 27001, SOC 2, PCI DSS, and industry-specific compliance guidance\"\n    },\n    {\n      icon: <Clock className=\"w-6 h-6\" />,\n      title: \"Rapid Response\",\n      description: \"24/7 incident response and emergency security consultation available\"\n    }\n  ];\n\n  const guarantees = [\n    \"Comprehensive security reports with actionable recommendations\",\n    \"Executive summary for leadership and technical details for IT teams\",\n    \"30-day post-assessment support for remediation guidance\",\n    \"Compliance with industry standards and regulatory requirements\",\n    \"Transparent pricing with no hidden fees or surprise costs\"\n  ];\n\n  return (\n    <section className=\"py-20 lg:py-28 bg-gradient-to-br from-[var(--color-dark-blue)] to-[var(--color-blue)] relative overflow-hidden\">\n      {/* Background Pattern */}\n      <div className=\"absolute inset-0 bg-cover bg-center bg-no-repeat opacity-10\"\n           style={{ backgroundImage: 'url(/images/hexagon.svg)' }}>\n      </div>\n      \n      <div className=\"relative z-10 container mx-auto px-4 max-w-7xl\">\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-16 items-center\">\n          {/* Left Column - Main CTA */}\n          <div className=\"text-white\">\n            <h2 className=\"text-4xl lg:text-5xl font-bold mb-6 leading-tight\">\n              Ready to Secure Your\n              <span className=\"text-[var(--color-yellow)]\"> Digital Assets?</span>\n            </h2>\n            \n            <p className=\"text-xl text-white/90 mb-8 leading-relaxed\">\n              Join 200+ enterprises who trust SecurityLit for their cybersecurity needs. \n              Get a personalized consultation and detailed security roadmap for your organization.\n            </p>\n\n            {/* Benefits Grid */}\n            <div className=\"grid grid-cols-1 sm:grid-cols-2 gap-6 mb-10\">\n              {benefits.map((benefit, index) => (\n                <div key={index} className=\"flex items-start gap-4\">\n                  <div className=\"w-12 h-12 bg-[var(--color-yellow)] rounded-lg flex items-center justify-center flex-shrink-0\">\n                    <div className=\"text-[var(--color-dark-blue)]\">\n                      {benefit.icon}\n                    </div>\n                  </div>\n                  <div>\n                    <h3 className=\"font-semibold text-white mb-2\">{benefit.title}</h3>\n                    <p className=\"text-white/80 text-sm leading-relaxed\">{benefit.description}</p>\n                  </div>\n                </div>\n              ))}\n            </div>\n\n            {/* CTA Buttons */}\n            <div className=\"flex flex-col sm:flex-row gap-4\">\n              <a\n                href=\"#pricing-contact\"\n                className=\"bg-[var(--color-yellow)] hover:bg-[var(--color-yellow-hover)] text-[var(--color-dark-blue)] px-8 py-4 rounded-xl font-bold text-lg transition-all duration-300 shadow-lg hover:shadow-xl flex items-center justify-center gap-2 group\"\n              >\n                Get Custom Quote\n                <ArrowRight className=\"w-5 h-5 group-hover:translate-x-1 transition-transform\" />\n              </a>\n              \n              <a\n                href=\"/contact\"\n                className=\"border-2 border-white text-white hover:bg-white hover:text-[var(--color-dark-blue)] px-8 py-4 rounded-xl font-bold text-lg transition-all duration-300 flex items-center justify-center gap-2\"\n              >\n                Schedule Consultation\n              </a>\n            </div>\n          </div>\n\n          {/* Right Column - Guarantees */}\n          <div className=\"bg-white/10 backdrop-blur-sm rounded-3xl p-8 lg:p-10 border border-white/20\">\n            <h3 className=\"text-2xl lg:text-3xl font-bold text-white mb-6\">\n              What You Get with SecurityLit\n            </h3>\n            \n            <div className=\"space-y-4\">\n              {guarantees.map((guarantee, index) => (\n                <div key={index} className=\"flex items-start gap-4\">\n                  <div className=\"w-6 h-6 bg-[var(--color-yellow)] rounded-full flex items-center justify-center flex-shrink-0 mt-1\">\n                    <CheckCircle className=\"w-4 h-4 text-[var(--color-dark-blue)]\" />\n                  </div>\n                  <p className=\"text-white/90 leading-relaxed\">{guarantee}</p>\n                </div>\n              ))}\n            </div>\n\n            {/* Trust Indicators */}\n            <div className=\"mt-8 pt-8 border-t border-white/20\">\n              <div className=\"grid grid-cols-3 gap-6 text-center\">\n                <div>\n                  <div className=\"text-3xl font-bold text-[var(--color-yellow)] mb-1\">200+</div>\n                  <div className=\"text-white/80 text-sm\">Clients Secured</div>\n                </div>\n                <div>\n                  <div className=\"text-3xl font-bold text-[var(--color-yellow)] mb-1\">99.9%</div>\n                  <div className=\"text-white/80 text-sm\">Client Satisfaction</div>\n                </div>\n                <div>\n                  <div className=\"text-3xl font-bold text-[var(--color-yellow)] mb-1\">24/7</div>\n                  <div className=\"text-white/80 text-sm\">Support Available</div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Bottom Section - Industry Recognition */}\n        <div className=\"mt-16 pt-16 border-t border-white/20\">\n          <div className=\"text-center mb-12\">\n            <h3 className=\"text-2xl lg:text-3xl font-bold text-white mb-4\">\n              Trusted by Industry Leaders\n            </h3>\n            <p className=\"text-white/80 text-lg max-w-3xl mx-auto\">\n              SecurityLit has helped organizations across finance, healthcare, technology, and government \n              sectors strengthen their cybersecurity posture and achieve compliance.\n            </p>\n          </div>\n\n          {/* Industry Sectors */}\n          <div className=\"grid grid-cols-2 md:grid-cols-4 gap-6\">\n            {[\n              { name: \"Financial Services\", icon: \"🏦\" },\n              { name: \"Healthcare\", icon: \"🏥\" },\n              { name: \"Technology\", icon: \"💻\" },\n              { name: \"Government\", icon: \"🏛️\" }\n            ].map((sector, index) => (\n              <div key={index} className=\"bg-white/10 backdrop-blur-sm rounded-xl p-6 text-center border border-white/20\">\n                <div className=\"text-4xl mb-3\">{sector.icon}</div>\n                <div className=\"text-white font-semibold\">{sector.name}</div>\n              </div>\n            ))}\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAHA;;;;AAKe,SAAS;IACtB,MAAM,WAAW;QACf;YACE,oBAAM,6LAAC,yMAAA,CAAA,SAAM;gBAAC,WAAU;;;;;;YACxB,OAAO;YACP,aAAa;QACf;QACA;YACE,oBAAM,6LAAC,uMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;YACvB,OAAO;YACP,aAAa;QACf;QACA;YACE,oBAAM,6LAAC,uMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;YACvB,OAAO;YACP,aAAa;QACf;QACA;YACE,oBAAM,6LAAC,uMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;YACvB,OAAO;YACP,aAAa;QACf;KACD;IAED,MAAM,aAAa;QACjB;QACA;QACA;QACA;QACA;KACD;IAED,qBACE,6LAAC;QAAQ,WAAU;;0BAEjB,6LAAC;gBAAI,WAAU;gBACV,OAAO;oBAAE,iBAAiB;gBAA2B;;;;;;0BAG1D,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;;4CAAoD;0DAEhE,6LAAC;gDAAK,WAAU;0DAA6B;;;;;;;;;;;;kDAG/C,6LAAC;wCAAE,WAAU;kDAA6C;;;;;;kDAM1D,6LAAC;wCAAI,WAAU;kDACZ,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,6LAAC;gDAAgB,WAAU;;kEACzB,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAI,WAAU;sEACZ,QAAQ,IAAI;;;;;;;;;;;kEAGjB,6LAAC;;0EACC,6LAAC;gEAAG,WAAU;0EAAiC,QAAQ,KAAK;;;;;;0EAC5D,6LAAC;gEAAE,WAAU;0EAAyC,QAAQ,WAAW;;;;;;;;;;;;;+CARnE;;;;;;;;;;kDAed,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,MAAK;gDACL,WAAU;;oDACX;kEAEC,6LAAC,qNAAA,CAAA,aAAU;wDAAC,WAAU;;;;;;;;;;;;0DAGxB,6LAAC;gDACC,MAAK;gDACL,WAAU;0DACX;;;;;;;;;;;;;;;;;;0CAOL,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAiD;;;;;;kDAI/D,6LAAC;wCAAI,WAAU;kDACZ,WAAW,GAAG,CAAC,CAAC,WAAW,sBAC1B,6LAAC;gDAAgB,WAAU;;kEACzB,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC,8NAAA,CAAA,cAAW;4DAAC,WAAU;;;;;;;;;;;kEAEzB,6LAAC;wDAAE,WAAU;kEAAiC;;;;;;;+CAJtC;;;;;;;;;;kDAUd,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;;sEACC,6LAAC;4DAAI,WAAU;sEAAqD;;;;;;sEACpE,6LAAC;4DAAI,WAAU;sEAAwB;;;;;;;;;;;;8DAEzC,6LAAC;;sEACC,6LAAC;4DAAI,WAAU;sEAAqD;;;;;;sEACpE,6LAAC;4DAAI,WAAU;sEAAwB;;;;;;;;;;;;8DAEzC,6LAAC;;sEACC,6LAAC;4DAAI,WAAU;sEAAqD;;;;;;sEACpE,6LAAC;4DAAI,WAAU;sEAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQjD,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAiD;;;;;;kDAG/D,6LAAC;wCAAE,WAAU;kDAA0C;;;;;;;;;;;;0CAOzD,6LAAC;gCAAI,WAAU;0CACZ;oCACC;wCAAE,MAAM;wCAAsB,MAAM;oCAAK;oCACzC;wCAAE,MAAM;wCAAc,MAAM;oCAAK;oCACjC;wCAAE,MAAM;wCAAc,MAAM;oCAAK;oCACjC;wCAAE,MAAM;wCAAc,MAAM;oCAAM;iCACnC,CAAC,GAAG,CAAC,CAAC,QAAQ,sBACb,6LAAC;wCAAgB,WAAU;;0DACzB,6LAAC;gDAAI,WAAU;0DAAiB,OAAO,IAAI;;;;;;0DAC3C,6LAAC;gDAAI,WAAU;0DAA4B,OAAO,IAAI;;;;;;;uCAF9C;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUxB;KA5JwB", "debugId": null}}, {"offset": {"line": 2273, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/src/app/contact/components/ContactForm.jsx"], "sourcesContent": ["\"use client\";\n\nimport React, { useState } from \"react\";\n\nexport default function ContactForm() {\n  const [formData, setFormData] = useState({\n    firstName: '',\n    lastName: '',\n    email: '',\n    company: '',\n    organizationSize: '',\n    industry: ''\n  });\n\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const [apiError, setApiError] = useState(\"\");\n  const [success, setSuccess] = useState(\"\");\n\n  const handleInputChange = (e) => {\n    const { name, value } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    if (isSubmitting) return;\n    setIsSubmitting(true);\n    setApiError(\"\");\n    setSuccess(\"\");\n\n    try {\n      // Make the API request to store submission and send email\n      const response = await fetch(\"/api/submissions\", {\n        method: \"POST\",\n        headers: { \"Content-Type\": \"application/json\" },\n        body: JSON.stringify({\n          name: `${formData.firstName} ${formData.lastName}`,\n          email: formData.email,\n          company: formData.company,\n          organizationSize: formData.organizationSize,\n          industry: formData.industry,\n          message: `Contact inquiry from ${formData.firstName} ${formData.lastName} at ${formData.company}. Organization size: ${formData.organizationSize}, Industry: ${formData.industry}`,\n          formType: 'contact-form',\n          subject: 'New Contact Form Submission from SecurityLit Contact Page'\n        }),\n      });\n\n      const result = await response.json();\n      if (response.ok && result.success) {\n        // Clear form fields on success\n        setFormData({\n          firstName: '',\n          lastName: '',\n          email: '',\n          company: '',\n          organizationSize: '',\n          industry: ''\n        });\n        setSuccess(\"Thank you! We'll get back to you within 24 hours.\");\n      } else {\n        setApiError(result.error || \"Something went wrong. Please try again.\");\n      }\n    } catch (error) {\n      console.error(\"Error submitting form:\", error);\n      setApiError(\"Failed to send message. Please try again.\");\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n\n  return (\n    <div className=\"lg:w-1/2 bg-white\">\n      <div className=\"max-w-lg mx-auto p-8 pt-28 pb-16 lg:p-12 lg:pt-28 lg:pb-16\">\n        <div className=\"mb-6\">\n          <h2 className=\"text-3xl lg:text-4xl font-bold text-[var(--color-dark-blue)] mb-4\">\n            Complete the form to talk to an expert.\n          </h2>\n          <p className=\"text-lg text-gray-600\">\n            Get in touch for a free consultation and discover how we can protect your organization.\n          </p>\n        </div>\n\n        <form onSubmit={handleSubmit} className=\"space-y-5\">\n          {/* Error Message */}\n          {apiError && (\n            <div className=\"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg\">\n              {apiError}\n            </div>\n          )}\n\n          {/* Success Message */}\n          {success && (\n            <div className=\"bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg\">\n              {success}\n            </div>\n          )}\n\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-5\">\n            <div>\n              <label htmlFor=\"firstName\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                First name <span className=\"text-red-500\">*</span>\n              </label>\n              <input \n                type=\"text\" \n                id=\"firstName\" \n                name=\"firstName\" \n                value={formData.firstName} \n                onChange={handleInputChange} \n                required \n                className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[var(--color-blue)] transition-all\"\n              />\n            </div>\n            <div>\n              <label htmlFor=\"lastName\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Last name <span className=\"text-red-500\">*</span>\n              </label>\n              <input \n                type=\"text\" \n                id=\"lastName\" \n                name=\"lastName\" \n                value={formData.lastName} \n                onChange={handleInputChange} \n                required \n                className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[var(--color-blue)] transition-all\"\n              />\n            </div>\n          </div>\n          \n          <div>\n            <label htmlFor=\"email\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n              Email <span className=\"text-red-500\">*</span>\n            </label>\n            <input \n              type=\"email\" \n              id=\"email\" \n              name=\"email\" \n              value={formData.email} \n              onChange={handleInputChange} \n              required \n              className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[var(--color-blue)] transition-all\"\n            />\n          </div>\n          \n          <div>\n            <label htmlFor=\"company\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n              Company <span className=\"text-red-500\">*</span>\n            </label>\n            <input \n              type=\"text\" \n              id=\"company\" \n              name=\"company\" \n              value={formData.company} \n              onChange={handleInputChange} \n              required \n              className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[var(--color-blue)] transition-all\"\n            />\n          </div>\n          \n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-5\">\n            <div>\n              <label htmlFor=\"organizationSize\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Company size <span className=\"text-red-500\">*</span>\n              </label>\n              <select \n                id=\"organizationSize\" \n                name=\"organizationSize\" \n                value={formData.organizationSize} \n                onChange={handleInputChange} \n                required \n                className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[var(--color-blue)] transition-all bg-white\"\n              >\n                <option value=\"\">Choose size</option>\n                <option value=\"startup\">Startup (1-50)</option>\n                <option value=\"smb\">SMB (51-500)</option>\n                <option value=\"enterprise\">Enterprise (500+)</option>\n                <option value=\"government\">Government</option>\n              </select>\n            </div>\n            <div>\n              <label htmlFor=\"industry\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Company industry <span className=\"text-red-500\">*</span>\n              </label>\n              <select \n                id=\"industry\" \n                name=\"industry\" \n                value={formData.industry} \n                onChange={handleInputChange} \n                required \n                className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[var(--color-blue)] transition-all bg-white\"\n              >\n                <option value=\"\">Choose industry</option>\n                <option value=\"technology\">Technology</option>\n                <option value=\"finance\">Finance</option>\n                <option value=\"healthcare\">Healthcare</option>\n                <option value=\"manufacturing\">Manufacturing</option>\n                <option value=\"retail\">Retail</option>\n                <option value=\"education\">Education</option>\n                <option value=\"government\">Government</option>\n                <option value=\"other\">Other</option>\n              </select>\n            </div>\n          </div>\n          \n          <button\n            type=\"submit\"\n            disabled={isSubmitting}\n            className=\"w-full bg-[var(--color-blue)] hover:bg-[var(--color-blue-secondary)] disabled:bg-gray-400 text-white px-8 py-4 rounded-lg font-bold text-lg transition-all duration-300 shadow-lg hover:shadow-xl flex items-center justify-center gap-2\"\n          >\n            {isSubmitting ? (\n              <>\n                <div className=\"animate-spin rounded-full h-5 w-5 border-b-2 border-white\"></div>\n                Submitting...\n              </>\n            ) : (\n              'Submit Inquiry'\n            )}\n          </button>\n        </form>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;;;AAFA;;AAIe,SAAS;;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,WAAW;QACX,UAAU;QACV,OAAO;QACP,SAAS;QACT,kBAAkB;QAClB,UAAU;IACZ;IAEA,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,MAAM,oBAAoB,CAAC;QACzB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM;QAChC,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,CAAC,KAAK,EAAE;YACV,CAAC;IACH;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,IAAI,cAAc;QAClB,gBAAgB;QAChB,YAAY;QACZ,WAAW;QAEX,IAAI;YACF,0DAA0D;YAC1D,MAAM,WAAW,MAAM,MAAM,oBAAoB;gBAC/C,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;oBACnB,MAAM,AAAC,GAAwB,OAAtB,SAAS,SAAS,EAAC,KAAqB,OAAlB,SAAS,QAAQ;oBAChD,OAAO,SAAS,KAAK;oBACrB,SAAS,SAAS,OAAO;oBACzB,kBAAkB,SAAS,gBAAgB;oBAC3C,UAAU,SAAS,QAAQ;oBAC3B,SAAS,AAAC,wBAA6C,OAAtB,SAAS,SAAS,EAAC,KAA2B,OAAxB,SAAS,QAAQ,EAAC,QAA8C,OAAxC,SAAS,OAAO,EAAC,yBAA+D,OAAxC,SAAS,gBAAgB,EAAC,gBAAgC,OAAlB,SAAS,QAAQ;oBAChL,UAAU;oBACV,SAAS;gBACX;YACF;YAEA,MAAM,SAAS,MAAM,SAAS,IAAI;YAClC,IAAI,SAAS,EAAE,IAAI,OAAO,OAAO,EAAE;gBACjC,+BAA+B;gBAC/B,YAAY;oBACV,WAAW;oBACX,UAAU;oBACV,OAAO;oBACP,SAAS;oBACT,kBAAkB;oBAClB,UAAU;gBACZ;gBACA,WAAW;YACb,OAAO;gBACL,YAAY,OAAO,KAAK,IAAI;YAC9B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,YAAY;QACd,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAoE;;;;;;sCAGlF,6LAAC;4BAAE,WAAU;sCAAwB;;;;;;;;;;;;8BAKvC,6LAAC;oBAAK,UAAU;oBAAc,WAAU;;wBAErC,0BACC,6LAAC;4BAAI,WAAU;sCACZ;;;;;;wBAKJ,yBACC,6LAAC;4BAAI,WAAU;sCACZ;;;;;;sCAIL,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAM,SAAQ;4CAAY,WAAU;;gDAA+C;8DACvE,6LAAC;oDAAK,WAAU;8DAAe;;;;;;;;;;;;sDAE5C,6LAAC;4CACC,MAAK;4CACL,IAAG;4CACH,MAAK;4CACL,OAAO,SAAS,SAAS;4CACzB,UAAU;4CACV,QAAQ;4CACR,WAAU;;;;;;;;;;;;8CAGd,6LAAC;;sDACC,6LAAC;4CAAM,SAAQ;4CAAW,WAAU;;gDAA+C;8DACvE,6LAAC;oDAAK,WAAU;8DAAe;;;;;;;;;;;;sDAE3C,6LAAC;4CACC,MAAK;4CACL,IAAG;4CACH,MAAK;4CACL,OAAO,SAAS,QAAQ;4CACxB,UAAU;4CACV,QAAQ;4CACR,WAAU;;;;;;;;;;;;;;;;;;sCAKhB,6LAAC;;8CACC,6LAAC;oCAAM,SAAQ;oCAAQ,WAAU;;wCAA+C;sDACxE,6LAAC;4CAAK,WAAU;sDAAe;;;;;;;;;;;;8CAEvC,6LAAC;oCACC,MAAK;oCACL,IAAG;oCACH,MAAK;oCACL,OAAO,SAAS,KAAK;oCACrB,UAAU;oCACV,QAAQ;oCACR,WAAU;;;;;;;;;;;;sCAId,6LAAC;;8CACC,6LAAC;oCAAM,SAAQ;oCAAU,WAAU;;wCAA+C;sDACxE,6LAAC;4CAAK,WAAU;sDAAe;;;;;;;;;;;;8CAEzC,6LAAC;oCACC,MAAK;oCACL,IAAG;oCACH,MAAK;oCACL,OAAO,SAAS,OAAO;oCACvB,UAAU;oCACV,QAAQ;oCACR,WAAU;;;;;;;;;;;;sCAId,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAM,SAAQ;4CAAmB,WAAU;;gDAA+C;8DAC5E,6LAAC;oDAAK,WAAU;8DAAe;;;;;;;;;;;;sDAE9C,6LAAC;4CACC,IAAG;4CACH,MAAK;4CACL,OAAO,SAAS,gBAAgB;4CAChC,UAAU;4CACV,QAAQ;4CACR,WAAU;;8DAEV,6LAAC;oDAAO,OAAM;8DAAG;;;;;;8DACjB,6LAAC;oDAAO,OAAM;8DAAU;;;;;;8DACxB,6LAAC;oDAAO,OAAM;8DAAM;;;;;;8DACpB,6LAAC;oDAAO,OAAM;8DAAa;;;;;;8DAC3B,6LAAC;oDAAO,OAAM;8DAAa;;;;;;;;;;;;;;;;;;8CAG/B,6LAAC;;sDACC,6LAAC;4CAAM,SAAQ;4CAAW,WAAU;;gDAA+C;8DAChE,6LAAC;oDAAK,WAAU;8DAAe;;;;;;;;;;;;sDAElD,6LAAC;4CACC,IAAG;4CACH,MAAK;4CACL,OAAO,SAAS,QAAQ;4CACxB,UAAU;4CACV,QAAQ;4CACR,WAAU;;8DAEV,6LAAC;oDAAO,OAAM;8DAAG;;;;;;8DACjB,6LAAC;oDAAO,OAAM;8DAAa;;;;;;8DAC3B,6LAAC;oDAAO,OAAM;8DAAU;;;;;;8DACxB,6LAAC;oDAAO,OAAM;8DAAa;;;;;;8DAC3B,6LAAC;oDAAO,OAAM;8DAAgB;;;;;;8DAC9B,6LAAC;oDAAO,OAAM;8DAAS;;;;;;8DACvB,6LAAC;oDAAO,OAAM;8DAAY;;;;;;8DAC1B,6LAAC;oDAAO,OAAM;8DAAa;;;;;;8DAC3B,6LAAC;oDAAO,OAAM;8DAAQ;;;;;;;;;;;;;;;;;;;;;;;;sCAK5B,6LAAC;4BACC,MAAK;4BACL,UAAU;4BACV,WAAU;sCAET,6BACC;;kDACE,6LAAC;wCAAI,WAAU;;;;;;oCAAkE;;+CAInF;;;;;;;;;;;;;;;;;;;;;;;AAOd;GA5NwB;KAAA", "debugId": null}}, {"offset": {"line": 2822, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/src/app/pricing/components/PricingContactForm.jsx"], "sourcesContent": ["\"use client\";\n\nimport React from 'react';\nimport { Mail, Phone, MapPin, Clock } from 'lucide-react';\nimport ContactForm from '../../contact/components/ContactForm';\n\nexport default function PricingContactForm() {\n\n  const contactInfo = [\n    {\n      icon: <Mail className=\"w-5 h-5\" />,\n      title: \"Email Us\",\n      content: \"<EMAIL>\",\n      description: \"Get detailed pricing information\"\n    },\n    {\n      icon: <Phone className=\"w-5 h-5\" />,\n      title: \"Call Us\",\n      content: \"+****************\",\n      description: \"Speak with our security experts\"\n    },\n    {\n      icon: <Clock className=\"w-5 h-5\" />,\n      title: \"Response Time\",\n      content: \"Within 24 hours\",\n      description: \"We respond to all inquiries promptly\"\n    },\n    {\n      icon: <MapPin className=\"w-5 h-5\" />,\n      title: \"Global Presence\",\n      content: \"USA, India, Singapore\",\n      description: \"Serving clients worldwide\"\n    }\n  ];\n\n  return (\n    <section id=\"pricing-contact\" className=\"py-20 lg:py-28 bg-[var(--bg-light-blue)]\">\n      <div className=\"container mx-auto px-4 max-w-7xl\">\n        {/* Header */}\n        <div className=\"text-center mb-16\">\n          <h2 className=\"text-4xl lg:text-5xl font-bold text-[var(--color-dark-blue)] mb-6\">\n            Get Your Custom Security\n            <span className=\"text-[var(--color-blue)]\"> Proposal</span>\n          </h2>\n          <p className=\"text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed\">\n            Ready to strengthen your cybersecurity posture? Fill out the form below and our security experts \n            will provide you with a detailed proposal tailored to your specific requirements.\n          </p>\n        </div>\n\n        <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-12\">\n          {/* Contact Information */}\n          <div className=\"lg:col-span-1\">\n            <div className=\"bg-white rounded-2xl p-8 shadow-lg border border-[var(--color-blue)]/10 h-fit\">\n              <h3 className=\"text-2xl font-bold text-[var(--color-dark-blue)] mb-6\">\n                Contact Information\n              </h3>\n              \n              <div className=\"space-y-6\">\n                {contactInfo.map((info, index) => (\n                  <div key={index} className=\"flex items-start gap-4\">\n                    <div className=\"w-12 h-12 bg-[var(--color-blue)]/10 rounded-lg flex items-center justify-center flex-shrink-0\">\n                      <div className=\"text-[var(--color-blue)]\">\n                        {info.icon}\n                      </div>\n                    </div>\n                    <div>\n                      <h4 className=\"font-semibold text-[var(--color-dark-blue)] mb-1\">{info.title}</h4>\n                      <p className=\"text-[var(--color-blue)] font-medium mb-1\">{info.content}</p>\n                      <p className=\"text-gray-600 text-sm\">{info.description}</p>\n                    </div>\n                  </div>\n                ))}\n              </div>\n\n              {/* Additional Info */}\n              <div className=\"mt-8 pt-8 border-t border-gray-200\">\n                <h4 className=\"font-semibold text-[var(--color-dark-blue)] mb-4\">Why Choose SecurityLit?</h4>\n                <ul className=\"space-y-2 text-sm text-gray-600\">\n                  <li className=\"flex items-center gap-2\">\n                    <div className=\"w-2 h-2 bg-[var(--color-blue)] rounded-full\"></div>\n                    Certified security professionals\n                  </li>\n                  <li className=\"flex items-center gap-2\">\n                    <div className=\"w-2 h-2 bg-[var(--color-blue)] rounded-full\"></div>\n                    Transparent, competitive pricing\n                  </li>\n                  <li className=\"flex items-center gap-2\">\n                    <div className=\"w-2 h-2 bg-[var(--color-blue)] rounded-full\"></div>\n                    Comprehensive security reports\n                  </li>\n                  <li className=\"flex items-center gap-2\">\n                    <div className=\"w-2 h-2 bg-[var(--color-blue)] rounded-full\"></div>\n                    24/7 incident response support\n                  </li>\n                </ul>\n              </div>\n            </div>\n          </div>\n\n          {/* Contact Form */}\n          <div className=\"lg:col-span-2\">\n            <div className=\"bg-white rounded-2xl shadow-lg border border-[var(--color-blue)]/10 overflow-hidden\">\n              <div className=\"p-8 lg:p-10\">\n                <h3 className=\"text-2xl font-bold text-[var(--color-dark-blue)] mb-4\">\n                  Request Custom Pricing\n                </h3>\n                <p className=\"text-gray-600 mb-8\">\n                  Fill out the form below and our security experts will provide you with a detailed proposal tailored to your specific requirements.\n                </p>\n              </div>\n\n              {/* Use existing ContactForm component */}\n              <ContactForm />\n            </div>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AACA;AAJA;;;;;AAMe,SAAS;IAEtB,MAAM,cAAc;QAClB;YACE,oBAAM,6LAAC,qMAAA,CAAA,OAAI;gBAAC,WAAU;;;;;;YACtB,OAAO;YACP,SAAS;YACT,aAAa;QACf;QACA;YACE,oBAAM,6LAAC,uMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;YACvB,OAAO;YACP,SAAS;YACT,aAAa;QACf;QACA;YACE,oBAAM,6LAAC,uMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;YACvB,OAAO;YACP,SAAS;YACT,aAAa;QACf;QACA;YACE,oBAAM,6LAAC,6MAAA,CAAA,SAAM;gBAAC,WAAU;;;;;;YACxB,OAAO;YACP,SAAS;YACT,aAAa;QACf;KACD;IAED,qBACE,6LAAC;QAAQ,IAAG;QAAkB,WAAU;kBACtC,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;;gCAAoE;8CAEhF,6LAAC;oCAAK,WAAU;8CAA2B;;;;;;;;;;;;sCAE7C,6LAAC;4BAAE,WAAU;sCAA0D;;;;;;;;;;;;8BAMzE,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAwD;;;;;;kDAItE,6LAAC;wCAAI,WAAU;kDACZ,YAAY,GAAG,CAAC,CAAC,MAAM,sBACtB,6LAAC;gDAAgB,WAAU;;kEACzB,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAI,WAAU;sEACZ,KAAK,IAAI;;;;;;;;;;;kEAGd,6LAAC;;0EACC,6LAAC;gEAAG,WAAU;0EAAoD,KAAK,KAAK;;;;;;0EAC5E,6LAAC;gEAAE,WAAU;0EAA6C,KAAK,OAAO;;;;;;0EACtE,6LAAC;gEAAE,WAAU;0EAAyB,KAAK,WAAW;;;;;;;;;;;;;+CAThD;;;;;;;;;;kDAgBd,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAAmD;;;;;;0DACjE,6LAAC;gDAAG,WAAU;;kEACZ,6LAAC;wDAAG,WAAU;;0EACZ,6LAAC;gEAAI,WAAU;;;;;;4DAAoD;;;;;;;kEAGrE,6LAAC;wDAAG,WAAU;;0EACZ,6LAAC;gEAAI,WAAU;;;;;;4DAAoD;;;;;;;kEAGrE,6LAAC;wDAAG,WAAU;;0EACZ,6LAAC;gEAAI,WAAU;;;;;;4DAAoD;;;;;;;kEAGrE,6LAAC;wDAAG,WAAU;;0EACZ,6LAAC;gEAAI,WAAU;;;;;;4DAAoD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAS7E,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAAwD;;;;;;0DAGtE,6LAAC;gDAAE,WAAU;0DAAqB;;;;;;;;;;;;kDAMpC,6LAAC,sJAAA,CAAA,UAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO1B;KAlHwB", "debugId": null}}]}