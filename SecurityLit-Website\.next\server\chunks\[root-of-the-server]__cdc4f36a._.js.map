{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 82, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/src/app/utils/emailValidator.js"], "sourcesContent": ["const freeEmailDomains = [\n    \"gmail.com\", \"yahoo.com\", \"hotmail.com\", \"outlook.com\",\n    \"aol.com\", \"icloud.com\", \"zoho.com\", \"protonmail.com\",\n    \"gmx.com\", \"yandex.com\", \"mail.com\", \"live.com\", \"me.com\",\n    \"msn.com\", \"inbox.com\"\n  ];\n  \n  const disposableEmailDomains = [\n    \"temp-mail.org\", \"1secmail.com\", \"1secmail.org\", \"1secmail.net\",\n    \"disposableemailaddresses.com\", \"mailinator.com\", \"guerrillamail.com\",\n    \"10minutemail.com\", \"getnada.com\", \"throwawaymail.com\", \"yopmail.com\",\n    \"trashmail.com\", \"maildrop.cc\", \"fakeinbox.com\", \"moakt.com\", \n    \"meltmail.com\", \"spambog.com\", \"mailcatch.com\", \"spamex.com\", \n    \"spammotel.com\", \"spamgourmet.com\", \"discard.email\", \"airmail.cc\",\n    \"sharklasers.com\", \"mytemp.email\", \"dispostable.com\", \"tempmailo.com\", \n    \"emailondeck.com\", \"mintemail.com\", \"eyepaste.com\", \"33mail.com\", \n    \"tmail.com\", \"instant-mail.com\", \"opayq.com\", \"tmpbox.net\", \n    \"kasmail.com\", \"mohmal.com\", \"anonbox.net\", \"disbox.org\", \n    \"easytrashmail.com\", \"fakemailgenerator.com\", \"getairmail.com\",\n    \"guerillamail.org\", \"guerillamailblock.com\", \"hot-mail.gq\", \n    \"inboxkitten.com\", \"mailsac.com\", \"mail.tm\", \"mytrashmail.com\", \n    \"tutanota.com\", \"privatemail.com\", \"jetable.org\", \"temporary-mail.net\",\n    \"crazymailing.com\", \"heweek.com\", \"example.com\", \"test.com\"\n  ];\n  \n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n  \n  // Known spam traps\n  const spamTraps = [\n    \"<EMAIL>\", \"<EMAIL>\", \"<EMAIL>\"\n  ];\n  \n  // Function to check if the email is a business email\n  export const isBusinessEmail = (email) => {\n    // Check if email is undefined or null\n    if (!email) {\n      console.log(\"Email is undefined or null\");\n      return false;\n    }\n  \n    if (!emailRegex.test(email)) {\n      console.log(\"Invalid email format\");\n      return false;  \n    }\n  \n    const domain = email.split(\"@\")[1];\n  \n    if (freeEmailDomains.includes(domain) || disposableEmailDomains.includes(domain)) {\n      console.log(\"Free or disposable domain detected\");\n      return false;  \n    }\n  \n    if (spamTraps.includes(email.toLowerCase())) {\n      console.log(\"Spam trap detected\");\n      return false; \n    }\n  \n    return true;\n  };\n  \n  // Function to validate email format (less strict for SecurityLit)\n  export const isValidEmail = (email) => {\n    if (!email) {\n      return false;\n    }\n    return emailRegex.test(email);\n  };\n"], "names": [], "mappings": ";;;;AAAA,MAAM,mBAAmB;IACrB;IAAa;IAAa;IAAe;IACzC;IAAW;IAAc;IAAY;IACrC;IAAW;IAAc;IAAY;IAAY;IACjD;IAAW;CACZ;AAED,MAAM,yBAAyB;IAC7B;IAAiB;IAAgB;IAAgB;IACjD;IAAgC;IAAkB;IAClD;IAAoB;IAAe;IAAqB;IACxD;IAAiB;IAAe;IAAiB;IACjD;IAAgB;IAAe;IAAiB;IAChD;IAAiB;IAAmB;IAAiB;IACrD;IAAmB;IAAgB;IAAmB;IACtD;IAAmB;IAAiB;IAAgB;IACpD;IAAa;IAAoB;IAAa;IAC9C;IAAe;IAAc;IAAe;IAC5C;IAAqB;IAAyB;IAC9C;IAAoB;IAAyB;IAC7C;IAAmB;IAAe;IAAW;IAC7C;IAAgB;IAAmB;IAAe;IAClD;IAAoB;IAAc;IAAe;CAClD;AAED,MAAM,aAAa;AAEnB,mBAAmB;AACnB,MAAM,YAAY;IAChB;IAAwB;IAAoB;CAC7C;AAGM,MAAM,kBAAkB,CAAC;IAC9B,sCAAsC;IACtC,IAAI,CAAC,OAAO;QACV,QAAQ,GAAG,CAAC;QACZ,OAAO;IACT;IAEA,IAAI,CAAC,WAAW,IAAI,CAAC,QAAQ;QAC3B,QAAQ,GAAG,CAAC;QACZ,OAAO;IACT;IAEA,MAAM,SAAS,MAAM,KAAK,CAAC,IAAI,CAAC,EAAE;IAElC,IAAI,iBAAiB,QAAQ,CAAC,WAAW,uBAAuB,QAAQ,CAAC,SAAS;QAChF,QAAQ,GAAG,CAAC;QACZ,OAAO;IACT;IAEA,IAAI,UAAU,QAAQ,CAAC,MAAM,WAAW,KAAK;QAC3C,QAAQ,GAAG,CAAC;QACZ,OAAO;IACT;IAEA,OAAO;AACT;AAGO,MAAM,eAAe,CAAC;IAC3B,IAAI,CAAC,OAAO;QACV,OAAO;IACT;IACA,OAAO,WAAW,IAAI,CAAC;AACzB", "debugId": null}}, {"offset": {"line": 352, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/src/app/utils/send-email.js"], "sourcesContent": ["import { EmailClient, KnownEmailSendStatus } from \"@azure/communication-email\";\n\n// Check if connection string is available\nconst connectionString = process.env.NEXT_PUBLIC_COMMUNICATION_SERVICES_CONNECTION_STRING;\nlet emailClient;\n\n// Only initialize email client if connection string is available\nif (connectionString) {\n  try {\n    emailClient = new EmailClient(connectionString);\n  } catch (error) {\n    console.error(\"Failed to initialize email client:\", error);\n  }\n} else {\n  console.warn(\"Communication Services connection string is not defined\");\n}\n\n// Constants for polling\nconst POLLER_WAIT_TIME = 10; // seconds\nconst MAX_POLL_TIME = 120; // seconds\n\n// Helper function to parse form data from body\nfunction parseFormData(body) {\n  const lines = body.split('\\n');\n  const formData = {};\n  \n  lines.forEach(line => {\n    const [key, ...valueParts] = line.split(': ');\n    if (key && valueParts.length > 0) {\n      formData[key] = valueParts.join(': ');\n    }\n  });\n  \n  return formData;\n}\n\n// Helper function to determine form type from subject\nfunction getFormType(subject) {\n  if (subject.includes('Contact')) return 'contact-form';\n  if (subject.includes('Training')) return 'training-enrollment';\n  if (subject.includes('Newsletter')) return 'newsletter-signup';\n  if (subject.includes('Premium')) return 'premium-training-inquiry';\n  return 'general-inquiry';\n}\n\n// Helper function to store submission with retry logic\nconst MAX_RETRIES = 3;\nconst storeSubmission = async (submissionData) => {\n  let retries = 0;\n  while (retries < MAX_RETRIES) {\n    try {\n      // Use absolute URL with window.location.origin\n      const baseUrl = typeof window !== 'undefined' ? window.location.origin : 'http://localhost:3000';\n      const response = await fetch(`${baseUrl}/api/submissions`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(submissionData),\n      });\n\n      if (!response.ok) {\n        throw new Error(`HTTP error! status: ${response.status}`);\n      }\n\n      return true;\n    } catch (error) {\n      retries++;\n      if (retries === MAX_RETRIES) {\n        console.error('Failed to store submission after retries:', error);\n        return false;\n      }\n      // Wait before retrying (exponential backoff)\n      await new Promise(resolve => setTimeout(resolve, Math.pow(2, retries) * 1000));\n    }\n  }\n  return false;\n};\n\nexport async function sendEmail(subject, body, attachments = []) {\n  try {\n    // Parse form data and prepare submission\n    const formData = parseFormData(body);\n    const submissionData = {\n      subject,\n      body,\n      attachments: attachments.length > 0 ? 'Has attachments' : 'No attachments',\n      formType: getFormType(subject),\n      formData,\n      timestamp: new Date().toISOString()\n    };\n\n    // Store submission (non-blocking)\n    const storePromise = storeSubmission(submissionData);\n\n    // Check if email client is initialized\n    if (!emailClient) {\n      console.warn(\"Email client not initialized, skipping email send\");\n      await storePromise; // Still wait for storage to complete\n      return true; // Return success for development environments\n    }\n\n    // Prepare email message\n    const message = {\n      senderAddress: process.env.NEXT_PUBLIC_EMAIL_USER,\n      content: {\n        subject,\n        plainText: body,\n        attachments,\n      },\n      recipients: {\n        to: [\n          {\n            address: process.env.NEXT_PUBLIC_RECIEVER_EMAIL || '<EMAIL>',\n            displayName: \"SecurityLit Team\",\n          },\n        ],\n      },\n    };\n\n    // Send email\n    const poller = await emailClient.beginSend(message);\n\n    if (!poller.getOperationState().isStarted) {\n      throw new Error(\"Email sending failed to start\");\n    }\n\n    // Poll for completion with timeout\n    let timeElapsed = 0;\n    while (!poller.isDone()) {\n      await poller.poll();\n      await new Promise(resolve => setTimeout(resolve, POLLER_WAIT_TIME * 1000));\n      timeElapsed += POLLER_WAIT_TIME;\n\n      if (timeElapsed > MAX_POLL_TIME) {\n        throw new Error(\"Email sending timed out\");\n      }\n    }\n\n    const result = poller.getResult();\n    if (result.status !== KnownEmailSendStatus.Succeeded) {\n      throw new Error(result.error || \"Email sending failed\");\n    }\n\n    // Wait for storage to complete\n    await storePromise;\n\n    console.log(`Successfully sent the email (operation id: ${result.id})`);\n    return true;\n  } catch (error) {\n    console.error(\"Failed to send email:\", error);\n    throw error;\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AAAA;;AAEA,0CAA0C;AAC1C,MAAM,mBAAmB,QAAQ,GAAG,CAAC,oDAAoD;AACzF,IAAI;AAEJ,iEAAiE;AACjE,IAAI,kBAAkB;IACpB,IAAI;QACF,cAAc,IAAI,wLAAA,CAAA,cAAW,CAAC;IAChC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,sCAAsC;IACtD;AACF,OAAO;IACL,QAAQ,IAAI,CAAC;AACf;AAEA,wBAAwB;AACxB,MAAM,mBAAmB,IAAI,UAAU;AACvC,MAAM,gBAAgB,KAAK,UAAU;AAErC,+CAA+C;AAC/C,SAAS,cAAc,IAAI;IACzB,MAAM,QAAQ,KAAK,KAAK,CAAC;IACzB,MAAM,WAAW,CAAC;IAElB,MAAM,OAAO,CAAC,CAAA;QACZ,MAAM,CAAC,KAAK,GAAG,WAAW,GAAG,KAAK,KAAK,CAAC;QACxC,IAAI,OAAO,WAAW,MAAM,GAAG,GAAG;YAChC,QAAQ,CAAC,IAAI,GAAG,WAAW,IAAI,CAAC;QAClC;IACF;IAEA,OAAO;AACT;AAEA,sDAAsD;AACtD,SAAS,YAAY,OAAO;IAC1B,IAAI,QAAQ,QAAQ,CAAC,YAAY,OAAO;IACxC,IAAI,QAAQ,QAAQ,CAAC,aAAa,OAAO;IACzC,IAAI,QAAQ,QAAQ,CAAC,eAAe,OAAO;IAC3C,IAAI,QAAQ,QAAQ,CAAC,YAAY,OAAO;IACxC,OAAO;AACT;AAEA,uDAAuD;AACvD,MAAM,cAAc;AACpB,MAAM,kBAAkB,OAAO;IAC7B,IAAI,UAAU;IACd,MAAO,UAAU,YAAa;QAC5B,IAAI;YACF,+CAA+C;YAC/C,MAAM,UAAU,sCAAgC,0BAAyB;YACzE,MAAM,WAAW,MAAM,MAAM,GAAG,QAAQ,gBAAgB,CAAC,EAAE;gBACzD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;YAC1D;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd;YACA,IAAI,YAAY,aAAa;gBAC3B,QAAQ,KAAK,CAAC,6CAA6C;gBAC3D,OAAO;YACT;YACA,6CAA6C;YAC7C,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS,KAAK,GAAG,CAAC,GAAG,WAAW;QAC1E;IACF;IACA,OAAO;AACT;AAEO,eAAe,UAAU,OAAO,EAAE,IAAI,EAAE,cAAc,EAAE;IAC7D,IAAI;QACF,yCAAyC;QACzC,MAAM,WAAW,cAAc;QAC/B,MAAM,iBAAiB;YACrB;YACA;YACA,aAAa,YAAY,MAAM,GAAG,IAAI,oBAAoB;YAC1D,UAAU,YAAY;YACtB;YACA,WAAW,IAAI,OAAO,WAAW;QACnC;QAEA,kCAAkC;QAClC,MAAM,eAAe,gBAAgB;QAErC,uCAAuC;QACvC,IAAI,CAAC,aAAa;YAChB,QAAQ,IAAI,CAAC;YACb,MAAM,cAAc,qCAAqC;YACzD,OAAO,MAAM,8CAA8C;QAC7D;QAEA,wBAAwB;QACxB,MAAM,UAAU;YACd,eAAe,QAAQ,GAAG,CAAC,sBAAsB;YACjD,SAAS;gBACP;gBACA,WAAW;gBACX;YACF;YACA,YAAY;gBACV,IAAI;oBACF;wBACE,SAAS,QAAQ,GAAG,CAAC,0BAA0B,IAAI;wBACnD,aAAa;oBACf;iBACD;YACH;QACF;QAEA,aAAa;QACb,MAAM,SAAS,MAAM,YAAY,SAAS,CAAC;QAE3C,IAAI,CAAC,OAAO,iBAAiB,GAAG,SAAS,EAAE;YACzC,MAAM,IAAI,MAAM;QAClB;QAEA,mCAAmC;QACnC,IAAI,cAAc;QAClB,MAAO,CAAC,OAAO,MAAM,GAAI;YACvB,MAAM,OAAO,IAAI;YACjB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS,mBAAmB;YACpE,eAAe;YAEf,IAAI,cAAc,eAAe;gBAC/B,MAAM,IAAI,MAAM;YAClB;QACF;QAEA,MAAM,SAAS,OAAO,SAAS;QAC/B,IAAI,OAAO,MAAM,KAAK,gNAAA,CAAA,uBAAoB,CAAC,SAAS,EAAE;YACpD,MAAM,IAAI,MAAM,OAAO,KAAK,IAAI;QAClC;QAEA,+BAA+B;QAC/B,MAAM;QAEN,QAAQ,GAAG,CAAC,CAAC,2CAA2C,EAAE,OAAO,EAAE,CAAC,CAAC,CAAC;QACtE,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yBAAyB;QACvC,MAAM;IACR;AACF", "debugId": null}}, {"offset": {"line": 494, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/src/app/api/submissions/route.js"], "sourcesContent": ["import { NextResponse } from 'next/server';\nimport fs from 'fs/promises';\nimport path from 'path';\nimport { isBusinessEmail, isValidEmail } from '../../utils/emailValidator';\nimport { sendEmail } from '../../utils/send-email';\n\n// Constants\nconst MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB max file size\nconst MAX_SUBMISSIONS = 10000; // Maximum number of submissions to keep\nconst SUBMISSIONS_PATH = path.join(process.cwd(), 'data', 'submissions.json');\n\n// Helper function to ensure data directory exists\nasync function ensureDataDirectory() {\n  const dataDir = path.join(process.cwd(), 'data');\n  try {\n    await fs.access(dataDir);\n  } catch {\n    await fs.mkdir(dataDir, { recursive: true });\n  }\n}\n\n// Helper function to read submissions\nasync function readSubmissions() {\n  try {\n    const data = await fs.readFile(SUBMISSIONS_PATH, 'utf8');\n    return JSON.parse(data);\n  } catch (error) {\n    // File doesn't exist or is invalid, return empty array\n    return [];\n  }\n}\n\n// Helper function to write submissions\nasync function writeSubmissions(submissions) {\n  // Keep only the most recent submissions if we exceed the limit\n  if (submissions.length > MAX_SUBMISSIONS) {\n    submissions = submissions.slice(-MAX_SUBMISSIONS);\n  }\n  \n  await fs.writeFile(SUBMISSIONS_PATH, JSON.stringify(submissions, null, 2));\n}\n\nexport async function POST(request) {\n  try {\n    // Ensure data directory exists\n    await ensureDataDirectory();\n\n    // Parse and validate request data\n    const data = await request.json();\n    if (!data || typeof data !== 'object') {\n      return NextResponse.json(\n        { error: 'Invalid submission data' },\n        { status: 400 }\n      );\n    }\n\n    // Required fields per form type\n    const requiredFieldsMap = {\n      'contact-form': ['name', 'email', 'message'],\n      'training-enrollment': ['fullName', 'email', 'phone'],\n      'newsletter-signup': ['name', 'email'],\n      'premium-training-inquiry': ['name', 'email', 'message'],\n      'general-inquiry': ['name', 'email', 'message']\n    };\n\n    const formType = data.formType;\n    if (!formType || !requiredFieldsMap[formType]) {\n      return NextResponse.json(\n        { error: 'Missing or unknown formType.' },\n        { status: 400 }\n      );\n    }\n\n    // Validate required fields for this formType\n    const requiredFields = requiredFieldsMap[formType];\n    const missingFields = requiredFields.filter(field => !data[field] || String(data[field]).trim() === '');\n    if (missingFields.length > 0) {\n      return NextResponse.json(\n        { error: `Missing required field(s): ${missingFields.join(', ')}` },\n        { status: 400 }\n      );\n    }\n\n    // Email validation - use business email validation for contact forms, regular validation for others\n    const emailToValidate = data.email || data.businessEmail || null;\n    if (!emailToValidate) {\n      return NextResponse.json(\n        { error: 'Email address is required.' },\n        { status: 400 }\n      );\n    }\n\n    // For contact forms and training enrollment, require business email\n    // For newsletter signup, allow any valid email\n    if (formType === 'contact-form' || formType === 'training-enrollment') {\n      if (!isBusinessEmail(emailToValidate)) {\n        return NextResponse.json(\n          { error: 'Please use a valid business email address.' },\n          { status: 400 }\n        );\n      }\n    } else {\n      if (!isValidEmail(emailToValidate)) {\n        return NextResponse.json(\n          { error: 'Please use a valid email address.' },\n          { status: 400 }\n        );\n      }\n    }\n\n    // Add timestamp if not present\n    const submissionWithTimestamp = {\n      ...data,\n      timestamp: data.timestamp || new Date().toISOString()\n    };\n\n    // Read existing submissions\n    const submissions = await readSubmissions();\n\n    // Add new submission\n    submissions.push(submissionWithTimestamp);\n\n    // Write back to file\n    await writeSubmissions(submissions);\n\n    // Send email with form data\n    const subject = data.subject || `New ${formType.replace('-', ' ')} Submission`;\n    const body = Object.entries(data).map(([key, value]) => `${key}: ${value}`).join('\\n');\n    await sendEmail(subject, body);\n\n    return NextResponse.json({ success: true });\n  } catch (error) {\n    console.error('Error storing submission or sending email:', error);\n    return NextResponse.json(\n      { error: 'Failed to store submission or send email' },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;;;;;;AAEA,YAAY;AACZ,MAAM,gBAAgB,KAAK,OAAO,MAAM,qBAAqB;AAC7D,MAAM,kBAAkB,OAAO,wCAAwC;AACvE,MAAM,mBAAmB,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,IAAI,QAAQ;AAE1D,kDAAkD;AAClD,eAAe;IACb,MAAM,UAAU,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,IAAI;IACzC,IAAI;QACF,MAAM,qHAAA,CAAA,UAAE,CAAC,MAAM,CAAC;IAClB,EAAE,OAAM;QACN,MAAM,qHAAA,CAAA,UAAE,CAAC,KAAK,CAAC,SAAS;YAAE,WAAW;QAAK;IAC5C;AACF;AAEA,sCAAsC;AACtC,eAAe;IACb,IAAI;QACF,MAAM,OAAO,MAAM,qHAAA,CAAA,UAAE,CAAC,QAAQ,CAAC,kBAAkB;QACjD,OAAO,KAAK,KAAK,CAAC;IACpB,EAAE,OAAO,OAAO;QACd,uDAAuD;QACvD,OAAO,EAAE;IACX;AACF;AAEA,uCAAuC;AACvC,eAAe,iBAAiB,WAAW;IACzC,+DAA+D;IAC/D,IAAI,YAAY,MAAM,GAAG,iBAAiB;QACxC,cAAc,YAAY,KAAK,CAAC,CAAC;IACnC;IAEA,MAAM,qHAAA,CAAA,UAAE,CAAC,SAAS,CAAC,kBAAkB,KAAK,SAAS,CAAC,aAAa,MAAM;AACzE;AAEO,eAAe,KAAK,OAAO;IAChC,IAAI;QACF,+BAA+B;QAC/B,MAAM;QAEN,kCAAkC;QAClC,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,IAAI,CAAC,QAAQ,OAAO,SAAS,UAAU;YACrC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAA0B,GACnC;gBAAE,QAAQ;YAAI;QAElB;QAEA,gCAAgC;QAChC,MAAM,oBAAoB;YACxB,gBAAgB;gBAAC;gBAAQ;gBAAS;aAAU;YAC5C,uBAAuB;gBAAC;gBAAY;gBAAS;aAAQ;YACrD,qBAAqB;gBAAC;gBAAQ;aAAQ;YACtC,4BAA4B;gBAAC;gBAAQ;gBAAS;aAAU;YACxD,mBAAmB;gBAAC;gBAAQ;gBAAS;aAAU;QACjD;QAEA,MAAM,WAAW,KAAK,QAAQ;QAC9B,IAAI,CAAC,YAAY,CAAC,iBAAiB,CAAC,SAAS,EAAE;YAC7C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAA+B,GACxC;gBAAE,QAAQ;YAAI;QAElB;QAEA,6CAA6C;QAC7C,MAAM,iBAAiB,iBAAiB,CAAC,SAAS;QAClD,MAAM,gBAAgB,eAAe,MAAM,CAAC,CAAA,QAAS,CAAC,IAAI,CAAC,MAAM,IAAI,OAAO,IAAI,CAAC,MAAM,EAAE,IAAI,OAAO;QACpG,IAAI,cAAc,MAAM,GAAG,GAAG;YAC5B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO,CAAC,2BAA2B,EAAE,cAAc,IAAI,CAAC,OAAO;YAAC,GAClE;gBAAE,QAAQ;YAAI;QAElB;QAEA,oGAAoG;QACpG,MAAM,kBAAkB,KAAK,KAAK,IAAI,KAAK,aAAa,IAAI;QAC5D,IAAI,CAAC,iBAAiB;YACpB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAA6B,GACtC;gBAAE,QAAQ;YAAI;QAElB;QAEA,oEAAoE;QACpE,+CAA+C;QAC/C,IAAI,aAAa,kBAAkB,aAAa,uBAAuB;YACrE,IAAI,CAAC,CAAA,GAAA,uIAAA,CAAA,kBAAe,AAAD,EAAE,kBAAkB;gBACrC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;oBAAE,OAAO;gBAA6C,GACtD;oBAAE,QAAQ;gBAAI;YAElB;QACF,OAAO;YACL,IAAI,CAAC,CAAA,GAAA,uIAAA,CAAA,eAAY,AAAD,EAAE,kBAAkB;gBAClC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;oBAAE,OAAO;gBAAoC,GAC7C;oBAAE,QAAQ;gBAAI;YAElB;QACF;QAEA,+BAA+B;QAC/B,MAAM,0BAA0B;YAC9B,GAAG,IAAI;YACP,WAAW,KAAK,SAAS,IAAI,IAAI,OAAO,WAAW;QACrD;QAEA,4BAA4B;QAC5B,MAAM,cAAc,MAAM;QAE1B,qBAAqB;QACrB,YAAY,IAAI,CAAC;QAEjB,qBAAqB;QACrB,MAAM,iBAAiB;QAEvB,4BAA4B;QAC5B,MAAM,UAAU,KAAK,OAAO,IAAI,CAAC,IAAI,EAAE,SAAS,OAAO,CAAC,KAAK,KAAK,WAAW,CAAC;QAC9E,MAAM,OAAO,OAAO,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,KAAK,MAAM,GAAK,GAAG,IAAI,EAAE,EAAE,OAAO,EAAE,IAAI,CAAC;QACjF,MAAM,CAAA,GAAA,sIAAA,CAAA,YAAS,AAAD,EAAE,SAAS;QAEzB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE,SAAS;QAAK;IAC3C,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,8CAA8C;QAC5D,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAA2C,GACpD;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}