"use client";

import React, { useState } from "react";

export default function ContactForm() {
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    company: '',
    organizationSize: '',
    industry: ''
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [apiError, setApiError] = useState("");
  const [success, setSuccess] = useState("");

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (isSubmitting) return;
    setIsSubmitting(true);
    setApiError("");
    setSuccess("");

    try {
      // Make the API request to store submission and send email
      const response = await fetch("/api/submissions", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          name: `${formData.firstName} ${formData.lastName}`,
          email: formData.email,
          company: formData.company,
          organizationSize: formData.organizationSize,
          industry: formData.industry,
          message: `Contact inquiry from ${formData.firstName} ${formData.lastName} at ${formData.company}. Organization size: ${formData.organizationSize}, Industry: ${formData.industry}`,
          formType: 'contact-form',
          subject: 'New Contact Form Submission from SecurityLit Contact Page'
        }),
      });

      const result = await response.json();
      if (response.ok && result.success) {
        // Clear form fields on success
        setFormData({
          firstName: '',
          lastName: '',
          email: '',
          company: '',
          organizationSize: '',
          industry: ''
        });
        setSuccess("Thank you! We'll get back to you within 24 hours.");
      } else {
        setApiError(result.error || "Something went wrong. Please try again.");
      }
    } catch (error) {
      console.error("Error submitting form:", error);
      setApiError("Failed to send message. Please try again.");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="lg:w-1/2 bg-white">
      <div className="max-w-lg mx-auto p-8 pt-28 pb-16 lg:p-12 lg:pt-28 lg:pb-16">
        <div className="mb-6">
          <h2 className="text-3xl lg:text-4xl font-bold text-[var(--color-dark-blue)] mb-4">
            Complete the form to talk to an expert.
          </h2>
          <p className="text-lg text-gray-600">
            Get in touch for a free consultation and discover how we can protect your organization.
          </p>
        </div>

        <form onSubmit={handleSubmit} className="space-y-5">
          {/* Error Message */}
          {apiError && (
            <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
              {apiError}
            </div>
          )}

          {/* Success Message */}
          {success && (
            <div className="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg">
              {success}
            </div>
          )}

          <div className="grid grid-cols-1 md:grid-cols-2 gap-5">
            <div>
              <label htmlFor="firstName" className="block text-sm font-medium text-gray-700 mb-2">
                First name <span className="text-red-500">*</span>
              </label>
              <input 
                type="text" 
                id="firstName" 
                name="firstName" 
                value={formData.firstName} 
                onChange={handleInputChange} 
                required 
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[var(--color-blue)] transition-all"
              />
            </div>
            <div>
              <label htmlFor="lastName" className="block text-sm font-medium text-gray-700 mb-2">
                Last name <span className="text-red-500">*</span>
              </label>
              <input 
                type="text" 
                id="lastName" 
                name="lastName" 
                value={formData.lastName} 
                onChange={handleInputChange} 
                required 
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[var(--color-blue)] transition-all"
              />
            </div>
          </div>
          
          <div>
            <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
              Email <span className="text-red-500">*</span>
            </label>
            <input 
              type="email" 
              id="email" 
              name="email" 
              value={formData.email} 
              onChange={handleInputChange} 
              required 
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[var(--color-blue)] transition-all"
            />
          </div>
          
          <div>
            <label htmlFor="company" className="block text-sm font-medium text-gray-700 mb-2">
              Company <span className="text-red-500">*</span>
            </label>
            <input 
              type="text" 
              id="company" 
              name="company" 
              value={formData.company} 
              onChange={handleInputChange} 
              required 
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[var(--color-blue)] transition-all"
            />
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-5">
            <div>
              <label htmlFor="organizationSize" className="block text-sm font-medium text-gray-700 mb-2">
                Company size <span className="text-red-500">*</span>
              </label>
              <select 
                id="organizationSize" 
                name="organizationSize" 
                value={formData.organizationSize} 
                onChange={handleInputChange} 
                required 
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[var(--color-blue)] transition-all bg-white"
              >
                <option value="">Choose size</option>
                <option value="startup">Startup (1-50)</option>
                <option value="smb">SMB (51-500)</option>
                <option value="enterprise">Enterprise (500+)</option>
                <option value="government">Government</option>
              </select>
            </div>
            <div>
              <label htmlFor="industry" className="block text-sm font-medium text-gray-700 mb-2">
                Company industry <span className="text-red-500">*</span>
              </label>
              <select 
                id="industry" 
                name="industry" 
                value={formData.industry} 
                onChange={handleInputChange} 
                required 
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[var(--color-blue)] transition-all bg-white"
              >
                <option value="">Choose industry</option>
                <option value="technology">Technology</option>
                <option value="finance">Finance</option>
                <option value="healthcare">Healthcare</option>
                <option value="manufacturing">Manufacturing</option>
                <option value="retail">Retail</option>
                <option value="education">Education</option>
                <option value="government">Government</option>
                <option value="other">Other</option>
              </select>
            </div>
          </div>
          
          <button
            type="submit"
            disabled={isSubmitting}
            className="w-full bg-[var(--color-blue)] hover:bg-[var(--color-blue-secondary)] disabled:bg-gray-400 text-white px-8 py-4 rounded-lg font-bold text-lg transition-all duration-300 shadow-lg hover:shadow-xl flex items-center justify-center gap-2"
          >
            {isSubmitting ? (
              <>
                <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                Submitting...
              </>
            ) : (
              'Submit Inquiry'
            )}
          </button>
        </form>
      </div>
    </div>
  );
}
