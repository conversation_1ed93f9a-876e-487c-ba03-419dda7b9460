{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/src/app/common/components/BreadcrumbNavigation.jsx"], "sourcesContent": ["\"use client\";\r\nimport React, { useState } from 'react';\r\nimport Link from 'next/link';\r\nimport { ChevronRight, Home, FileText, Users, Settings, MapPin, Shield, Code, Smartphone, Globe, DollarSign, Building, Phone, Download } from 'lucide-react';\r\nimport { motion } from 'framer-motion';\r\n\r\n/**\r\n * Truncated Text Component for Mobile Breadcrumbs\r\n * Shows truncated text on mobile with click to expand functionality\r\n */\r\nconst TruncatedText = ({ text, maxLength = 25, onExpansionChange }) => {\r\n  const [isExpanded, setIsExpanded] = useState(false);\r\n\r\n  const handleToggle = () => {\r\n    const newExpanded = !isExpanded;\r\n    setIsExpanded(newExpanded);\r\n    if (onExpansionChange) {\r\n      onExpansionChange(newExpanded);\r\n    }\r\n  };\r\n\r\n  if (text.length <= maxLength) {\r\n    return <span>{text}</span>;\r\n  }\r\n\r\n  return (\r\n    <span\r\n      className=\"cursor-pointer\"\r\n      onClick={handleToggle}\r\n      title={isExpanded ? \"Click to collapse\" : \"Click to expand\"}\r\n    >\r\n      <span className=\"sm:hidden\">\r\n        {isExpanded ? text : `${text.substring(0, maxLength)}...`}\r\n      </span>\r\n      <span className=\"hidden sm:inline\">\r\n        {text}\r\n      </span>\r\n    </span>\r\n  );\r\n};\r\n\r\n/**\r\n * Structured Data Component for SEO\r\n */\r\nconst BreadcrumbStructuredData = ({ items }) => {\r\n  const structuredData = {\r\n    \"@context\": \"https://schema.org\",\r\n    \"@type\": \"BreadcrumbList\",\r\n    \"itemListElement\": items.map((item, index) => ({\r\n      \"@type\": \"ListItem\",\r\n      \"position\": index + 1,\r\n      \"name\": item.name,\r\n      \"item\": item.current ? undefined : `https://securitylit.com${item.url}`\r\n    }))\r\n  };\r\n\r\n  return (\r\n    <script\r\n      type=\"application/ld+json\"\r\n      dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}\r\n    />\r\n  );\r\n};\r\n\r\n/**\r\n * Universal Breadcrumb Navigation Component with SEO structured data\r\n * Provides clear navigation hierarchy and improves SEO across all pages\r\n */\r\nconst BreadcrumbNavigation = ({ items, className = \"\" }) => {\r\n  const [hasExpandedText, setHasExpandedText] = useState(false);\r\n\r\n  // Check if this is a blog page for conditional padding\r\n  const isBlogPage = items?.some(item =>\r\n    item.url?.includes('/Blogs') ||\r\n    item.iconKey === 'blogs' ||\r\n    className?.includes('blog')\r\n  );\r\n\r\n  return (\r\n    <>\r\n      {/* Structured Data for SEO */}\r\n      <BreadcrumbStructuredData items={items} />\r\n      \r\n      {/* Simple Breadcrumb Navigation */}\r\n      <nav\r\n        className={`bg-transparent py-2 sm:py-3 ${\r\n          isBlogPage\r\n            ? hasExpandedText\r\n              ? 'pb-6 sm:pb-4'\r\n              : 'pb-3 sm:pb-4'\r\n            : 'pb-2 sm:pb-3'\r\n        } ${className}`}\r\n        aria-label=\"Breadcrumb navigation\"\r\n      >\r\n        <div className=\"max-w-7xl mx-auto px-4\">\r\n          <motion.ol\r\n            className={`flex items-center space-x-1 text-xs sm:text-sm ${\r\n              className?.includes('text-white')\r\n                ? 'text-white'\r\n                : className?.includes('text-blue-600')\r\n                ? 'text-blue-600'\r\n                : 'text-gray-600'\r\n            }`}\r\n            initial={{ opacity: 0 }}\r\n            animate={{ opacity: 1 }}\r\n            transition={{ duration: 0.4 }}\r\n          >\r\n            {items.map((item, index) => (\r\n              <motion.li\r\n                key={index}\r\n                className=\"flex items-center\"\r\n                initial={{ opacity: 0 }}\r\n                animate={{ opacity: 1 }}\r\n                transition={{ duration: 0.3, delay: index * 0.1 }}\r\n              >\r\n                {index > 0 && (\r\n                  <ChevronRight\r\n                    className={`w-3 h-3 mx-1 ${\r\n                      className?.includes('text-white')\r\n                        ? 'text-white/70'\r\n                        : className?.includes('text-blue-600')\r\n                        ? 'text-blue-400'\r\n                        : 'text-gray-400'\r\n                    }`}\r\n                    aria-hidden=\"true\"\r\n                  />\r\n                )}\r\n\r\n                {item.current ? (\r\n                  <span\r\n                    className={`font-medium flex items-center gap-1 ${\r\n                      className?.includes('text-white')\r\n                        ? 'text-white'\r\n                        : className?.includes('text-blue-600')\r\n                        ? 'text-blue-600'\r\n                        : 'text-gray-900'\r\n                    }`}\r\n                    aria-current=\"page\"\r\n                    title={item.description}\r\n                  >\r\n                    {item.iconKey === 'home' && (\r\n                      <Home className={`w-3 h-3 ${\r\n                        className?.includes('text-white')\r\n                          ? 'text-white'\r\n                          : className?.includes('text-blue-600')\r\n                          ? 'text-blue-600'\r\n                          : 'text-blue-600'\r\n                      }`} />\r\n                    )}\r\n                    {item.iconKey === 'blogs' && (\r\n                      <FileText className={`w-3 h-3 ${\r\n                        className?.includes('text-white')\r\n                          ? 'text-white'\r\n                          : className?.includes('text-blue-600')\r\n                          ? 'text-blue-600'\r\n                          : 'text-blue-600'\r\n                      }`} />\r\n                    )}\r\n                    {item.iconKey === 'contact-us' && (\r\n                      <Phone className={`w-3 h-3 ${\r\n                        className?.includes('text-white')\r\n                          ? 'text-white'\r\n                          : className?.includes('text-blue-600')\r\n                          ? 'text-blue-600'\r\n                          : 'text-blue-600'\r\n                      }`} />\r\n                    )}\r\n                    <TruncatedText\r\n                      text={item.name}\r\n                      onExpansionChange={setHasExpandedText}\r\n                    />\r\n                  </span>\r\n                ) : (\r\n                  <Link\r\n                    href={item.url}\r\n                    className={`transition-colors duration-200 flex items-center gap-1 hover:underline ${\r\n                      className?.includes('text-white')\r\n                        ? 'text-white hover:text-white/80'\r\n                        : className?.includes('text-blue-600')\r\n                        ? 'text-blue-600 hover:text-blue-800'\r\n                        : 'text-blue-600 hover:text-blue-800'\r\n                    }`}\r\n                    title={item.description}\r\n                  >\r\n                    {item.iconKey === 'home' && (\r\n                      <Home className={`w-3 h-3 ${\r\n                        className?.includes('text-white')\r\n                          ? 'text-white'\r\n                          : className?.includes('text-blue-600')\r\n                          ? 'text-blue-600'\r\n                          : 'text-current'\r\n                      }`} />\r\n                    )}\r\n                    {item.iconKey === 'blogs' && (\r\n                      <FileText className={`w-3 h-3 ${\r\n                        className?.includes('text-white')\r\n                          ? 'text-white'\r\n                          : className?.includes('text-blue-600')\r\n                          ? 'text-blue-600'\r\n                          : 'text-current'\r\n                      }`} />\r\n                    )}\r\n                    {item.iconKey === 'contact-us' && (\r\n                      <Phone className={`w-3 h-3 ${\r\n                        className?.includes('text-white')\r\n                          ? 'text-white'\r\n                          : className?.includes('text-blue-600')\r\n                          ? 'text-blue-600'\r\n                          : 'text-current'\r\n                      }`} />\r\n                    )}\r\n                    <TruncatedText\r\n                      text={item.name}\r\n                      onExpansionChange={setHasExpandedText}\r\n                    />\r\n                  </Link>\r\n                )}\r\n              </motion.li>\r\n            ))}\r\n          </motion.ol>\r\n        </div>\r\n      </nav>\r\n    </>\r\n  );\r\n};\r\n\r\n// Client-side helper function for generating breadcrumb items\r\nexport const generateBreadcrumbs = (pageType, params = {}) => {\r\n  const baseBreadcrumb = {\r\n    name: \"Home\",\r\n    url: \"/\",\r\n    iconKey: \"home\",\r\n    description: \"Return to homepage\"\r\n  };\r\n\r\n  const breadcrumbConfigs = {\r\n    blog: (slug) => [\r\n      baseBreadcrumb,\r\n      {\r\n        name: \"Blog\",\r\n        url: \"/Blogs\",\r\n        iconKey: \"blogs\",\r\n        description: \"View all blog posts\"\r\n      },\r\n      {\r\n        name: params.title || \"Blog Post\",\r\n        url: `/Blogs/${slug}`,\r\n        current: true,\r\n        description: `${params.title || 'Blog post'} - Cybersecurity insights and penetration testing guidance`\r\n      }\r\n    ],\r\n    \r\n    service: (service) => [\r\n      baseBreadcrumb,\r\n      {\r\n        name: \"Services\",\r\n        url: \"/Services\",\r\n        iconKey: \"services\",\r\n        description: \"View all cybersecurity services\"\r\n      },\r\n      {\r\n        name: params.title || service,\r\n        url: `/Services/${service}`,\r\n        current: true,\r\n        iconKey: service,\r\n        description: `${params.title || service} penetration testing services`\r\n      }\r\n    ],\r\n\r\n    location: (country) => [\r\n      baseBreadcrumb,\r\n      {\r\n        name: \"Locations\",\r\n        url: \"/Locations\",\r\n        iconKey: \"locations\",\r\n        description: \"View all locations\"\r\n      },\r\n      {\r\n        name: params.countryName || country,\r\n        url: `/Locations/${country}`,\r\n        current: true,\r\n        description: `${params.countryName || country} cybersecurity services`\r\n      }\r\n    ],\r\n\r\n    company: (page) => [\r\n      baseBreadcrumb,\r\n      {\r\n        name: \"Company\",\r\n        url: \"/Company\",\r\n        iconKey: \"company\",\r\n        description: \"Learn about SecurityLit\"\r\n      },\r\n      {\r\n        name: params.title || page,\r\n        url: `/Company/${page}`,\r\n        current: true,\r\n        description: params.description || `${page} information`\r\n      }\r\n    ],\r\n\r\n    'company-size': (size) => [\r\n      baseBreadcrumb,\r\n      {\r\n        name: \"Company Size\",\r\n        url: \"/Company-size\",\r\n        iconKey: \"company\",\r\n        description: \"Solutions for different company sizes\"\r\n      },\r\n      {\r\n        name: params.title || size,\r\n        url: `/Company-size/${size}`,\r\n        current: true,\r\n        description: params.description || `${size} cybersecurity solutions`\r\n      }\r\n    ],\r\n\r\n    industry: (industry) => [\r\n      baseBreadcrumb,\r\n      {\r\n        name: \"Industries\",\r\n        url: \"/Industries\",\r\n        iconKey: \"company\",\r\n        description: \"Industry-specific cybersecurity solutions\"\r\n      },\r\n      {\r\n        name: params.title || industry,\r\n        url: `/Industries/${industry}`,\r\n        current: true,\r\n        description: params.description || `${industry} cybersecurity solutions`\r\n      }\r\n    ],\r\n\r\n    product: (product) => [\r\n      baseBreadcrumb,\r\n      {\r\n        name: \"Product\",\r\n        url: \"/Product\",\r\n        iconKey: \"product\",\r\n        description: \"Explore our cybersecurity products\"\r\n      },\r\n      {\r\n        name: params.title || product,\r\n        url: `/Product/${product}`,\r\n        current: true,\r\n        description: params.description || `${product} product information`\r\n      }\r\n    ],\r\n\r\n    simple: (pageName, url) => [\r\n      baseBreadcrumb,\r\n      {\r\n        name: pageName,\r\n        url: url,\r\n        current: true,\r\n        iconKey: params.iconKey,\r\n        description: params.description || `${pageName} page`\r\n      }\r\n    ]\r\n  };\r\n\r\n  return breadcrumbConfigs[pageType] || (() => [baseBreadcrumb]);\r\n};\r\n\r\nexport default BreadcrumbNavigation; "], "names": [], "mappings": ";;;;;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AAJA;;;;;;AAMA;;;CAGC,GACD,MAAM,gBAAgB,CAAC,EAAE,IAAI,EAAE,YAAY,EAAE,EAAE,iBAAiB,EAAE;IAChE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,eAAe;QACnB,MAAM,cAAc,CAAC;QACrB,cAAc;QACd,IAAI,mBAAmB;YACrB,kBAAkB;QACpB;IACF;IAEA,IAAI,KAAK,MAAM,IAAI,WAAW;QAC5B,qBAAO,8OAAC;sBAAM;;;;;;IAChB;IAEA,qBACE,8OAAC;QACC,WAAU;QACV,SAAS;QACT,OAAO,aAAa,sBAAsB;;0BAE1C,8OAAC;gBAAK,WAAU;0BACb,aAAa,OAAO,GAAG,KAAK,SAAS,CAAC,GAAG,WAAW,GAAG,CAAC;;;;;;0BAE3D,8OAAC;gBAAK,WAAU;0BACb;;;;;;;;;;;;AAIT;AAEA;;CAEC,GACD,MAAM,2BAA2B,CAAC,EAAE,KAAK,EAAE;IACzC,MAAM,iBAAiB;QACrB,YAAY;QACZ,SAAS;QACT,mBAAmB,MAAM,GAAG,CAAC,CAAC,MAAM,QAAU,CAAC;gBAC7C,SAAS;gBACT,YAAY,QAAQ;gBACpB,QAAQ,KAAK,IAAI;gBACjB,QAAQ,KAAK,OAAO,GAAG,YAAY,CAAC,uBAAuB,EAAE,KAAK,GAAG,EAAE;YACzE,CAAC;IACH;IAEA,qBACE,8OAAC;QACC,MAAK;QACL,yBAAyB;YAAE,QAAQ,KAAK,SAAS,CAAC;QAAgB;;;;;;AAGxE;AAEA;;;CAGC,GACD,MAAM,uBAAuB,CAAC,EAAE,KAAK,EAAE,YAAY,EAAE,EAAE;IACrD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvD,uDAAuD;IACvD,MAAM,aAAa,OAAO,KAAK,CAAA,OAC7B,KAAK,GAAG,EAAE,SAAS,aACnB,KAAK,OAAO,KAAK,WACjB,WAAW,SAAS;IAGtB,qBACE;;0BAEE,8OAAC;gBAAyB,OAAO;;;;;;0BAGjC,8OAAC;gBACC,WAAW,CAAC,4BAA4B,EACtC,aACI,kBACE,iBACA,iBACF,eACL,CAAC,EAAE,WAAW;gBACf,cAAW;0BAEX,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;wBACR,WAAW,CAAC,+CAA+C,EACzD,WAAW,SAAS,gBAChB,eACA,WAAW,SAAS,mBACpB,kBACA,iBACJ;wBACF,SAAS;4BAAE,SAAS;wBAAE;wBACtB,SAAS;4BAAE,SAAS;wBAAE;wBACtB,YAAY;4BAAE,UAAU;wBAAI;kCAE3B,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;gCAER,WAAU;gCACV,SAAS;oCAAE,SAAS;gCAAE;gCACtB,SAAS;oCAAE,SAAS;gCAAE;gCACtB,YAAY;oCAAE,UAAU;oCAAK,OAAO,QAAQ;gCAAI;;oCAE/C,QAAQ,mBACP,8OAAC,sNAAA,CAAA,eAAY;wCACX,WAAW,CAAC,aAAa,EACvB,WAAW,SAAS,gBAChB,kBACA,WAAW,SAAS,mBACpB,kBACA,iBACJ;wCACF,eAAY;;;;;;oCAIf,KAAK,OAAO,iBACX,8OAAC;wCACC,WAAW,CAAC,oCAAoC,EAC9C,WAAW,SAAS,gBAChB,eACA,WAAW,SAAS,mBACpB,kBACA,iBACJ;wCACF,gBAAa;wCACb,OAAO,KAAK,WAAW;;4CAEtB,KAAK,OAAO,KAAK,wBAChB,8OAAC,mMAAA,CAAA,OAAI;gDAAC,WAAW,CAAC,QAAQ,EACxB,WAAW,SAAS,gBAChB,eACA,WAAW,SAAS,mBACpB,kBACA,iBACJ;;;;;;4CAEH,KAAK,OAAO,KAAK,yBAChB,8OAAC,8MAAA,CAAA,WAAQ;gDAAC,WAAW,CAAC,QAAQ,EAC5B,WAAW,SAAS,gBAChB,eACA,WAAW,SAAS,mBACpB,kBACA,iBACJ;;;;;;4CAEH,KAAK,OAAO,KAAK,8BAChB,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAW,CAAC,QAAQ,EACzB,WAAW,SAAS,gBAChB,eACA,WAAW,SAAS,mBACpB,kBACA,iBACJ;;;;;;0DAEJ,8OAAC;gDACC,MAAM,KAAK,IAAI;gDACf,mBAAmB;;;;;;;;;;;iGAIvB,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAM,KAAK,GAAG;wCACd,WAAW,CAAC,uEAAuE,EACjF,WAAW,SAAS,gBAChB,mCACA,WAAW,SAAS,mBACpB,sCACA,qCACJ;wCACF,OAAO,KAAK,WAAW;;4CAEtB,KAAK,OAAO,KAAK,wBAChB,8OAAC,mMAAA,CAAA,OAAI;gDAAC,WAAW,CAAC,QAAQ,EACxB,WAAW,SAAS,gBAChB,eACA,WAAW,SAAS,mBACpB,kBACA,gBACJ;;;;;;4CAEH,KAAK,OAAO,KAAK,yBAChB,8OAAC,8MAAA,CAAA,WAAQ;gDAAC,WAAW,CAAC,QAAQ,EAC5B,WAAW,SAAS,gBAChB,eACA,WAAW,SAAS,mBACpB,kBACA,gBACJ;;;;;;4CAEH,KAAK,OAAO,KAAK,8BAChB,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAW,CAAC,QAAQ,EACzB,WAAW,SAAS,gBAChB,eACA,WAAW,SAAS,mBACpB,kBACA,gBACJ;;;;;;0DAEJ,8OAAC;gDACC,MAAM,KAAK,IAAI;gDACf,mBAAmB;;;;;;;;;;;;;+BAxGpB;;;;;;;;;;;;;;;;;;;;;;AAmHrB;AAGO,MAAM,sBAAsB,CAAC,UAAU,SAAS,CAAC,CAAC;IACvD,MAAM,iBAAiB;QACrB,MAAM;QACN,KAAK;QACL,SAAS;QACT,aAAa;IACf;IAEA,MAAM,oBAAoB;QACxB,MAAM,CAAC,OAAS;gBACd;gBACA;oBACE,MAAM;oBACN,KAAK;oBACL,SAAS;oBACT,aAAa;gBACf;gBACA;oBACE,MAAM,OAAO,KAAK,IAAI;oBACtB,KAAK,CAAC,OAAO,EAAE,MAAM;oBACrB,SAAS;oBACT,aAAa,GAAG,OAAO,KAAK,IAAI,YAAY,0DAA0D,CAAC;gBACzG;aACD;QAED,SAAS,CAAC,UAAY;gBACpB;gBACA;oBACE,MAAM;oBACN,KAAK;oBACL,SAAS;oBACT,aAAa;gBACf;gBACA;oBACE,MAAM,OAAO,KAAK,IAAI;oBACtB,KAAK,CAAC,UAAU,EAAE,SAAS;oBAC3B,SAAS;oBACT,SAAS;oBACT,aAAa,GAAG,OAAO,KAAK,IAAI,QAAQ,6BAA6B,CAAC;gBACxE;aACD;QAED,UAAU,CAAC,UAAY;gBACrB;gBACA;oBACE,MAAM;oBACN,KAAK;oBACL,SAAS;oBACT,aAAa;gBACf;gBACA;oBACE,MAAM,OAAO,WAAW,IAAI;oBAC5B,KAAK,CAAC,WAAW,EAAE,SAAS;oBAC5B,SAAS;oBACT,aAAa,GAAG,OAAO,WAAW,IAAI,QAAQ,uBAAuB,CAAC;gBACxE;aACD;QAED,SAAS,CAAC,OAAS;gBACjB;gBACA;oBACE,MAAM;oBACN,KAAK;oBACL,SAAS;oBACT,aAAa;gBACf;gBACA;oBACE,MAAM,OAAO,KAAK,IAAI;oBACtB,KAAK,CAAC,SAAS,EAAE,MAAM;oBACvB,SAAS;oBACT,aAAa,OAAO,WAAW,IAAI,GAAG,KAAK,YAAY,CAAC;gBAC1D;aACD;QAED,gBAAgB,CAAC,OAAS;gBACxB;gBACA;oBACE,MAAM;oBACN,KAAK;oBACL,SAAS;oBACT,aAAa;gBACf;gBACA;oBACE,MAAM,OAAO,KAAK,IAAI;oBACtB,KAAK,CAAC,cAAc,EAAE,MAAM;oBAC5B,SAAS;oBACT,aAAa,OAAO,WAAW,IAAI,GAAG,KAAK,wBAAwB,CAAC;gBACtE;aACD;QAED,UAAU,CAAC,WAAa;gBACtB;gBACA;oBACE,MAAM;oBACN,KAAK;oBACL,SAAS;oBACT,aAAa;gBACf;gBACA;oBACE,MAAM,OAAO,KAAK,IAAI;oBACtB,KAAK,CAAC,YAAY,EAAE,UAAU;oBAC9B,SAAS;oBACT,aAAa,OAAO,WAAW,IAAI,GAAG,SAAS,wBAAwB,CAAC;gBAC1E;aACD;QAED,SAAS,CAAC,UAAY;gBACpB;gBACA;oBACE,MAAM;oBACN,KAAK;oBACL,SAAS;oBACT,aAAa;gBACf;gBACA;oBACE,MAAM,OAAO,KAAK,IAAI;oBACtB,KAAK,CAAC,SAAS,EAAE,SAAS;oBAC1B,SAAS;oBACT,aAAa,OAAO,WAAW,IAAI,GAAG,QAAQ,oBAAoB,CAAC;gBACrE;aACD;QAED,QAAQ,CAAC,UAAU,MAAQ;gBACzB;gBACA;oBACE,MAAM;oBACN,KAAK;oBACL,SAAS;oBACT,SAAS,OAAO,OAAO;oBACvB,aAAa,OAAO,WAAW,IAAI,GAAG,SAAS,KAAK,CAAC;gBACvD;aACD;IACH;IAEA,OAAO,iBAAiB,CAAC,SAAS,IAAI,CAAC,IAAM;YAAC;SAAe;AAC/D;uCAEe", "debugId": null}}, {"offset": {"line": 386, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/src/app/news/components/NewsHero.jsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React from \"react\";\r\nimport { motion } from \"framer-motion\";\r\nimport { Newspaper, Award, ArrowRight } from \"lucide-react\";\r\nimport BreadcrumbNavigation from \"../../common/components/BreadcrumbNavigation\";\r\n\r\nexport default function NewsHero() {\r\n  const breadcrumbItems = [\r\n    {\r\n      name: \"Home\",\r\n      url: \"/\",\r\n      iconKey: \"home\",\r\n      description: \"Return to homepage\",\r\n    },\r\n    {\r\n      name: \"News\",\r\n      url: \"/news\",\r\n      current: true,\r\n      iconKey: \"newspaper\",\r\n      description: \"SecurityLit in the News\",\r\n    },\r\n  ];\r\n\r\n  return (\r\n    <section className=\"relative bg-gradient-to-br from-[var(--color-dark-blue)] to-[var(--color-blue)] min-h-[60vh] flex items-center overflow-hidden\">\r\n      {/* Background Pattern */}\r\n      <div className=\"absolute inset-0 bg-[url('/images/hexagon.svg')] bg-cover opacity-10\" />\r\n      {/* Remove blue overlay, keep dark blue as dominant */}\r\n      {/* <div className=\"absolute inset-0 bg-[var(--color-blue)]/80\" /> */}\r\n      <div className=\"absolute right-0 top-0 w-1/3 h-full bg-gradient-to-l from-white/10 to-transparent\" />\r\n\r\n      <div className=\"relative z-10 container mx-auto px-4 max-w-7xl w-full\">\r\n        <div className=\"pt-24 pb-16 flex flex-col items-center text-center\">\r\n          {/* Breadcrumb */}\r\n          <div className=\"mb-4 w-full flex justify-center\">\r\n            <BreadcrumbNavigation items={breadcrumbItems} className=\"text-white\" />\r\n          </div>\r\n\r\n          {/* Main Headline */}\r\n          <motion.h1\r\n            initial={{ opacity: 0, y: 20 }}\r\n            animate={{ opacity: 1, y: 0 }}\r\n            transition={{ duration: 0.8 }}\r\n            className=\"text-4xl lg:text-5xl font-bold text-white mb-4 leading-tight drop-shadow-lg\"\r\n          >\r\n            Security Lit in News\r\n          </motion.h1>\r\n\r\n          {/* Subheadline */}\r\n          <motion.h2\r\n            initial={{ opacity: 0, y: 20 }}\r\n            animate={{ opacity: 1, y: 0 }}\r\n            transition={{ duration: 0.8, delay: 0.2 }}\r\n            className=\"text-xl lg:text-2xl font-semibold text-white/90 mb-6\"\r\n          >\r\n            Recognition for our efforts to make the internet a safer place\r\n          </motion.h2>\r\n\r\n          {/* News Icon */}\r\n          <motion.div\r\n            initial={{ opacity: 0, scale: 0.8 }}\r\n            animate={{ opacity: 1, scale: 1 }}\r\n            transition={{ duration: 0.8, delay: 0.4 }}\r\n            className=\"flex items-center justify-center mb-6\"\r\n          >\r\n            <div className=\"w-20 h-20 bg-white/10 rounded-2xl flex items-center justify-center shadow-lg\">\r\n              <Newspaper className=\"w-12 h-12 text-white\" aria-label=\"News\" />\r\n            </div>\r\n          </motion.div>\r\n\r\n          {/* Optional: Featured In/As Seen On logos */}\r\n          {/* <div className=\"flex flex-wrap justify-center gap-6 mt-4\">\r\n            <img src=\"/images/news/csc_sponsor.png\" alt=\"CSC Sponsor\" className=\"h-10\" />\r\n            <img src=\"/images/news/ankita-feature-nzbusiness.png\" alt=\"NZ Business\" className=\"h-10\" />\r\n            ...\r\n          </div> */}\r\n        </div>\r\n      </div>\r\n    </section>\r\n  );\r\n} "], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAOe,SAAS;IACtB,MAAM,kBAAkB;QACtB;YACE,MAAM;YACN,KAAK;YACL,SAAS;YACT,aAAa;QACf;QACA;YACE,MAAM;YACN,KAAK;YACL,SAAS;YACT,SAAS;YACT,aAAa;QACf;KACD;IAED,qBACE,8OAAC;QAAQ,WAAU;;0BAEjB,8OAAC;gBAAI,WAAU;;;;;;0BAGf,8OAAC;gBAAI,WAAU;;;;;;0BAEf,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,2JAAA,CAAA,UAAoB;gCAAC,OAAO;gCAAiB,WAAU;;;;;;;;;;;sCAI1D,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;4BACR,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;4BAAI;4BAC5B,WAAU;sCACX;;;;;;sCAKD,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;4BACR,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,WAAU;sCACX;;;;;;sCAKD,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,OAAO;4BAAI;4BAClC,SAAS;gCAAE,SAAS;gCAAG,OAAO;4BAAE;4BAChC,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,WAAU;sCAEV,cAAA,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,4MAAA,CAAA,YAAS;oCAAC,WAAU;oCAAuB,cAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAcrE", "debugId": null}}, {"offset": {"line": 548, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/src/app/news/components/FeaturedInLogos.jsx"], "sourcesContent": ["import React from \"react\";\r\n\r\nconst logos = [\r\n  {\r\n    src: \"/images/news/csc_sponsor.png\",\r\n    alt: \"CSC Sponsor\",\r\n  },\r\n  {\r\n    src: \"/images/news/ankita-feature-nzbusiness.png\",\r\n    alt: \"NZ Business Feature\",\r\n  },\r\n  {\r\n    src: \"/images/news/reseller_news.jpeg\",\r\n    alt: \"Reseller News\",\r\n  },\r\n  {\r\n    src: \"/images/news/ankita_feature_wis.jpeg\",\r\n    alt: \"Security Magazine NZ\",\r\n  },\r\n  {\r\n    src: \"/images/news/wis_rising_star.png\",\r\n    alt: \"Women in Security Awards NZSM\",\r\n  },\r\n  {\r\n    src: \"/images/news/jozsef_digitrend.png\",\r\n    alt: \"Digitrend Feature\",\r\n  },\r\n  {\r\n    src: \"/images/news/top_women_in_malaysia.jpg\",\r\n    alt: \"Women in Security Malaysia\",\r\n  },\r\n  {\r\n    src: \"/images/news/securitylit_feature_wis.png\",\r\n    alt: \"Women in Security Magazine\",\r\n  },\r\n];\r\n\r\nexport default function FeaturedInLogos() {\r\n  return (\r\n    <section className=\"bg-white py-6 shadow-sm border-b border-gray-100\">\r\n      <div className=\"container mx-auto px-4 max-w-7xl flex flex-col items-center\">\r\n        <div className=\"flex items-center gap-3 mb-3\">\r\n          <span className=\"text-lg font-bold text-[var(--color-dark-blue)] tracking-wide uppercase\">Featured In</span>\r\n          <span className=\"w-8 h-1 bg-[var(--color-yellow)] rounded-full\" />\r\n        </div>\r\n        <div className=\"flex flex-wrap justify-center items-center gap-8 md:gap-12 w-full\">\r\n          {logos.map((logo, idx) => (\r\n            <img\r\n              key={idx}\r\n              src={logo.src}\r\n              alt={logo.alt}\r\n              className=\"h-10 md:h-12 max-w-[120px] object-contain grayscale hover:grayscale-0 transition duration-300\"\r\n              loading=\"lazy\"\r\n            />\r\n          ))}\r\n        </div>\r\n      </div>\r\n    </section>\r\n  );\r\n}"], "names": [], "mappings": ";;;;AAAA;;;AAEA,MAAM,QAAQ;IACZ;QACE,KAAK;QACL,KAAK;IACP;IACA;QACE,KAAK;QACL,KAAK;IACP;IACA;QACE,KAAK;QACL,KAAK;IACP;IACA;QACE,KAAK;QACL,KAAK;IACP;IACA;QACE,KAAK;QACL,KAAK;IACP;IACA;QACE,KAAK;QACL,KAAK;IACP;IACA;QACE,KAAK;QACL,KAAK;IACP;IACA;QACE,KAAK;QACL,KAAK;IACP;CACD;AAEc,SAAS;IACtB,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAK,WAAU;sCAA0E;;;;;;sCAC1F,8OAAC;4BAAK,WAAU;;;;;;;;;;;;8BAElB,8OAAC;oBAAI,WAAU;8BACZ,MAAM,GAAG,CAAC,CAAC,MAAM,oBAChB,8OAAC;4BAEC,KAAK,KAAK,GAAG;4BACb,KAAK,KAAK,GAAG;4BACb,WAAU;4BACV,SAAQ;2BAJH;;;;;;;;;;;;;;;;;;;;;AAWnB", "debugId": null}}, {"offset": {"line": 652, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/src/app/news/components/newsData.js"], "sourcesContent": ["export const newsItems = [\r\n  {\r\n    image: \"/images/news/csc_sponsor.png\",\r\n    alt: \"CSC Sponsor\",\r\n    headline: \"Security Lit’s contribution in New Zealand cyber security challenge 2021.\",\r\n    description: \"Recognized for supporting the next generation of cyber talent.\",\r\n    link: \"https://cybersecuritychallenge.org.nz/\",\r\n    outlet: \"CSC\",\r\n    date: \"2021\"\r\n  },\r\n  {\r\n    image: \"/images/news/ankita-feature-nzbusiness.png\",\r\n    alt: \"NZ business feature\",\r\n    headline: \"<PERSON><PERSON><PERSON> inspires and urges youth to take cybersecurity as a career path and give back to the society.\",\r\n    description: \"Featured in NZBusiness for inspiring youth in cybersecurity.\",\r\n    link: \"https://nzbusiness.co.nz/news-items/entrepreneur-inspires-youth-follow-cybersecurity-career-path\",\r\n    outlet: \"NZBusiness\",\r\n    date: \"2021\"\r\n  },\r\n  {\r\n    image: \"/images/news/reseller_news.jpeg\",\r\n    alt: \"Reseller NZ\",\r\n    headline: \"Reseller News congratulates <PERSON><PERSON><PERSON> on becoming a finalist in the Entrepreneur Category.\",\r\n    description: \"Finalist in Women in ICT Awards, Reseller News.\",\r\n    link: \"https://www.reseller.co.nz/article/688717/reseller-news-unveils-record-breaking-number-of-finalists-for-new-look-women-in-ict-awards/\",\r\n    outlet: \"Reseller News\",\r\n    date: \"2021\"\r\n  },\r\n  {\r\n    image: \"/images/news/ankita_feature_wis.jpeg\",\r\n    alt: \"Security Lit feature\",\r\n    headline: \"Story of the founding of Security Lit amidst a global pandemic.\",\r\n    description: \"Featured in Security Magazine NZ.\",\r\n    link: \"https://defsec.net.nz/2021/03/30/security-magazine-nz-april-2021/\",\r\n    outlet: \"Security Magazine NZ\",\r\n    date: \"2021\"\r\n  },\r\n  {\r\n    image: \"/images/news/wis_rising_star.png\",\r\n    alt: \"Rising star\",\r\n    headline: \"Ankita Dhakar winner of Women in Security Awards Aotearoa (NZSM) - Rising Star Category!\",\r\n    description: \"Winner of NZSM Rising Star Category.\",\r\n    link: \"https://defsec.net.nz/2020/12/03/december-2020-january-2021/\",\r\n    outlet: \"NZSM\",\r\n    date: \"2020\"\r\n  },\r\n  {\r\n    image: \"/images/news/jozsef_digitrend.png\",\r\n    alt: \"Joszef feature in digitrend\",\r\n    headline: \"An article featuring our head of cyber security, Jozsef Gacsal.\",\r\n    description: \"Featured in Digitrend.\",\r\n    link: \"https://digitrendi.hu/verbeli-multi-startuphoz-igazolt-gacsal-jozsef/\",\r\n    outlet: \"Digitrend\",\r\n    date: \"2021\"\r\n  },\r\n  {\r\n    image: \"/images/news/top_women_in_malaysia.jpg\",\r\n    alt: \"Ankita - Guest Speaker\",\r\n    headline: \"Ankita Dhakar shared the stage as a guest speaker with industry leaders from different parts of the world to encourage and support women in Security (Malaysia).\",\r\n    description: \"Guest speaker at Women in Security, Malaysia.\",\r\n    link: \"https://www.youtube.com/watch?v=iV-59sZ3y58\",\r\n    outlet: \"Women in Security Malaysia\",\r\n    date: \"2021\"\r\n  },\r\n  {\r\n    image: \"/images/news/securitylit_feature_wis.png\",\r\n    alt: \"Security Lit feature\",\r\n    headline: \"Security Lit Championing a Cyber-Safe World.\",\r\n    description: \"Featured in Women in Security Magazine.\",\r\n    link: \"https://womeninsecuritymagazine.com/issues/issue-02/\",\r\n    outlet: \"Women in Security Magazine\",\r\n    date: \"2021\"\r\n  }\r\n];"], "names": [], "mappings": ";;;AAAO,MAAM,YAAY;IACvB;QACE,OAAO;QACP,KAAK;QACL,UAAU;QACV,aAAa;QACb,MAAM;QACN,QAAQ;QACR,MAAM;IACR;IACA;QACE,OAAO;QACP,KAAK;QACL,UAAU;QACV,aAAa;QACb,MAAM;QACN,QAAQ;QACR,MAAM;IACR;IACA;QACE,OAAO;QACP,KAAK;QACL,UAAU;QACV,aAAa;QACb,MAAM;QACN,QAAQ;QACR,MAAM;IACR;IACA;QACE,OAAO;QACP,KAAK;QACL,UAAU;QACV,aAAa;QACb,MAAM;QACN,QAAQ;QACR,MAAM;IACR;IACA;QACE,OAAO;QACP,KAAK;QACL,UAAU;QACV,aAAa;QACb,MAAM;QACN,QAAQ;QACR,MAAM;IACR;IACA;QACE,OAAO;QACP,KAAK;QACL,UAAU;QACV,aAAa;QACb,MAAM;QACN,QAAQ;QACR,MAAM;IACR;IACA;QACE,OAAO;QACP,KAAK;QACL,UAAU;QACV,aAAa;QACb,MAAM;QACN,QAAQ;QACR,MAAM;IACR;IACA;QACE,OAAO;QACP,KAAK;QACL,UAAU;QACV,aAAa;QACb,MAAM;QACN,QAAQ;QACR,MAAM;IACR;CACD", "debugId": null}}, {"offset": {"line": 733, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/src/app/news/components/NewsGrid.jsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React from \"react\";\r\nimport { ArrowRight } from \"lucide-react\";\r\nimport { newsItems } from \"./newsData\";\r\n\r\nexport default function NewsGrid() {\r\n  return (\r\n    <section className=\"py-20 bg-white relative\">\r\n      <div className=\"container mx-auto px-4 max-w-7xl\">\r\n        <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-10\">\r\n          {newsItems.map((item, idx) => (\r\n            <article\r\n              key={idx}\r\n              className=\"group bg-white rounded-3xl shadow-[0_8px_30px_rgb(0,0,0,0.08)] hover:shadow-[0_20px_40px_rgb(0,0,0,0.12)] border border-gray-100 overflow-hidden flex flex-col transition-all duration-300 focus-within:ring-2 focus-within:ring-[var(--color-blue)]\"\r\n              tabIndex={0}\r\n            >\r\n              <div className=\"relative h-56 bg-gray-100 overflow-hidden flex items-center justify-center\">\r\n                <img\r\n                  src={item.image}\r\n                  alt={item.alt}\r\n                  className=\"object-contain h-full w-full transition-transform duration-300 group-hover:scale-105\"\r\n                  loading=\"lazy\"\r\n                />\r\n                <span className=\"absolute top-4 left-4 bg-gradient-to-r from-[var(--color-blue)] to-[var(--color-blue-secondary)] text-white text-xs font-semibold px-3 py-1 rounded-full shadow-lg\">\r\n                  {item.outlet}\r\n                </span>\r\n              </div>\r\n              <div className=\"flex-1 flex flex-col p-6\">\r\n                <h3 className=\"text-lg font-bold text-[var(--color-dark-blue)] mb-2 line-clamp-2\">\r\n                  {item.headline}\r\n                </h3>\r\n                <p className=\"text-[var(--foreground-secondary)] text-sm mb-4 line-clamp-3\">\r\n                  {item.description}\r\n                </p>\r\n                <div className=\"mt-auto flex items-center justify-between\">\r\n                  <span className=\"text-xs text-gray-400\">{item.date}</span>\r\n                  <a\r\n                    href={item.link}\r\n                    target=\"_blank\"\r\n                    rel=\"noopener noreferrer\"\r\n                    className=\"inline-flex items-center gap-2 text-[var(--color-blue)] font-semibold hover:underline focus:underline focus:outline-none\"\r\n                    aria-label={`Learn more about: ${item.headline}`}\r\n                  >\r\n                    Learn more\r\n                    <ArrowRight className=\"w-4 h-4\" />\r\n                  </a>\r\n                </div>\r\n              </div>\r\n            </article>\r\n          ))}\r\n        </div>\r\n      </div>\r\n    </section>\r\n  );\r\n} "], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAMe,SAAS;IACtB,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;0BACZ,4IAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,MAAM,oBACpB,8OAAC;wBAEC,WAAU;wBACV,UAAU;;0CAEV,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,KAAK,KAAK,KAAK;wCACf,KAAK,KAAK,GAAG;wCACb,WAAU;wCACV,SAAQ;;;;;;kDAEV,8OAAC;wCAAK,WAAU;kDACb,KAAK,MAAM;;;;;;;;;;;;0CAGhB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDACX,KAAK,QAAQ;;;;;;kDAEhB,8OAAC;wCAAE,WAAU;kDACV,KAAK,WAAW;;;;;;kDAEnB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAU;0DAAyB,KAAK,IAAI;;;;;;0DAClD,8OAAC;gDACC,MAAM,KAAK,IAAI;gDACf,QAAO;gDACP,KAAI;gDACJ,WAAU;gDACV,cAAY,CAAC,kBAAkB,EAAE,KAAK,QAAQ,EAAE;;oDACjD;kEAEC,8OAAC,kNAAA,CAAA,aAAU;wDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;uBAhCvB;;;;;;;;;;;;;;;;;;;;AA0CnB", "debugId": null}}, {"offset": {"line": 872, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/src/app/news/components/NewsCTA.jsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React from 'react';\r\nimport { motion } from 'framer-motion';\r\nimport { ArrowRight, Globe, Award, Users } from 'lucide-react';\r\n\r\nexport default function NewsCTA() {\r\n  return (\r\n    <section className=\"py-24 bg-gradient-to-br from-[var(--color-dark-blue)] via-[var(--color-dark-blue-hover)] to-[var(--color-dark-blue-bg)] relative overflow-hidden\">\r\n      {/* Background Elements */}\r\n      <div className=\"absolute inset-0\">\r\n        <div className=\"absolute top-0 left-0 w-full h-full bg-[url('data:image/svg+xml,%3Csvg%20width%3D%2260%22%20height%3D%2260%22%20viewBox%3D%220%200%2060%2060%22%20xmlns%3D%22http%3A//www.w3.org/2000/svg%22%3E%3Cg%20fill%3D%22none%22%20fill-rule%3D%22evenodd%22%3E%3Cg%20fill%3D%22%23ffffff%22%20fill-opacity%3D%220.03%22%3E%3Ccircle%20cx%3D%2230%22%20cy%3D%2230%22%20r%3D%221%22/%3E%3C/g%3E%3C/g%3E%3C/svg%3E')] opacity-50\"></div>\r\n        <div className=\"absolute top-20 left-10 w-72 h-72 bg-[var(--color-yellow)]/20 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob\"></div>\r\n        <div className=\"absolute bottom-20 right-10 w-72 h-72 bg-[var(--color-blue)]/20 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob animation-delay-2000\"></div>\r\n      </div>\r\n\r\n      <div className=\"relative z-10 container mx-auto px-4 max-w-7xl\">\r\n        <div className=\"text-center\">\r\n          {/* Header */}\r\n          <motion.div \r\n            initial={{ opacity: 0, y: 30 }}\r\n            whileInView={{ opacity: 1, y: 0 }}\r\n            transition={{ duration: 0.8 }}\r\n            viewport={{ once: true }}\r\n            className=\"mb-16\"\r\n          >\r\n            <motion.div \r\n              initial={{ opacity: 0, scale: 0.8 }}\r\n              whileInView={{ opacity: 1, scale: 1 }}\r\n              transition={{ delay: 0.2, duration: 0.6 }}\r\n              viewport={{ once: true }}\r\n              className=\"inline-flex items-center bg-[var(--color-yellow)]/20 px-4 py-2 rounded-full mb-6\"\r\n            >\r\n              <Globe className=\"w-4 h-4 text-[var(--color-yellow)] mr-2\" />\r\n              <span className=\"text-sm font-medium text-[var(--color-yellow)]\">Stay Connected</span>\r\n            </motion.div>\r\n            \r\n            <motion.h2 \r\n              initial={{ opacity: 0, y: 20 }}\r\n              whileInView={{ opacity: 1, y: 0 }}\r\n              transition={{ delay: 0.4, duration: 0.6 }}\r\n              viewport={{ once: true }}\r\n              className=\"text-4xl lg:text-5xl font-bold text-white mb-6\"\r\n            >\r\n              Join Our Journey\r\n              <span className=\"block bg-gradient-to-r from-[var(--color-yellow)] to-[var(--color-yellow-hover)] bg-clip-text text-transparent\">\r\n                Towards a Safer Digital World\r\n              </span>\r\n            </motion.h2>\r\n            \r\n            <motion.p \r\n              initial={{ opacity: 0, y: 20 }}\r\n              whileInView={{ opacity: 1, y: 0 }}\r\n              transition={{ delay: 0.6, duration: 0.6 }}\r\n              viewport={{ once: true }}\r\n              className=\"text-xl text-white/80 max-w-4xl mx-auto leading-relaxed\"\r\n            >\r\n              Be part of our mission to make cybersecurity education accessible to everyone. Follow our journey, stay updated with our latest achievements, and join our community of security professionals.\r\n            </motion.p>\r\n          </motion.div>\r\n\r\n          {/* CTA Cards */}\r\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8 mb-16\">\r\n            <motion.div\r\n              initial={{ opacity: 0, y: 30 }}\r\n              whileInView={{ opacity: 1, y: 0 }}\r\n              transition={{ delay: 0.8, duration: 0.6 }}\r\n              viewport={{ once: true }}\r\n              whileHover={{ y: -5 }}\r\n              className=\"group\"\r\n            >\r\n              <div className=\"bg-white/10 backdrop-blur-xl rounded-3xl p-8 border border-white/20 shadow-2xl hover:shadow-3xl transition-all duration-300\">\r\n                <div className=\"w-16 h-16 bg-gradient-to-br from-[var(--color-yellow)] to-[var(--color-yellow-hover)] rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform\">\r\n                  <Globe className=\"w-8 h-8 text-[var(--color-dark-blue)]\" />\r\n                </div>\r\n                <h3 className=\"text-2xl font-bold text-white mb-4\">Follow Our Journey</h3>\r\n                <p className=\"text-white/70 mb-6 leading-relaxed\">\r\n                  Stay updated with our latest achievements, media coverage, and contributions to the cybersecurity community.\r\n                </p>\r\n                <motion.button\r\n                  whileHover={{ scale: 1.05 }}\r\n                  whileTap={{ scale: 0.95 }}\r\n                  className=\"w-full bg-gradient-to-r from-[var(--color-yellow)] to-[var(--color-yellow-hover)] text-[var(--color-dark-blue)] py-3 px-6 rounded-xl font-semibold shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center gap-2\"\r\n                >\r\n                  Follow Us\r\n                  <ArrowRight className=\"w-4 h-4\" />\r\n                </motion.button>\r\n              </div>\r\n            </motion.div>\r\n\r\n            <motion.div\r\n              initial={{ opacity: 0, y: 30 }}\r\n              whileInView={{ opacity: 1, y: 0 }}\r\n              transition={{ delay: 1, duration: 0.6 }}\r\n              viewport={{ once: true }}\r\n              whileHover={{ y: -5 }}\r\n              className=\"group\"\r\n            >\r\n              <div className=\"bg-white/10 backdrop-blur-xl rounded-3xl p-8 border border-white/20 shadow-2xl hover:shadow-3xl transition-all duration-300\">\r\n                <div className=\"w-16 h-16 bg-gradient-to-br from-[var(--color-blue)] to-[var(--color-blue-secondary)] rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform\">\r\n                  <Award className=\"w-8 h-8 text-white\" />\r\n                </div>\r\n                <h3 className=\"text-2xl font-bold text-white mb-4\">Join Our Training</h3>\r\n                <p className=\"text-white/70 mb-6 leading-relaxed\">\r\n                  Start your cybersecurity journey with our comprehensive training programs designed for all skill levels.\r\n                </p>\r\n                <motion.a\r\n                  href=\"/CybersecurityTraining\"\r\n                  whileHover={{ scale: 1.05 }}\r\n                  whileTap={{ scale: 0.95 }}\r\n                  className=\"w-full bg-gradient-to-r from-[var(--color-blue)] to-[var(--color-blue-secondary)] text-white py-3 px-6 rounded-xl font-semibold shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center gap-2\"\r\n                >\r\n                  Start Training\r\n                  <ArrowRight className=\"w-4 h-4\" />\r\n                </motion.a>\r\n              </div>\r\n            </motion.div>\r\n\r\n            <motion.div\r\n              initial={{ opacity: 0, y: 30 }}\r\n              whileInView={{ opacity: 1, y: 0 }}\r\n              transition={{ delay: 1.2, duration: 0.6 }}\r\n              viewport={{ once: true }}\r\n              whileHover={{ y: -5 }}\r\n              className=\"group\"\r\n            >\r\n              <div className=\"bg-white/10 backdrop-blur-xl rounded-3xl p-8 border border-white/20 shadow-2xl hover:shadow-3xl transition-all duration-300\">\r\n                <div className=\"w-16 h-16 bg-gradient-to-br from-[var(--color-dark-blue)] to-[var(--color-dark-blue-hover)] rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform\">\r\n                  <Users className=\"w-8 h-8 text-white\" />\r\n                </div>\r\n                <h3 className=\"text-2xl font-bold text-white mb-4\">Connect With Us</h3>\r\n                <p className=\"text-white/70 mb-6 leading-relaxed\">\r\n                  Join our community of cybersecurity professionals and stay connected with industry leaders and experts.\r\n                </p>\r\n                <motion.button\r\n                  whileHover={{ scale: 1.05 }}\r\n                  whileTap={{ scale: 0.95 }}\r\n                  className=\"w-full bg-gradient-to-r from-[var(--color-dark-blue)] to-[var(--color-dark-blue-hover)] text-white py-3 px-6 rounded-xl font-semibold shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center gap-2\"\r\n                >\r\n                  Get Connected\r\n                  <ArrowRight className=\"w-4 h-4\" />\r\n                </motion.button>\r\n              </div>\r\n            </motion.div>\r\n          </div>\r\n\r\n          {/* Bottom CTA */}\r\n          <motion.div\r\n            initial={{ opacity: 0, y: 30 }}\r\n            whileInView={{ opacity: 1, y: 0 }}\r\n            transition={{ delay: 1.4, duration: 0.6 }}\r\n            viewport={{ once: true }}\r\n            className=\"text-center\"\r\n          >\r\n            <motion.button\r\n              whileHover={{ scale: 1.05 }}\r\n              whileTap={{ scale: 0.95 }}\r\n              className=\"bg-gradient-to-r from-[var(--color-yellow)] to-[var(--color-yellow-hover)] text-[var(--color-dark-blue)] px-12 py-4 rounded-2xl font-bold text-xl shadow-2xl hover:shadow-3xl transition-all duration-300 flex items-center gap-3 mx-auto\"\r\n            >\r\n              <Globe className=\"w-6 h-6\" />\r\n              Explore Our Full Story\r\n              <ArrowRight className=\"w-6 h-6\" />\r\n            </motion.button>\r\n            <p className=\"text-white/70 mt-6 text-lg\">\r\n              Discover more about our mission, achievements, and commitment to cybersecurity education.\r\n            </p>\r\n          </motion.div>\r\n        </div>\r\n      </div>\r\n    </section>\r\n  );\r\n} "], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAJA;;;;;AAMe,SAAS;IACtB,qBACE,8OAAC;QAAQ,WAAU;;0BAEjB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;;;;;;;0BAGjB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;4BAAI;4BAC5B,UAAU;gCAAE,MAAM;4BAAK;4BACvB,WAAU;;8CAEV,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,OAAO;oCAAI;oCAClC,aAAa;wCAAE,SAAS;wCAAG,OAAO;oCAAE;oCACpC,YAAY;wCAAE,OAAO;wCAAK,UAAU;oCAAI;oCACxC,UAAU;wCAAE,MAAM;oCAAK;oCACvB,WAAU;;sDAEV,8OAAC,oMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;sDACjB,8OAAC;4CAAK,WAAU;sDAAiD;;;;;;;;;;;;8CAGnE,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;oCACR,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,aAAa;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAChC,YAAY;wCAAE,OAAO;wCAAK,UAAU;oCAAI;oCACxC,UAAU;wCAAE,MAAM;oCAAK;oCACvB,WAAU;;wCACX;sDAEC,8OAAC;4CAAK,WAAU;sDAAiH;;;;;;;;;;;;8CAKnI,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;oCACP,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,aAAa;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAChC,YAAY;wCAAE,OAAO;wCAAK,UAAU;oCAAI;oCACxC,UAAU;wCAAE,MAAM;oCAAK;oCACvB,WAAU;8CACX;;;;;;;;;;;;sCAMH,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,aAAa;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAChC,YAAY;wCAAE,OAAO;wCAAK,UAAU;oCAAI;oCACxC,UAAU;wCAAE,MAAM;oCAAK;oCACvB,YAAY;wCAAE,GAAG,CAAC;oCAAE;oCACpB,WAAU;8CAEV,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;;;;;;0DAEnB,8OAAC;gDAAG,WAAU;0DAAqC;;;;;;0DACnD,8OAAC;gDAAE,WAAU;0DAAqC;;;;;;0DAGlD,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;gDACZ,YAAY;oDAAE,OAAO;gDAAK;gDAC1B,UAAU;oDAAE,OAAO;gDAAK;gDACxB,WAAU;;oDACX;kEAEC,8OAAC,kNAAA,CAAA,aAAU;wDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;8CAK5B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,aAAa;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAChC,YAAY;wCAAE,OAAO;wCAAG,UAAU;oCAAI;oCACtC,UAAU;wCAAE,MAAM;oCAAK;oCACvB,YAAY;wCAAE,GAAG,CAAC;oCAAE;oCACpB,WAAU;8CAEV,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;;;;;;0DAEnB,8OAAC;gDAAG,WAAU;0DAAqC;;;;;;0DACnD,8OAAC;gDAAE,WAAU;0DAAqC;;;;;;0DAGlD,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;gDACP,MAAK;gDACL,YAAY;oDAAE,OAAO;gDAAK;gDAC1B,UAAU;oDAAE,OAAO;gDAAK;gDACxB,WAAU;;oDACX;kEAEC,8OAAC,kNAAA,CAAA,aAAU;wDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;8CAK5B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,aAAa;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAChC,YAAY;wCAAE,OAAO;wCAAK,UAAU;oCAAI;oCACxC,UAAU;wCAAE,MAAM;oCAAK;oCACvB,YAAY;wCAAE,GAAG,CAAC;oCAAE;oCACpB,WAAU;8CAEV,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;;;;;;0DAEnB,8OAAC;gDAAG,WAAU;0DAAqC;;;;;;0DACnD,8OAAC;gDAAE,WAAU;0DAAqC;;;;;;0DAGlD,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;gDACZ,YAAY;oDAAE,OAAO;gDAAK;gDAC1B,UAAU;oDAAE,OAAO;gDAAK;gDACxB,WAAU;;oDACX;kEAEC,8OAAC,kNAAA,CAAA,aAAU;wDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAO9B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,OAAO;gCAAK,UAAU;4BAAI;4BACxC,UAAU;gCAAE,MAAM;4BAAK;4BACvB,WAAU;;8CAEV,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;oCACZ,YAAY;wCAAE,OAAO;oCAAK;oCAC1B,UAAU;wCAAE,OAAO;oCAAK;oCACxB,WAAU;;sDAEV,8OAAC,oMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;wCAAY;sDAE7B,8OAAC,kNAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;;;;;;;8CAExB,8OAAC;oCAAE,WAAU;8CAA6B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQtD", "debugId": null}}, {"offset": {"line": 1399, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/src/app/news/page.jsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React from 'react';\r\nimport NewsHero from './components/NewsHero';\r\nimport FeaturedInLogos from './components/FeaturedInLogos';\r\nimport NewsGrid from './components/NewsGrid';\r\nimport NewsCTA from './components/NewsCTA';\r\n\r\nexport default function NewsPage() {\r\n  return (\r\n    <div className=\"min-h-screen bg-white\">\r\n      {/* Hero Section */}\r\n      <NewsHero />\r\n      {/* Featured In Logos */}\r\n      <FeaturedInLogos />\r\n      {/* News Grid */}\r\n      <NewsGrid />\r\n      {/* CTA Section */}\r\n      <NewsCTA />\r\n    </div>\r\n  );\r\n} "], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AANA;;;;;;;AAQe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC,6IAAA,CAAA,UAAQ;;;;;0BAET,8OAAC,oJAAA,CAAA,UAAe;;;;;0BAEhB,8OAAC,6IAAA,CAAA,UAAQ;;;;;0BAET,8OAAC,4IAAA,CAAA,UAAO;;;;;;;;;;;AAGd", "debugId": null}}]}