"use client";

import React, { useState } from 'react';
import { Mail, Phone, MapPin, Clock } from 'lucide-react';

export default function PricingContactForm() {
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    company: '',
    jobTitle: '',
    organizationSize: '',
    industry: '',
    securityService: '',
    timeline: '',
    budget: '',
    message: ''
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [apiError, setApiError] = useState("");
  const [success, setSuccess] = useState("");

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (isSubmitting) return;
    setIsSubmitting(true);
    setApiError("");
    setSuccess("");

    try {
      const response = await fetch("/api/submissions", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          name: `${formData.firstName} ${formData.lastName}`,
          email: formData.email,
          company: formData.company,
          jobTitle: formData.jobTitle,
          organizationSize: formData.organizationSize,
          industry: formData.industry,
          securityService: formData.securityService,
          timeline: formData.timeline,
          budget: formData.budget,
          message: formData.message || `Pricing inquiry from ${formData.firstName} ${formData.lastName} at ${formData.company}. Service: ${formData.securityService}, Timeline: ${formData.timeline}, Budget: ${formData.budget}`,
          formType: 'pricing-inquiry',
          subject: 'New Pricing Inquiry from SecurityLit Pricing Page'
        }),
      });

      const result = await response.json();
      if (response.ok && result.success) {
        setFormData({
          firstName: '',
          lastName: '',
          email: '',
          company: '',
          jobTitle: '',
          organizationSize: '',
          industry: '',
          securityService: '',
          timeline: '',
          budget: '',
          message: ''
        });
        setSuccess("Thank you! Our security experts will contact you within 24 hours with a detailed proposal.");
      } else {
        setApiError(result.error || "Something went wrong. Please try again.");
      }
    } catch (error) {
      console.error("Error submitting form:", error);
      setApiError("Failed to send inquiry. Please try again.");
    } finally {
      setIsSubmitting(false);
    }
  };

  const contactInfo = [
    {
      icon: <Mail className="w-5 h-5" />,
      title: "Email Us",
      content: "<EMAIL>",
      description: "Get detailed pricing information"
    },
    {
      icon: <Phone className="w-5 h-5" />,
      title: "Call Us",
      content: "+****************",
      description: "Speak with our security experts"
    },
    {
      icon: <Clock className="w-5 h-5" />,
      title: "Response Time",
      content: "Within 24 hours",
      description: "We respond to all inquiries promptly"
    },
    {
      icon: <MapPin className="w-5 h-5" />,
      title: "Global Presence",
      content: "USA, India, Singapore",
      description: "Serving clients worldwide"
    }
  ];

  return (
    <section id="pricing-contact" className="py-20 lg:py-28 bg-[var(--bg-light-blue)]">
      <div className="container mx-auto px-4 max-w-7xl">
        {/* Header */}
        <div className="text-center mb-16">
          <h2 className="text-4xl lg:text-5xl font-bold text-[var(--color-dark-blue)] mb-6">
            Get Your Custom Security
            <span className="text-[var(--color-blue)]"> Proposal</span>
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            Ready to strengthen your cybersecurity posture? Fill out the form below and our security experts 
            will provide you with a detailed proposal tailored to your specific requirements.
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-12">
          {/* Contact Information */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-2xl p-8 shadow-lg border border-[var(--color-blue)]/10 h-fit">
              <h3 className="text-2xl font-bold text-[var(--color-dark-blue)] mb-6">
                Contact Information
              </h3>
              
              <div className="space-y-6">
                {contactInfo.map((info, index) => (
                  <div key={index} className="flex items-start gap-4">
                    <div className="w-12 h-12 bg-[var(--color-blue)]/10 rounded-lg flex items-center justify-center flex-shrink-0">
                      <div className="text-[var(--color-blue)]">
                        {info.icon}
                      </div>
                    </div>
                    <div>
                      <h4 className="font-semibold text-[var(--color-dark-blue)] mb-1">{info.title}</h4>
                      <p className="text-[var(--color-blue)] font-medium mb-1">{info.content}</p>
                      <p className="text-gray-600 text-sm">{info.description}</p>
                    </div>
                  </div>
                ))}
              </div>

              {/* Additional Info */}
              <div className="mt-8 pt-8 border-t border-gray-200">
                <h4 className="font-semibold text-[var(--color-dark-blue)] mb-4">Why Choose SecurityLit?</h4>
                <ul className="space-y-2 text-sm text-gray-600">
                  <li className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-[var(--color-blue)] rounded-full"></div>
                    Certified security professionals
                  </li>
                  <li className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-[var(--color-blue)] rounded-full"></div>
                    Transparent, competitive pricing
                  </li>
                  <li className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-[var(--color-blue)] rounded-full"></div>
                    Comprehensive security reports
                  </li>
                  <li className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-[var(--color-blue)] rounded-full"></div>
                    24/7 incident response support
                  </li>
                </ul>
              </div>
            </div>
          </div>

          {/* Contact Form */}
          <div className="lg:col-span-2">
            <div className="bg-white rounded-2xl p-8 lg:p-10 shadow-lg border border-[var(--color-blue)]/10">
              <h3 className="text-2xl font-bold text-[var(--color-dark-blue)] mb-8">
                Request Custom Pricing
              </h3>

              <form onSubmit={handleSubmit} className="space-y-6">
                {/* Error/Success Messages */}
                {apiError && (
                  <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
                    {apiError}
                  </div>
                )}

                {success && (
                  <div className="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg">
                    {success}
                  </div>
                )}

                {/* Name Fields */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label htmlFor="firstName" className="block text-sm font-semibold text-[var(--color-dark-blue)] mb-2">
                      First Name <span className="text-red-500">*</span>
                    </label>
                    <input
                      type="text"
                      id="firstName"
                      name="firstName"
                      value={formData.firstName}
                      onChange={handleInputChange}
                      required
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[var(--color-blue)] focus:border-[var(--color-blue)] transition-all"
                    />
                  </div>
                  <div>
                    <label htmlFor="lastName" className="block text-sm font-semibold text-[var(--color-dark-blue)] mb-2">
                      Last Name <span className="text-red-500">*</span>
                    </label>
                    <input
                      type="text"
                      id="lastName"
                      name="lastName"
                      value={formData.lastName}
                      onChange={handleInputChange}
                      required
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[var(--color-blue)] focus:border-[var(--color-blue)] transition-all"
                    />
                  </div>
                </div>

                {/* Email and Company */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label htmlFor="email" className="block text-sm font-semibold text-[var(--color-dark-blue)] mb-2">
                      Email Address <span className="text-red-500">*</span>
                    </label>
                    <input
                      type="email"
                      id="email"
                      name="email"
                      value={formData.email}
                      onChange={handleInputChange}
                      required
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[var(--color-blue)] focus:border-[var(--color-blue)] transition-all"
                    />
                  </div>
                  <div>
                    <label htmlFor="company" className="block text-sm font-semibold text-[var(--color-dark-blue)] mb-2">
                      Company <span className="text-red-500">*</span>
                    </label>
                    <input
                      type="text"
                      id="company"
                      name="company"
                      value={formData.company}
                      onChange={handleInputChange}
                      required
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[var(--color-blue)] focus:border-[var(--color-blue)] transition-all"
                    />
                  </div>
                </div>

                {/* Job Title and Organization Size */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label htmlFor="jobTitle" className="block text-sm font-semibold text-[var(--color-dark-blue)] mb-2">
                      Job Title
                    </label>
                    <input
                      type="text"
                      id="jobTitle"
                      name="jobTitle"
                      value={formData.jobTitle}
                      onChange={handleInputChange}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[var(--color-blue)] focus:border-[var(--color-blue)] transition-all"
                    />
                  </div>
                  <div>
                    <label htmlFor="organizationSize" className="block text-sm font-semibold text-[var(--color-dark-blue)] mb-2">
                      Organization Size <span className="text-red-500">*</span>
                    </label>
                    <select
                      id="organizationSize"
                      name="organizationSize"
                      value={formData.organizationSize}
                      onChange={handleInputChange}
                      required
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[var(--color-blue)] focus:border-[var(--color-blue)] transition-all bg-white"
                    >
                      <option value="">Select Size</option>
                      <option value="startup">Startup (1-50 employees)</option>
                      <option value="smb">SMB (51-500 employees)</option>
                      <option value="enterprise">Enterprise (500+ employees)</option>
                      <option value="government">Government/Public Sector</option>
                    </select>
                  </div>
                </div>

                {/* Industry and Security Service */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label htmlFor="industry" className="block text-sm font-semibold text-[var(--color-dark-blue)] mb-2">
                      Industry <span className="text-red-500">*</span>
                    </label>
                    <select
                      id="industry"
                      name="industry"
                      value={formData.industry}
                      onChange={handleInputChange}
                      required
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[var(--color-blue)] focus:border-[var(--color-blue)] transition-all bg-white"
                    >
                      <option value="">Select Industry</option>
                      <option value="technology">Technology</option>
                      <option value="finance">Financial Services</option>
                      <option value="healthcare">Healthcare</option>
                      <option value="manufacturing">Manufacturing</option>
                      <option value="retail">Retail/E-commerce</option>
                      <option value="education">Education</option>
                      <option value="government">Government</option>
                      <option value="other">Other</option>
                    </select>
                  </div>
                  <div>
                    <label htmlFor="securityService" className="block text-sm font-semibold text-[var(--color-dark-blue)] mb-2">
                      Security Service Needed <span className="text-red-500">*</span>
                    </label>
                    <select
                      id="securityService"
                      name="securityService"
                      value={formData.securityService}
                      onChange={handleInputChange}
                      required
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[var(--color-blue)] focus:border-[var(--color-blue)] transition-all bg-white"
                    >
                      <option value="">Select Service</option>
                      <option value="web-application">Web Application Security</option>
                      <option value="api-security">API Security Testing</option>
                      <option value="mobile-application">Mobile Application Security</option>
                      <option value="network-security">Network Security Assessment</option>
                      <option value="cloud-security">Cloud Security Assessment</option>
                      <option value="compliance">Compliance Assessment</option>
                      <option value="multiple">Multiple Services</option>
                    </select>
                  </div>
                </div>

                {/* Timeline and Budget */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label htmlFor="timeline" className="block text-sm font-semibold text-[var(--color-dark-blue)] mb-2">
                      Project Timeline
                    </label>
                    <select
                      id="timeline"
                      name="timeline"
                      value={formData.timeline}
                      onChange={handleInputChange}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[var(--color-blue)] focus:border-[var(--color-blue)] transition-all bg-white"
                    >
                      <option value="">Select Timeline</option>
                      <option value="immediate">Immediate (Within 2 weeks)</option>
                      <option value="1-month">Within 1 month</option>
                      <option value="3-months">Within 3 months</option>
                      <option value="6-months">Within 6 months</option>
                      <option value="planning">Planning phase</option>
                    </select>
                  </div>
                  <div>
                    <label htmlFor="budget" className="block text-sm font-semibold text-[var(--color-dark-blue)] mb-2">
                      Budget Range
                    </label>
                    <select
                      id="budget"
                      name="budget"
                      value={formData.budget}
                      onChange={handleInputChange}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[var(--color-blue)] focus:border-[var(--color-blue)] transition-all bg-white"
                    >
                      <option value="">Select Budget</option>
                      <option value="under-10k">Under $10,000</option>
                      <option value="10k-25k">$10,000 - $25,000</option>
                      <option value="25k-50k">$25,000 - $50,000</option>
                      <option value="50k-100k">$50,000 - $100,000</option>
                      <option value="over-100k">Over $100,000</option>
                    </select>
                  </div>
                </div>

                {/* Message */}
                <div>
                  <label htmlFor="message" className="block text-sm font-semibold text-[var(--color-dark-blue)] mb-2">
                    Additional Requirements or Questions
                  </label>
                  <textarea
                    id="message"
                    name="message"
                    value={formData.message}
                    onChange={handleInputChange}
                    rows={4}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[var(--color-blue)] focus:border-[var(--color-blue)] transition-all resize-vertical"
                    placeholder="Tell us about your specific security requirements, compliance needs, or any questions you have..."
                  />
                </div>

                {/* Submit Button */}
                <button
                  type="submit"
                  disabled={isSubmitting}
                  className="w-full bg-[var(--color-blue)] hover:bg-[var(--color-blue-secondary)] disabled:bg-gray-400 text-white px-8 py-4 rounded-lg font-bold text-lg transition-all duration-300 shadow-lg hover:shadow-xl flex items-center justify-center gap-2"
                >
                  {isSubmitting ? (
                    <>
                      <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                      Submitting Request...
                    </>
                  ) : (
                    'Get Custom Proposal'
                  )}
                </button>
              </form>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
