"use client";

import React from 'react';
import { Mail, Phone, MapPin, Clock } from 'lucide-react';
import ContactForm from '../../contact/components/ContactForm';

export default function PricingContactForm() {

  const contactInfo = [
    {
      icon: <Mail className="w-5 h-5" />,
      title: "Email Us",
      content: "<EMAIL>",
      description: "Get detailed pricing information"
    },
    {
      icon: <Phone className="w-5 h-5" />,
      title: "Call Us",
      content: "+****************",
      description: "Speak with our security experts"
    },
    {
      icon: <Clock className="w-5 h-5" />,
      title: "Response Time",
      content: "Within 24 hours",
      description: "We respond to all inquiries promptly"
    },
    {
      icon: <MapPin className="w-5 h-5" />,
      title: "Global Presence",
      content: "USA, India, Singapore",
      description: "Serving clients worldwide"
    }
  ];

  return (
    <section id="pricing-contact" className="py-20 lg:py-28 bg-[var(--bg-light-blue)]">
      <div className="container mx-auto px-4 max-w-7xl">
        {/* Header */}
        <div className="text-center mb-16">
          <h2 className="text-4xl lg:text-5xl font-bold text-[var(--color-dark-blue)] mb-6">
            Get Your Custom Security
            <span className="text-[var(--color-blue)]"> Proposal</span>
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            Ready to strengthen your cybersecurity posture? Fill out the form below and our security experts 
            will provide you with a detailed proposal tailored to your specific requirements.
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-12">
          {/* Contact Information */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-2xl p-8 shadow-lg border border-[var(--color-blue)]/10 h-fit">
              <h3 className="text-2xl font-bold text-[var(--color-dark-blue)] mb-6">
                Contact Information
              </h3>
              
              <div className="space-y-6">
                {contactInfo.map((info, index) => (
                  <div key={index} className="flex items-start gap-4">
                    <div className="w-12 h-12 bg-[var(--color-blue)]/10 rounded-lg flex items-center justify-center flex-shrink-0">
                      <div className="text-[var(--color-blue)]">
                        {info.icon}
                      </div>
                    </div>
                    <div>
                      <h4 className="font-semibold text-[var(--color-dark-blue)] mb-1">{info.title}</h4>
                      <p className="text-[var(--color-blue)] font-medium mb-1">{info.content}</p>
                      <p className="text-gray-600 text-sm">{info.description}</p>
                    </div>
                  </div>
                ))}
              </div>

              {/* Additional Info */}
              <div className="mt-8 pt-8 border-t border-gray-200">
                <h4 className="font-semibold text-[var(--color-dark-blue)] mb-4">Why Choose SecurityLit?</h4>
                <ul className="space-y-2 text-sm text-gray-600">
                  <li className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-[var(--color-blue)] rounded-full"></div>
                    Certified security professionals
                  </li>
                  <li className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-[var(--color-blue)] rounded-full"></div>
                    Transparent, competitive pricing
                  </li>
                  <li className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-[var(--color-blue)] rounded-full"></div>
                    Comprehensive security reports
                  </li>
                  <li className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-[var(--color-blue)] rounded-full"></div>
                    24/7 incident response support
                  </li>
                </ul>
              </div>
            </div>
          </div>

          {/* Contact Form */}
          <div className="lg:col-span-2">
            <div className="bg-white rounded-2xl shadow-lg border border-[var(--color-blue)]/10 overflow-hidden">
              <div className="p-8 lg:p-10">
                <h3 className="text-2xl font-bold text-[var(--color-dark-blue)] mb-4">
                  Request Custom Pricing
                </h3>
                <p className="text-gray-600 mb-8">
                  Fill out the form below and our security experts will provide you with a detailed proposal tailored to your specific requirements.
                </p>
              </div>

              {/* Use existing ContactForm component */}
              <ContactForm />
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
