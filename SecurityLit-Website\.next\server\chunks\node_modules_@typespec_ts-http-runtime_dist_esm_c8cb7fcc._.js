module.exports = {

"[project]/node_modules/@typespec/ts-http-runtime/dist/esm/util/random.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

// Copyright (c) Microsoft Corporation.
// Licensed under the MIT License.
/**
 * Returns a random integer value between a lower and upper bound,
 * inclusive of both bounds.
 * Note that this uses Math.random and isn't secure. If you need to use
 * this for any kind of security purpose, find a better source of random.
 * @param min - The smallest integer value allowed.
 * @param max - The largest integer value allowed.
 */ __turbopack_context__.s({
    "getRandomIntegerInclusive": ()=>getRandomIntegerInclusive
});
function getRandomIntegerInclusive(min, max) {
    // Make sure inputs are integers.
    min = Math.ceil(min);
    max = Math.floor(max);
    // Pick a random offset from zero to the size of the range.
    // Since Math.random() can never return 1, we have to make the range one larger
    // in order to be inclusive of the maximum value after we take the floor.
    const offset = Math.floor(Math.random() * (max - min + 1));
    return offset + min;
} //# sourceMappingURL=random.js.map
}),
"[project]/node_modules/@typespec/ts-http-runtime/dist/esm/util/delay.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

// Copyright (c) Microsoft Corporation.
// Licensed under the MIT License.
__turbopack_context__.s({
    "calculateRetryDelay": ()=>calculateRetryDelay
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$util$2f$random$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@typespec/ts-http-runtime/dist/esm/util/random.js [app-route] (ecmascript)");
;
function calculateRetryDelay(retryAttempt, config) {
    // Exponentially increase the delay each time
    const exponentialDelay = config.retryDelayInMs * Math.pow(2, retryAttempt);
    // Don't let the delay exceed the maximum
    const clampedDelay = Math.min(config.maxRetryDelayInMs, exponentialDelay);
    // Allow the final value to have some "jitter" (within 50% of the delay size) so
    // that retries across multiple clients don't occur simultaneously.
    const retryAfterInMs = clampedDelay / 2 + (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$util$2f$random$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getRandomIntegerInclusive"])(0, clampedDelay / 2);
    return {
        retryAfterInMs
    };
} //# sourceMappingURL=delay.js.map
}),
"[project]/node_modules/@typespec/ts-http-runtime/dist/esm/util/object.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

// Copyright (c) Microsoft Corporation.
// Licensed under the MIT License.
/**
 * Helper to determine when an input is a generic JS object.
 * @returns true when input is an object type that is not null, Array, RegExp, or Date.
 */ __turbopack_context__.s({
    "isObject": ()=>isObject
});
function isObject(input) {
    return typeof input === "object" && input !== null && !Array.isArray(input) && !(input instanceof RegExp) && !(input instanceof Date);
} //# sourceMappingURL=object.js.map
}),
"[project]/node_modules/@typespec/ts-http-runtime/dist/esm/util/error.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

// Copyright (c) Microsoft Corporation.
// Licensed under the MIT License.
__turbopack_context__.s({
    "isError": ()=>isError
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$util$2f$object$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@typespec/ts-http-runtime/dist/esm/util/object.js [app-route] (ecmascript)");
;
function isError(e) {
    if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$util$2f$object$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isObject"])(e)) {
        const hasName = typeof e.name === "string";
        const hasMessage = typeof e.message === "string";
        return hasName && hasMessage;
    }
    return false;
} //# sourceMappingURL=error.js.map
}),
"[project]/node_modules/@typespec/ts-http-runtime/dist/esm/util/sha256.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

// Copyright (c) Microsoft Corporation.
// Licensed under the MIT License.
__turbopack_context__.s({
    "computeSha256Hash": ()=>computeSha256Hash,
    "computeSha256Hmac": ()=>computeSha256Hmac
});
var __TURBOPACK__imported__module__$5b$externals$5d2f$node$3a$crypto__$5b$external$5d$__$28$node$3a$crypto$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/node:crypto [external] (node:crypto, cjs)");
;
async function computeSha256Hmac(key, stringToSign, encoding) {
    const decodedKey = Buffer.from(key, "base64");
    return (0, __TURBOPACK__imported__module__$5b$externals$5d2f$node$3a$crypto__$5b$external$5d$__$28$node$3a$crypto$2c$__cjs$29$__["createHmac"])("sha256", decodedKey).update(stringToSign).digest(encoding);
}
async function computeSha256Hash(content, encoding) {
    return (0, __TURBOPACK__imported__module__$5b$externals$5d2f$node$3a$crypto__$5b$external$5d$__$28$node$3a$crypto$2c$__cjs$29$__["createHash"])("sha256").update(content).digest(encoding);
} //# sourceMappingURL=sha256.js.map
}),
"[project]/node_modules/@typespec/ts-http-runtime/dist/esm/util/uuidUtils.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

// Copyright (c) Microsoft Corporation.
// Licensed under the MIT License.
__turbopack_context__.s({
    "randomUUID": ()=>randomUUID
});
var __TURBOPACK__imported__module__$5b$externals$5d2f$node$3a$crypto__$5b$external$5d$__$28$node$3a$crypto$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/node:crypto [external] (node:crypto, cjs)");
var _a;
;
// NOTE: This is a workaround until we can use `globalThis.crypto.randomUUID` in Node.js 19+.
const uuidFunction = typeof ((_a = globalThis === null || globalThis === void 0 ? void 0 : globalThis.crypto) === null || _a === void 0 ? void 0 : _a.randomUUID) === "function" ? globalThis.crypto.randomUUID.bind(globalThis.crypto) : __TURBOPACK__imported__module__$5b$externals$5d2f$node$3a$crypto__$5b$external$5d$__$28$node$3a$crypto$2c$__cjs$29$__["randomUUID"];
function randomUUID() {
    return uuidFunction();
} //# sourceMappingURL=uuidUtils.js.map
}),
"[project]/node_modules/@typespec/ts-http-runtime/dist/esm/util/checkEnvironment.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

// Copyright (c) Microsoft Corporation.
// Licensed under the MIT License.
__turbopack_context__.s({
    "isBrowser": ()=>isBrowser,
    "isBun": ()=>isBun,
    "isDeno": ()=>isDeno,
    "isNodeLike": ()=>isNodeLike,
    "isNodeRuntime": ()=>isNodeRuntime,
    "isReactNative": ()=>isReactNative,
    "isWebWorker": ()=>isWebWorker
});
var _a, _b, _c, _d;
const isBrowser = "undefined" !== "undefined" && typeof window.document !== "undefined";
const isWebWorker = typeof self === "object" && typeof (self === null || self === void 0 ? void 0 : self.importScripts) === "function" && (((_a = self.constructor) === null || _a === void 0 ? void 0 : _a.name) === "DedicatedWorkerGlobalScope" || ((_b = self.constructor) === null || _b === void 0 ? void 0 : _b.name) === "ServiceWorkerGlobalScope" || ((_c = self.constructor) === null || _c === void 0 ? void 0 : _c.name) === "SharedWorkerGlobalScope");
const isDeno = typeof Deno !== "undefined" && typeof Deno.version !== "undefined" && typeof Deno.version.deno !== "undefined";
const isBun = typeof Bun !== "undefined" && typeof Bun.version !== "undefined";
const isNodeLike = typeof globalThis.process !== "undefined" && Boolean(globalThis.process.version) && Boolean((_d = globalThis.process.versions) === null || _d === void 0 ? void 0 : _d.node);
const isNodeRuntime = isNodeLike && !isBun && !isDeno;
const isReactNative = typeof navigator !== "undefined" && (navigator === null || navigator === void 0 ? void 0 : navigator.product) === "ReactNative"; //# sourceMappingURL=checkEnvironment.js.map
}),
"[project]/node_modules/@typespec/ts-http-runtime/dist/esm/util/bytesEncoding.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

// Copyright (c) Microsoft Corporation.
// Licensed under the MIT License.
/**
 * The helper that transforms bytes with specific character encoding into string
 * @param bytes - the uint8array bytes
 * @param format - the format we use to encode the byte
 * @returns a string of the encoded string
 */ __turbopack_context__.s({
    "stringToUint8Array": ()=>stringToUint8Array,
    "uint8ArrayToString": ()=>uint8ArrayToString
});
function uint8ArrayToString(bytes, format) {
    return Buffer.from(bytes).toString(format);
}
function stringToUint8Array(value, format) {
    return Buffer.from(value, format);
} //# sourceMappingURL=bytesEncoding.js.map
}),
"[project]/node_modules/@typespec/ts-http-runtime/dist/esm/util/sanitizer.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

// Copyright (c) Microsoft Corporation.
// Licensed under the MIT License.
__turbopack_context__.s({
    "Sanitizer": ()=>Sanitizer
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$util$2f$object$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@typespec/ts-http-runtime/dist/esm/util/object.js [app-route] (ecmascript)");
;
const RedactedString = "REDACTED";
// Make sure this list is up-to-date with the one under core/logger/Readme#Keyconcepts
const defaultAllowedHeaderNames = [
    "x-ms-client-request-id",
    "x-ms-return-client-request-id",
    "x-ms-useragent",
    "x-ms-correlation-request-id",
    "x-ms-request-id",
    "client-request-id",
    "ms-cv",
    "return-client-request-id",
    "traceparent",
    "Access-Control-Allow-Credentials",
    "Access-Control-Allow-Headers",
    "Access-Control-Allow-Methods",
    "Access-Control-Allow-Origin",
    "Access-Control-Expose-Headers",
    "Access-Control-Max-Age",
    "Access-Control-Request-Headers",
    "Access-Control-Request-Method",
    "Origin",
    "Accept",
    "Accept-Encoding",
    "Cache-Control",
    "Connection",
    "Content-Length",
    "Content-Type",
    "Date",
    "ETag",
    "Expires",
    "If-Match",
    "If-Modified-Since",
    "If-None-Match",
    "If-Unmodified-Since",
    "Last-Modified",
    "Pragma",
    "Request-Id",
    "Retry-After",
    "Server",
    "Transfer-Encoding",
    "User-Agent",
    "WWW-Authenticate"
];
const defaultAllowedQueryParameters = [
    "api-version"
];
class Sanitizer {
    constructor({ additionalAllowedHeaderNames: allowedHeaderNames = [], additionalAllowedQueryParameters: allowedQueryParameters = [] } = {}){
        allowedHeaderNames = defaultAllowedHeaderNames.concat(allowedHeaderNames);
        allowedQueryParameters = defaultAllowedQueryParameters.concat(allowedQueryParameters);
        this.allowedHeaderNames = new Set(allowedHeaderNames.map((n)=>n.toLowerCase()));
        this.allowedQueryParameters = new Set(allowedQueryParameters.map((p)=>p.toLowerCase()));
    }
    /**
     * Sanitizes an object for logging.
     * @param obj - The object to sanitize
     * @returns - The sanitized object as a string
     */ sanitize(obj) {
        const seen = new Set();
        return JSON.stringify(obj, (key, value)=>{
            // Ensure Errors include their interesting non-enumerable members
            if (value instanceof Error) {
                return Object.assign(Object.assign({}, value), {
                    name: value.name,
                    message: value.message
                });
            }
            if (key === "headers") {
                return this.sanitizeHeaders(value);
            } else if (key === "url") {
                return this.sanitizeUrl(value);
            } else if (key === "query") {
                return this.sanitizeQuery(value);
            } else if (key === "body") {
                // Don't log the request body
                return undefined;
            } else if (key === "response") {
                // Don't log response again
                return undefined;
            } else if (key === "operationSpec") {
                // When using sendOperationRequest, the request carries a massive
                // field with the autorest spec. No need to log it.
                return undefined;
            } else if (Array.isArray(value) || (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$util$2f$object$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isObject"])(value)) {
                if (seen.has(value)) {
                    return "[Circular]";
                }
                seen.add(value);
            }
            return value;
        }, 2);
    }
    /**
     * Sanitizes a URL for logging.
     * @param value - The URL to sanitize
     * @returns - The sanitized URL as a string
     */ sanitizeUrl(value) {
        if (typeof value !== "string" || value === null || value === "") {
            return value;
        }
        const url = new URL(value);
        if (!url.search) {
            return value;
        }
        for (const [key] of url.searchParams){
            if (!this.allowedQueryParameters.has(key.toLowerCase())) {
                url.searchParams.set(key, RedactedString);
            }
        }
        return url.toString();
    }
    sanitizeHeaders(obj) {
        const sanitized = {};
        for (const key of Object.keys(obj)){
            if (this.allowedHeaderNames.has(key.toLowerCase())) {
                sanitized[key] = obj[key];
            } else {
                sanitized[key] = RedactedString;
            }
        }
        return sanitized;
    }
    sanitizeQuery(value) {
        if (typeof value !== "object" || value === null) {
            return value;
        }
        const sanitized = {};
        for (const k of Object.keys(value)){
            if (this.allowedQueryParameters.has(k.toLowerCase())) {
                sanitized[k] = value[k];
            } else {
                sanitized[k] = RedactedString;
            }
        }
        return sanitized;
    }
} //# sourceMappingURL=sanitizer.js.map
}),
"[project]/node_modules/@typespec/ts-http-runtime/dist/esm/util/internal.js [app-route] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

// Copyright (c) Microsoft Corporation.
// Licensed under the MIT License.
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$util$2f$delay$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@typespec/ts-http-runtime/dist/esm/util/delay.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$util$2f$random$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@typespec/ts-http-runtime/dist/esm/util/random.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$util$2f$object$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@typespec/ts-http-runtime/dist/esm/util/object.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$util$2f$error$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@typespec/ts-http-runtime/dist/esm/util/error.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$util$2f$sha256$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@typespec/ts-http-runtime/dist/esm/util/sha256.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$util$2f$uuidUtils$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@typespec/ts-http-runtime/dist/esm/util/uuidUtils.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$util$2f$checkEnvironment$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@typespec/ts-http-runtime/dist/esm/util/checkEnvironment.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$util$2f$bytesEncoding$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@typespec/ts-http-runtime/dist/esm/util/bytesEncoding.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$util$2f$sanitizer$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@typespec/ts-http-runtime/dist/esm/util/sanitizer.js [app-route] (ecmascript)"); //# sourceMappingURL=internal.js.map
;
;
;
;
;
;
;
;
;
}),
"[project]/node_modules/@typespec/ts-http-runtime/dist/esm/util/internal.js [app-route] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$util$2f$delay$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@typespec/ts-http-runtime/dist/esm/util/delay.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$util$2f$random$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@typespec/ts-http-runtime/dist/esm/util/random.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$util$2f$object$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@typespec/ts-http-runtime/dist/esm/util/object.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$util$2f$error$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@typespec/ts-http-runtime/dist/esm/util/error.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$util$2f$sha256$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@typespec/ts-http-runtime/dist/esm/util/sha256.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$util$2f$uuidUtils$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@typespec/ts-http-runtime/dist/esm/util/uuidUtils.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$util$2f$checkEnvironment$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@typespec/ts-http-runtime/dist/esm/util/checkEnvironment.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$util$2f$bytesEncoding$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@typespec/ts-http-runtime/dist/esm/util/bytesEncoding.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$util$2f$sanitizer$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@typespec/ts-http-runtime/dist/esm/util/sanitizer.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$util$2f$internal$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@typespec/ts-http-runtime/dist/esm/util/internal.js [app-route] (ecmascript) <locals>");
}),
"[project]/node_modules/@typespec/ts-http-runtime/dist/esm/abort-controller/AbortError.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

// Copyright (c) Microsoft Corporation.
// Licensed under the MIT License.
/**
 * This error is thrown when an asynchronous operation has been aborted.
 * Check for this error by testing the `name` that the name property of the
 * error matches `"AbortError"`.
 *
 * @example
 * ```ts snippet:ReadmeSampleAbortError
 * import { AbortError } from "@typespec/ts-http-runtime";
 *
 * async function doAsyncWork(options: { abortSignal: AbortSignal }): Promise<void> {
 *   if (options.abortSignal.aborted) {
 *     throw new AbortError();
 *   }
 *
 *   // do async work
 * }
 *
 * const controller = new AbortController();
 * controller.abort();
 *
 * try {
 *   doAsyncWork({ abortSignal: controller.signal });
 * } catch (e) {
 *   if (e instanceof Error && e.name === "AbortError") {
 *     // handle abort error here.
 *   }
 * }
 * ```
 */ __turbopack_context__.s({
    "AbortError": ()=>AbortError
});
class AbortError extends Error {
    constructor(message){
        super(message);
        this.name = "AbortError";
    }
} //# sourceMappingURL=AbortError.js.map
}),
"[project]/node_modules/@typespec/ts-http-runtime/dist/esm/logger/log.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

// Copyright (c) Microsoft Corporation.
// Licensed under the MIT License.
__turbopack_context__.s({
    "log": ()=>log
});
var __TURBOPACK__imported__module__$5b$externals$5d2f$node$3a$os__$5b$external$5d$__$28$node$3a$os$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/node:os [external] (node:os, cjs)");
var __TURBOPACK__imported__module__$5b$externals$5d2f$node$3a$util__$5b$external$5d$__$28$node$3a$util$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/node:util [external] (node:util, cjs)");
var __TURBOPACK__imported__module__$5b$externals$5d2f$node$3a$process__$5b$external$5d$__$28$node$3a$process$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/node:process [external] (node:process, cjs)");
;
;
;
function log(message, ...args) {
    __TURBOPACK__imported__module__$5b$externals$5d2f$node$3a$process__$5b$external$5d$__$28$node$3a$process$2c$__cjs$29$__["stderr"].write(`${__TURBOPACK__imported__module__$5b$externals$5d2f$node$3a$util__$5b$external$5d$__$28$node$3a$util$2c$__cjs$29$__["default"].format(message, ...args)}${__TURBOPACK__imported__module__$5b$externals$5d2f$node$3a$os__$5b$external$5d$__$28$node$3a$os$2c$__cjs$29$__["EOL"]}`);
} //# sourceMappingURL=log.js.map
}),
"[project]/node_modules/@typespec/ts-http-runtime/dist/esm/logger/debug.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

// Copyright (c) Microsoft Corporation.
// Licensed under the MIT License.
__turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$logger$2f$log$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@typespec/ts-http-runtime/dist/esm/logger/log.js [app-route] (ecmascript)");
;
const debugEnvVariable = typeof process !== "undefined" && process.env && process.env.DEBUG || undefined;
let enabledString;
let enabledNamespaces = [];
let skippedNamespaces = [];
const debuggers = [];
if (debugEnvVariable) {
    enable(debugEnvVariable);
}
const debugObj = Object.assign((namespace)=>{
    return createDebugger(namespace);
}, {
    enable,
    enabled,
    disable,
    log: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$logger$2f$log$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["log"]
});
function enable(namespaces) {
    enabledString = namespaces;
    enabledNamespaces = [];
    skippedNamespaces = [];
    const wildcard = /\*/g;
    const namespaceList = namespaces.split(",").map((ns)=>ns.trim().replace(wildcard, ".*?"));
    for (const ns of namespaceList){
        if (ns.startsWith("-")) {
            skippedNamespaces.push(new RegExp(`^${ns.substr(1)}$`));
        } else {
            enabledNamespaces.push(new RegExp(`^${ns}$`));
        }
    }
    for (const instance of debuggers){
        instance.enabled = enabled(instance.namespace);
    }
}
function enabled(namespace) {
    if (namespace.endsWith("*")) {
        return true;
    }
    for (const skipped of skippedNamespaces){
        if (skipped.test(namespace)) {
            return false;
        }
    }
    for (const enabledNamespace of enabledNamespaces){
        if (enabledNamespace.test(namespace)) {
            return true;
        }
    }
    return false;
}
function disable() {
    const result = enabledString || "";
    enable("");
    return result;
}
function createDebugger(namespace) {
    const newDebugger = Object.assign(debug, {
        enabled: enabled(namespace),
        destroy,
        log: debugObj.log,
        namespace,
        extend
    });
    function debug(...args) {
        if (!newDebugger.enabled) {
            return;
        }
        if (args.length > 0) {
            args[0] = `${namespace} ${args[0]}`;
        }
        newDebugger.log(...args);
    }
    debuggers.push(newDebugger);
    return newDebugger;
}
function destroy() {
    const index = debuggers.indexOf(this);
    if (index >= 0) {
        debuggers.splice(index, 1);
        return true;
    }
    return false;
}
function extend(namespace) {
    const newDebugger = createDebugger(`${this.namespace}:${namespace}`);
    newDebugger.log = this.log;
    return newDebugger;
}
const __TURBOPACK__default__export__ = debugObj;
 //# sourceMappingURL=debug.js.map
}),
"[project]/node_modules/@typespec/ts-http-runtime/dist/esm/logger/logger.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

// Copyright (c) Microsoft Corporation.
// Licensed under the MIT License.
__turbopack_context__.s({
    "TypeSpecRuntimeLogger": ()=>TypeSpecRuntimeLogger,
    "createClientLogger": ()=>createClientLogger,
    "createLoggerContext": ()=>createLoggerContext,
    "getLogLevel": ()=>getLogLevel,
    "setLogLevel": ()=>setLogLevel
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$logger$2f$debug$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@typespec/ts-http-runtime/dist/esm/logger/debug.js [app-route] (ecmascript)");
;
const TYPESPEC_RUNTIME_LOG_LEVELS = [
    "verbose",
    "info",
    "warning",
    "error"
];
const levelMap = {
    verbose: 400,
    info: 300,
    warning: 200,
    error: 100
};
function patchLogMethod(parent, child) {
    child.log = (...args)=>{
        parent.log(...args);
    };
}
function isTypeSpecRuntimeLogLevel(level) {
    return TYPESPEC_RUNTIME_LOG_LEVELS.includes(level);
}
function createLoggerContext(options) {
    const registeredLoggers = new Set();
    const logLevelFromEnv = typeof process !== "undefined" && process.env && process.env[options.logLevelEnvVarName] || undefined;
    let logLevel;
    const clientLogger = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$logger$2f$debug$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"])(options.namespace);
    clientLogger.log = (...args)=>{
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$logger$2f$debug$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].log(...args);
    };
    function contextSetLogLevel(level) {
        if (level && !isTypeSpecRuntimeLogLevel(level)) {
            throw new Error(`Unknown log level '${level}'. Acceptable values: ${TYPESPEC_RUNTIME_LOG_LEVELS.join(",")}`);
        }
        logLevel = level;
        const enabledNamespaces = [];
        for (const logger of registeredLoggers){
            if (shouldEnable(logger)) {
                enabledNamespaces.push(logger.namespace);
            }
        }
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$logger$2f$debug$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].enable(enabledNamespaces.join(","));
    }
    if (logLevelFromEnv) {
        // avoid calling setLogLevel because we don't want a mis-set environment variable to crash
        if (isTypeSpecRuntimeLogLevel(logLevelFromEnv)) {
            contextSetLogLevel(logLevelFromEnv);
        } else {
            console.error(`${options.logLevelEnvVarName} set to unknown log level '${logLevelFromEnv}'; logging is not enabled. Acceptable values: ${TYPESPEC_RUNTIME_LOG_LEVELS.join(", ")}.`);
        }
    }
    function shouldEnable(logger) {
        return Boolean(logLevel && levelMap[logger.level] <= levelMap[logLevel]);
    }
    function createLogger(parent, level) {
        const logger = Object.assign(parent.extend(level), {
            level
        });
        patchLogMethod(parent, logger);
        if (shouldEnable(logger)) {
            const enabledNamespaces = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$logger$2f$debug$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].disable();
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$logger$2f$debug$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].enable(enabledNamespaces + "," + logger.namespace);
        }
        registeredLoggers.add(logger);
        return logger;
    }
    function contextGetLogLevel() {
        return logLevel;
    }
    function contextCreateClientLogger(namespace) {
        const clientRootLogger = clientLogger.extend(namespace);
        patchLogMethod(clientLogger, clientRootLogger);
        return {
            error: createLogger(clientRootLogger, "error"),
            warning: createLogger(clientRootLogger, "warning"),
            info: createLogger(clientRootLogger, "info"),
            verbose: createLogger(clientRootLogger, "verbose")
        };
    }
    return {
        setLogLevel: contextSetLogLevel,
        getLogLevel: contextGetLogLevel,
        createClientLogger: contextCreateClientLogger,
        logger: clientLogger
    };
}
const context = createLoggerContext({
    logLevelEnvVarName: "TYPESPEC_RUNTIME_LOG_LEVEL",
    namespace: "typeSpecRuntime"
});
const TypeSpecRuntimeLogger = context.logger;
function setLogLevel(logLevel) {
    context.setLogLevel(logLevel);
}
function getLogLevel() {
    return context.getLogLevel();
}
function createClientLogger(namespace) {
    return context.createClientLogger(namespace);
} //# sourceMappingURL=logger.js.map
}),
"[project]/node_modules/@typespec/ts-http-runtime/dist/esm/httpHeaders.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

// Copyright (c) Microsoft Corporation.
// Licensed under the MIT License.
__turbopack_context__.s({
    "createHttpHeaders": ()=>createHttpHeaders
});
function normalizeName(name) {
    return name.toLowerCase();
}
function* headerIterator(map) {
    for (const entry of map.values()){
        yield [
            entry.name,
            entry.value
        ];
    }
}
class HttpHeadersImpl {
    constructor(rawHeaders){
        this._headersMap = new Map();
        if (rawHeaders) {
            for (const headerName of Object.keys(rawHeaders)){
                this.set(headerName, rawHeaders[headerName]);
            }
        }
    }
    /**
     * Set a header in this collection with the provided name and value. The name is
     * case-insensitive.
     * @param name - The name of the header to set. This value is case-insensitive.
     * @param value - The value of the header to set.
     */ set(name, value) {
        this._headersMap.set(normalizeName(name), {
            name,
            value: String(value).trim()
        });
    }
    /**
     * Get the header value for the provided header name, or undefined if no header exists in this
     * collection with the provided name.
     * @param name - The name of the header. This value is case-insensitive.
     */ get(name) {
        var _a;
        return (_a = this._headersMap.get(normalizeName(name))) === null || _a === void 0 ? void 0 : _a.value;
    }
    /**
     * Get whether or not this header collection contains a header entry for the provided header name.
     * @param name - The name of the header to set. This value is case-insensitive.
     */ has(name) {
        return this._headersMap.has(normalizeName(name));
    }
    /**
     * Remove the header with the provided headerName.
     * @param name - The name of the header to remove.
     */ delete(name) {
        this._headersMap.delete(normalizeName(name));
    }
    /**
     * Get the JSON object representation of this HTTP header collection.
     */ toJSON(options = {}) {
        const result = {};
        if (options.preserveCase) {
            for (const entry of this._headersMap.values()){
                result[entry.name] = entry.value;
            }
        } else {
            for (const [normalizedName, entry] of this._headersMap){
                result[normalizedName] = entry.value;
            }
        }
        return result;
    }
    /**
     * Get the string representation of this HTTP header collection.
     */ toString() {
        return JSON.stringify(this.toJSON({
            preserveCase: true
        }));
    }
    /**
     * Iterate over tuples of header [name, value] pairs.
     */ [Symbol.iterator]() {
        return headerIterator(this._headersMap);
    }
}
function createHttpHeaders(rawHeaders) {
    return new HttpHeadersImpl(rawHeaders);
} //# sourceMappingURL=httpHeaders.js.map
}),
"[project]/node_modules/@typespec/ts-http-runtime/dist/esm/auth/schemes.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

// Copyright (c) Microsoft Corporation.
// Licensed under the MIT License.
__turbopack_context__.s({});
;
 //# sourceMappingURL=schemes.js.map
}),
"[project]/node_modules/@typespec/ts-http-runtime/dist/esm/auth/oauth2Flows.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

// Copyright (c) Microsoft Corporation.
// Licensed under the MIT License.
__turbopack_context__.s({});
;
 //# sourceMappingURL=oauth2Flows.js.map
}),
"[project]/node_modules/@typespec/ts-http-runtime/dist/esm/pipelineRequest.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

// Copyright (c) Microsoft Corporation.
// Licensed under the MIT License.
__turbopack_context__.s({
    "createPipelineRequest": ()=>createPipelineRequest
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$httpHeaders$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@typespec/ts-http-runtime/dist/esm/httpHeaders.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$util$2f$uuidUtils$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@typespec/ts-http-runtime/dist/esm/util/uuidUtils.js [app-route] (ecmascript)");
;
;
class PipelineRequestImpl {
    constructor(options){
        var _a, _b, _c, _d, _e, _f, _g;
        this.url = options.url;
        this.body = options.body;
        this.headers = (_a = options.headers) !== null && _a !== void 0 ? _a : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$httpHeaders$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createHttpHeaders"])();
        this.method = (_b = options.method) !== null && _b !== void 0 ? _b : "GET";
        this.timeout = (_c = options.timeout) !== null && _c !== void 0 ? _c : 0;
        this.multipartBody = options.multipartBody;
        this.formData = options.formData;
        this.disableKeepAlive = (_d = options.disableKeepAlive) !== null && _d !== void 0 ? _d : false;
        this.proxySettings = options.proxySettings;
        this.streamResponseStatusCodes = options.streamResponseStatusCodes;
        this.withCredentials = (_e = options.withCredentials) !== null && _e !== void 0 ? _e : false;
        this.abortSignal = options.abortSignal;
        this.onUploadProgress = options.onUploadProgress;
        this.onDownloadProgress = options.onDownloadProgress;
        this.requestId = options.requestId || (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$util$2f$uuidUtils$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["randomUUID"])();
        this.allowInsecureConnection = (_f = options.allowInsecureConnection) !== null && _f !== void 0 ? _f : false;
        this.enableBrowserStreams = (_g = options.enableBrowserStreams) !== null && _g !== void 0 ? _g : false;
        this.requestOverrides = options.requestOverrides;
        this.authSchemes = options.authSchemes;
    }
}
function createPipelineRequest(options) {
    return new PipelineRequestImpl(options);
} //# sourceMappingURL=pipelineRequest.js.map
}),
"[project]/node_modules/@typespec/ts-http-runtime/dist/esm/pipeline.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

// Copyright (c) Microsoft Corporation.
// Licensed under the MIT License.
__turbopack_context__.s({
    "createEmptyPipeline": ()=>createEmptyPipeline
});
const ValidPhaseNames = new Set([
    "Deserialize",
    "Serialize",
    "Retry",
    "Sign"
]);
/**
 * A private implementation of Pipeline.
 * Do not export this class from the package.
 * @internal
 */ class HttpPipeline {
    constructor(policies){
        var _a;
        this._policies = [];
        this._policies = (_a = policies === null || policies === void 0 ? void 0 : policies.slice(0)) !== null && _a !== void 0 ? _a : [];
        this._orderedPolicies = undefined;
    }
    addPolicy(policy, options = {}) {
        if (options.phase && options.afterPhase) {
            throw new Error("Policies inside a phase cannot specify afterPhase.");
        }
        if (options.phase && !ValidPhaseNames.has(options.phase)) {
            throw new Error(`Invalid phase name: ${options.phase}`);
        }
        if (options.afterPhase && !ValidPhaseNames.has(options.afterPhase)) {
            throw new Error(`Invalid afterPhase name: ${options.afterPhase}`);
        }
        this._policies.push({
            policy,
            options
        });
        this._orderedPolicies = undefined;
    }
    removePolicy(options) {
        const removedPolicies = [];
        this._policies = this._policies.filter((policyDescriptor)=>{
            if (options.name && policyDescriptor.policy.name === options.name || options.phase && policyDescriptor.options.phase === options.phase) {
                removedPolicies.push(policyDescriptor.policy);
                return false;
            } else {
                return true;
            }
        });
        this._orderedPolicies = undefined;
        return removedPolicies;
    }
    sendRequest(httpClient, request) {
        const policies = this.getOrderedPolicies();
        const pipeline = policies.reduceRight((next, policy)=>{
            return (req)=>{
                return policy.sendRequest(req, next);
            };
        }, (req)=>httpClient.sendRequest(req));
        return pipeline(request);
    }
    getOrderedPolicies() {
        if (!this._orderedPolicies) {
            this._orderedPolicies = this.orderPolicies();
        }
        return this._orderedPolicies;
    }
    clone() {
        return new HttpPipeline(this._policies);
    }
    static create() {
        return new HttpPipeline();
    }
    orderPolicies() {
        /**
         * The goal of this method is to reliably order pipeline policies
         * based on their declared requirements when they were added.
         *
         * Order is first determined by phase:
         *
         * 1. Serialize Phase
         * 2. Policies not in a phase
         * 3. Deserialize Phase
         * 4. Retry Phase
         * 5. Sign Phase
         *
         * Within each phase, policies are executed in the order
         * they were added unless they were specified to execute
         * before/after other policies or after a particular phase.
         *
         * To determine the final order, we will walk the policy list
         * in phase order multiple times until all dependencies are
         * satisfied.
         *
         * `afterPolicies` are the set of policies that must be
         * executed before a given policy. This requirement is
         * considered satisfied when each of the listed policies
         * have been scheduled.
         *
         * `beforePolicies` are the set of policies that must be
         * executed after a given policy. Since this dependency
         * can be expressed by converting it into a equivalent
         * `afterPolicies` declarations, they are normalized
         * into that form for simplicity.
         *
         * An `afterPhase` dependency is considered satisfied when all
         * policies in that phase have scheduled.
         *
         */ const result = [];
        // Track all policies we know about.
        const policyMap = new Map();
        function createPhase(name) {
            return {
                name,
                policies: new Set(),
                hasRun: false,
                hasAfterPolicies: false
            };
        }
        // Track policies for each phase.
        const serializePhase = createPhase("Serialize");
        const noPhase = createPhase("None");
        const deserializePhase = createPhase("Deserialize");
        const retryPhase = createPhase("Retry");
        const signPhase = createPhase("Sign");
        // a list of phases in order
        const orderedPhases = [
            serializePhase,
            noPhase,
            deserializePhase,
            retryPhase,
            signPhase
        ];
        // Small helper function to map phase name to each Phase
        function getPhase(phase) {
            if (phase === "Retry") {
                return retryPhase;
            } else if (phase === "Serialize") {
                return serializePhase;
            } else if (phase === "Deserialize") {
                return deserializePhase;
            } else if (phase === "Sign") {
                return signPhase;
            } else {
                return noPhase;
            }
        }
        // First walk each policy and create a node to track metadata.
        for (const descriptor of this._policies){
            const policy = descriptor.policy;
            const options = descriptor.options;
            const policyName = policy.name;
            if (policyMap.has(policyName)) {
                throw new Error("Duplicate policy names not allowed in pipeline");
            }
            const node = {
                policy,
                dependsOn: new Set(),
                dependants: new Set()
            };
            if (options.afterPhase) {
                node.afterPhase = getPhase(options.afterPhase);
                node.afterPhase.hasAfterPolicies = true;
            }
            policyMap.set(policyName, node);
            const phase = getPhase(options.phase);
            phase.policies.add(node);
        }
        // Now that each policy has a node, connect dependency references.
        for (const descriptor of this._policies){
            const { policy, options } = descriptor;
            const policyName = policy.name;
            const node = policyMap.get(policyName);
            if (!node) {
                throw new Error(`Missing node for policy ${policyName}`);
            }
            if (options.afterPolicies) {
                for (const afterPolicyName of options.afterPolicies){
                    const afterNode = policyMap.get(afterPolicyName);
                    if (afterNode) {
                        // Linking in both directions helps later
                        // when we want to notify dependants.
                        node.dependsOn.add(afterNode);
                        afterNode.dependants.add(node);
                    }
                }
            }
            if (options.beforePolicies) {
                for (const beforePolicyName of options.beforePolicies){
                    const beforeNode = policyMap.get(beforePolicyName);
                    if (beforeNode) {
                        // To execute before another node, make it
                        // depend on the current node.
                        beforeNode.dependsOn.add(node);
                        node.dependants.add(beforeNode);
                    }
                }
            }
        }
        function walkPhase(phase) {
            phase.hasRun = true;
            // Sets iterate in insertion order
            for (const node of phase.policies){
                if (node.afterPhase && (!node.afterPhase.hasRun || node.afterPhase.policies.size)) {
                    continue;
                }
                if (node.dependsOn.size === 0) {
                    // If there's nothing else we're waiting for, we can
                    // add this policy to the result list.
                    result.push(node.policy);
                    // Notify anything that depends on this policy that
                    // the policy has been scheduled.
                    for (const dependant of node.dependants){
                        dependant.dependsOn.delete(node);
                    }
                    policyMap.delete(node.policy.name);
                    phase.policies.delete(node);
                }
            }
        }
        function walkPhases() {
            for (const phase of orderedPhases){
                walkPhase(phase);
                // if the phase isn't complete
                if (phase.policies.size > 0 && phase !== noPhase) {
                    if (!noPhase.hasRun) {
                        // Try running noPhase to see if that unblocks this phase next tick.
                        // This can happen if a phase that happens before noPhase
                        // is waiting on a noPhase policy to complete.
                        walkPhase(noPhase);
                    }
                    // Don't proceed to the next phase until this phase finishes.
                    return;
                }
                if (phase.hasAfterPolicies) {
                    // Run any policies unblocked by this phase
                    walkPhase(noPhase);
                }
            }
        }
        // Iterate until we've put every node in the result list.
        let iteration = 0;
        while(policyMap.size > 0){
            iteration++;
            const initialResultLength = result.length;
            // Keep walking each phase in order until we can order every node.
            walkPhases();
            // The result list *should* get at least one larger each time
            // after the first full pass.
            // Otherwise, we're going to loop forever.
            if (result.length <= initialResultLength && iteration > 1) {
                throw new Error("Cannot satisfy policy dependencies due to requirements cycle.");
            }
        }
        return result;
    }
}
function createEmptyPipeline() {
    return HttpPipeline.create();
} //# sourceMappingURL=pipeline.js.map
}),
"[project]/node_modules/@typespec/ts-http-runtime/dist/esm/util/inspect.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

// Copyright (c) Microsoft Corporation.
// Licensed under the MIT License.
__turbopack_context__.s({
    "custom": ()=>custom
});
var __TURBOPACK__imported__module__$5b$externals$5d2f$node$3a$util__$5b$external$5d$__$28$node$3a$util$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/node:util [external] (node:util, cjs)");
;
const custom = __TURBOPACK__imported__module__$5b$externals$5d2f$node$3a$util__$5b$external$5d$__$28$node$3a$util$2c$__cjs$29$__["inspect"].custom; //# sourceMappingURL=inspect.js.map
}),
"[project]/node_modules/@typespec/ts-http-runtime/dist/esm/restError.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

// Copyright (c) Microsoft Corporation.
// Licensed under the MIT License.
__turbopack_context__.s({
    "RestError": ()=>RestError,
    "isRestError": ()=>isRestError
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$util$2f$error$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@typespec/ts-http-runtime/dist/esm/util/error.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$util$2f$inspect$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@typespec/ts-http-runtime/dist/esm/util/inspect.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$util$2f$sanitizer$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@typespec/ts-http-runtime/dist/esm/util/sanitizer.js [app-route] (ecmascript)");
;
;
;
const errorSanitizer = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$util$2f$sanitizer$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Sanitizer"]();
class RestError extends Error {
    constructor(message, options = {}){
        super(message);
        this.name = "RestError";
        this.code = options.code;
        this.statusCode = options.statusCode;
        // The request and response may contain sensitive information in the headers or body.
        // To help prevent this sensitive information being accidentally logged, the request and response
        // properties are marked as non-enumerable here. This prevents them showing up in the output of
        // JSON.stringify and console.log.
        Object.defineProperty(this, "request", {
            value: options.request,
            enumerable: false
        });
        Object.defineProperty(this, "response", {
            value: options.response,
            enumerable: false
        });
        // Logging method for util.inspect in Node
        Object.defineProperty(this, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$util$2f$inspect$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["custom"], {
            value: ()=>{
                // Extract non-enumerable properties and add them back. This is OK since in this output the request and
                // response get sanitized.
                return `RestError: ${this.message} \n ${errorSanitizer.sanitize(Object.assign(Object.assign({}, this), {
                    request: this.request,
                    response: this.response
                }))}`;
            },
            enumerable: false
        });
        Object.setPrototypeOf(this, RestError.prototype);
    }
}
/**
 * Something went wrong when making the request.
 * This means the actual request failed for some reason,
 * such as a DNS issue or the connection being lost.
 */ RestError.REQUEST_SEND_ERROR = "REQUEST_SEND_ERROR";
/**
 * This means that parsing the response from the server failed.
 * It may have been malformed.
 */ RestError.PARSE_ERROR = "PARSE_ERROR";
function isRestError(e) {
    if (e instanceof RestError) {
        return true;
    }
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$util$2f$error$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isError"])(e) && e.name === "RestError";
} //# sourceMappingURL=restError.js.map
}),
"[project]/node_modules/@typespec/ts-http-runtime/dist/esm/log.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

// Copyright (c) Microsoft Corporation.
// Licensed under the MIT License.
__turbopack_context__.s({
    "logger": ()=>logger
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$logger$2f$logger$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@typespec/ts-http-runtime/dist/esm/logger/logger.js [app-route] (ecmascript)");
;
const logger = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$logger$2f$logger$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createClientLogger"])("ts-http-runtime"); //# sourceMappingURL=log.js.map
}),
"[project]/node_modules/@typespec/ts-http-runtime/dist/esm/nodeHttpClient.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

// Copyright (c) Microsoft Corporation.
// Licensed under the MIT License.
__turbopack_context__.s({
    "createNodeHttpClient": ()=>createNodeHttpClient,
    "getBodyLength": ()=>getBodyLength
});
var __TURBOPACK__imported__module__$5b$externals$5d2f$node$3a$http__$5b$external$5d$__$28$node$3a$http$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/node:http [external] (node:http, cjs)");
var __TURBOPACK__imported__module__$5b$externals$5d2f$node$3a$https__$5b$external$5d$__$28$node$3a$https$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/node:https [external] (node:https, cjs)");
var __TURBOPACK__imported__module__$5b$externals$5d2f$node$3a$zlib__$5b$external$5d$__$28$node$3a$zlib$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/node:zlib [external] (node:zlib, cjs)");
var __TURBOPACK__imported__module__$5b$externals$5d2f$node$3a$stream__$5b$external$5d$__$28$node$3a$stream$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/node:stream [external] (node:stream, cjs)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$abort$2d$controller$2f$AbortError$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@typespec/ts-http-runtime/dist/esm/abort-controller/AbortError.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$httpHeaders$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@typespec/ts-http-runtime/dist/esm/httpHeaders.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$restError$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@typespec/ts-http-runtime/dist/esm/restError.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$log$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@typespec/ts-http-runtime/dist/esm/log.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$util$2f$sanitizer$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@typespec/ts-http-runtime/dist/esm/util/sanitizer.js [app-route] (ecmascript)");
;
;
;
;
;
;
;
;
;
const DEFAULT_TLS_SETTINGS = {};
function isReadableStream(body) {
    return body && typeof body.pipe === "function";
}
function isStreamComplete(stream) {
    if (stream.readable === false) {
        return Promise.resolve();
    }
    return new Promise((resolve)=>{
        const handler = ()=>{
            resolve();
            stream.removeListener("close", handler);
            stream.removeListener("end", handler);
            stream.removeListener("error", handler);
        };
        stream.on("close", handler);
        stream.on("end", handler);
        stream.on("error", handler);
    });
}
function isArrayBuffer(body) {
    return body && typeof body.byteLength === "number";
}
class ReportTransform extends __TURBOPACK__imported__module__$5b$externals$5d2f$node$3a$stream__$5b$external$5d$__$28$node$3a$stream$2c$__cjs$29$__["Transform"] {
    // eslint-disable-next-line @typescript-eslint/no-unsafe-function-type
    _transform(chunk, _encoding, callback) {
        this.push(chunk);
        this.loadedBytes += chunk.length;
        try {
            this.progressCallback({
                loadedBytes: this.loadedBytes
            });
            callback();
        } catch (e) {
            callback(e);
        }
    }
    constructor(progressCallback){
        super();
        this.loadedBytes = 0;
        this.progressCallback = progressCallback;
    }
}
/**
 * A HttpClient implementation that uses Node's "https" module to send HTTPS requests.
 * @internal
 */ class NodeHttpClient {
    constructor(){
        this.cachedHttpsAgents = new WeakMap();
    }
    /**
     * Makes a request over an underlying transport layer and returns the response.
     * @param request - The request to be made.
     */ async sendRequest(request) {
        var _a, _b, _c;
        const abortController = new AbortController();
        let abortListener;
        if (request.abortSignal) {
            if (request.abortSignal.aborted) {
                throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$abort$2d$controller$2f$AbortError$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["AbortError"]("The operation was aborted. Request has already been canceled.");
            }
            abortListener = (event)=>{
                if (event.type === "abort") {
                    abortController.abort();
                }
            };
            request.abortSignal.addEventListener("abort", abortListener);
        }
        let timeoutId;
        if (request.timeout > 0) {
            timeoutId = setTimeout(()=>{
                const sanitizer = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$util$2f$sanitizer$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Sanitizer"]();
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$log$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["logger"].info(`request to '${sanitizer.sanitizeUrl(request.url)}' timed out. canceling...`);
                abortController.abort();
            }, request.timeout);
        }
        const acceptEncoding = request.headers.get("Accept-Encoding");
        const shouldDecompress = (acceptEncoding === null || acceptEncoding === void 0 ? void 0 : acceptEncoding.includes("gzip")) || (acceptEncoding === null || acceptEncoding === void 0 ? void 0 : acceptEncoding.includes("deflate"));
        let body = typeof request.body === "function" ? request.body() : request.body;
        if (body && !request.headers.has("Content-Length")) {
            const bodyLength = getBodyLength(body);
            if (bodyLength !== null) {
                request.headers.set("Content-Length", bodyLength);
            }
        }
        let responseStream;
        try {
            if (body && request.onUploadProgress) {
                const onUploadProgress = request.onUploadProgress;
                const uploadReportStream = new ReportTransform(onUploadProgress);
                uploadReportStream.on("error", (e)=>{
                    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$log$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["logger"].error("Error in upload progress", e);
                });
                if (isReadableStream(body)) {
                    body.pipe(uploadReportStream);
                } else {
                    uploadReportStream.end(body);
                }
                body = uploadReportStream;
            }
            const res = await this.makeRequest(request, abortController, body);
            if (timeoutId !== undefined) {
                clearTimeout(timeoutId);
            }
            const headers = getResponseHeaders(res);
            const status = (_a = res.statusCode) !== null && _a !== void 0 ? _a : 0;
            const response = {
                status,
                headers,
                request
            };
            // Responses to HEAD must not have a body.
            // If they do return a body, that body must be ignored.
            if (request.method === "HEAD") {
                // call resume() and not destroy() to avoid closing the socket
                // and losing keep alive
                res.resume();
                return response;
            }
            responseStream = shouldDecompress ? getDecodedResponseStream(res, headers) : res;
            const onDownloadProgress = request.onDownloadProgress;
            if (onDownloadProgress) {
                const downloadReportStream = new ReportTransform(onDownloadProgress);
                downloadReportStream.on("error", (e)=>{
                    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$log$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["logger"].error("Error in download progress", e);
                });
                responseStream.pipe(downloadReportStream);
                responseStream = downloadReportStream;
            }
            if (// Value of POSITIVE_INFINITY in streamResponseStatusCodes is considered as any status code
            ((_b = request.streamResponseStatusCodes) === null || _b === void 0 ? void 0 : _b.has(Number.POSITIVE_INFINITY)) || ((_c = request.streamResponseStatusCodes) === null || _c === void 0 ? void 0 : _c.has(response.status))) {
                response.readableStreamBody = responseStream;
            } else {
                response.bodyAsText = await streamToText(responseStream);
            }
            return response;
        } finally{
            // clean up event listener
            if (request.abortSignal && abortListener) {
                let uploadStreamDone = Promise.resolve();
                if (isReadableStream(body)) {
                    uploadStreamDone = isStreamComplete(body);
                }
                let downloadStreamDone = Promise.resolve();
                if (isReadableStream(responseStream)) {
                    downloadStreamDone = isStreamComplete(responseStream);
                }
                Promise.all([
                    uploadStreamDone,
                    downloadStreamDone
                ]).then(()=>{
                    var _a;
                    // eslint-disable-next-line promise/always-return
                    if (abortListener) {
                        (_a = request.abortSignal) === null || _a === void 0 ? void 0 : _a.removeEventListener("abort", abortListener);
                    }
                }).catch((e)=>{
                    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$log$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["logger"].warning("Error when cleaning up abortListener on httpRequest", e);
                });
            }
        }
    }
    makeRequest(request, abortController, body) {
        var _a;
        const url = new URL(request.url);
        const isInsecure = url.protocol !== "https:";
        if (isInsecure && !request.allowInsecureConnection) {
            throw new Error(`Cannot connect to ${request.url} while allowInsecureConnection is false.`);
        }
        const agent = (_a = request.agent) !== null && _a !== void 0 ? _a : this.getOrCreateAgent(request, isInsecure);
        const options = Object.assign({
            agent,
            hostname: url.hostname,
            path: `${url.pathname}${url.search}`,
            port: url.port,
            method: request.method,
            headers: request.headers.toJSON({
                preserveCase: true
            })
        }, request.requestOverrides);
        return new Promise((resolve, reject)=>{
            const req = isInsecure ? __TURBOPACK__imported__module__$5b$externals$5d2f$node$3a$http__$5b$external$5d$__$28$node$3a$http$2c$__cjs$29$__["request"](options, resolve) : __TURBOPACK__imported__module__$5b$externals$5d2f$node$3a$https__$5b$external$5d$__$28$node$3a$https$2c$__cjs$29$__["request"](options, resolve);
            req.once("error", (err)=>{
                var _a;
                reject(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$restError$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["RestError"](err.message, {
                    code: (_a = err.code) !== null && _a !== void 0 ? _a : __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$restError$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["RestError"].REQUEST_SEND_ERROR,
                    request
                }));
            });
            abortController.signal.addEventListener("abort", ()=>{
                const abortError = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$abort$2d$controller$2f$AbortError$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["AbortError"]("The operation was aborted. Rejecting from abort signal callback while making request.");
                req.destroy(abortError);
                reject(abortError);
            });
            if (body && isReadableStream(body)) {
                body.pipe(req);
            } else if (body) {
                if (typeof body === "string" || Buffer.isBuffer(body)) {
                    req.end(body);
                } else if (isArrayBuffer(body)) {
                    req.end(ArrayBuffer.isView(body) ? Buffer.from(body.buffer) : Buffer.from(body));
                } else {
                    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$log$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["logger"].error("Unrecognized body type", body);
                    reject(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$restError$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["RestError"]("Unrecognized body type"));
                }
            } else {
                // streams don't like "undefined" being passed as data
                req.end();
            }
        });
    }
    getOrCreateAgent(request, isInsecure) {
        var _a;
        const disableKeepAlive = request.disableKeepAlive;
        // Handle Insecure requests first
        if (isInsecure) {
            if (disableKeepAlive) {
                // keepAlive:false is the default so we don't need a custom Agent
                return __TURBOPACK__imported__module__$5b$externals$5d2f$node$3a$http__$5b$external$5d$__$28$node$3a$http$2c$__cjs$29$__["globalAgent"];
            }
            if (!this.cachedHttpAgent) {
                // If there is no cached agent create a new one and cache it.
                this.cachedHttpAgent = new __TURBOPACK__imported__module__$5b$externals$5d2f$node$3a$http__$5b$external$5d$__$28$node$3a$http$2c$__cjs$29$__["Agent"]({
                    keepAlive: true
                });
            }
            return this.cachedHttpAgent;
        } else {
            if (disableKeepAlive && !request.tlsSettings) {
                // When there are no tlsSettings and keepAlive is false
                // we don't need a custom agent
                return __TURBOPACK__imported__module__$5b$externals$5d2f$node$3a$https__$5b$external$5d$__$28$node$3a$https$2c$__cjs$29$__["globalAgent"];
            }
            // We use the tlsSettings to index cached clients
            const tlsSettings = (_a = request.tlsSettings) !== null && _a !== void 0 ? _a : DEFAULT_TLS_SETTINGS;
            // Get the cached agent or create a new one with the
            // provided values for keepAlive and tlsSettings
            let agent = this.cachedHttpsAgents.get(tlsSettings);
            if (agent && agent.options.keepAlive === !disableKeepAlive) {
                return agent;
            }
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$log$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["logger"].info("No cached TLS Agent exist, creating a new Agent");
            agent = new __TURBOPACK__imported__module__$5b$externals$5d2f$node$3a$https__$5b$external$5d$__$28$node$3a$https$2c$__cjs$29$__["Agent"](Object.assign({
                // keepAlive is true if disableKeepAlive is false.
                keepAlive: !disableKeepAlive
            }, tlsSettings));
            this.cachedHttpsAgents.set(tlsSettings, agent);
            return agent;
        }
    }
}
function getResponseHeaders(res) {
    const headers = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$httpHeaders$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createHttpHeaders"])();
    for (const header of Object.keys(res.headers)){
        const value = res.headers[header];
        if (Array.isArray(value)) {
            if (value.length > 0) {
                headers.set(header, value[0]);
            }
        } else if (value) {
            headers.set(header, value);
        }
    }
    return headers;
}
function getDecodedResponseStream(stream, headers) {
    const contentEncoding = headers.get("Content-Encoding");
    if (contentEncoding === "gzip") {
        const unzip = __TURBOPACK__imported__module__$5b$externals$5d2f$node$3a$zlib__$5b$external$5d$__$28$node$3a$zlib$2c$__cjs$29$__["createGunzip"]();
        stream.pipe(unzip);
        return unzip;
    } else if (contentEncoding === "deflate") {
        const inflate = __TURBOPACK__imported__module__$5b$externals$5d2f$node$3a$zlib__$5b$external$5d$__$28$node$3a$zlib$2c$__cjs$29$__["createInflate"]();
        stream.pipe(inflate);
        return inflate;
    }
    return stream;
}
function streamToText(stream) {
    return new Promise((resolve, reject)=>{
        const buffer = [];
        stream.on("data", (chunk)=>{
            if (Buffer.isBuffer(chunk)) {
                buffer.push(chunk);
            } else {
                buffer.push(Buffer.from(chunk));
            }
        });
        stream.on("end", ()=>{
            resolve(Buffer.concat(buffer).toString("utf8"));
        });
        stream.on("error", (e)=>{
            if (e && (e === null || e === void 0 ? void 0 : e.name) === "AbortError") {
                reject(e);
            } else {
                reject(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$restError$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["RestError"](`Error reading response as text: ${e.message}`, {
                    code: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$restError$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["RestError"].PARSE_ERROR
                }));
            }
        });
    });
}
function getBodyLength(body) {
    if (!body) {
        return 0;
    } else if (Buffer.isBuffer(body)) {
        return body.length;
    } else if (isReadableStream(body)) {
        return null;
    } else if (isArrayBuffer(body)) {
        return body.byteLength;
    } else if (typeof body === "string") {
        return Buffer.from(body).length;
    } else {
        return null;
    }
}
function createNodeHttpClient() {
    return new NodeHttpClient();
} //# sourceMappingURL=nodeHttpClient.js.map
}),
"[project]/node_modules/@typespec/ts-http-runtime/dist/esm/defaultHttpClient.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

// Copyright (c) Microsoft Corporation.
// Licensed under the MIT License.
__turbopack_context__.s({
    "createDefaultHttpClient": ()=>createDefaultHttpClient
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$nodeHttpClient$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@typespec/ts-http-runtime/dist/esm/nodeHttpClient.js [app-route] (ecmascript)");
;
function createDefaultHttpClient() {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$nodeHttpClient$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createNodeHttpClient"])();
} //# sourceMappingURL=defaultHttpClient.js.map
}),
"[project]/node_modules/@typespec/ts-http-runtime/dist/esm/policies/logPolicy.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

// Copyright (c) Microsoft Corporation.
// Licensed under the MIT License.
__turbopack_context__.s({
    "logPolicy": ()=>logPolicy,
    "logPolicyName": ()=>logPolicyName
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$log$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@typespec/ts-http-runtime/dist/esm/log.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$util$2f$sanitizer$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@typespec/ts-http-runtime/dist/esm/util/sanitizer.js [app-route] (ecmascript)");
;
;
const logPolicyName = "logPolicy";
function logPolicy(options = {}) {
    var _a;
    const logger = (_a = options.logger) !== null && _a !== void 0 ? _a : __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$log$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["logger"].info;
    const sanitizer = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$util$2f$sanitizer$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Sanitizer"]({
        additionalAllowedHeaderNames: options.additionalAllowedHeaderNames,
        additionalAllowedQueryParameters: options.additionalAllowedQueryParameters
    });
    return {
        name: logPolicyName,
        async sendRequest (request, next) {
            if (!logger.enabled) {
                return next(request);
            }
            logger(`Request: ${sanitizer.sanitize(request)}`);
            const response = await next(request);
            logger(`Response status code: ${response.status}`);
            logger(`Headers: ${sanitizer.sanitize(response.headers)}`);
            return response;
        }
    };
} //# sourceMappingURL=logPolicy.js.map
}),
"[project]/node_modules/@typespec/ts-http-runtime/dist/esm/policies/redirectPolicy.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

// Copyright (c) Microsoft Corporation.
// Licensed under the MIT License.
/**
 * The programmatic identifier of the redirectPolicy.
 */ __turbopack_context__.s({
    "redirectPolicy": ()=>redirectPolicy,
    "redirectPolicyName": ()=>redirectPolicyName
});
const redirectPolicyName = "redirectPolicy";
/**
 * Methods that are allowed to follow redirects 301 and 302
 */ const allowedRedirect = [
    "GET",
    "HEAD"
];
function redirectPolicy(options = {}) {
    const { maxRetries = 20 } = options;
    return {
        name: redirectPolicyName,
        async sendRequest (request, next) {
            const response = await next(request);
            return handleRedirect(next, response, maxRetries);
        }
    };
}
async function handleRedirect(next, response, maxRetries, currentRetries = 0) {
    const { request, status, headers } = response;
    const locationHeader = headers.get("location");
    if (locationHeader && (status === 300 || status === 301 && allowedRedirect.includes(request.method) || status === 302 && allowedRedirect.includes(request.method) || status === 303 && request.method === "POST" || status === 307) && currentRetries < maxRetries) {
        const url = new URL(locationHeader, request.url);
        request.url = url.toString();
        // POST request with Status code 303 should be converted into a
        // redirected GET request if the redirect url is present in the location header
        if (status === 303) {
            request.method = "GET";
            request.headers.delete("Content-Length");
            delete request.body;
        }
        request.headers.delete("Authorization");
        const res = await next(request);
        return handleRedirect(next, res, maxRetries, currentRetries + 1);
    }
    return response;
} //# sourceMappingURL=redirectPolicy.js.map
}),
"[project]/node_modules/@typespec/ts-http-runtime/dist/esm/util/userAgentPlatform.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

// Copyright (c) Microsoft Corporation.
// Licensed under the MIT License.
__turbopack_context__.s({
    "getHeaderName": ()=>getHeaderName,
    "setPlatformSpecificData": ()=>setPlatformSpecificData
});
var __TURBOPACK__imported__module__$5b$externals$5d2f$node$3a$os__$5b$external$5d$__$28$node$3a$os$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/node:os [external] (node:os, cjs)");
var __TURBOPACK__imported__module__$5b$externals$5d2f$node$3a$process__$5b$external$5d$__$28$node$3a$process$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/node:process [external] (node:process, cjs)");
;
;
function getHeaderName() {
    return "User-Agent";
}
async function setPlatformSpecificData(map) {
    if (__TURBOPACK__imported__module__$5b$externals$5d2f$node$3a$process__$5b$external$5d$__$28$node$3a$process$2c$__cjs$29$__ && __TURBOPACK__imported__module__$5b$externals$5d2f$node$3a$process__$5b$external$5d$__$28$node$3a$process$2c$__cjs$29$__.versions) {
        const versions = __TURBOPACK__imported__module__$5b$externals$5d2f$node$3a$process__$5b$external$5d$__$28$node$3a$process$2c$__cjs$29$__.versions;
        if (versions.bun) {
            map.set("Bun", versions.bun);
        } else if (versions.deno) {
            map.set("Deno", versions.deno);
        } else if (versions.node) {
            map.set("Node", versions.node);
        }
    }
    map.set("OS", `(${__TURBOPACK__imported__module__$5b$externals$5d2f$node$3a$os__$5b$external$5d$__$28$node$3a$os$2c$__cjs$29$__["arch"]()}-${__TURBOPACK__imported__module__$5b$externals$5d2f$node$3a$os__$5b$external$5d$__$28$node$3a$os$2c$__cjs$29$__["type"]()}-${__TURBOPACK__imported__module__$5b$externals$5d2f$node$3a$os__$5b$external$5d$__$28$node$3a$os$2c$__cjs$29$__["release"]()})`);
} //# sourceMappingURL=userAgentPlatform.js.map
}),
"[project]/node_modules/@typespec/ts-http-runtime/dist/esm/constants.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

// Copyright (c) Microsoft Corporation.
// Licensed under the MIT License.
__turbopack_context__.s({
    "DEFAULT_RETRY_POLICY_COUNT": ()=>DEFAULT_RETRY_POLICY_COUNT,
    "SDK_VERSION": ()=>SDK_VERSION
});
const SDK_VERSION = "0.3.0";
const DEFAULT_RETRY_POLICY_COUNT = 3; //# sourceMappingURL=constants.js.map
}),
"[project]/node_modules/@typespec/ts-http-runtime/dist/esm/util/userAgent.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

// Copyright (c) Microsoft Corporation.
// Licensed under the MIT License.
__turbopack_context__.s({
    "getUserAgentHeaderName": ()=>getUserAgentHeaderName,
    "getUserAgentValue": ()=>getUserAgentValue
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$util$2f$userAgentPlatform$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@typespec/ts-http-runtime/dist/esm/util/userAgentPlatform.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@typespec/ts-http-runtime/dist/esm/constants.js [app-route] (ecmascript)");
;
;
function getUserAgentString(telemetryInfo) {
    const parts = [];
    for (const [key, value] of telemetryInfo){
        const token = value ? `${key}/${value}` : key;
        parts.push(token);
    }
    return parts.join(" ");
}
function getUserAgentHeaderName() {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$util$2f$userAgentPlatform$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getHeaderName"])();
}
async function getUserAgentValue(prefix) {
    const runtimeInfo = new Map();
    runtimeInfo.set("ts-http-runtime", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["SDK_VERSION"]);
    await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$util$2f$userAgentPlatform$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["setPlatformSpecificData"])(runtimeInfo);
    const defaultAgent = getUserAgentString(runtimeInfo);
    const userAgentValue = prefix ? `${prefix} ${defaultAgent}` : defaultAgent;
    return userAgentValue;
} //# sourceMappingURL=userAgent.js.map
}),
"[project]/node_modules/@typespec/ts-http-runtime/dist/esm/policies/userAgentPolicy.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

// Copyright (c) Microsoft Corporation.
// Licensed under the MIT License.
__turbopack_context__.s({
    "userAgentPolicy": ()=>userAgentPolicy,
    "userAgentPolicyName": ()=>userAgentPolicyName
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$util$2f$userAgent$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@typespec/ts-http-runtime/dist/esm/util/userAgent.js [app-route] (ecmascript)");
;
const UserAgentHeaderName = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$util$2f$userAgent$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getUserAgentHeaderName"])();
const userAgentPolicyName = "userAgentPolicy";
function userAgentPolicy(options = {}) {
    const userAgentValue = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$util$2f$userAgent$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getUserAgentValue"])(options.userAgentPrefix);
    return {
        name: userAgentPolicyName,
        async sendRequest (request, next) {
            if (!request.headers.has(UserAgentHeaderName)) {
                request.headers.set(UserAgentHeaderName, await userAgentValue);
            }
            return next(request);
        }
    };
} //# sourceMappingURL=userAgentPolicy.js.map
}),
"[project]/node_modules/@typespec/ts-http-runtime/dist/esm/policies/decompressResponsePolicy.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

// Copyright (c) Microsoft Corporation.
// Licensed under the MIT License.
/**
 * The programmatic identifier of the decompressResponsePolicy.
 */ __turbopack_context__.s({
    "decompressResponsePolicy": ()=>decompressResponsePolicy,
    "decompressResponsePolicyName": ()=>decompressResponsePolicyName
});
const decompressResponsePolicyName = "decompressResponsePolicy";
function decompressResponsePolicy() {
    return {
        name: decompressResponsePolicyName,
        async sendRequest (request, next) {
            // HEAD requests have no body
            if (request.method !== "HEAD") {
                request.headers.set("Accept-Encoding", "gzip,deflate");
            }
            return next(request);
        }
    };
} //# sourceMappingURL=decompressResponsePolicy.js.map
}),
"[project]/node_modules/@typespec/ts-http-runtime/dist/esm/util/helpers.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

// Copyright (c) Microsoft Corporation.
// Licensed under the MIT License.
__turbopack_context__.s({
    "delay": ()=>delay,
    "parseHeaderValueAsNumber": ()=>parseHeaderValueAsNumber
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$abort$2d$controller$2f$AbortError$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@typespec/ts-http-runtime/dist/esm/abort-controller/AbortError.js [app-route] (ecmascript)");
;
const StandardAbortMessage = "The operation was aborted.";
function delay(delayInMs, value, options) {
    return new Promise((resolve, reject)=>{
        let timer = undefined;
        let onAborted = undefined;
        const rejectOnAbort = ()=>{
            return reject(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$abort$2d$controller$2f$AbortError$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["AbortError"]((options === null || options === void 0 ? void 0 : options.abortErrorMsg) ? options === null || options === void 0 ? void 0 : options.abortErrorMsg : StandardAbortMessage));
        };
        const removeListeners = ()=>{
            if ((options === null || options === void 0 ? void 0 : options.abortSignal) && onAborted) {
                options.abortSignal.removeEventListener("abort", onAborted);
            }
        };
        onAborted = ()=>{
            if (timer) {
                clearTimeout(timer);
            }
            removeListeners();
            return rejectOnAbort();
        };
        if ((options === null || options === void 0 ? void 0 : options.abortSignal) && options.abortSignal.aborted) {
            return rejectOnAbort();
        }
        timer = setTimeout(()=>{
            removeListeners();
            resolve(value);
        }, delayInMs);
        if (options === null || options === void 0 ? void 0 : options.abortSignal) {
            options.abortSignal.addEventListener("abort", onAborted);
        }
    });
}
function parseHeaderValueAsNumber(response, headerName) {
    const value = response.headers.get(headerName);
    if (!value) return;
    const valueAsNum = Number(value);
    if (Number.isNaN(valueAsNum)) return;
    return valueAsNum;
} //# sourceMappingURL=helpers.js.map
}),
"[project]/node_modules/@typespec/ts-http-runtime/dist/esm/retryStrategies/throttlingRetryStrategy.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

// Copyright (c) Microsoft Corporation.
// Licensed under the MIT License.
__turbopack_context__.s({
    "isThrottlingRetryResponse": ()=>isThrottlingRetryResponse,
    "throttlingRetryStrategy": ()=>throttlingRetryStrategy
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$util$2f$helpers$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@typespec/ts-http-runtime/dist/esm/util/helpers.js [app-route] (ecmascript)");
;
/**
 * The header that comes back from services representing
 * the amount of time (minimum) to wait to retry (in seconds or timestamp after which we can retry).
 */ const RetryAfterHeader = "Retry-After";
/**
 * The headers that come back from services representing
 * the amount of time (minimum) to wait to retry.
 *
 * "retry-after-ms", "x-ms-retry-after-ms" : milliseconds
 * "Retry-After" : seconds or timestamp
 */ const AllRetryAfterHeaders = [
    "retry-after-ms",
    "x-ms-retry-after-ms",
    RetryAfterHeader
];
/**
 * A response is a throttling retry response if it has a throttling status code (429 or 503),
 * as long as one of the [ "Retry-After" or "retry-after-ms" or "x-ms-retry-after-ms" ] headers has a valid value.
 *
 * Returns the `retryAfterInMs` value if the response is a throttling retry response.
 * If not throttling retry response, returns `undefined`.
 *
 * @internal
 */ function getRetryAfterInMs(response) {
    if (!(response && [
        429,
        503
    ].includes(response.status))) return undefined;
    try {
        // Headers: "retry-after-ms", "x-ms-retry-after-ms", "Retry-After"
        for (const header of AllRetryAfterHeaders){
            const retryAfterValue = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$util$2f$helpers$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["parseHeaderValueAsNumber"])(response, header);
            if (retryAfterValue === 0 || retryAfterValue) {
                // "Retry-After" header ==> seconds
                // "retry-after-ms", "x-ms-retry-after-ms" headers ==> milli-seconds
                const multiplyingFactor = header === RetryAfterHeader ? 1000 : 1;
                return retryAfterValue * multiplyingFactor; // in milli-seconds
            }
        }
        // RetryAfterHeader ("Retry-After") has a special case where it might be formatted as a date instead of a number of seconds
        const retryAfterHeader = response.headers.get(RetryAfterHeader);
        if (!retryAfterHeader) return;
        const date = Date.parse(retryAfterHeader);
        const diff = date - Date.now();
        // negative diff would mean a date in the past, so retry asap with 0 milliseconds
        return Number.isFinite(diff) ? Math.max(0, diff) : undefined;
    } catch (_a) {
        return undefined;
    }
}
function isThrottlingRetryResponse(response) {
    return Number.isFinite(getRetryAfterInMs(response));
}
function throttlingRetryStrategy() {
    return {
        name: "throttlingRetryStrategy",
        retry ({ response }) {
            const retryAfterInMs = getRetryAfterInMs(response);
            if (!Number.isFinite(retryAfterInMs)) {
                return {
                    skipStrategy: true
                };
            }
            return {
                retryAfterInMs
            };
        }
    };
} //# sourceMappingURL=throttlingRetryStrategy.js.map
}),
"[project]/node_modules/@typespec/ts-http-runtime/dist/esm/retryStrategies/exponentialRetryStrategy.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

// Copyright (c) Microsoft Corporation.
// Licensed under the MIT License.
__turbopack_context__.s({
    "exponentialRetryStrategy": ()=>exponentialRetryStrategy,
    "isExponentialRetryResponse": ()=>isExponentialRetryResponse,
    "isSystemError": ()=>isSystemError
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$util$2f$delay$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@typespec/ts-http-runtime/dist/esm/util/delay.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$retryStrategies$2f$throttlingRetryStrategy$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@typespec/ts-http-runtime/dist/esm/retryStrategies/throttlingRetryStrategy.js [app-route] (ecmascript)");
;
;
// intervals are in milliseconds
const DEFAULT_CLIENT_RETRY_INTERVAL = 1000;
const DEFAULT_CLIENT_MAX_RETRY_INTERVAL = 1000 * 64;
function exponentialRetryStrategy(options = {}) {
    var _a, _b;
    const retryInterval = (_a = options.retryDelayInMs) !== null && _a !== void 0 ? _a : DEFAULT_CLIENT_RETRY_INTERVAL;
    const maxRetryInterval = (_b = options.maxRetryDelayInMs) !== null && _b !== void 0 ? _b : DEFAULT_CLIENT_MAX_RETRY_INTERVAL;
    return {
        name: "exponentialRetryStrategy",
        retry ({ retryCount, response, responseError }) {
            const matchedSystemError = isSystemError(responseError);
            const ignoreSystemErrors = matchedSystemError && options.ignoreSystemErrors;
            const isExponential = isExponentialRetryResponse(response);
            const ignoreExponentialResponse = isExponential && options.ignoreHttpStatusCodes;
            const unknownResponse = response && ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$retryStrategies$2f$throttlingRetryStrategy$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isThrottlingRetryResponse"])(response) || !isExponential);
            if (unknownResponse || ignoreExponentialResponse || ignoreSystemErrors) {
                return {
                    skipStrategy: true
                };
            }
            if (responseError && !matchedSystemError && !isExponential) {
                return {
                    errorToThrow: responseError
                };
            }
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$util$2f$delay$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["calculateRetryDelay"])(retryCount, {
                retryDelayInMs: retryInterval,
                maxRetryDelayInMs: maxRetryInterval
            });
        }
    };
}
function isExponentialRetryResponse(response) {
    return Boolean(response && response.status !== undefined && (response.status >= 500 || response.status === 408) && response.status !== 501 && response.status !== 505);
}
function isSystemError(err) {
    if (!err) {
        return false;
    }
    return err.code === "ETIMEDOUT" || err.code === "ESOCKETTIMEDOUT" || err.code === "ECONNREFUSED" || err.code === "ECONNRESET" || err.code === "ENOENT" || err.code === "ENOTFOUND";
} //# sourceMappingURL=exponentialRetryStrategy.js.map
}),
"[project]/node_modules/@typespec/ts-http-runtime/dist/esm/policies/retryPolicy.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

// Copyright (c) Microsoft Corporation.
// Licensed under the MIT License.
__turbopack_context__.s({
    "retryPolicy": ()=>retryPolicy
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$util$2f$helpers$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@typespec/ts-http-runtime/dist/esm/util/helpers.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$abort$2d$controller$2f$AbortError$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@typespec/ts-http-runtime/dist/esm/abort-controller/AbortError.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$logger$2f$logger$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@typespec/ts-http-runtime/dist/esm/logger/logger.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@typespec/ts-http-runtime/dist/esm/constants.js [app-route] (ecmascript)");
;
;
;
;
const retryPolicyLogger = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$logger$2f$logger$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createClientLogger"])("ts-http-runtime retryPolicy");
/**
 * The programmatic identifier of the retryPolicy.
 */ const retryPolicyName = "retryPolicy";
function retryPolicy(strategies, options = {
    maxRetries: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["DEFAULT_RETRY_POLICY_COUNT"]
}) {
    const logger = options.logger || retryPolicyLogger;
    return {
        name: retryPolicyName,
        async sendRequest (request, next) {
            var _a, _b;
            let response;
            let responseError;
            let retryCount = -1;
            retryRequest: while(true){
                retryCount += 1;
                response = undefined;
                responseError = undefined;
                try {
                    logger.info(`Retry ${retryCount}: Attempting to send request`, request.requestId);
                    response = await next(request);
                    logger.info(`Retry ${retryCount}: Received a response from request`, request.requestId);
                } catch (e) {
                    logger.error(`Retry ${retryCount}: Received an error from request`, request.requestId);
                    // RestErrors are valid targets for the retry strategies.
                    // If none of the retry strategies can work with them, they will be thrown later in this policy.
                    // If the received error is not a RestError, it is immediately thrown.
                    responseError = e;
                    if (!e || responseError.name !== "RestError") {
                        throw e;
                    }
                    response = responseError.response;
                }
                if ((_a = request.abortSignal) === null || _a === void 0 ? void 0 : _a.aborted) {
                    logger.error(`Retry ${retryCount}: Request aborted.`);
                    const abortError = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$abort$2d$controller$2f$AbortError$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["AbortError"]();
                    throw abortError;
                }
                if (retryCount >= ((_b = options.maxRetries) !== null && _b !== void 0 ? _b : __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["DEFAULT_RETRY_POLICY_COUNT"])) {
                    logger.info(`Retry ${retryCount}: Maximum retries reached. Returning the last received response, or throwing the last received error.`);
                    if (responseError) {
                        throw responseError;
                    } else if (response) {
                        return response;
                    } else {
                        throw new Error("Maximum retries reached with no response or error to throw");
                    }
                }
                logger.info(`Retry ${retryCount}: Processing ${strategies.length} retry strategies.`);
                strategiesLoop: for (const strategy of strategies){
                    const strategyLogger = strategy.logger || logger;
                    strategyLogger.info(`Retry ${retryCount}: Processing retry strategy ${strategy.name}.`);
                    const modifiers = strategy.retry({
                        retryCount,
                        response,
                        responseError
                    });
                    if (modifiers.skipStrategy) {
                        strategyLogger.info(`Retry ${retryCount}: Skipped.`);
                        continue strategiesLoop;
                    }
                    const { errorToThrow, retryAfterInMs, redirectTo } = modifiers;
                    if (errorToThrow) {
                        strategyLogger.error(`Retry ${retryCount}: Retry strategy ${strategy.name} throws error:`, errorToThrow);
                        throw errorToThrow;
                    }
                    if (retryAfterInMs || retryAfterInMs === 0) {
                        strategyLogger.info(`Retry ${retryCount}: Retry strategy ${strategy.name} retries after ${retryAfterInMs}`);
                        await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$util$2f$helpers$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["delay"])(retryAfterInMs, undefined, {
                            abortSignal: request.abortSignal
                        });
                        continue retryRequest;
                    }
                    if (redirectTo) {
                        strategyLogger.info(`Retry ${retryCount}: Retry strategy ${strategy.name} redirects to ${redirectTo}`);
                        request.url = redirectTo;
                        continue retryRequest;
                    }
                }
                if (responseError) {
                    logger.info(`None of the retry strategies could work with the received error. Throwing it.`);
                    throw responseError;
                }
                if (response) {
                    logger.info(`None of the retry strategies could work with the received response. Returning it.`);
                    return response;
                }
            // If all the retries skip and there's no response,
            // we're still in the retry loop, so a new request will be sent
            // until `maxRetries` is reached.
            }
        }
    };
} //# sourceMappingURL=retryPolicy.js.map
}),
"[project]/node_modules/@typespec/ts-http-runtime/dist/esm/policies/defaultRetryPolicy.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

// Copyright (c) Microsoft Corporation.
// Licensed under the MIT License.
__turbopack_context__.s({
    "defaultRetryPolicy": ()=>defaultRetryPolicy,
    "defaultRetryPolicyName": ()=>defaultRetryPolicyName
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$retryStrategies$2f$exponentialRetryStrategy$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@typespec/ts-http-runtime/dist/esm/retryStrategies/exponentialRetryStrategy.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$retryStrategies$2f$throttlingRetryStrategy$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@typespec/ts-http-runtime/dist/esm/retryStrategies/throttlingRetryStrategy.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$policies$2f$retryPolicy$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@typespec/ts-http-runtime/dist/esm/policies/retryPolicy.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@typespec/ts-http-runtime/dist/esm/constants.js [app-route] (ecmascript)");
;
;
;
;
const defaultRetryPolicyName = "defaultRetryPolicy";
function defaultRetryPolicy(options = {}) {
    var _a;
    return {
        name: defaultRetryPolicyName,
        sendRequest: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$policies$2f$retryPolicy$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["retryPolicy"])([
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$retryStrategies$2f$throttlingRetryStrategy$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["throttlingRetryStrategy"])(),
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$retryStrategies$2f$exponentialRetryStrategy$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["exponentialRetryStrategy"])(options)
        ], {
            maxRetries: (_a = options.maxRetries) !== null && _a !== void 0 ? _a : __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["DEFAULT_RETRY_POLICY_COUNT"]
        }).sendRequest
    };
} //# sourceMappingURL=defaultRetryPolicy.js.map
}),
"[project]/node_modules/@typespec/ts-http-runtime/dist/esm/policies/formDataPolicy.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

// Copyright (c) Microsoft Corporation.
// Licensed under the MIT License.
__turbopack_context__.s({
    "formDataPolicy": ()=>formDataPolicy,
    "formDataPolicyName": ()=>formDataPolicyName
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$util$2f$bytesEncoding$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@typespec/ts-http-runtime/dist/esm/util/bytesEncoding.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$util$2f$checkEnvironment$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@typespec/ts-http-runtime/dist/esm/util/checkEnvironment.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$httpHeaders$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@typespec/ts-http-runtime/dist/esm/httpHeaders.js [app-route] (ecmascript)");
;
;
;
const formDataPolicyName = "formDataPolicy";
function formDataToFormDataMap(formData) {
    var _a;
    const formDataMap = {};
    for (const [key, value] of formData.entries()){
        (_a = formDataMap[key]) !== null && _a !== void 0 ? _a : formDataMap[key] = [];
        formDataMap[key].push(value);
    }
    return formDataMap;
}
function formDataPolicy() {
    return {
        name: formDataPolicyName,
        async sendRequest (request, next) {
            if (__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$util$2f$checkEnvironment$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isNodeLike"] && typeof FormData !== "undefined" && request.body instanceof FormData) {
                request.formData = formDataToFormDataMap(request.body);
                request.body = undefined;
            }
            if (request.formData) {
                const contentType = request.headers.get("Content-Type");
                if (contentType && contentType.indexOf("application/x-www-form-urlencoded") !== -1) {
                    request.body = wwwFormUrlEncode(request.formData);
                } else {
                    await prepareFormData(request.formData, request);
                }
                request.formData = undefined;
            }
            return next(request);
        }
    };
}
function wwwFormUrlEncode(formData) {
    const urlSearchParams = new URLSearchParams();
    for (const [key, value] of Object.entries(formData)){
        if (Array.isArray(value)) {
            for (const subValue of value){
                urlSearchParams.append(key, subValue.toString());
            }
        } else {
            urlSearchParams.append(key, value.toString());
        }
    }
    return urlSearchParams.toString();
}
async function prepareFormData(formData, request) {
    // validate content type (multipart/form-data)
    const contentType = request.headers.get("Content-Type");
    if (contentType && !contentType.startsWith("multipart/form-data")) {
        // content type is specified and is not multipart/form-data. Exit.
        return;
    }
    request.headers.set("Content-Type", contentType !== null && contentType !== void 0 ? contentType : "multipart/form-data");
    // set body to MultipartRequestBody using content from FormDataMap
    const parts = [];
    for (const [fieldName, values] of Object.entries(formData)){
        for (const value of Array.isArray(values) ? values : [
            values
        ]){
            if (typeof value === "string") {
                parts.push({
                    headers: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$httpHeaders$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createHttpHeaders"])({
                        "Content-Disposition": `form-data; name="${fieldName}"`
                    }),
                    body: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$util$2f$bytesEncoding$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["stringToUint8Array"])(value, "utf-8")
                });
            } else if (value === undefined || value === null || typeof value !== "object") {
                throw new Error(`Unexpected value for key ${fieldName}: ${value}. Value should be serialized to string first.`);
            } else {
                // using || instead of ?? here since if value.name is empty we should create a file name
                const fileName = value.name || "blob";
                const headers = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$httpHeaders$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createHttpHeaders"])();
                headers.set("Content-Disposition", `form-data; name="${fieldName}"; filename="${fileName}"`);
                // again, || is used since an empty value.type means the content type is unset
                headers.set("Content-Type", value.type || "application/octet-stream");
                parts.push({
                    headers,
                    body: value
                });
            }
        }
    }
    request.multipartBody = {
        parts
    };
} //# sourceMappingURL=formDataPolicy.js.map
}),
"[project]/node_modules/@typespec/ts-http-runtime/dist/esm/policies/proxyPolicy.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

// Copyright (c) Microsoft Corporation.
// Licensed under the MIT License.
__turbopack_context__.s({
    "getDefaultProxySettings": ()=>getDefaultProxySettings,
    "globalNoProxyList": ()=>globalNoProxyList,
    "loadNoProxy": ()=>loadNoProxy,
    "proxyPolicy": ()=>proxyPolicy,
    "proxyPolicyName": ()=>proxyPolicyName
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$https$2d$proxy$2d$agent$2f$dist$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/https-proxy-agent/dist/index.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$http$2d$proxy$2d$agent$2f$dist$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/http-proxy-agent/dist/index.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$log$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@typespec/ts-http-runtime/dist/esm/log.js [app-route] (ecmascript)");
;
;
;
const HTTPS_PROXY = "HTTPS_PROXY";
const HTTP_PROXY = "HTTP_PROXY";
const ALL_PROXY = "ALL_PROXY";
const NO_PROXY = "NO_PROXY";
const proxyPolicyName = "proxyPolicy";
const globalNoProxyList = [];
let noProxyListLoaded = false;
/** A cache of whether a host should bypass the proxy. */ const globalBypassedMap = new Map();
function getEnvironmentValue(name) {
    if (process.env[name]) {
        return process.env[name];
    } else if (process.env[name.toLowerCase()]) {
        return process.env[name.toLowerCase()];
    }
    return undefined;
}
function loadEnvironmentProxyValue() {
    if ("TURBOPACK compile-time falsy", 0) //TURBOPACK unreachable
    ;
    const httpsProxy = getEnvironmentValue(HTTPS_PROXY);
    const allProxy = getEnvironmentValue(ALL_PROXY);
    const httpProxy = getEnvironmentValue(HTTP_PROXY);
    return httpsProxy || allProxy || httpProxy;
}
/**
 * Check whether the host of a given `uri` matches any pattern in the no proxy list.
 * If there's a match, any request sent to the same host shouldn't have the proxy settings set.
 * This implementation is a port of https://github.com/Azure/azure-sdk-for-net/blob/8cca811371159e527159c7eb65602477898683e2/sdk/core/Azure.Core/src/Pipeline/Internal/HttpEnvironmentProxy.cs#L210
 */ function isBypassed(uri, noProxyList, bypassedMap) {
    if (noProxyList.length === 0) {
        return false;
    }
    const host = new URL(uri).hostname;
    if (bypassedMap === null || bypassedMap === void 0 ? void 0 : bypassedMap.has(host)) {
        return bypassedMap.get(host);
    }
    let isBypassedFlag = false;
    for (const pattern of noProxyList){
        if (pattern[0] === ".") {
            // This should match either domain it self or any subdomain or host
            // .foo.com will match foo.com it self or *.foo.com
            if (host.endsWith(pattern)) {
                isBypassedFlag = true;
            } else {
                if (host.length === pattern.length - 1 && host === pattern.slice(1)) {
                    isBypassedFlag = true;
                }
            }
        } else {
            if (host === pattern) {
                isBypassedFlag = true;
            }
        }
    }
    bypassedMap === null || bypassedMap === void 0 ? void 0 : bypassedMap.set(host, isBypassedFlag);
    return isBypassedFlag;
}
function loadNoProxy() {
    const noProxy = getEnvironmentValue(NO_PROXY);
    noProxyListLoaded = true;
    if (noProxy) {
        return noProxy.split(",").map((item)=>item.trim()).filter((item)=>item.length);
    }
    return [];
}
function getDefaultProxySettings(proxyUrl) {
    if (!proxyUrl) {
        proxyUrl = loadEnvironmentProxyValue();
        if (!proxyUrl) {
            return undefined;
        }
    }
    const parsedUrl = new URL(proxyUrl);
    const schema = parsedUrl.protocol ? parsedUrl.protocol + "//" : "";
    return {
        host: schema + parsedUrl.hostname,
        port: Number.parseInt(parsedUrl.port || "80"),
        username: parsedUrl.username,
        password: parsedUrl.password
    };
}
/**
 * This method attempts to parse a proxy URL from the environment
 * variables `HTTPS_PROXY` or `HTTP_PROXY`.
 */ function getDefaultProxySettingsInternal() {
    const envProxy = loadEnvironmentProxyValue();
    return envProxy ? new URL(envProxy) : undefined;
}
function getUrlFromProxySettings(settings) {
    let parsedProxyUrl;
    try {
        parsedProxyUrl = new URL(settings.host);
    } catch (_a) {
        throw new Error(`Expecting a valid host string in proxy settings, but found "${settings.host}".`);
    }
    parsedProxyUrl.port = String(settings.port);
    if (settings.username) {
        parsedProxyUrl.username = settings.username;
    }
    if (settings.password) {
        parsedProxyUrl.password = settings.password;
    }
    return parsedProxyUrl;
}
function setProxyAgentOnRequest(request, cachedAgents, proxyUrl) {
    // Custom Agent should take precedence so if one is present
    // we should skip to avoid overwriting it.
    if (request.agent) {
        return;
    }
    const url = new URL(request.url);
    const isInsecure = url.protocol !== "https:";
    if (request.tlsSettings) {
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$log$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["logger"].warning("TLS settings are not supported in combination with custom Proxy, certificates provided to the client will be ignored.");
    }
    const headers = request.headers.toJSON();
    if (isInsecure) {
        if (!cachedAgents.httpProxyAgent) {
            cachedAgents.httpProxyAgent = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$http$2d$proxy$2d$agent$2f$dist$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HttpProxyAgent"](proxyUrl, {
                headers
            });
        }
        request.agent = cachedAgents.httpProxyAgent;
    } else {
        if (!cachedAgents.httpsProxyAgent) {
            cachedAgents.httpsProxyAgent = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$https$2d$proxy$2d$agent$2f$dist$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HttpsProxyAgent"](proxyUrl, {
                headers
            });
        }
        request.agent = cachedAgents.httpsProxyAgent;
    }
}
function proxyPolicy(proxySettings, options) {
    if (!noProxyListLoaded) {
        globalNoProxyList.push(...loadNoProxy());
    }
    const defaultProxy = proxySettings ? getUrlFromProxySettings(proxySettings) : getDefaultProxySettingsInternal();
    const cachedAgents = {};
    return {
        name: proxyPolicyName,
        async sendRequest (request, next) {
            var _a;
            if (!request.proxySettings && defaultProxy && !isBypassed(request.url, (_a = options === null || options === void 0 ? void 0 : options.customNoProxyList) !== null && _a !== void 0 ? _a : globalNoProxyList, (options === null || options === void 0 ? void 0 : options.customNoProxyList) ? undefined : globalBypassedMap)) {
                setProxyAgentOnRequest(request, cachedAgents, defaultProxy);
            } else if (request.proxySettings) {
                setProxyAgentOnRequest(request, cachedAgents, getUrlFromProxySettings(request.proxySettings));
            }
            return next(request);
        }
    };
} //# sourceMappingURL=proxyPolicy.js.map
}),
"[project]/node_modules/@typespec/ts-http-runtime/dist/esm/policies/agentPolicy.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

// Copyright (c) Microsoft Corporation.
// Licensed under the MIT License.
/**
 * Name of the Agent Policy
 */ __turbopack_context__.s({
    "agentPolicy": ()=>agentPolicy,
    "agentPolicyName": ()=>agentPolicyName
});
const agentPolicyName = "agentPolicy";
function agentPolicy(agent) {
    return {
        name: agentPolicyName,
        sendRequest: async (req, next)=>{
            // Users may define an agent on the request, honor it over the client level one
            if (!req.agent) {
                req.agent = agent;
            }
            return next(req);
        }
    };
} //# sourceMappingURL=agentPolicy.js.map
}),
"[project]/node_modules/@typespec/ts-http-runtime/dist/esm/policies/tlsPolicy.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

// Copyright (c) Microsoft Corporation.
// Licensed under the MIT License.
/**
 * Name of the TLS Policy
 */ __turbopack_context__.s({
    "tlsPolicy": ()=>tlsPolicy,
    "tlsPolicyName": ()=>tlsPolicyName
});
const tlsPolicyName = "tlsPolicy";
function tlsPolicy(tlsSettings) {
    return {
        name: tlsPolicyName,
        sendRequest: async (req, next)=>{
            // Users may define a request tlsSettings, honor those over the client level one
            if (!req.tlsSettings) {
                req.tlsSettings = tlsSettings;
            }
            return next(req);
        }
    };
} //# sourceMappingURL=tlsPolicy.js.map
}),
"[project]/node_modules/@typespec/ts-http-runtime/dist/esm/util/typeGuards.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

// Copyright (c) Microsoft Corporation.
// Licensed under the MIT License.
__turbopack_context__.s({
    "isBinaryBody": ()=>isBinaryBody,
    "isBlob": ()=>isBlob,
    "isNodeReadableStream": ()=>isNodeReadableStream,
    "isReadableStream": ()=>isReadableStream,
    "isWebReadableStream": ()=>isWebReadableStream
});
function isNodeReadableStream(x) {
    return Boolean(x && typeof x["pipe"] === "function");
}
function isWebReadableStream(x) {
    return Boolean(x && typeof x.getReader === "function" && typeof x.tee === "function");
}
function isBinaryBody(body) {
    return body !== undefined && (body instanceof Uint8Array || isReadableStream(body) || typeof body === "function" || body instanceof Blob);
}
function isReadableStream(x) {
    return isNodeReadableStream(x) || isWebReadableStream(x);
}
function isBlob(x) {
    return typeof x.stream === "function";
} //# sourceMappingURL=typeGuards.js.map
}),
"[project]/node_modules/@typespec/ts-http-runtime/dist/esm/util/concat.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

// Copyright (c) Microsoft Corporation.
// Licensed under the MIT License.
__turbopack_context__.s({
    "concat": ()=>concat
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$tslib$2f$tslib$2e$es6$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/tslib/tslib.es6.mjs [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$externals$5d2f$stream__$5b$external$5d$__$28$stream$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/stream [external] (stream, cjs)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$util$2f$typeGuards$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@typespec/ts-http-runtime/dist/esm/util/typeGuards.js [app-route] (ecmascript)");
;
;
;
function streamAsyncIterator() {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$tslib$2f$tslib$2e$es6$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["__asyncGenerator"])(this, arguments, function* streamAsyncIterator_1() {
        const reader = this.getReader();
        try {
            while(true){
                const { done, value } = yield (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$tslib$2f$tslib$2e$es6$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["__await"])(reader.read());
                if (done) {
                    return yield (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$tslib$2f$tslib$2e$es6$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["__await"])(void 0);
                }
                yield yield (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$tslib$2f$tslib$2e$es6$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["__await"])(value);
            }
        } finally{
            reader.releaseLock();
        }
    });
}
function makeAsyncIterable(webStream) {
    if (!webStream[Symbol.asyncIterator]) {
        webStream[Symbol.asyncIterator] = streamAsyncIterator.bind(webStream);
    }
    if (!webStream.values) {
        webStream.values = streamAsyncIterator.bind(webStream);
    }
}
function ensureNodeStream(stream) {
    if (stream instanceof ReadableStream) {
        makeAsyncIterable(stream);
        return __TURBOPACK__imported__module__$5b$externals$5d2f$stream__$5b$external$5d$__$28$stream$2c$__cjs$29$__["Readable"].fromWeb(stream);
    } else {
        return stream;
    }
}
function toStream(source) {
    if (source instanceof Uint8Array) {
        return __TURBOPACK__imported__module__$5b$externals$5d2f$stream__$5b$external$5d$__$28$stream$2c$__cjs$29$__["Readable"].from(Buffer.from(source));
    } else if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$util$2f$typeGuards$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isBlob"])(source)) {
        return ensureNodeStream(source.stream());
    } else {
        return ensureNodeStream(source);
    }
}
async function concat(sources) {
    return function() {
        const streams = sources.map((x)=>typeof x === "function" ? x() : x).map(toStream);
        return __TURBOPACK__imported__module__$5b$externals$5d2f$stream__$5b$external$5d$__$28$stream$2c$__cjs$29$__["Readable"].from(function() {
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$tslib$2f$tslib$2e$es6$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["__asyncGenerator"])(this, arguments, function*() {
                var _a, e_1, _b, _c;
                for (const stream of streams){
                    try {
                        for(var _d = true, stream_1 = (e_1 = void 0, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$tslib$2f$tslib$2e$es6$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["__asyncValues"])(stream)), stream_1_1; stream_1_1 = yield (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$tslib$2f$tslib$2e$es6$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["__await"])(stream_1.next()), _a = stream_1_1.done, !_a; _d = true){
                            _c = stream_1_1.value;
                            _d = false;
                            const chunk = _c;
                            yield yield (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$tslib$2f$tslib$2e$es6$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["__await"])(chunk);
                        }
                    } catch (e_1_1) {
                        e_1 = {
                            error: e_1_1
                        };
                    } finally{
                        try {
                            if (!_d && !_a && (_b = stream_1.return)) yield (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$tslib$2f$tslib$2e$es6$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["__await"])(_b.call(stream_1));
                        } finally{
                            if (e_1) throw e_1.error;
                        }
                    }
                }
            });
        }());
    };
} //# sourceMappingURL=concat.js.map
}),
"[project]/node_modules/@typespec/ts-http-runtime/dist/esm/policies/multipartPolicy.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

// Copyright (c) Microsoft Corporation.
// Licensed under the MIT License.
__turbopack_context__.s({
    "multipartPolicy": ()=>multipartPolicy,
    "multipartPolicyName": ()=>multipartPolicyName
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$util$2f$bytesEncoding$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@typespec/ts-http-runtime/dist/esm/util/bytesEncoding.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$util$2f$typeGuards$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@typespec/ts-http-runtime/dist/esm/util/typeGuards.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$util$2f$uuidUtils$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@typespec/ts-http-runtime/dist/esm/util/uuidUtils.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$util$2f$concat$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@typespec/ts-http-runtime/dist/esm/util/concat.js [app-route] (ecmascript)");
;
;
;
;
function generateBoundary() {
    return `----AzSDKFormBoundary${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$util$2f$uuidUtils$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["randomUUID"])()}`;
}
function encodeHeaders(headers) {
    let result = "";
    for (const [key, value] of headers){
        result += `${key}: ${value}\r\n`;
    }
    return result;
}
function getLength(source) {
    if (source instanceof Uint8Array) {
        return source.byteLength;
    } else if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$util$2f$typeGuards$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isBlob"])(source)) {
        // if was created using createFile then -1 means we have an unknown size
        return source.size === -1 ? undefined : source.size;
    } else {
        return undefined;
    }
}
function getTotalLength(sources) {
    let total = 0;
    for (const source of sources){
        const partLength = getLength(source);
        if (partLength === undefined) {
            return undefined;
        } else {
            total += partLength;
        }
    }
    return total;
}
async function buildRequestBody(request, parts, boundary) {
    const sources = [
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$util$2f$bytesEncoding$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["stringToUint8Array"])(`--${boundary}`, "utf-8"),
        ...parts.flatMap((part)=>[
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$util$2f$bytesEncoding$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["stringToUint8Array"])("\r\n", "utf-8"),
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$util$2f$bytesEncoding$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["stringToUint8Array"])(encodeHeaders(part.headers), "utf-8"),
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$util$2f$bytesEncoding$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["stringToUint8Array"])("\r\n", "utf-8"),
                part.body,
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$util$2f$bytesEncoding$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["stringToUint8Array"])(`\r\n--${boundary}`, "utf-8")
            ]),
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$util$2f$bytesEncoding$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["stringToUint8Array"])("--\r\n\r\n", "utf-8")
    ];
    const contentLength = getTotalLength(sources);
    if (contentLength) {
        request.headers.set("Content-Length", contentLength);
    }
    request.body = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$util$2f$concat$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["concat"])(sources);
}
const multipartPolicyName = "multipartPolicy";
const maxBoundaryLength = 70;
const validBoundaryCharacters = new Set(`abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'()+,-./:=?`);
function assertValidBoundary(boundary) {
    if (boundary.length > maxBoundaryLength) {
        throw new Error(`Multipart boundary "${boundary}" exceeds maximum length of 70 characters`);
    }
    if (Array.from(boundary).some((x)=>!validBoundaryCharacters.has(x))) {
        throw new Error(`Multipart boundary "${boundary}" contains invalid characters`);
    }
}
function multipartPolicy() {
    return {
        name: multipartPolicyName,
        async sendRequest (request, next) {
            var _a;
            if (!request.multipartBody) {
                return next(request);
            }
            if (request.body) {
                throw new Error("multipartBody and regular body cannot be set at the same time");
            }
            let boundary = request.multipartBody.boundary;
            const contentTypeHeader = (_a = request.headers.get("Content-Type")) !== null && _a !== void 0 ? _a : "multipart/mixed";
            const parsedHeader = contentTypeHeader.match(/^(multipart\/[^ ;]+)(?:; *boundary=(.+))?$/);
            if (!parsedHeader) {
                throw new Error(`Got multipart request body, but content-type header was not multipart: ${contentTypeHeader}`);
            }
            const [, contentType, parsedBoundary] = parsedHeader;
            if (parsedBoundary && boundary && parsedBoundary !== boundary) {
                throw new Error(`Multipart boundary was specified as ${parsedBoundary} in the header, but got ${boundary} in the request body`);
            }
            boundary !== null && boundary !== void 0 ? boundary : boundary = parsedBoundary;
            if (boundary) {
                assertValidBoundary(boundary);
            } else {
                boundary = generateBoundary();
            }
            request.headers.set("Content-Type", `${contentType}; boundary=${boundary}`);
            await buildRequestBody(request, request.multipartBody.parts, boundary);
            request.multipartBody = undefined;
            return next(request);
        }
    };
} //# sourceMappingURL=multipartPolicy.js.map
}),
"[project]/node_modules/@typespec/ts-http-runtime/dist/esm/createPipelineFromOptions.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

// Copyright (c) Microsoft Corporation.
// Licensed under the MIT License.
__turbopack_context__.s({
    "createPipelineFromOptions": ()=>createPipelineFromOptions
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$policies$2f$logPolicy$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@typespec/ts-http-runtime/dist/esm/policies/logPolicy.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$pipeline$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@typespec/ts-http-runtime/dist/esm/pipeline.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$policies$2f$redirectPolicy$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@typespec/ts-http-runtime/dist/esm/policies/redirectPolicy.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$policies$2f$userAgentPolicy$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@typespec/ts-http-runtime/dist/esm/policies/userAgentPolicy.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$policies$2f$decompressResponsePolicy$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@typespec/ts-http-runtime/dist/esm/policies/decompressResponsePolicy.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$policies$2f$defaultRetryPolicy$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@typespec/ts-http-runtime/dist/esm/policies/defaultRetryPolicy.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$policies$2f$formDataPolicy$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@typespec/ts-http-runtime/dist/esm/policies/formDataPolicy.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$util$2f$checkEnvironment$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@typespec/ts-http-runtime/dist/esm/util/checkEnvironment.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$policies$2f$proxyPolicy$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@typespec/ts-http-runtime/dist/esm/policies/proxyPolicy.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$policies$2f$agentPolicy$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@typespec/ts-http-runtime/dist/esm/policies/agentPolicy.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$policies$2f$tlsPolicy$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@typespec/ts-http-runtime/dist/esm/policies/tlsPolicy.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$policies$2f$multipartPolicy$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@typespec/ts-http-runtime/dist/esm/policies/multipartPolicy.js [app-route] (ecmascript)");
;
;
;
;
;
;
;
;
;
;
;
;
function createPipelineFromOptions(options) {
    const pipeline = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$pipeline$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createEmptyPipeline"])();
    if (__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$util$2f$checkEnvironment$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isNodeLike"]) {
        if (options.agent) {
            pipeline.addPolicy((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$policies$2f$agentPolicy$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["agentPolicy"])(options.agent));
        }
        if (options.tlsOptions) {
            pipeline.addPolicy((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$policies$2f$tlsPolicy$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["tlsPolicy"])(options.tlsOptions));
        }
        pipeline.addPolicy((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$policies$2f$proxyPolicy$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["proxyPolicy"])(options.proxyOptions));
        pipeline.addPolicy((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$policies$2f$decompressResponsePolicy$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["decompressResponsePolicy"])());
    }
    pipeline.addPolicy((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$policies$2f$formDataPolicy$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["formDataPolicy"])(), {
        beforePolicies: [
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$policies$2f$multipartPolicy$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["multipartPolicyName"]
        ]
    });
    pipeline.addPolicy((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$policies$2f$userAgentPolicy$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["userAgentPolicy"])(options.userAgentOptions));
    // The multipart policy is added after policies with no phase, so that
    // policies can be added between it and formDataPolicy to modify
    // properties (e.g., making the boundary constant in recorded tests).
    pipeline.addPolicy((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$policies$2f$multipartPolicy$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["multipartPolicy"])(), {
        afterPhase: "Deserialize"
    });
    pipeline.addPolicy((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$policies$2f$defaultRetryPolicy$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["defaultRetryPolicy"])(options.retryOptions), {
        phase: "Retry"
    });
    if (__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$util$2f$checkEnvironment$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isNodeLike"]) {
        // Both XHR and Fetch expect to handle redirects automatically,
        // so only include this policy when we're in Node.
        pipeline.addPolicy((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$policies$2f$redirectPolicy$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["redirectPolicy"])(options.redirectOptions), {
            afterPhase: "Retry"
        });
    }
    pipeline.addPolicy((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$policies$2f$logPolicy$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["logPolicy"])(options.loggingOptions), {
        afterPhase: "Sign"
    });
    return pipeline;
} //# sourceMappingURL=createPipelineFromOptions.js.map
}),
"[project]/node_modules/@typespec/ts-http-runtime/dist/esm/client/apiVersionPolicy.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

// Copyright (c) Microsoft Corporation.
// Licensed under the MIT License.
__turbopack_context__.s({
    "apiVersionPolicy": ()=>apiVersionPolicy,
    "apiVersionPolicyName": ()=>apiVersionPolicyName
});
const apiVersionPolicyName = "ApiVersionPolicy";
function apiVersionPolicy(options) {
    return {
        name: apiVersionPolicyName,
        sendRequest: (req, next)=>{
            // Use the apiVesion defined in request url directly
            // Append one if there is no apiVesion and we have one at client options
            const url = new URL(req.url);
            if (!url.searchParams.get("api-version") && options.apiVersion) {
                req.url = `${req.url}${Array.from(url.searchParams.keys()).length > 0 ? "&" : "?"}api-version=${options.apiVersion}`;
            }
            return next(req);
        }
    };
} //# sourceMappingURL=apiVersionPolicy.js.map
}),
"[project]/node_modules/@typespec/ts-http-runtime/dist/esm/auth/credentials.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

// Copyright (c) Microsoft Corporation.
// Licensed under the MIT License.
/**
 * Type guard to check if a credential is an OAuth2 token credential.
 */ __turbopack_context__.s({
    "isApiKeyCredential": ()=>isApiKeyCredential,
    "isBasicCredential": ()=>isBasicCredential,
    "isBearerTokenCredential": ()=>isBearerTokenCredential,
    "isOAuth2TokenCredential": ()=>isOAuth2TokenCredential
});
function isOAuth2TokenCredential(credential) {
    return "getOAuth2Token" in credential;
}
function isBearerTokenCredential(credential) {
    return "getBearerToken" in credential;
}
function isBasicCredential(credential) {
    return "username" in credential && "password" in credential;
}
function isApiKeyCredential(credential) {
    return "key" in credential;
} //# sourceMappingURL=credentials.js.map
}),
"[project]/node_modules/@typespec/ts-http-runtime/dist/esm/policies/auth/checkInsecureConnection.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

// Copyright (c) Microsoft Corporation.
// Licensed under the MIT License.
__turbopack_context__.s({
    "ensureSecureConnection": ()=>ensureSecureConnection
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$log$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@typespec/ts-http-runtime/dist/esm/log.js [app-route] (ecmascript)");
;
// Ensure the warining is only emitted once
let insecureConnectionWarningEmmitted = false;
/**
 * Checks if the request is allowed to be sent over an insecure connection.
 *
 * A request is allowed to be sent over an insecure connection when:
 * - The `allowInsecureConnection` option is set to `true`.
 * - The request has the `allowInsecureConnection` property set to `true`.
 * - The request is being sent to `localhost` or `127.0.0.1`
 */ function allowInsecureConnection(request, options) {
    if (options.allowInsecureConnection && request.allowInsecureConnection) {
        const url = new URL(request.url);
        if (url.hostname === "localhost" || url.hostname === "127.0.0.1") {
            return true;
        }
    }
    return false;
}
/**
 * Logs a warning about sending a token over an insecure connection.
 *
 * This function will emit a node warning once, but log the warning every time.
 */ function emitInsecureConnectionWarning() {
    const warning = "Sending token over insecure transport. Assume any token issued is compromised.";
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$log$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["logger"].warning(warning);
    if (typeof (process === null || process === void 0 ? void 0 : process.emitWarning) === "function" && !insecureConnectionWarningEmmitted) {
        insecureConnectionWarningEmmitted = true;
        process.emitWarning(warning);
    }
}
function ensureSecureConnection(request, options) {
    if (!request.url.toLowerCase().startsWith("https://")) {
        if (allowInsecureConnection(request, options)) {
            emitInsecureConnectionWarning();
        } else {
            throw new Error("Authentication is not permitted for non-TLS protected (non-https) URLs when allowInsecureConnection is false.");
        }
    }
} //# sourceMappingURL=checkInsecureConnection.js.map
}),
"[project]/node_modules/@typespec/ts-http-runtime/dist/esm/policies/auth/apiKeyAuthenticationPolicy.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

// Copyright (c) Microsoft Corporation.
// Licensed under the MIT License.
__turbopack_context__.s({
    "apiKeyAuthenticationPolicy": ()=>apiKeyAuthenticationPolicy,
    "apiKeyAuthenticationPolicyName": ()=>apiKeyAuthenticationPolicyName
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$policies$2f$auth$2f$checkInsecureConnection$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@typespec/ts-http-runtime/dist/esm/policies/auth/checkInsecureConnection.js [app-route] (ecmascript)");
;
const apiKeyAuthenticationPolicyName = "apiKeyAuthenticationPolicy";
function apiKeyAuthenticationPolicy(options) {
    return {
        name: apiKeyAuthenticationPolicyName,
        async sendRequest (request, next) {
            var _a, _b;
            // Ensure allowInsecureConnection is explicitly set when sending request to non-https URLs
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$policies$2f$auth$2f$checkInsecureConnection$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ensureSecureConnection"])(request, options);
            const scheme = (_b = (_a = request.authSchemes) !== null && _a !== void 0 ? _a : options.authSchemes) === null || _b === void 0 ? void 0 : _b.find((x)=>x.kind === "apiKey");
            // Skip adding authentication header if no API key authentication scheme is found
            if (!scheme) {
                return next(request);
            }
            if (scheme.apiKeyLocation !== "header") {
                throw new Error(`Unsupported API key location: ${scheme.apiKeyLocation}`);
            }
            request.headers.set(scheme.name, options.credential.key);
            return next(request);
        }
    };
} //# sourceMappingURL=apiKeyAuthenticationPolicy.js.map
}),
"[project]/node_modules/@typespec/ts-http-runtime/dist/esm/policies/auth/basicAuthenticationPolicy.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

// Copyright (c) Microsoft Corporation.
// Licensed under the MIT License.
__turbopack_context__.s({
    "basicAuthenticationPolicy": ()=>basicAuthenticationPolicy,
    "basicAuthenticationPolicyName": ()=>basicAuthenticationPolicyName
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$util$2f$bytesEncoding$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@typespec/ts-http-runtime/dist/esm/util/bytesEncoding.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$policies$2f$auth$2f$checkInsecureConnection$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@typespec/ts-http-runtime/dist/esm/policies/auth/checkInsecureConnection.js [app-route] (ecmascript)");
;
;
const basicAuthenticationPolicyName = "bearerAuthenticationPolicy";
function basicAuthenticationPolicy(options) {
    return {
        name: basicAuthenticationPolicyName,
        async sendRequest (request, next) {
            var _a, _b;
            // Ensure allowInsecureConnection is explicitly set when sending request to non-https URLs
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$policies$2f$auth$2f$checkInsecureConnection$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ensureSecureConnection"])(request, options);
            const scheme = (_b = (_a = request.authSchemes) !== null && _a !== void 0 ? _a : options.authSchemes) === null || _b === void 0 ? void 0 : _b.find((x)=>x.kind === "http" && x.scheme === "basic");
            // Skip adding authentication header if no basic authentication scheme is found
            if (!scheme) {
                return next(request);
            }
            const { username, password } = options.credential;
            const headerValue = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$util$2f$bytesEncoding$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["uint8ArrayToString"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$util$2f$bytesEncoding$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["stringToUint8Array"])(`${username}:${password}`, "utf-8"), "base64");
            request.headers.set("Authorization", `Basic ${headerValue}`);
            return next(request);
        }
    };
} //# sourceMappingURL=basicAuthenticationPolicy.js.map
}),
"[project]/node_modules/@typespec/ts-http-runtime/dist/esm/policies/auth/bearerAuthenticationPolicy.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

// Copyright (c) Microsoft Corporation.
// Licensed under the MIT License.
__turbopack_context__.s({
    "bearerAuthenticationPolicy": ()=>bearerAuthenticationPolicy,
    "bearerAuthenticationPolicyName": ()=>bearerAuthenticationPolicyName
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$policies$2f$auth$2f$checkInsecureConnection$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@typespec/ts-http-runtime/dist/esm/policies/auth/checkInsecureConnection.js [app-route] (ecmascript)");
;
const bearerAuthenticationPolicyName = "bearerAuthenticationPolicy";
function bearerAuthenticationPolicy(options) {
    return {
        name: bearerAuthenticationPolicyName,
        async sendRequest (request, next) {
            var _a, _b;
            // Ensure allowInsecureConnection is explicitly set when sending request to non-https URLs
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$policies$2f$auth$2f$checkInsecureConnection$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ensureSecureConnection"])(request, options);
            const scheme = (_b = (_a = request.authSchemes) !== null && _a !== void 0 ? _a : options.authSchemes) === null || _b === void 0 ? void 0 : _b.find((x)=>x.kind === "http" && x.scheme === "bearer");
            // Skip adding authentication header if no bearer authentication scheme is found
            if (!scheme) {
                return next(request);
            }
            const token = await options.credential.getBearerToken({
                abortSignal: request.abortSignal
            });
            request.headers.set("Authorization", `Bearer ${token}`);
            return next(request);
        }
    };
} //# sourceMappingURL=bearerAuthenticationPolicy.js.map
}),
"[project]/node_modules/@typespec/ts-http-runtime/dist/esm/policies/auth/oauth2AuthenticationPolicy.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

// Copyright (c) Microsoft Corporation.
// Licensed under the MIT License.
__turbopack_context__.s({
    "oauth2AuthenticationPolicy": ()=>oauth2AuthenticationPolicy,
    "oauth2AuthenticationPolicyName": ()=>oauth2AuthenticationPolicyName
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$policies$2f$auth$2f$checkInsecureConnection$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@typespec/ts-http-runtime/dist/esm/policies/auth/checkInsecureConnection.js [app-route] (ecmascript)");
;
const oauth2AuthenticationPolicyName = "oauth2AuthenticationPolicy";
function oauth2AuthenticationPolicy(options) {
    return {
        name: oauth2AuthenticationPolicyName,
        async sendRequest (request, next) {
            var _a, _b;
            // Ensure allowInsecureConnection is explicitly set when sending request to non-https URLs
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$policies$2f$auth$2f$checkInsecureConnection$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ensureSecureConnection"])(request, options);
            const scheme = (_b = (_a = request.authSchemes) !== null && _a !== void 0 ? _a : options.authSchemes) === null || _b === void 0 ? void 0 : _b.find((x)=>x.kind === "oauth2");
            // Skip adding authentication header if no OAuth2 authentication scheme is found
            if (!scheme) {
                return next(request);
            }
            const token = await options.credential.getOAuth2Token(scheme.flows, {
                abortSignal: request.abortSignal
            });
            request.headers.set("Authorization", `Bearer ${token}`);
            return next(request);
        }
    };
} //# sourceMappingURL=oauth2AuthenticationPolicy.js.map
}),
"[project]/node_modules/@typespec/ts-http-runtime/dist/esm/client/clientHelpers.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

// Copyright (c) Microsoft Corporation.
// Licensed under the MIT License.
__turbopack_context__.s({
    "createDefaultPipeline": ()=>createDefaultPipeline,
    "getCachedDefaultHttpsClient": ()=>getCachedDefaultHttpsClient
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$defaultHttpClient$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@typespec/ts-http-runtime/dist/esm/defaultHttpClient.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$createPipelineFromOptions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@typespec/ts-http-runtime/dist/esm/createPipelineFromOptions.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$client$2f$apiVersionPolicy$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@typespec/ts-http-runtime/dist/esm/client/apiVersionPolicy.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$auth$2f$credentials$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@typespec/ts-http-runtime/dist/esm/auth/credentials.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$policies$2f$auth$2f$apiKeyAuthenticationPolicy$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@typespec/ts-http-runtime/dist/esm/policies/auth/apiKeyAuthenticationPolicy.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$policies$2f$auth$2f$basicAuthenticationPolicy$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@typespec/ts-http-runtime/dist/esm/policies/auth/basicAuthenticationPolicy.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$policies$2f$auth$2f$bearerAuthenticationPolicy$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@typespec/ts-http-runtime/dist/esm/policies/auth/bearerAuthenticationPolicy.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$policies$2f$auth$2f$oauth2AuthenticationPolicy$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@typespec/ts-http-runtime/dist/esm/policies/auth/oauth2AuthenticationPolicy.js [app-route] (ecmascript)");
;
;
;
;
;
;
;
;
let cachedHttpClient;
function createDefaultPipeline(options = {}) {
    const pipeline = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$createPipelineFromOptions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createPipelineFromOptions"])(options);
    pipeline.addPolicy((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$client$2f$apiVersionPolicy$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["apiVersionPolicy"])(options));
    const { credential, authSchemes, allowInsecureConnection } = options;
    if (credential) {
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$auth$2f$credentials$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isApiKeyCredential"])(credential)) {
            pipeline.addPolicy((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$policies$2f$auth$2f$apiKeyAuthenticationPolicy$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["apiKeyAuthenticationPolicy"])({
                authSchemes,
                credential,
                allowInsecureConnection
            }));
        } else if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$auth$2f$credentials$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isBasicCredential"])(credential)) {
            pipeline.addPolicy((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$policies$2f$auth$2f$basicAuthenticationPolicy$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["basicAuthenticationPolicy"])({
                authSchemes,
                credential,
                allowInsecureConnection
            }));
        } else if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$auth$2f$credentials$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isBearerTokenCredential"])(credential)) {
            pipeline.addPolicy((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$policies$2f$auth$2f$bearerAuthenticationPolicy$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["bearerAuthenticationPolicy"])({
                authSchemes,
                credential,
                allowInsecureConnection
            }));
        } else if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$auth$2f$credentials$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isOAuth2TokenCredential"])(credential)) {
            pipeline.addPolicy((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$policies$2f$auth$2f$oauth2AuthenticationPolicy$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["oauth2AuthenticationPolicy"])({
                authSchemes,
                credential,
                allowInsecureConnection
            }));
        }
    }
    return pipeline;
}
function getCachedDefaultHttpsClient() {
    if (!cachedHttpClient) {
        cachedHttpClient = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$defaultHttpClient$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createDefaultHttpClient"])();
    }
    return cachedHttpClient;
} //# sourceMappingURL=clientHelpers.js.map
}),
"[project]/node_modules/@typespec/ts-http-runtime/dist/esm/client/multipart.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

// Copyright (c) Microsoft Corporation.
// Licensed under the MIT License.
__turbopack_context__.s({
    "buildBodyPart": ()=>buildBodyPart,
    "buildMultipartBody": ()=>buildMultipartBody
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$restError$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@typespec/ts-http-runtime/dist/esm/restError.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$httpHeaders$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@typespec/ts-http-runtime/dist/esm/httpHeaders.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$util$2f$bytesEncoding$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@typespec/ts-http-runtime/dist/esm/util/bytesEncoding.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$util$2f$typeGuards$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@typespec/ts-http-runtime/dist/esm/util/typeGuards.js [app-route] (ecmascript)");
;
;
;
;
/**
 * Get value of a header in the part descriptor ignoring case
 */ function getHeaderValue(descriptor, headerName) {
    if (descriptor.headers) {
        const actualHeaderName = Object.keys(descriptor.headers).find((x)=>x.toLowerCase() === headerName.toLowerCase());
        if (actualHeaderName) {
            return descriptor.headers[actualHeaderName];
        }
    }
    return undefined;
}
function getPartContentType(descriptor) {
    const contentTypeHeader = getHeaderValue(descriptor, "content-type");
    if (contentTypeHeader) {
        return contentTypeHeader;
    }
    // Special value of null means content type is to be omitted
    if (descriptor.contentType === null) {
        return undefined;
    }
    if (descriptor.contentType) {
        return descriptor.contentType;
    }
    const { body } = descriptor;
    if (body === null || body === undefined) {
        return undefined;
    }
    if (typeof body === "string" || typeof body === "number" || typeof body === "boolean") {
        return "text/plain; charset=UTF-8";
    }
    if (body instanceof Blob) {
        return body.type || "application/octet-stream";
    }
    if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$util$2f$typeGuards$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isBinaryBody"])(body)) {
        return "application/octet-stream";
    }
    // arbitrary non-text object -> generic JSON content type by default. We will try to JSON.stringify the body.
    return "application/json";
}
/**
 * Enclose value in quotes and escape special characters, for use in the Content-Disposition header
 */ function escapeDispositionField(value) {
    return JSON.stringify(value);
}
function getContentDisposition(descriptor) {
    var _a;
    const contentDispositionHeader = getHeaderValue(descriptor, "content-disposition");
    if (contentDispositionHeader) {
        return contentDispositionHeader;
    }
    if (descriptor.dispositionType === undefined && descriptor.name === undefined && descriptor.filename === undefined) {
        return undefined;
    }
    const dispositionType = (_a = descriptor.dispositionType) !== null && _a !== void 0 ? _a : "form-data";
    let disposition = dispositionType;
    if (descriptor.name) {
        disposition += `; name=${escapeDispositionField(descriptor.name)}`;
    }
    let filename = undefined;
    if (descriptor.filename) {
        filename = descriptor.filename;
    } else if (typeof File !== "undefined" && descriptor.body instanceof File) {
        const filenameFromFile = descriptor.body.name;
        if (filenameFromFile !== "") {
            filename = filenameFromFile;
        }
    }
    if (filename) {
        disposition += `; filename=${escapeDispositionField(filename)}`;
    }
    return disposition;
}
function normalizeBody(body, contentType) {
    if (body === undefined) {
        // zero-length body
        return new Uint8Array([]);
    }
    // binary and primitives should go straight on the wire regardless of content type
    if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$util$2f$typeGuards$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isBinaryBody"])(body)) {
        return body;
    }
    if (typeof body === "string" || typeof body === "number" || typeof body === "boolean") {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$util$2f$bytesEncoding$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["stringToUint8Array"])(String(body), "utf-8");
    }
    // stringify objects for JSON-ish content types e.g. application/json, application/merge-patch+json, application/vnd.oci.manifest.v1+json, application.json; charset=UTF-8
    if (contentType && /application\/(.+\+)?json(;.+)?/i.test(String(contentType))) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$util$2f$bytesEncoding$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["stringToUint8Array"])(JSON.stringify(body), "utf-8");
    }
    throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$restError$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["RestError"](`Unsupported body/content-type combination: ${body}, ${contentType}`);
}
function buildBodyPart(descriptor) {
    var _a;
    const contentType = getPartContentType(descriptor);
    const contentDisposition = getContentDisposition(descriptor);
    const headers = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$httpHeaders$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createHttpHeaders"])((_a = descriptor.headers) !== null && _a !== void 0 ? _a : {});
    if (contentType) {
        headers.set("content-type", contentType);
    }
    if (contentDisposition) {
        headers.set("content-disposition", contentDisposition);
    }
    const body = normalizeBody(descriptor.body, contentType);
    return {
        headers,
        body
    };
}
function buildMultipartBody(parts) {
    return {
        parts: parts.map(buildBodyPart)
    };
} //# sourceMappingURL=multipart.js.map
}),
"[project]/node_modules/@typespec/ts-http-runtime/dist/esm/client/sendRequest.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

// Copyright (c) Microsoft Corporation.
// Licensed under the MIT License.
__turbopack_context__.s({
    "sendRequest": ()=>sendRequest
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$restError$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@typespec/ts-http-runtime/dist/esm/restError.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$httpHeaders$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@typespec/ts-http-runtime/dist/esm/httpHeaders.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$pipelineRequest$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@typespec/ts-http-runtime/dist/esm/pipelineRequest.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$client$2f$clientHelpers$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@typespec/ts-http-runtime/dist/esm/client/clientHelpers.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$util$2f$typeGuards$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@typespec/ts-http-runtime/dist/esm/util/typeGuards.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$client$2f$multipart$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@typespec/ts-http-runtime/dist/esm/client/multipart.js [app-route] (ecmascript)");
;
;
;
;
;
;
async function sendRequest(method, url, pipeline, options = {}, customHttpClient) {
    var _a;
    const httpClient = customHttpClient !== null && customHttpClient !== void 0 ? customHttpClient : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$client$2f$clientHelpers$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getCachedDefaultHttpsClient"])();
    const request = buildPipelineRequest(method, url, options);
    try {
        const response = await pipeline.sendRequest(httpClient, request);
        const headers = response.headers.toJSON();
        const stream = (_a = response.readableStreamBody) !== null && _a !== void 0 ? _a : response.browserStreamBody;
        const parsedBody = options.responseAsStream || stream !== undefined ? undefined : getResponseBody(response);
        const body = stream !== null && stream !== void 0 ? stream : parsedBody;
        if (options === null || options === void 0 ? void 0 : options.onResponse) {
            options.onResponse(Object.assign(Object.assign({}, response), {
                request,
                rawHeaders: headers,
                parsedBody
            }));
        }
        return {
            request,
            headers,
            status: `${response.status}`,
            body
        };
    } catch (e) {
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$restError$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isRestError"])(e) && e.response && options.onResponse) {
            const { response } = e;
            const rawHeaders = response.headers.toJSON();
            // UNBRANDED DIFFERENCE: onResponse callback does not have a second __legacyError property
            options === null || options === void 0 ? void 0 : options.onResponse(Object.assign(Object.assign({}, response), {
                request,
                rawHeaders
            }), e);
        }
        throw e;
    }
}
/**
 * Function to determine the request content type
 * @param options - request options InternalRequestParameters
 * @returns returns the content-type
 */ function getRequestContentType(options = {}) {
    var _a, _b, _c;
    return (_c = (_a = options.contentType) !== null && _a !== void 0 ? _a : (_b = options.headers) === null || _b === void 0 ? void 0 : _b["content-type"]) !== null && _c !== void 0 ? _c : getContentType(options.body);
}
/**
 * Function to determine the content-type of a body
 * this is used if an explicit content-type is not provided
 * @param body - body in the request
 * @returns returns the content-type
 */ function getContentType(body) {
    if (ArrayBuffer.isView(body)) {
        return "application/octet-stream";
    }
    if (typeof body === "string") {
        try {
            JSON.parse(body);
            return "application/json";
        } catch (error) {
            // If we fail to parse the body, it is not json
            return undefined;
        }
    }
    // By default return json
    return "application/json";
}
function buildPipelineRequest(method, url, options = {}) {
    var _a, _b, _c;
    const requestContentType = getRequestContentType(options);
    const { body, multipartBody } = getRequestBody(options.body, requestContentType);
    const hasContent = body !== undefined || multipartBody !== undefined;
    const headers = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$httpHeaders$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createHttpHeaders"])(Object.assign(Object.assign(Object.assign({}, options.headers ? options.headers : {}), {
        accept: (_c = (_a = options.accept) !== null && _a !== void 0 ? _a : (_b = options.headers) === null || _b === void 0 ? void 0 : _b.accept) !== null && _c !== void 0 ? _c : "application/json"
    }), hasContent && requestContentType && {
        "content-type": requestContentType
    }));
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$pipelineRequest$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createPipelineRequest"])({
        url,
        method,
        body,
        multipartBody,
        headers,
        allowInsecureConnection: options.allowInsecureConnection,
        abortSignal: options.abortSignal,
        onUploadProgress: options.onUploadProgress,
        onDownloadProgress: options.onDownloadProgress,
        timeout: options.timeout,
        enableBrowserStreams: true,
        streamResponseStatusCodes: options.responseAsStream ? new Set([
            Number.POSITIVE_INFINITY
        ]) : undefined
    });
}
/**
 * Prepares the body before sending the request
 */ function getRequestBody(body, contentType = "") {
    if (body === undefined) {
        return {
            body: undefined
        };
    }
    if (typeof FormData !== "undefined" && body instanceof FormData) {
        return {
            body
        };
    }
    if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$util$2f$typeGuards$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isReadableStream"])(body)) {
        return {
            body
        };
    }
    if (ArrayBuffer.isView(body)) {
        return {
            body: body instanceof Uint8Array ? body : JSON.stringify(body)
        };
    }
    const firstType = contentType.split(";")[0];
    switch(firstType){
        case "application/json":
            return {
                body: JSON.stringify(body)
            };
        case "multipart/form-data":
            if (Array.isArray(body)) {
                return {
                    multipartBody: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$client$2f$multipart$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["buildMultipartBody"])(body)
                };
            }
            return {
                body: JSON.stringify(body)
            };
        case "text/plain":
            return {
                body: String(body)
            };
        default:
            if (typeof body === "string") {
                return {
                    body
                };
            }
            return {
                body: JSON.stringify(body)
            };
    }
}
/**
 * Prepares the response body
 */ function getResponseBody(response) {
    var _a, _b;
    // Set the default response type
    const contentType = (_a = response.headers.get("content-type")) !== null && _a !== void 0 ? _a : "";
    const firstType = contentType.split(";")[0];
    const bodyToParse = (_b = response.bodyAsText) !== null && _b !== void 0 ? _b : "";
    if (firstType === "text/plain") {
        return String(bodyToParse);
    }
    // Default to "application/json" and fallback to string;
    try {
        return bodyToParse ? JSON.parse(bodyToParse) : undefined;
    } catch (error) {
        // If we were supposed to get a JSON object and failed to
        // parse, throw a parse error
        if (firstType === "application/json") {
            throw createParseError(response, error);
        }
        // We are not sure how to handle the response so we return it as
        // plain text.
        return String(bodyToParse);
    }
}
function createParseError(response, err) {
    var _a;
    const msg = `Error "${err}" occurred while parsing the response body - ${response.bodyAsText}.`;
    const errCode = (_a = err.code) !== null && _a !== void 0 ? _a : __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$restError$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["RestError"].PARSE_ERROR;
    return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$restError$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["RestError"](msg, {
        code: errCode,
        statusCode: response.status,
        request: response.request,
        response: response
    });
} //# sourceMappingURL=sendRequest.js.map
}),
"[project]/node_modules/@typespec/ts-http-runtime/dist/esm/client/urlHelpers.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

// Copyright (c) Microsoft Corporation.
// Licensed under the MIT License.
__turbopack_context__.s({
    "buildBaseUrl": ()=>buildBaseUrl,
    "buildRequestUrl": ()=>buildRequestUrl,
    "replaceAll": ()=>replaceAll
});
function isQueryParameterWithOptions(x) {
    const value = x.value;
    return value !== undefined && value.toString !== undefined && typeof value.toString === "function";
}
function buildRequestUrl(endpoint, routePath, pathParameters, options = {}) {
    if (routePath.startsWith("https://") || routePath.startsWith("http://")) {
        return routePath;
    }
    endpoint = buildBaseUrl(endpoint, options);
    routePath = buildRoutePath(routePath, pathParameters, options);
    const requestUrl = appendQueryParams(`${endpoint}/${routePath}`, options);
    const url = new URL(requestUrl);
    return url.toString()// Remove double forward slashes
    .replace(/([^:]\/)\/+/g, "$1");
}
function getQueryParamValue(key, allowReserved, style, param) {
    let separator;
    if (style === "pipeDelimited") {
        separator = "|";
    } else if (style === "spaceDelimited") {
        separator = "%20";
    } else {
        separator = ",";
    }
    let paramValues;
    if (Array.isArray(param)) {
        paramValues = param;
    } else if (typeof param === "object" && param.toString === Object.prototype.toString) {
        // If the parameter is an object without a custom toString implementation (e.g. a Date),
        // then we should deconstruct the object into an array [key1, value1, key2, value2, ...].
        paramValues = Object.entries(param).flat();
    } else {
        paramValues = [
            param
        ];
    }
    const value = paramValues.map((p)=>{
        if (p === null || p === undefined) {
            return "";
        }
        if (!p.toString || typeof p.toString !== "function") {
            throw new Error(`Query parameters must be able to be represented as string, ${key} can't`);
        }
        const rawValue = p.toISOString !== undefined ? p.toISOString() : p.toString();
        return allowReserved ? rawValue : encodeURIComponent(rawValue);
    }).join(separator);
    return `${allowReserved ? key : encodeURIComponent(key)}=${value}`;
}
function appendQueryParams(url, options = {}) {
    var _a, _b, _c, _d;
    if (!options.queryParameters) {
        return url;
    }
    const parsedUrl = new URL(url);
    const queryParams = options.queryParameters;
    const paramStrings = [];
    for (const key of Object.keys(queryParams)){
        const param = queryParams[key];
        if (param === undefined || param === null) {
            continue;
        }
        const hasMetadata = isQueryParameterWithOptions(param);
        const rawValue = hasMetadata ? param.value : param;
        const explode = hasMetadata ? (_a = param.explode) !== null && _a !== void 0 ? _a : false : false;
        const style = hasMetadata && param.style ? param.style : "form";
        if (explode) {
            if (Array.isArray(rawValue)) {
                for (const item of rawValue){
                    paramStrings.push(getQueryParamValue(key, (_b = options.skipUrlEncoding) !== null && _b !== void 0 ? _b : false, style, item));
                }
            } else if (typeof rawValue === "object") {
                // For object explode, the name of the query parameter is ignored and we use the object key instead
                for (const [actualKey, value] of Object.entries(rawValue)){
                    paramStrings.push(getQueryParamValue(actualKey, (_c = options.skipUrlEncoding) !== null && _c !== void 0 ? _c : false, style, value));
                }
            } else {
                // Explode doesn't really make sense for primitives
                throw new Error("explode can only be set to true for objects and arrays");
            }
        } else {
            paramStrings.push(getQueryParamValue(key, (_d = options.skipUrlEncoding) !== null && _d !== void 0 ? _d : false, style, rawValue));
        }
    }
    if (parsedUrl.search !== "") {
        parsedUrl.search += "&";
    }
    parsedUrl.search += paramStrings.join("&");
    return parsedUrl.toString();
}
function buildBaseUrl(endpoint, options) {
    var _a;
    if (!options.pathParameters) {
        return endpoint;
    }
    const pathParams = options.pathParameters;
    for (const [key, param] of Object.entries(pathParams)){
        if (param === undefined || param === null) {
            throw new Error(`Path parameters ${key} must not be undefined or null`);
        }
        if (!param.toString || typeof param.toString !== "function") {
            throw new Error(`Path parameters must be able to be represented as string, ${key} can't`);
        }
        let value = param.toISOString !== undefined ? param.toISOString() : String(param);
        if (!options.skipUrlEncoding) {
            value = encodeURIComponent(param);
        }
        endpoint = (_a = replaceAll(endpoint, `{${key}}`, value)) !== null && _a !== void 0 ? _a : "";
    }
    return endpoint;
}
function buildRoutePath(routePath, pathParameters, options = {}) {
    var _a;
    for (const pathParam of pathParameters){
        const allowReserved = typeof pathParam === "object" && ((_a = pathParam.allowReserved) !== null && _a !== void 0 ? _a : false);
        let value = typeof pathParam === "object" ? pathParam.value : pathParam;
        if (!options.skipUrlEncoding && !allowReserved) {
            value = encodeURIComponent(value);
        }
        routePath = routePath.replace(/\{[\w-]+\}/, String(value));
    }
    return routePath;
}
function replaceAll(value, searchValue, replaceValue) {
    return !value || !searchValue ? value : value.split(searchValue).join(replaceValue || "");
} //# sourceMappingURL=urlHelpers.js.map
}),
"[project]/node_modules/@typespec/ts-http-runtime/dist/esm/client/getClient.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

// Copyright (c) Microsoft Corporation.
// Licensed under the MIT License.
__turbopack_context__.s({
    "getClient": ()=>getClient
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$client$2f$clientHelpers$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@typespec/ts-http-runtime/dist/esm/client/clientHelpers.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$client$2f$sendRequest$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@typespec/ts-http-runtime/dist/esm/client/sendRequest.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$client$2f$urlHelpers$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@typespec/ts-http-runtime/dist/esm/client/urlHelpers.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$util$2f$checkEnvironment$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@typespec/ts-http-runtime/dist/esm/util/checkEnvironment.js [app-route] (ecmascript)");
;
;
;
;
function getClient(endpoint, clientOptions = {}) {
    var _a, _b, _c;
    const pipeline = (_a = clientOptions.pipeline) !== null && _a !== void 0 ? _a : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$client$2f$clientHelpers$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createDefaultPipeline"])(clientOptions);
    if ((_b = clientOptions.additionalPolicies) === null || _b === void 0 ? void 0 : _b.length) {
        for (const { policy, position } of clientOptions.additionalPolicies){
            // Sign happens after Retry and is commonly needed to occur
            // before policies that intercept post-retry.
            const afterPhase = position === "perRetry" ? "Sign" : undefined;
            pipeline.addPolicy(policy, {
                afterPhase
            });
        }
    }
    const { allowInsecureConnection, httpClient } = clientOptions;
    const endpointUrl = (_c = clientOptions.endpoint) !== null && _c !== void 0 ? _c : endpoint;
    const client = (path, ...args)=>{
        const getUrl = (requestOptions)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$client$2f$urlHelpers$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["buildRequestUrl"])(endpointUrl, path, args, Object.assign({
                allowInsecureConnection
            }, requestOptions));
        return {
            get: (requestOptions = {})=>{
                return buildOperation("GET", getUrl(requestOptions), pipeline, requestOptions, allowInsecureConnection, httpClient);
            },
            post: (requestOptions = {})=>{
                return buildOperation("POST", getUrl(requestOptions), pipeline, requestOptions, allowInsecureConnection, httpClient);
            },
            put: (requestOptions = {})=>{
                return buildOperation("PUT", getUrl(requestOptions), pipeline, requestOptions, allowInsecureConnection, httpClient);
            },
            patch: (requestOptions = {})=>{
                return buildOperation("PATCH", getUrl(requestOptions), pipeline, requestOptions, allowInsecureConnection, httpClient);
            },
            delete: (requestOptions = {})=>{
                return buildOperation("DELETE", getUrl(requestOptions), pipeline, requestOptions, allowInsecureConnection, httpClient);
            },
            head: (requestOptions = {})=>{
                return buildOperation("HEAD", getUrl(requestOptions), pipeline, requestOptions, allowInsecureConnection, httpClient);
            },
            options: (requestOptions = {})=>{
                return buildOperation("OPTIONS", getUrl(requestOptions), pipeline, requestOptions, allowInsecureConnection, httpClient);
            },
            trace: (requestOptions = {})=>{
                return buildOperation("TRACE", getUrl(requestOptions), pipeline, requestOptions, allowInsecureConnection, httpClient);
            }
        };
    };
    return {
        path: client,
        pathUnchecked: client,
        pipeline
    };
}
function buildOperation(method, url, pipeline, options, allowInsecureConnection, httpClient) {
    var _a;
    allowInsecureConnection = (_a = options.allowInsecureConnection) !== null && _a !== void 0 ? _a : allowInsecureConnection;
    return {
        then: function(onFulfilled, onrejected) {
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$client$2f$sendRequest$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["sendRequest"])(method, url, pipeline, Object.assign(Object.assign({}, options), {
                allowInsecureConnection
            }), httpClient).then(onFulfilled, onrejected);
        },
        async asBrowserStream () {
            if (__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$util$2f$checkEnvironment$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isNodeLike"]) {
                throw new Error("`asBrowserStream` is supported only in the browser environment. Use `asNodeStream` instead to obtain the response body stream. If you require a Web stream of the response in Node, consider using `Readable.toWeb` on the result of `asNodeStream`.");
            } else {
                return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$client$2f$sendRequest$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["sendRequest"])(method, url, pipeline, Object.assign(Object.assign({}, options), {
                    allowInsecureConnection,
                    responseAsStream: true
                }), httpClient);
            }
        },
        async asNodeStream () {
            if (__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$util$2f$checkEnvironment$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isNodeLike"]) {
                return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$client$2f$sendRequest$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["sendRequest"])(method, url, pipeline, Object.assign(Object.assign({}, options), {
                    allowInsecureConnection,
                    responseAsStream: true
                }), httpClient);
            } else {
                throw new Error("`isNodeStream` is not supported in the browser environment. Use `asBrowserStream` to obtain the response body stream.");
            }
        }
    };
} //# sourceMappingURL=getClient.js.map
}),
"[project]/node_modules/@typespec/ts-http-runtime/dist/esm/client/operationOptionHelpers.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

// Copyright (c) Microsoft Corporation.
// Licensed under the MIT License.
/**
 * Helper function to convert OperationOptions to RequestParameters
 * @param options - the options that are used by Modular layer to send the request
 * @returns the result of the conversion in RequestParameters of RLC layer
 */ __turbopack_context__.s({
    "operationOptionsToRequestParameters": ()=>operationOptionsToRequestParameters
});
function operationOptionsToRequestParameters(options) {
    var _a, _b, _c, _d, _e, _f;
    return {
        allowInsecureConnection: (_a = options.requestOptions) === null || _a === void 0 ? void 0 : _a.allowInsecureConnection,
        timeout: (_b = options.requestOptions) === null || _b === void 0 ? void 0 : _b.timeout,
        skipUrlEncoding: (_c = options.requestOptions) === null || _c === void 0 ? void 0 : _c.skipUrlEncoding,
        abortSignal: options.abortSignal,
        onUploadProgress: (_d = options.requestOptions) === null || _d === void 0 ? void 0 : _d.onUploadProgress,
        onDownloadProgress: (_e = options.requestOptions) === null || _e === void 0 ? void 0 : _e.onDownloadProgress,
        headers: Object.assign({}, (_f = options.requestOptions) === null || _f === void 0 ? void 0 : _f.headers),
        onResponse: options.onResponse
    };
} //# sourceMappingURL=operationOptionHelpers.js.map
}),
"[project]/node_modules/@typespec/ts-http-runtime/dist/esm/client/restError.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

// Copyright (c) Microsoft Corporation.
// Licensed under the MIT License.
__turbopack_context__.s({
    "createRestError": ()=>createRestError
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$restError$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@typespec/ts-http-runtime/dist/esm/restError.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$httpHeaders$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@typespec/ts-http-runtime/dist/esm/httpHeaders.js [app-route] (ecmascript)");
;
;
function createRestError(messageOrResponse, response) {
    var _a, _b, _c;
    const resp = typeof messageOrResponse === "string" ? response : messageOrResponse;
    const internalError = (_b = (_a = resp.body) === null || _a === void 0 ? void 0 : _a.error) !== null && _b !== void 0 ? _b : resp.body;
    const message = typeof messageOrResponse === "string" ? messageOrResponse : (_c = internalError === null || internalError === void 0 ? void 0 : internalError.message) !== null && _c !== void 0 ? _c : `Unexpected status code: ${resp.status}`;
    return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$restError$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["RestError"](message, {
        statusCode: statusCodeToNumber(resp.status),
        code: internalError === null || internalError === void 0 ? void 0 : internalError.code,
        request: resp.request,
        response: toPipelineResponse(resp)
    });
}
function toPipelineResponse(response) {
    var _a;
    return {
        headers: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$httpHeaders$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createHttpHeaders"])(response.headers),
        request: response.request,
        status: (_a = statusCodeToNumber(response.status)) !== null && _a !== void 0 ? _a : -1
    };
}
function statusCodeToNumber(statusCode) {
    const status = Number.parseInt(statusCode);
    return Number.isNaN(status) ? undefined : status;
} //# sourceMappingURL=restError.js.map
}),
"[project]/node_modules/@typespec/ts-http-runtime/dist/esm/index.js [app-route] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

// Copyright (c) Microsoft Corporation.
// Licensed under the MIT License.
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$abort$2d$controller$2f$AbortError$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@typespec/ts-http-runtime/dist/esm/abort-controller/AbortError.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$logger$2f$logger$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@typespec/ts-http-runtime/dist/esm/logger/logger.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$httpHeaders$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@typespec/ts-http-runtime/dist/esm/httpHeaders.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$auth$2f$schemes$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@typespec/ts-http-runtime/dist/esm/auth/schemes.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$auth$2f$oauth2Flows$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@typespec/ts-http-runtime/dist/esm/auth/oauth2Flows.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$pipelineRequest$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@typespec/ts-http-runtime/dist/esm/pipelineRequest.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$pipeline$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@typespec/ts-http-runtime/dist/esm/pipeline.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$restError$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@typespec/ts-http-runtime/dist/esm/restError.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$util$2f$bytesEncoding$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@typespec/ts-http-runtime/dist/esm/util/bytesEncoding.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$defaultHttpClient$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@typespec/ts-http-runtime/dist/esm/defaultHttpClient.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$client$2f$getClient$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@typespec/ts-http-runtime/dist/esm/client/getClient.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$client$2f$operationOptionHelpers$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@typespec/ts-http-runtime/dist/esm/client/operationOptionHelpers.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$client$2f$restError$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@typespec/ts-http-runtime/dist/esm/client/restError.js [app-route] (ecmascript)"); //# sourceMappingURL=index.js.map
;
;
;
;
;
;
;
;
;
;
;
;
;
}),
"[project]/node_modules/@typespec/ts-http-runtime/dist/esm/index.js [app-route] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$abort$2d$controller$2f$AbortError$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@typespec/ts-http-runtime/dist/esm/abort-controller/AbortError.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$logger$2f$logger$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@typespec/ts-http-runtime/dist/esm/logger/logger.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$httpHeaders$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@typespec/ts-http-runtime/dist/esm/httpHeaders.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$auth$2f$schemes$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@typespec/ts-http-runtime/dist/esm/auth/schemes.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$auth$2f$oauth2Flows$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@typespec/ts-http-runtime/dist/esm/auth/oauth2Flows.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$pipelineRequest$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@typespec/ts-http-runtime/dist/esm/pipelineRequest.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$pipeline$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@typespec/ts-http-runtime/dist/esm/pipeline.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$restError$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@typespec/ts-http-runtime/dist/esm/restError.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$util$2f$bytesEncoding$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@typespec/ts-http-runtime/dist/esm/util/bytesEncoding.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$defaultHttpClient$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@typespec/ts-http-runtime/dist/esm/defaultHttpClient.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$client$2f$getClient$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@typespec/ts-http-runtime/dist/esm/client/getClient.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$client$2f$operationOptionHelpers$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@typespec/ts-http-runtime/dist/esm/client/operationOptionHelpers.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$client$2f$restError$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@typespec/ts-http-runtime/dist/esm/client/restError.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@typespec/ts-http-runtime/dist/esm/index.js [app-route] (ecmascript) <locals>");
}),
"[project]/node_modules/@typespec/ts-http-runtime/dist/esm/logger/internal.js [app-route] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

// Copyright (c) Microsoft Corporation.
// Licensed under the MIT License.
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$logger$2f$logger$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@typespec/ts-http-runtime/dist/esm/logger/logger.js [app-route] (ecmascript)"); //# sourceMappingURL=internal.js.map
;
}),
"[project]/node_modules/@typespec/ts-http-runtime/dist/esm/logger/internal.js [app-route] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$logger$2f$logger$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@typespec/ts-http-runtime/dist/esm/logger/logger.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$logger$2f$internal$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@typespec/ts-http-runtime/dist/esm/logger/internal.js [app-route] (ecmascript) <locals>");
}),
"[project]/node_modules/@typespec/ts-http-runtime/dist/esm/policies/exponentialRetryPolicy.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

// Copyright (c) Microsoft Corporation.
// Licensed under the MIT License.
__turbopack_context__.s({
    "exponentialRetryPolicy": ()=>exponentialRetryPolicy,
    "exponentialRetryPolicyName": ()=>exponentialRetryPolicyName
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$retryStrategies$2f$exponentialRetryStrategy$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@typespec/ts-http-runtime/dist/esm/retryStrategies/exponentialRetryStrategy.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$policies$2f$retryPolicy$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@typespec/ts-http-runtime/dist/esm/policies/retryPolicy.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@typespec/ts-http-runtime/dist/esm/constants.js [app-route] (ecmascript)");
;
;
;
const exponentialRetryPolicyName = "exponentialRetryPolicy";
function exponentialRetryPolicy(options = {}) {
    var _a;
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$policies$2f$retryPolicy$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["retryPolicy"])([
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$retryStrategies$2f$exponentialRetryStrategy$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["exponentialRetryStrategy"])(Object.assign(Object.assign({}, options), {
            ignoreSystemErrors: true
        }))
    ], {
        maxRetries: (_a = options.maxRetries) !== null && _a !== void 0 ? _a : __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["DEFAULT_RETRY_POLICY_COUNT"]
    });
} //# sourceMappingURL=exponentialRetryPolicy.js.map
}),
"[project]/node_modules/@typespec/ts-http-runtime/dist/esm/policies/systemErrorRetryPolicy.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

// Copyright (c) Microsoft Corporation.
// Licensed under the MIT License.
__turbopack_context__.s({
    "systemErrorRetryPolicy": ()=>systemErrorRetryPolicy,
    "systemErrorRetryPolicyName": ()=>systemErrorRetryPolicyName
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$retryStrategies$2f$exponentialRetryStrategy$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@typespec/ts-http-runtime/dist/esm/retryStrategies/exponentialRetryStrategy.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$policies$2f$retryPolicy$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@typespec/ts-http-runtime/dist/esm/policies/retryPolicy.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@typespec/ts-http-runtime/dist/esm/constants.js [app-route] (ecmascript)");
;
;
;
const systemErrorRetryPolicyName = "systemErrorRetryPolicy";
function systemErrorRetryPolicy(options = {}) {
    var _a;
    return {
        name: systemErrorRetryPolicyName,
        sendRequest: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$policies$2f$retryPolicy$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["retryPolicy"])([
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$retryStrategies$2f$exponentialRetryStrategy$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["exponentialRetryStrategy"])(Object.assign(Object.assign({}, options), {
                ignoreHttpStatusCodes: true
            }))
        ], {
            maxRetries: (_a = options.maxRetries) !== null && _a !== void 0 ? _a : __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["DEFAULT_RETRY_POLICY_COUNT"]
        }).sendRequest
    };
} //# sourceMappingURL=systemErrorRetryPolicy.js.map
}),
"[project]/node_modules/@typespec/ts-http-runtime/dist/esm/policies/throttlingRetryPolicy.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

// Copyright (c) Microsoft Corporation.
// Licensed under the MIT License.
__turbopack_context__.s({
    "throttlingRetryPolicy": ()=>throttlingRetryPolicy,
    "throttlingRetryPolicyName": ()=>throttlingRetryPolicyName
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$retryStrategies$2f$throttlingRetryStrategy$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@typespec/ts-http-runtime/dist/esm/retryStrategies/throttlingRetryStrategy.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$policies$2f$retryPolicy$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@typespec/ts-http-runtime/dist/esm/policies/retryPolicy.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@typespec/ts-http-runtime/dist/esm/constants.js [app-route] (ecmascript)");
;
;
;
const throttlingRetryPolicyName = "throttlingRetryPolicy";
function throttlingRetryPolicy(options = {}) {
    var _a;
    return {
        name: throttlingRetryPolicyName,
        sendRequest: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$policies$2f$retryPolicy$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["retryPolicy"])([
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$retryStrategies$2f$throttlingRetryStrategy$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["throttlingRetryStrategy"])()
        ], {
            maxRetries: (_a = options.maxRetries) !== null && _a !== void 0 ? _a : __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["DEFAULT_RETRY_POLICY_COUNT"]
        }).sendRequest
    };
} //# sourceMappingURL=throttlingRetryPolicy.js.map
}),
"[project]/node_modules/@typespec/ts-http-runtime/dist/esm/policies/internal.js [app-route] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

// Copyright (c) Microsoft Corporation.
// Licensed under the MIT License.
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$policies$2f$agentPolicy$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@typespec/ts-http-runtime/dist/esm/policies/agentPolicy.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$policies$2f$decompressResponsePolicy$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@typespec/ts-http-runtime/dist/esm/policies/decompressResponsePolicy.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$policies$2f$defaultRetryPolicy$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@typespec/ts-http-runtime/dist/esm/policies/defaultRetryPolicy.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$policies$2f$exponentialRetryPolicy$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@typespec/ts-http-runtime/dist/esm/policies/exponentialRetryPolicy.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$policies$2f$retryPolicy$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@typespec/ts-http-runtime/dist/esm/policies/retryPolicy.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$policies$2f$systemErrorRetryPolicy$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@typespec/ts-http-runtime/dist/esm/policies/systemErrorRetryPolicy.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$policies$2f$throttlingRetryPolicy$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@typespec/ts-http-runtime/dist/esm/policies/throttlingRetryPolicy.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$policies$2f$formDataPolicy$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@typespec/ts-http-runtime/dist/esm/policies/formDataPolicy.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$policies$2f$logPolicy$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@typespec/ts-http-runtime/dist/esm/policies/logPolicy.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$policies$2f$multipartPolicy$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@typespec/ts-http-runtime/dist/esm/policies/multipartPolicy.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$policies$2f$proxyPolicy$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@typespec/ts-http-runtime/dist/esm/policies/proxyPolicy.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$policies$2f$redirectPolicy$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@typespec/ts-http-runtime/dist/esm/policies/redirectPolicy.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$policies$2f$tlsPolicy$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@typespec/ts-http-runtime/dist/esm/policies/tlsPolicy.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$policies$2f$userAgentPolicy$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@typespec/ts-http-runtime/dist/esm/policies/userAgentPolicy.js [app-route] (ecmascript)"); //# sourceMappingURL=internal.js.map
;
;
;
;
;
;
;
;
;
;
;
;
;
;
}),
"[project]/node_modules/@typespec/ts-http-runtime/dist/esm/policies/internal.js [app-route] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$policies$2f$agentPolicy$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@typespec/ts-http-runtime/dist/esm/policies/agentPolicy.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$policies$2f$decompressResponsePolicy$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@typespec/ts-http-runtime/dist/esm/policies/decompressResponsePolicy.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$policies$2f$defaultRetryPolicy$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@typespec/ts-http-runtime/dist/esm/policies/defaultRetryPolicy.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$policies$2f$exponentialRetryPolicy$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@typespec/ts-http-runtime/dist/esm/policies/exponentialRetryPolicy.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$policies$2f$retryPolicy$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@typespec/ts-http-runtime/dist/esm/policies/retryPolicy.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$policies$2f$systemErrorRetryPolicy$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@typespec/ts-http-runtime/dist/esm/policies/systemErrorRetryPolicy.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$policies$2f$throttlingRetryPolicy$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@typespec/ts-http-runtime/dist/esm/policies/throttlingRetryPolicy.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$policies$2f$formDataPolicy$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@typespec/ts-http-runtime/dist/esm/policies/formDataPolicy.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$policies$2f$logPolicy$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@typespec/ts-http-runtime/dist/esm/policies/logPolicy.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$policies$2f$multipartPolicy$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@typespec/ts-http-runtime/dist/esm/policies/multipartPolicy.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$policies$2f$proxyPolicy$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@typespec/ts-http-runtime/dist/esm/policies/proxyPolicy.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$policies$2f$redirectPolicy$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@typespec/ts-http-runtime/dist/esm/policies/redirectPolicy.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$policies$2f$tlsPolicy$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@typespec/ts-http-runtime/dist/esm/policies/tlsPolicy.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$policies$2f$userAgentPolicy$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@typespec/ts-http-runtime/dist/esm/policies/userAgentPolicy.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$typespec$2f$ts$2d$http$2d$runtime$2f$dist$2f$esm$2f$policies$2f$internal$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@typespec/ts-http-runtime/dist/esm/policies/internal.js [app-route] (ecmascript) <locals>");
}),

};

//# sourceMappingURL=node_modules_%40typespec_ts-http-runtime_dist_esm_c8cb7fcc._.js.map