"use client";

import React from 'react';
import { Shield, Users, TrendingUp, ArrowRight } from 'lucide-react';

export default function CurriculumHighlights() {
  const cards = [
    {
      icon: Shield,
      title: "Conduct Advanced Security Assessments",
      description: "In the free tier, you'll gain the skills to perform comprehensive security assessments, vulnerability analyses, and penetration testing on various systems and networks, applying both theoretical knowledge and practical techniques learned during the training.",
      label: "Hands On Training"
    },
    {
      icon: Users,
      title: "Join Elite Cybersecurity Teams",
      description: "As a premium member, you'll be prepared to join similar high-level security teams, armed with industry-relevant skills and insider knowledge.",
      label: "Professional Internship"
    },
    {
      icon: TrendingUp,
      title: "Pursue Diverse Cybersecurity Careers",
      description: "Whether you choose the free or premium path, you'll explore various cybersecurity career options, understanding the roles and responsibilities in different organizations. This knowledge will help you make informed decisions about your career trajectory in the cybersecurity field.",
      label: "Hands On Training"
    }
  ];

  return (
    <section className="py-16 lg:py-24 bg-gradient-to-br from-[var(--color-dark-blue)] via-[var(--color-dark-blue-hover)] to-[var(--color-dark-blue-bg)] relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0">
        <div className="absolute top-0 left-0 w-full h-full bg-[url('data:image/svg+xml,%3Csvg%20width%3D%2260%22%20height%3D%2260%22%20viewBox%3D%220%200%2060%2060%22%20xmlns%3D%22http%3A//www.w3.org/2000/svg%22%3E%3Cg%20fill%3D%22none%22%20fill-rule%3D%22evenodd%22%3E%3Cg%20fill%3D%22%23ffffff%22%20fill-opacity%3D%220.03%22%3E%3Ccircle%20cx%3D%2230%22%20cy%3D%2230%22%20r%3D%221%22/%3E%3C/g%3E%3C/g%3E%3C/svg%3E')] opacity-50"></div>
        <div className="absolute top-20 left-10 w-72 h-72 bg-[var(--color-blue)]/20 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob"></div>
        <div className="absolute bottom-20 right-10 w-72 h-72 bg-[var(--color-blue-secondary)]/20 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob animation-delay-2000"></div>
      </div>

      <div className="relative z-10 container mx-auto px-4 max-w-7xl">
        {/* Header Section */}
        <div className="text-center mb-20">
          <div className="text-[var(--color-blue)] text-sm font-medium mb-4">
            Security Lit Presents
          </div>
          
          <h2 className="text-4xl lg:text-5xl font-bold text-white mb-8 leading-tight">
            Explore Our Comprehensive Curriculum
          </h2>
          
          <p className="text-lg text-white/90 max-w-4xl mx-auto leading-relaxed px-4">
            Dive into essential areas such as web application security, network penetration testing, cloud security, API security,
            and ethical hacking fundamentals.
          </p>

          {/* CTA Button */}
          <div className="mt-12">
            <button className="inline-flex items-center gap-3 px-8 py-4 bg-[var(--color-blue)] text-white font-semibold rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 hover:bg-[var(--color-blue-secondary)]">
              <Shield className="w-5 h-5" />
              Get the Curriculum Brochure
            </button>
                  </div>
                </div>
                
        {/* Three Cards Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 lg:gap-12">
          {cards.map((card, index) => (
            <div key={index} className="group relative">
              <div className="relative h-full bg-white/10 backdrop-blur-xl rounded-3xl p-8 border border-white/20 shadow-2xl hover:shadow-3xl transition-all duration-300">
                {/* Card Background Pattern */}
                <div className="absolute inset-0 bg-gradient-to-br from-[var(--color-blue)]/5 to-transparent rounded-3xl"></div>
                
                <div className="relative z-10 h-full flex flex-col">
                  {/* Icon */}
                  <div className="flex items-start justify-between mb-6">
                    <div className="w-14 h-14 bg-[var(--color-blue)]/20 backdrop-blur-sm rounded-2xl flex items-center justify-center shadow-lg">
                      <card.icon className="w-7 h-7 text-[var(--color-blue)]" />
                    </div>
                  </div>
                  
                  {/* Content */}
                <div className="flex-1">
                    <h3 className="text-xl lg:text-2xl font-bold text-white mb-4 leading-tight">
                      {card.title}
                    </h3>
                    <p className="text-lg text-white/80 leading-relaxed">
                      {card.description}
                    </p>
                  </div>

                  {/* Arrow Indicator */}
                  <div className="mt-6 pt-6 border-t border-[var(--color-blue)]/20">
                    <ArrowRight className="w-6 h-6 text-[var(--color-blue)] group-hover:translate-x-2 transition-transform" />
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
} 