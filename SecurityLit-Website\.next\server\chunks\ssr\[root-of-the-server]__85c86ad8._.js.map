{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 18, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/src/app/pricing/components/PricingCalculatorSection.jsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/app/pricing/components/PricingCalculatorSection.jsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/app/pricing/components/PricingCalculatorSection.jsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA2T,GACxV,yFACA", "debugId": null}}, {"offset": {"line": 30, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/src/app/pricing/components/PricingCalculatorSection.jsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/app/pricing/components/PricingCalculatorSection.jsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/app/pricing/components/PricingCalculatorSection.jsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAuS,GACpU,qEACA", "debugId": null}}, {"offset": {"line": 42, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 50, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/src/app/pricing/components/PricingCTA.jsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/app/pricing/components/PricingCTA.jsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/app/pricing/components/PricingCTA.jsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA6S,GAC1U,2EACA", "debugId": null}}, {"offset": {"line": 62, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/src/app/pricing/components/PricingCTA.jsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/app/pricing/components/PricingCTA.jsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/app/pricing/components/PricingCTA.jsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAyR,GACtT,uDACA", "debugId": null}}, {"offset": {"line": 74, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 82, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/src/app/contact/components/contactHero.jsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/app/contact/components/contactHero.jsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/app/contact/components/contactHero.jsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA8S,GAC3U,4EACA", "debugId": null}}, {"offset": {"line": 94, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/src/app/contact/components/contactHero.jsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/app/contact/components/contactHero.jsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/app/contact/components/contactHero.jsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA0R,GACvT,wDACA", "debugId": null}}, {"offset": {"line": 106, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 114, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/src/app/pricing/page.jsx"], "sourcesContent": ["import React from 'react';\nimport PricingCalculatorSection from './components/PricingCalculatorSection';\nimport PricingCTA from './components/PricingCTA';\nimport ContactHero from '../contact/components/contactHero';\n\nexport const metadata = {\n  title: \"Cybersecurity Services Pricing | SecurityLit - Enterprise Security Consulting\",\n  description: \"Get transparent pricing for SecurityLit's cybersecurity services including penetration testing, security assessments, and cloud security consulting. Calculate your project cost instantly.\",\n  keywords: \"cybersecurity pricing, penetration testing cost, security assessment pricing, cloud security consulting rates, SecurityLit pricing\",\n  openGraph: {\n    title: \"Cybersecurity Services Pricing | SecurityLit\",\n    description: \"Get transparent pricing for enterprise cybersecurity services. Calculate your project cost instantly with our pricing calculator.\",\n    url: \"https://securitylit.com/pricing\",\n    siteName: \"SecurityLit\",\n    images: [\n      {\n        url: \"https://securitylit.com/images/pricing-og.jpg\",\n        width: 1200,\n        height: 630,\n        alt: \"SecurityLit Cybersecurity Services Pricing\",\n      },\n    ],\n    locale: \"en_US\",\n    type: \"website\",\n  },\n  twitter: {\n    card: \"summary_large_image\",\n    title: \"Cybersecurity Services Pricing | SecurityLit\",\n    description: \"Get transparent pricing for enterprise cybersecurity services. Calculate your project cost instantly.\",\n    images: [\"https://securitylit.com/images/pricing-twitter.jpg\"],\n  },\n  alternates: {\n    canonical: \"https://securitylit.com/pricing\",\n  },\n};\n\nexport default function PricingPage() {\n  const pricingBreadcrumbItems = [\n    {\n      name: \"Home\",\n      url: \"/\",\n      iconKey: \"home\",\n      description: \"Return to homepage\"\n    },\n    {\n      name: \"Pricing\",\n      url: \"/pricing\",\n      current: true,\n      iconKey: \"pricing\",\n      description: \"Get transparent pricing for cybersecurity services and calculate your project cost\"\n    }\n  ];\n\n  return (\n    <div className=\"min-h-screen bg-white\">\n      {/* Main Calculator Section */}\n      <PricingCalculatorSection />\n\n      {/* Call to Action Section */}\n      <PricingCTA />\n\n      {/* Contact Hero Section with Custom Props */}\n      <ContactHero\n        title=\"Get Your Custom Security Quote\"\n        subtitle=\"Transparent pricing for enterprise cybersecurity\"\n        description=\"Ready to secure your organization? Fill out our form and our security experts will provide you with a detailed proposal tailored to your specific requirements. Get expert consultation and discover how we can protect your digital assets.\"\n        breadcrumbItems={pricingBreadcrumbItems}\n      />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;;;;;;AAEO,MAAM,WAAW;IACtB,OAAO;IACP,aAAa;IACb,UAAU;IACV,WAAW;QACT,OAAO;QACP,aAAa;QACb,KAAK;QACL,UAAU;QACV,QAAQ;YACN;gBACE,KAAK;gBACL,OAAO;gBACP,QAAQ;gBACR,KAAK;YACP;SACD;QACD,QAAQ;QACR,MAAM;IACR;IACA,SAAS;QACP,MAAM;QACN,OAAO;QACP,aAAa;QACb,QAAQ;YAAC;SAAqD;IAChE;IACA,YAAY;QACV,WAAW;IACb;AACF;AAEe,SAAS;IACtB,MAAM,yBAAyB;QAC7B;YACE,MAAM;YACN,KAAK;YACL,SAAS;YACT,aAAa;QACf;QACA;YACE,MAAM;YACN,KAAK;YAC<PERSON>,SAAS;YACT,SAAS;YACT,aAAa;QACf;KACD;IAED,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC,gKAAA,CAAA,UAAwB;;;;;0BAGzB,8OAAC,kJAAA,CAAA,UAAU;;;;;0BAGX,8OAAC,mJAAA,CAAA,UAAW;gBACV,OAAM;gBACN,UAAS;gBACT,aAAY;gBACZ,iBAAiB;;;;;;;;;;;;AAIzB", "debugId": null}}]}