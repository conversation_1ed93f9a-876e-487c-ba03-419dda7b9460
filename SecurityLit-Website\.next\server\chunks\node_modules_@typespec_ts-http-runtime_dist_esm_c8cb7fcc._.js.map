{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@typespec/ts-http-runtime/dist/esm/util/random.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/node_modules/%40typespec/ts-http-runtime/src/util/random.ts"], "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\n/**\n * Returns a random integer value between a lower and upper bound,\n * inclusive of both bounds.\n * Note that this uses Math.random and isn't secure. If you need to use\n * this for any kind of security purpose, find a better source of random.\n * @param min - The smallest integer value allowed.\n * @param max - The largest integer value allowed.\n */\nexport function getRandomIntegerInclusive(min: number, max: number): number {\n  // Make sure inputs are integers.\n  min = Math.ceil(min);\n  max = Math.floor(max);\n  // Pick a random offset from zero to the size of the range.\n  // Since Math.random() can never return 1, we have to make the range one larger\n  // in order to be inclusive of the maximum value after we take the floor.\n  const offset = Math.floor(Math.random() * (max - min + 1));\n  return offset + min;\n}\n"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAElC;;;;;;;GAOG;;;AACG,SAAU,yBAAyB,CAAC,GAAW,EAAE,GAAW;IAChE,iCAAiC;IACjC,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IACrB,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;IACtB,2DAA2D;IAC3D,+EAA+E;IAC/E,yEAAyE;IACzE,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;IAC3D,OAAO,MAAM,GAAG,GAAG,CAAC;AACtB,CAAC", "debugId": null}}, {"offset": {"line": 31, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@typespec/ts-http-runtime/dist/esm/util/delay.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/node_modules/%40typespec/ts-http-runtime/src/util/delay.ts"], "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport { getRandomIntegerInclusive } from \"./random.js\";\n\n/**\n * Calculates the delay interval for retry attempts using exponential delay with jitter.\n * @param retryAttempt - The current retry attempt number.\n * @param config - The exponential retry configuration.\n * @returns An object containing the calculated retry delay.\n */\nexport function calculateRetryDelay(\n  retryAttempt: number,\n  config: {\n    retryDelayInMs: number;\n    maxRetryDelayInMs: number;\n  },\n): { retryAfterInMs: number } {\n  // Exponentially increase the delay each time\n  const exponentialDelay = config.retryDelayInMs * Math.pow(2, retryAttempt);\n\n  // Don't let the delay exceed the maximum\n  const clampedDelay = Math.min(config.maxRetryDelayInMs, exponentialDelay);\n\n  // Allow the final value to have some \"jitter\" (within 50% of the delay size) so\n  // that retries across multiple clients don't occur simultaneously.\n  const retryAfterInMs = clampedDelay / 2 + getRandomIntegerInclusive(0, clampedDelay / 2);\n\n  return { retryAfterInMs };\n}\n"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;;;;AAElC,OAAO,EAAE,yBAAyB,EAAE,MAAM,aAAa,CAAC;;AAQlD,SAAU,mBAAmB,CACjC,YAAoB,EACpB,MAGC;IAED,6CAA6C;IAC7C,MAAM,gBAAgB,GAAG,MAAM,CAAC,cAAc,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,YAAY,CAAC,CAAC;IAE3E,yCAAyC;IACzC,MAAM,YAAY,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,iBAAiB,EAAE,gBAAgB,CAAC,CAAC;IAE1E,gFAAgF;IAChF,mEAAmE;IACnE,MAAM,cAAc,GAAG,YAAY,GAAG,CAAC,8LAAG,4BAAA,AAAyB,EAAC,CAAC,EAAE,YAAY,GAAG,CAAC,CAAC,CAAC;IAEzF,OAAO;QAAE,cAAc;IAAA,CAAE,CAAC;AAC5B,CAAC", "debugId": null}}, {"offset": {"line": 54, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@typespec/ts-http-runtime/dist/esm/util/object.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/node_modules/%40typespec/ts-http-runtime/src/util/object.ts"], "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\n/**\n * A generic shape for a plain JS object.\n */\nexport type UnknownObject = { [s: string]: unknown };\n\n/**\n * Helper to determine when an input is a generic JS object.\n * @returns true when input is an object type that is not null, Array, RegExp, or Date.\n */\nexport function isObject(input: unknown): input is UnknownObject {\n  return (\n    typeof input === \"object\" &&\n    input !== null &&\n    !Array.isArray(input) &&\n    !(input instanceof RegExp) &&\n    !(input instanceof Date)\n  );\n}\n"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAOlC;;;GAGG;;;AACG,SAAU,QAAQ,CAAC,KAAc;IACrC,OAAO,AACL,OAAO,KAAK,KAAK,QAAQ,IACzB,KAAK,KAAK,IAAI,IACd,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,IACrB,CAAC,CAAC,KAAK,YAAY,MAAM,CAAC,IAC1B,CAAC,CAAC,KAAK,YAAY,IAAI,CAAC,CACzB,CAAC;AACJ,CAAC", "debugId": null}}, {"offset": {"line": 69, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@typespec/ts-http-runtime/dist/esm/util/error.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/node_modules/%40typespec/ts-http-runtime/src/util/error.ts"], "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport { isObject } from \"./object.js\";\n\n/**\n * Typeguard for an error object shape (has name and message)\n * @param e - Something caught by a catch clause.\n */\nexport function isError(e: unknown): e is Error {\n  if (isObject(e)) {\n    const hasName = typeof e.name === \"string\";\n    const hasMessage = typeof e.message === \"string\";\n    return hasName && hasMessage;\n  }\n  return false;\n}\n"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;;;;AAElC,OAAO,EAAE,QAAQ,EAAE,MAAM,aAAa,CAAC;;AAMjC,SAAU,OAAO,CAAC,CAAU;IAChC,+LAAI,WAAA,AAAQ,EAAC,CAAC,CAAC,EAAE,CAAC;QAChB,MAAM,OAAO,GAAG,OAAO,CAAC,CAAC,IAAI,KAAK,QAAQ,CAAC;QAC3C,MAAM,UAAU,GAAG,OAAO,CAAC,CAAC,OAAO,KAAK,QAAQ,CAAC;QACjD,OAAO,OAAO,IAAI,UAAU,CAAC;IAC/B,CAAC;IACD,OAAO,KAAK,CAAC;AACf,CAAC", "debugId": null}}, {"offset": {"line": 88, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@typespec/ts-http-runtime/dist/esm/util/sha256.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/node_modules/%40typespec/ts-http-runtime/src/util/sha256.ts"], "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport { createHash, createHmac } from \"node:crypto\";\n\n/**\n * Generates a SHA-256 HMAC signature.\n * @param key - The HMAC key represented as a base64 string, used to generate the cryptographic HMAC hash.\n * @param stringToSign - The data to be signed.\n * @param encoding - The textual encoding to use for the returned HMAC digest.\n */\nexport async function computeSha256Hmac(\n  key: string,\n  stringToSign: string,\n  encoding: \"base64\" | \"hex\",\n): Promise<string> {\n  const decodedKey = Buffer.from(key, \"base64\");\n\n  return createHmac(\"sha256\", decodedKey).update(stringToSign).digest(encoding);\n}\n\n/**\n * Generates a SHA-256 hash.\n * @param content - The data to be included in the hash.\n * @param encoding - The textual encoding to use for the returned hash.\n */\nexport async function computeSha256Hash(\n  content: string,\n  encoding: \"base64\" | \"hex\",\n): Promise<string> {\n  return createHash(\"sha256\").update(content).digest(encoding);\n}\n"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;;;;;AAElC,OAAO,EAAE,UAAU,EAAE,UAAU,EAAE,MAAM,aAAa,CAAC;;AAQ9C,KAAK,UAAU,iBAAiB,CACrC,GAAW,EACX,YAAoB,EACpB,QAA0B;IAE1B,MAAM,UAAU,GAAG,MAAM,CAAC,IAAI,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC;IAE9C,iIAAO,aAAA,AAAU,EAAC,QAAQ,EAAE,UAAU,CAAC,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;AAChF,CAAC;AAOM,KAAK,UAAU,iBAAiB,CACrC,OAAe,EACf,QAA0B;IAE1B,iIAAO,aAAA,AAAU,EAAC,QAAQ,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;AAC/D,CAAC", "debugId": null}}, {"offset": {"line": 107, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@typespec/ts-http-runtime/dist/esm/util/uuidUtils.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/node_modules/%40typespec/ts-http-runtime/src/util/uuidUtils.ts"], "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport { randomUUID as v4RandomUUID } from \"node:crypto\";\n\ninterface Crypto {\n  randomUUID(): string;\n}\n\ndeclare const globalThis: {\n  crypto: Crypto;\n};\n\n// NOTE: This is a workaround until we can use `globalThis.crypto.randomUUID` in Node.js 19+.\nconst uuidFunction =\n  typeof globalThis?.crypto?.randomUUID === \"function\"\n    ? globalThis.crypto.randomUUID.bind(globalThis.crypto)\n    : v4RandomUUID;\n\n/**\n * Generated Universally Unique Identifier\n *\n * @returns RFC4122 v4 UUID.\n */\nexport function randomUUID(): string {\n  return uuidFunction();\n}\n"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;;;;AAElC,OAAO,EAAE,UAAU,IAAI,YAAY,EAAE,MAAM,aAAa,CAAC;;;AAUzD,6FAA6F;AAC7F,MAAM,YAAY,GAChB,OAAO,CAAA,CAAA,KAAA,UAAU,KAAA,QAAV,UAAU,KAAA,KAAA,IAAA,KAAA,IAAV,UAAU,CAAE,MAAM,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAE,UAAU,CAAA,KAAK,UAAU,GAChD,UAAU,CAAC,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,yHACpD,aAAY,CAAC;AAOb,SAAU,UAAU;IACxB,OAAO,YAAY,EAAE,CAAC;AACxB,CAAC", "debugId": null}}, {"offset": {"line": 124, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@typespec/ts-http-runtime/dist/esm/util/checkEnvironment.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/node_modules/%40typespec/ts-http-runtime/src/util/checkEnvironment.ts"], "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\ninterface Window {\n  document: unknown;\n}\n\ninterface DedicatedWorkerGlobalScope {\n  constructor: {\n    name: string;\n  };\n\n  importScripts: (...paths: string[]) => void;\n}\n\ninterface Navigator {\n  product: string;\n}\n\ninterface DenoGlobal {\n  version: {\n    deno: string;\n  };\n}\n\ninterface BunGlobal {\n  version: string;\n}\n\n// eslint-disable-next-line @azure/azure-sdk/ts-no-window\ndeclare const window: Window;\ndeclare const self: DedicatedWorkerGlobalScope;\ndeclare const Deno: DenoGlobal;\ndeclare const Bun: BunGlobal;\ndeclare const navigator: Navigator;\n\n/**\n * A constant that indicates whether the environment the code is running is a Web Browser.\n */\n// eslint-disable-next-line @azure/azure-sdk/ts-no-window\nexport const isBrowser = typeof window !== \"undefined\" && typeof window.document !== \"undefined\";\n\n/**\n * A constant that indicates whether the environment the code is running is a Web Worker.\n */\nexport const isWebWorker =\n  typeof self === \"object\" &&\n  typeof self?.importScripts === \"function\" &&\n  (self.constructor?.name === \"DedicatedWorkerGlobalScope\" ||\n    self.constructor?.name === \"ServiceWorkerGlobalScope\" ||\n    self.constructor?.name === \"SharedWorkerGlobalScope\");\n\n/**\n * A constant that indicates whether the environment the code is running is Deno.\n */\nexport const isDeno =\n  typeof Deno !== \"undefined\" &&\n  typeof Deno.version !== \"undefined\" &&\n  typeof Deno.version.deno !== \"undefined\";\n\n/**\n * A constant that indicates whether the environment the code is running is Bun.sh.\n */\nexport const isBun = typeof Bun !== \"undefined\" && typeof Bun.version !== \"undefined\";\n\n/**\n * A constant that indicates whether the environment the code is running is a Node.js compatible environment.\n */\nexport const isNodeLike =\n  typeof globalThis.process !== \"undefined\" &&\n  Boolean(globalThis.process.version) &&\n  Boolean(globalThis.process.versions?.node);\n\n/**\n * A constant that indicates whether the environment the code is running is Node.JS.\n */\nexport const isNodeRuntime = isNodeLike && !isBun && !isDeno;\n\n/**\n * A constant that indicates whether the environment the code is running is in React-Native.\n */\n// https://github.com/facebook/react-native/blob/main/packages/react-native/Libraries/Core/setUpNavigator.js\nexport const isReactNative =\n  typeof navigator !== \"undefined\" && navigator?.product === \"ReactNative\";\n"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;;;;;;;;;;;AAuC3B,MAAM,SAAS,GAAG,OAAO,MAAM,GAAK,WAAW,IAAI,OAAO,MAAM,CAAC,QAAQ,KAAK,WAAW,CAAC;AAK1F,MAAM,WAAW,GACtB,OAAO,IAAI,KAAK,QAAQ,IACxB,OAAO,CAAA,IAAI,KAAA,QAAJ,IAAI,KAAA,KAAA,IAAA,KAAA,IAAJ,IAAI,CAAE,aAAa,CAAA,KAAK,UAAU,IACzC,CAAC,CAAA,CAAA,KAAA,IAAI,CAAC,WAAW,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAE,IAAI,MAAK,4BAA4B,IACtD,CAAA,CAAA,KAAA,IAAI,CAAC,WAAW,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAE,IAAI,MAAK,0BAA0B,IACrD,CAAA,CAAA,KAAA,IAAI,CAAC,WAAW,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAE,IAAI,MAAK,yBAAyB,CAAC,CAAC;AAKnD,MAAM,MAAM,GACjB,OAAO,IAAI,KAAK,WAAW,IAC3B,OAAO,IAAI,CAAC,OAAO,KAAK,WAAW,IACnC,OAAO,IAAI,CAAC,OAAO,CAAC,IAAI,KAAK,WAAW,CAAC;AAKpC,MAAM,KAAK,GAAG,OAAO,GAAG,KAAK,WAAW,IAAI,OAAO,GAAG,CAAC,OAAO,KAAK,WAAW,CAAC;AAK/E,MAAM,UAAU,GACrB,OAAO,UAAU,CAAC,OAAO,KAAK,WAAW,IACzC,OAAO,CAAC,UAAU,CAAC,OAAO,CAAC,OAAO,CAAC,IACnC,OAAO,CAAC,CAAA,KAAA,UAAU,CAAC,OAAO,CAAC,QAAQ,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAE,IAAI,CAAC,CAAC;AAKtC,MAAM,aAAa,GAAG,UAAU,IAAI,CAAC,KAAK,IAAI,CAAC,MAAM,CAAC;AAMtD,MAAM,aAAa,GACxB,OAAO,SAAS,KAAK,WAAW,IAAI,CAAA,SAAS,KAAA,QAAT,SAAS,KAAA,KAAA,IAAA,KAAA,IAAT,SAAS,CAAE,OAAO,MAAK,aAAa,CAAC", "debugId": null}}, {"offset": {"line": 147, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@typespec/ts-http-runtime/dist/esm/util/bytesEncoding.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/node_modules/%40typespec/ts-http-runtime/src/util/bytesEncoding.ts"], "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\n/** The supported character encoding type */\nexport type EncodingType = \"utf-8\" | \"base64\" | \"base64url\" | \"hex\";\n\n/**\n * The helper that transforms bytes with specific character encoding into string\n * @param bytes - the uint8array bytes\n * @param format - the format we use to encode the byte\n * @returns a string of the encoded string\n */\nexport function uint8ArrayToString(bytes: Uint8Array, format: EncodingType): string {\n  return Buffer.from(bytes).toString(format);\n}\n\n/**\n * The helper that transforms string to specific character encoded bytes array.\n * @param value - the string to be converted\n * @param format - the format we use to decode the value\n * @returns a uint8array\n */\nexport function stringToUint8Array(value: string, format: EncodingType): Uint8Array {\n  return Buffer.from(value, format);\n}\n"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAKlC;;;;;GAKG;;;;AACG,SAAU,kBAAkB,CAAC,KAAiB,EAAE,MAAoB;IACxE,OAAO,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;AAC7C,CAAC;AAQK,SAAU,kBAAkB,CAAC,KAAa,EAAE,MAAoB;IACpE,OAAO,MAAM,CAAC,IAAI,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;AACpC,CAAC", "debugId": null}}, {"offset": {"line": 168, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@typespec/ts-http-runtime/dist/esm/util/sanitizer.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/node_modules/%40typespec/ts-http-runtime/src/util/sanitizer.ts"], "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport { type UnknownObject, isObject } from \"./object.js\";\n\n/**\n * Sanitizer options\n */\nexport interface SanitizerOptions {\n  /**\n   * Header names whose values will be logged when logging is enabled.\n   * Defaults include a list of well-known safe headers. Any headers\n   * specified in this field will be added to that list.  Any other values will\n   * be written to logs as \"REDACTED\".\n   */\n  additionalAllowedHeaderNames?: string[];\n\n  /**\n   * Query string names whose values will be logged when logging is enabled. By default no\n   * query string values are logged.\n   */\n  additionalAllowedQueryParameters?: string[];\n}\n\nconst RedactedString = \"REDACTED\";\n\n// Make sure this list is up-to-date with the one under core/logger/Readme#Keyconcepts\nconst defaultAllowedHeaderNames = [\n  \"x-ms-client-request-id\",\n  \"x-ms-return-client-request-id\",\n  \"x-ms-useragent\",\n  \"x-ms-correlation-request-id\",\n  \"x-ms-request-id\",\n  \"client-request-id\",\n  \"ms-cv\",\n  \"return-client-request-id\",\n  \"traceparent\",\n\n  \"Access-Control-Allow-Credentials\",\n  \"Access-Control-Allow-Headers\",\n  \"Access-Control-Allow-Methods\",\n  \"Access-Control-Allow-Origin\",\n  \"Access-Control-Expose-Headers\",\n  \"Access-Control-Max-Age\",\n  \"Access-Control-Request-Headers\",\n  \"Access-Control-Request-Method\",\n  \"Origin\",\n\n  \"Accept\",\n  \"Accept-Encoding\",\n  \"Cache-Control\",\n  \"Connection\",\n  \"Content-Length\",\n  \"Content-Type\",\n  \"Date\",\n  \"ETag\",\n  \"Expires\",\n  \"If-Match\",\n  \"If-Modified-Since\",\n  \"If-None-Match\",\n  \"If-Unmodified-Since\",\n  \"Last-Modified\",\n  \"Pragma\",\n  \"Request-Id\",\n  \"Retry-After\",\n  \"Server\",\n  \"Transfer-Encoding\",\n  \"User-Agent\",\n  \"WWW-Authenticate\",\n];\n\nconst defaultAllowedQueryParameters: string[] = [\"api-version\"];\n\n/**\n * A utility class to sanitize objects for logging.\n */\nexport class Sanitizer {\n  private allowedHeaderNames: Set<string>;\n  private allowedQueryParameters: Set<string>;\n\n  constructor({\n    additionalAllowedHeaderNames: allowedHeaderNames = [],\n    additionalAllowedQueryParameters: allowedQueryParameters = [],\n  }: SanitizerOptions = {}) {\n    allowedHeaderNames = defaultAllowedHeaderNames.concat(allowedHeaderNames);\n    allowedQueryParameters = defaultAllowedQueryParameters.concat(allowedQueryParameters);\n\n    this.allowedHeaderNames = new Set(allowedHeaderNames.map((n) => n.toLowerCase()));\n    this.allowedQueryParameters = new Set(allowedQueryParameters.map((p) => p.toLowerCase()));\n  }\n\n  /**\n   * Sanitizes an object for logging.\n   * @param obj - The object to sanitize\n   * @returns - The sanitized object as a string\n   */\n  public sanitize(obj: unknown): string {\n    const seen = new Set<unknown>();\n    return JSON.stringify(\n      obj,\n      (key: string, value: unknown) => {\n        // Ensure Errors include their interesting non-enumerable members\n        if (value instanceof Error) {\n          return {\n            ...value,\n            name: value.name,\n            message: value.message,\n          };\n        }\n\n        if (key === \"headers\") {\n          return this.sanitizeHeaders(value as UnknownObject);\n        } else if (key === \"url\") {\n          return this.sanitizeUrl(value as string);\n        } else if (key === \"query\") {\n          return this.sanitizeQuery(value as UnknownObject);\n        } else if (key === \"body\") {\n          // Don't log the request body\n          return undefined;\n        } else if (key === \"response\") {\n          // Don't log response again\n          return undefined;\n        } else if (key === \"operationSpec\") {\n          // When using sendOperationRequest, the request carries a massive\n          // field with the autorest spec. No need to log it.\n          return undefined;\n        } else if (Array.isArray(value) || isObject(value)) {\n          if (seen.has(value)) {\n            return \"[Circular]\";\n          }\n          seen.add(value);\n        }\n\n        return value;\n      },\n      2,\n    );\n  }\n\n  /**\n   * Sanitizes a URL for logging.\n   * @param value - The URL to sanitize\n   * @returns - The sanitized URL as a string\n   */\n  public sanitizeUrl(value: string): string {\n    if (typeof value !== \"string\" || value === null || value === \"\") {\n      return value;\n    }\n\n    const url = new URL(value);\n\n    if (!url.search) {\n      return value;\n    }\n\n    for (const [key] of url.searchParams) {\n      if (!this.allowedQueryParameters.has(key.toLowerCase())) {\n        url.searchParams.set(key, RedactedString);\n      }\n    }\n\n    return url.toString();\n  }\n\n  private sanitizeHeaders(obj: UnknownObject): UnknownObject {\n    const sanitized: UnknownObject = {};\n    for (const key of Object.keys(obj)) {\n      if (this.allowedHeaderNames.has(key.toLowerCase())) {\n        sanitized[key] = obj[key];\n      } else {\n        sanitized[key] = RedactedString;\n      }\n    }\n    return sanitized;\n  }\n\n  private sanitizeQuery(value: UnknownObject): UnknownObject {\n    if (typeof value !== \"object\" || value === null) {\n      return value;\n    }\n\n    const sanitized: UnknownObject = {};\n\n    for (const k of Object.keys(value)) {\n      if (this.allowedQueryParameters.has(k.toLowerCase())) {\n        sanitized[k] = value[k];\n      } else {\n        sanitized[k] = RedactedString;\n      }\n    }\n\n    return sanitized;\n  }\n}\n"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;;;;AAElC,OAAO,EAAsB,QAAQ,EAAE,MAAM,aAAa,CAAC;;AAqB3D,MAAM,cAAc,GAAG,UAAU,CAAC;AAElC,sFAAsF;AACtF,MAAM,yBAAyB,GAAG;IAChC,wBAAwB;IACxB,+BAA+B;IAC/B,gBAAgB;IAChB,6BAA6B;IAC7B,iBAAiB;IACjB,mBAAmB;IACnB,OAAO;IACP,0BAA0B;IAC1B,aAAa;IAEb,kCAAkC;IAClC,8BAA8B;IAC9B,8BAA8B;IAC9B,6BAA6B;IAC7B,+BAA+B;IAC/B,wBAAwB;IACxB,gCAAgC;IAChC,+BAA+B;IAC/B,QAAQ;IAER,QAAQ;IACR,iBAAiB;IACjB,eAAe;IACf,YAAY;IACZ,gBAAgB;IAChB,cAAc;IACd,MAAM;IACN,MAAM;IACN,SAAS;IACT,UAAU;IACV,mBAAmB;IACnB,eAAe;IACf,qBAAqB;IACrB,eAAe;IACf,QAAQ;IACR,YAAY;IACZ,aAAa;IACb,QAAQ;IACR,mBAAmB;IACnB,YAAY;IACZ,kBAAkB;CACnB,CAAC;AAEF,MAAM,6BAA6B,GAAa;IAAC,aAAa;CAAC,CAAC;AAK1D,MAAO,SAAS;IAIpB,YAAY,EACV,4BAA4B,EAAE,kBAAkB,GAAG,EAAE,EACrD,gCAAgC,EAAE,sBAAsB,GAAG,EAAE,EAAA,GACzC,CAAA,CAAE,CAAA;QACtB,kBAAkB,GAAG,yBAAyB,CAAC,MAAM,CAAC,kBAAkB,CAAC,CAAC;QAC1E,sBAAsB,GAAG,6BAA6B,CAAC,MAAM,CAAC,sBAAsB,CAAC,CAAC;QAEtF,IAAI,CAAC,kBAAkB,GAAG,IAAI,GAAG,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAG,CAAD,AAAE,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC;QAClF,IAAI,CAAC,sBAAsB,GAAG,IAAI,GAAG,CAAC,sBAAsB,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAG,CAAD,AAAE,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC;IAC5F,CAAC;IAED;;;;OAIG,CACI,QAAQ,CAAC,GAAY,EAAA;QAC1B,MAAM,IAAI,GAAG,IAAI,GAAG,EAAW,CAAC;QAChC,OAAO,IAAI,CAAC,SAAS,CACnB,GAAG,EACH,CAAC,GAAW,EAAE,KAAc,EAAE,EAAE;YAC9B,iEAAiE;YACjE,IAAI,KAAK,YAAY,KAAK,EAAE,CAAC;gBAC3B,OAAA,OAAA,MAAA,CAAA,OAAA,MAAA,CAAA,CAAA,GACK,KAAK,GAAA;oBACR,IAAI,EAAE,KAAK,CAAC,IAAI;oBAChB,OAAO,EAAE,KAAK,CAAC,OAAO;gBAAA,GACtB;YACJ,CAAC;YAED,IAAI,GAAG,KAAK,SAAS,EAAE,CAAC;gBACtB,OAAO,IAAI,CAAC,eAAe,CAAC,KAAsB,CAAC,CAAC;YACtD,CAAC,MAAM,IAAI,GAAG,KAAK,KAAK,EAAE,CAAC;gBACzB,OAAO,IAAI,CAAC,WAAW,CAAC,KAAe,CAAC,CAAC;YAC3C,CAAC,MAAM,IAAI,GAAG,KAAK,OAAO,EAAE,CAAC;gBAC3B,OAAO,IAAI,CAAC,aAAa,CAAC,KAAsB,CAAC,CAAC;YACpD,CAAC,MAAM,IAAI,GAAG,KAAK,MAAM,EAAE,CAAC;gBAC1B,6BAA6B;gBAC7B,OAAO,SAAS,CAAC;YACnB,CAAC,MAAM,IAAI,GAAG,KAAK,UAAU,EAAE,CAAC;gBAC9B,2BAA2B;gBAC3B,OAAO,SAAS,CAAC;YACnB,CAAC,MAAM,IAAI,GAAG,KAAK,eAAe,EAAE,CAAC;gBACnC,iEAAiE;gBACjE,mDAAmD;gBACnD,OAAO,SAAS,CAAC;YACnB,CAAC,MAAM,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,+LAAI,WAAA,AAAQ,EAAC,KAAK,CAAC,EAAE,CAAC;gBACnD,IAAI,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC;oBACpB,OAAO,YAAY,CAAC;gBACtB,CAAC;gBACD,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;YAClB,CAAC;YAED,OAAO,KAAK,CAAC;QACf,CAAC,EACD,CAAC,CACF,CAAC;IACJ,CAAC;IAED;;;;OAIG,CACI,WAAW,CAAC,KAAa,EAAA;QAC9B,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,KAAK,KAAK,IAAI,IAAI,KAAK,KAAK,EAAE,EAAE,CAAC;YAChE,OAAO,KAAK,CAAC;QACf,CAAC;QAED,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,KAAK,CAAC,CAAC;QAE3B,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE,CAAC;YAChB,OAAO,KAAK,CAAC;QACf,CAAC;QAED,KAAK,MAAM,CAAC,GAAG,CAAC,IAAI,GAAG,CAAC,YAAY,CAAE,CAAC;YACrC,IAAI,CAAC,IAAI,CAAC,sBAAsB,CAAC,GAAG,CAAC,GAAG,CAAC,WAAW,EAAE,CAAC,EAAE,CAAC;gBACxD,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,GAAG,EAAE,cAAc,CAAC,CAAC;YAC5C,CAAC;QACH,CAAC;QAED,OAAO,GAAG,CAAC,QAAQ,EAAE,CAAC;IACxB,CAAC;IAEO,eAAe,CAAC,GAAkB,EAAA;QACxC,MAAM,SAAS,GAAkB,CAAA,CAAE,CAAC;QACpC,KAAK,MAAM,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAE,CAAC;YACnC,IAAI,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,GAAG,CAAC,WAAW,EAAE,CAAC,EAAE,CAAC;gBACnD,SAAS,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC;YAC5B,CAAC,MAAM,CAAC;gBACN,SAAS,CAAC,GAAG,CAAC,GAAG,cAAc,CAAC;YAClC,CAAC;QACH,CAAC;QACD,OAAO,SAAS,CAAC;IACnB,CAAC;IAEO,aAAa,CAAC,KAAoB,EAAA;QACxC,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,KAAK,KAAK,IAAI,EAAE,CAAC;YAChD,OAAO,KAAK,CAAC;QACf,CAAC;QAED,MAAM,SAAS,GAAkB,CAAA,CAAE,CAAC;QAEpC,KAAK,MAAM,CAAC,IAAI,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAE,CAAC;YACnC,IAAI,IAAI,CAAC,sBAAsB,CAAC,GAAG,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC,EAAE,CAAC;gBACrD,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;YAC1B,CAAC,MAAM,CAAC;gBACN,SAAS,CAAC,CAAC,CAAC,GAAG,cAAc,CAAC;YAChC,CAAC;QACH,CAAC;QAED,OAAO,SAAS,CAAC;IACnB,CAAC;CACF", "debugId": null}}, {"offset": {"line": 316, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@typespec/ts-http-runtime/dist/esm/util/internal.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/node_modules/%40typespec/ts-http-runtime/src/util/internal.ts"], "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nexport { calculateRetryDelay } from \"./delay.js\";\nexport { getRandomIntegerInclusive } from \"./random.js\";\nexport { isObject, type UnknownObject } from \"./object.js\";\nexport { isError } from \"./error.js\";\nexport { computeSha256Hash, computeSha256Hmac } from \"./sha256.js\";\nexport { randomUUID } from \"./uuidUtils.js\";\nexport {\n  isBrowser,\n  isBun,\n  isNodeLike,\n  isNodeRuntime,\n  isDeno,\n  isReactNative,\n  isWebWorker,\n} from \"./checkEnvironment.js\";\nexport { stringToUint8Array, uint8ArrayToString, type EncodingType } from \"./bytesEncoding.js\";\nexport { Sanitizer, type SanitizerOptions } from \"./sanitizer.js\";\n"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;;AAElC,OAAO,EAAE,mBAAmB,EAAE,MAAM,YAAY,CAAC;AACjD,OAAO,EAAE,yBAAyB,EAAE,MAAM,aAAa,CAAC;AACxD,OAAO,EAAE,QAAQ,EAAsB,MAAM,aAAa,CAAC;AAC3D,OAAO,EAAE,OAAO,EAAE,MAAM,YAAY,CAAC;AACrC,OAAO,EAAE,iBAAiB,EAAE,iBAAiB,EAAE,MAAM,aAAa,CAAC;AACnE,OAAO,EAAE,UAAU,EAAE,MAAM,gBAAgB,CAAC;AAC5C,OAAO,EACL,SAAS,EACT,KAAK,EACL,UAAU,EACV,aAAa,EACb,MAAM,EACN,aAAa,EACb,WAAW,GACZ,MAAM,uBAAuB,CAAC;AAC/B,OAAO,EAAE,kBAAkB,EAAE,kBAAkB,EAAqB,MAAM,oBAAoB,CAAC;AAC/F,OAAO,EAAE,SAAS,EAAyB,MAAM,gBAAgB,CAAC", "debugId": null}}, {"offset": {"line": 356, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@typespec/ts-http-runtime/dist/esm/abort-controller/AbortError.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/node_modules/%40typespec/ts-http-runtime/src/abort-controller/AbortError.ts"], "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\n/**\n * This error is thrown when an asynchronous operation has been aborted.\n * Check for this error by testing the `name` that the name property of the\n * error matches `\"AbortError\"`.\n *\n * @example\n * ```ts snippet:ReadmeSampleAbortError\n * import { AbortError } from \"@typespec/ts-http-runtime\";\n *\n * async function doAsyncWork(options: { abortSignal: AbortSignal }): Promise<void> {\n *   if (options.abortSignal.aborted) {\n *     throw new AbortError();\n *   }\n *\n *   // do async work\n * }\n *\n * const controller = new AbortController();\n * controller.abort();\n *\n * try {\n *   doAsyncWork({ abortSignal: controller.signal });\n * } catch (e) {\n *   if (e instanceof Error && e.name === \"AbortError\") {\n *     // handle abort error here.\n *   }\n * }\n * ```\n */\nexport class AbortError extends Error {\n  constructor(message?: string) {\n    super(message);\n    this.name = \"AbortError\";\n  }\n}\n"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAElC;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA4BG;;;AACG,MAAO,UAAW,SAAQ,KAAK;IACnC,YAAY,OAAgB,CAAA;QAC1B,KAAK,CAAC,OAAO,CAAC,CAAC;QACf,IAAI,CAAC,IAAI,GAAG,YAAY,CAAC;IAC3B,CAAC;CACF", "debugId": null}}, {"offset": {"line": 399, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@typespec/ts-http-runtime/dist/esm/logger/log.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/node_modules/%40typespec/ts-http-runtime/src/logger/log.ts"], "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport { EOL } from \"node:os\";\nimport util from \"node:util\";\nimport * as process from \"node:process\";\n\nexport function log(message: unknown, ...args: any[]): void {\n  process.stderr.write(`${util.format(message, ...args)}${EOL}`);\n}\n"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;;;;AAElC,OAAO,EAAE,GAAG,EAAE,MAAM,SAAS,CAAC;AAC9B,OAAO,IAAI,MAAM,WAAW,CAAC;AAC7B,OAAO,KAAK,OAAO,MAAM,cAAc,CAAC;;;;AAElC,SAAU,GAAG,CAAC,OAAgB,EAAE,GAAG,IAAW;4HAClD,OAAO,CAAC,CAAM,CAAC,KAAK,CAAC,qHAAG,UAAI,CAAC,MAAM,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,iHAAG,MAAG,EAAE,CAAC,CAAC;AACjE,CAAC", "debugId": null}}, {"offset": {"line": 417, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@typespec/ts-http-runtime/dist/esm/logger/debug.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/node_modules/%40typespec/ts-http-runtime/src/logger/debug.ts"], "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport { log } from \"./log.js\";\n\n/**\n * A simple mechanism for enabling logging.\n * Intended to mimic the publicly available `debug` package.\n */\nexport interface Debug {\n  /**\n   * Creates a new logger with the given namespace.\n   */\n  (namespace: string): Debugger;\n  /**\n   * The default log method (defaults to console)\n   */\n  log: (...args: any[]) => void;\n  /**\n   * Enables a particular set of namespaces.\n   * To enable multiple separate them with commas, e.g. \"info,debug\".\n   * Supports wildcards, e.g. \"typeSpecRuntime:*\"\n   * Supports skip syntax, e.g. \"typeSpecRuntime:*,-typeSpecRuntime:storage:*\" will enable\n   * everything under typeSpecRuntime except for things under typeSpecRuntime:storage.\n   */\n  enable: (namespaces: string) => void;\n  /**\n   * Checks if a particular namespace is enabled.\n   */\n  enabled: (namespace: string) => boolean;\n  /**\n   * Disables all logging, returns what was previously enabled.\n   */\n  disable: () => string;\n}\n\n/**\n * A log function that can be dynamically enabled and redirected.\n */\nexport interface Debugger {\n  /**\n   * Logs the given arguments to the `log` method.\n   */\n  (...args: any[]): void;\n  /**\n   * True if this logger is active and logging.\n   */\n  enabled: boolean;\n  /**\n   * Used to cleanup/remove this logger.\n   */\n  destroy: () => boolean;\n  /**\n   * The current log method. Can be overridden to redirect output.\n   */\n  log: (...args: any[]) => void;\n  /**\n   * The namespace of this logger.\n   */\n  namespace: string;\n  /**\n   * Extends this logger with a child namespace.\n   * Namespaces are separated with a ':' character.\n   */\n  extend: (namespace: string) => Debugger;\n}\n\nconst debugEnvVariable =\n  (typeof process !== \"undefined\" && process.env && process.env.DEBUG) || undefined;\n\nlet enabledString: string | undefined;\nlet enabledNamespaces: RegExp[] = [];\nlet skippedNamespaces: RegExp[] = [];\nconst debuggers: Debugger[] = [];\n\nif (debugEnvVariable) {\n  enable(debugEnvVariable);\n}\n\nconst debugObj: Debug = Object.assign(\n  (namespace: string): Debugger => {\n    return createDebugger(namespace);\n  },\n  {\n    enable,\n    enabled,\n    disable,\n    log,\n  },\n);\n\nfunction enable(namespaces: string): void {\n  enabledString = namespaces;\n  enabledNamespaces = [];\n  skippedNamespaces = [];\n  const wildcard = /\\*/g;\n  const namespaceList = namespaces.split(\",\").map((ns) => ns.trim().replace(wildcard, \".*?\"));\n  for (const ns of namespaceList) {\n    if (ns.startsWith(\"-\")) {\n      skippedNamespaces.push(new RegExp(`^${ns.substr(1)}$`));\n    } else {\n      enabledNamespaces.push(new RegExp(`^${ns}$`));\n    }\n  }\n  for (const instance of debuggers) {\n    instance.enabled = enabled(instance.namespace);\n  }\n}\n\nfunction enabled(namespace: string): boolean {\n  if (namespace.endsWith(\"*\")) {\n    return true;\n  }\n\n  for (const skipped of skippedNamespaces) {\n    if (skipped.test(namespace)) {\n      return false;\n    }\n  }\n  for (const enabledNamespace of enabledNamespaces) {\n    if (enabledNamespace.test(namespace)) {\n      return true;\n    }\n  }\n  return false;\n}\n\nfunction disable(): string {\n  const result = enabledString || \"\";\n  enable(\"\");\n  return result;\n}\n\nfunction createDebugger(namespace: string): Debugger {\n  const newDebugger: Debugger = Object.assign(debug, {\n    enabled: enabled(namespace),\n    destroy,\n    log: debugObj.log,\n    namespace,\n    extend,\n  });\n\n  function debug(...args: any[]): void {\n    if (!newDebugger.enabled) {\n      return;\n    }\n    if (args.length > 0) {\n      args[0] = `${namespace} ${args[0]}`;\n    }\n    newDebugger.log(...args);\n  }\n\n  debuggers.push(newDebugger);\n\n  return newDebugger;\n}\n\nfunction destroy(this: Debugger): boolean {\n  const index = debuggers.indexOf(this);\n  if (index >= 0) {\n    debuggers.splice(index, 1);\n    return true;\n  }\n  return false;\n}\n\nfunction extend(this: Debugger, namespace: string): Debugger {\n  const newDebugger = createDebugger(`${this.namespace}:${namespace}`);\n  newDebugger.log = this.log;\n  return newDebugger;\n}\n\nexport default debugObj;\n"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;;;;AAElC,OAAO,EAAE,GAAG,EAAE,MAAM,UAAU,CAAC;;AAgE/B,MAAM,gBAAgB,GACpB,AAAC,OAAO,OAAO,KAAK,WAAW,IAAI,OAAO,CAAC,GAAG,IAAI,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,GAAI,SAAS,CAAC;AAEpF,IAAI,aAAiC,CAAC;AACtC,IAAI,iBAAiB,GAAa,EAAE,CAAC;AACrC,IAAI,iBAAiB,GAAa,EAAE,CAAC;AACrC,MAAM,SAAS,GAAe,EAAE,CAAC;AAEjC,IAAI,gBAAgB,EAAE,CAAC;IACrB,MAAM,CAAC,gBAAgB,CAAC,CAAC;AAC3B,CAAC;AAED,MAAM,QAAQ,GAAU,MAAM,CAAC,MAAM,CACnC,CAAC,SAAiB,EAAY,EAAE;IAC9B,OAAO,cAAc,CAAC,SAAS,CAAC,CAAC;AACnC,CAAC,EACD;IACE,MAAM;IACN,OAAO;IACP,OAAO;+LACP,MAAG;CACJ,CACF,CAAC;AAEF,SAAS,MAAM,CAAC,UAAkB;IAChC,aAAa,GAAG,UAAU,CAAC;IAC3B,iBAAiB,GAAG,EAAE,CAAC;IACvB,iBAAiB,GAAG,EAAE,CAAC;IACvB,MAAM,QAAQ,GAAG,KAAK,CAAC;IACvB,MAAM,aAAa,GAAG,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAG,CAAD,CAAG,CAAC,IAAI,EAAE,CAAC,OAAO,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC,CAAC;IAC5F,KAAK,MAAM,EAAE,IAAI,aAAa,CAAE,CAAC;QAC/B,IAAI,EAAE,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC;YACvB,iBAAiB,CAAC,IAAI,CAAC,IAAI,MAAM,CAAC,CAAA,CAAA,EAAI,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CAAA,CAAA,CAAG,CAAC,CAAC,CAAC;QAC1D,CAAC,MAAM,CAAC;YACN,iBAAiB,CAAC,IAAI,CAAC,IAAI,MAAM,CAAC,CAAA,CAAA,EAAI,EAAE,CAAA,CAAA,CAAG,CAAC,CAAC,CAAC;QAChD,CAAC;IACH,CAAC;IACD,KAAK,MAAM,QAAQ,IAAI,SAAS,CAAE,CAAC;QACjC,QAAQ,CAAC,OAAO,GAAG,OAAO,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;IACjD,CAAC;AACH,CAAC;AAED,SAAS,OAAO,CAAC,SAAiB;IAChC,IAAI,SAAS,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;QAC5B,OAAO,IAAI,CAAC;IACd,CAAC;IAED,KAAK,MAAM,OAAO,IAAI,iBAAiB,CAAE,CAAC;QACxC,IAAI,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC;YAC5B,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IACD,KAAK,MAAM,gBAAgB,IAAI,iBAAiB,CAAE,CAAC;QACjD,IAAI,gBAAgB,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC;YACrC,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC;IACD,OAAO,KAAK,CAAC;AACf,CAAC;AAED,SAAS,OAAO;IACd,MAAM,MAAM,GAAG,aAAa,IAAI,EAAE,CAAC;IACnC,MAAM,CAAC,EAAE,CAAC,CAAC;IACX,OAAO,MAAM,CAAC;AAChB,CAAC;AAED,SAAS,cAAc,CAAC,SAAiB;IACvC,MAAM,WAAW,GAAa,MAAM,CAAC,MAAM,CAAC,KAAK,EAAE;QACjD,OAAO,EAAE,OAAO,CAAC,SAAS,CAAC;QAC3B,OAAO;QACP,GAAG,EAAE,QAAQ,CAAC,GAAG;QACjB,SAAS;QACT,MAAM;KACP,CAAC,CAAC;IAEH,SAAS,KAAK,CAAC,GAAG,IAAW;QAC3B,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,CAAC;YACzB,OAAO;QACT,CAAC;QACD,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACpB,IAAI,CAAC,CAAC,CAAC,GAAG,GAAG,SAAS,CAAA,CAAA,EAAI,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC;QACtC,CAAC;QACD,WAAW,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,CAAC;IAC3B,CAAC;IAED,SAAS,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;IAE5B,OAAO,WAAW,CAAC;AACrB,CAAC;AAED,SAAS,OAAO;IACd,MAAM,KAAK,GAAG,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;IACtC,IAAI,KAAK,IAAI,CAAC,EAAE,CAAC;QACf,SAAS,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;QAC3B,OAAO,IAAI,CAAC;IACd,CAAC;IACD,OAAO,KAAK,CAAC;AACf,CAAC;AAED,SAAS,MAAM,CAAiB,SAAiB;IAC/C,MAAM,WAAW,GAAG,cAAc,CAAC,GAAG,IAAI,CAAC,SAAS,CAAA,CAAA,EAAI,SAAS,EAAE,CAAC,CAAC;IACrE,WAAW,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC;IAC3B,OAAO,WAAW,CAAC;AACrB,CAAC;uCAEc,QAAQ,CAAC", "debugId": null}}, {"offset": {"line": 517, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@typespec/ts-http-runtime/dist/esm/logger/logger.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/node_modules/%40typespec/ts-http-runtime/src/logger/logger.ts"], "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport debug from \"./debug.js\";\n\nimport type { Debugger } from \"./debug.js\";\nexport type { Debugger };\n\n/**\n * The log levels supported by the logger.\n * The log levels in order of most verbose to least verbose are:\n * - verbose\n * - info\n * - warning\n * - error\n */\nexport type TypeSpecRuntimeLogLevel = \"verbose\" | \"info\" | \"warning\" | \"error\";\n\n/**\n * A TypeSpecRuntimeClientLogger is a function that can log to an appropriate severity level.\n */\nexport type TypeSpecRuntimeClientLogger = Debugger;\n\n/**\n * Defines the methods available on the SDK-facing logger.\n */\nexport interface TypeSpecRuntimeLogger {\n  /**\n   * Used for failures the program is unlikely to recover from,\n   * such as Out of Memory.\n   */\n  error: Debugger;\n  /**\n   * Used when a function fails to perform its intended task.\n   * Usually this means the function will throw an exception.\n   * Not used for self-healing events (e.g. automatic retry)\n   */\n  warning: Debugger;\n  /**\n   * Used when a function operates normally.\n   */\n  info: Debugger;\n  /**\n   * Used for detailed troubleshooting scenarios. This is\n   * intended for use by developers / system administrators\n   * for diagnosing specific failures.\n   */\n  verbose: Debugger;\n}\n\n/**\n * todo doc\n */\nexport interface LoggerContext {\n  /**\n   * Immediately enables logging at the specified log level. If no level is specified, logging is disabled.\n   * @param level - The log level to enable for logging.\n   * Options from most verbose to least verbose are:\n   * - verbose\n   * - info\n   * - warning\n   * - error\n   */\n  setLogLevel(logLevel?: TypeSpecRuntimeLogLevel): void;\n\n  /**\n   * Retrieves the currently specified log level.\n   */\n  getLogLevel(): TypeSpecRuntimeLogLevel | undefined;\n\n  /**\n   * Creates a logger for use by the SDKs that inherits from `TypeSpecRuntimeLogger`.\n   * @param namespace - The name of the SDK package.\n   * @hidden\n   */\n  createClientLogger(namespace: string): TypeSpecRuntimeLogger;\n\n  /**\n   * The TypeSpecRuntimeClientLogger provides a mechanism for overriding where logs are output to.\n   * By default, logs are sent to stderr.\n   * Override the `log` method to redirect logs to another location.\n   */\n  logger: TypeSpecRuntimeClientLogger;\n}\n\n/**\n * Option for creating a TypeSpecRuntimeLoggerContext.\n */\nexport interface CreateLoggerContextOptions {\n  /**\n   * The name of the environment variable to check for the log level.\n   */\n  logLevelEnvVarName: string;\n\n  /**\n   * The namespace of the logger.\n   */\n  namespace: string;\n}\n\nconst TYPESPEC_RUNTIME_LOG_LEVELS = [\"verbose\", \"info\", \"warning\", \"error\"];\n\ntype DebuggerWithLogLevel = Debugger & { level: TypeSpecRuntimeLogLevel };\n\nconst levelMap = {\n  verbose: 400,\n  info: 300,\n  warning: 200,\n  error: 100,\n};\n\nfunction patchLogMethod(\n  parent: TypeSpecRuntimeClientLogger,\n  child: TypeSpecRuntimeClientLogger | DebuggerWithLogLevel,\n): void {\n  child.log = (...args) => {\n    parent.log(...args);\n  };\n}\n\nfunction isTypeSpecRuntimeLogLevel(level: string): level is TypeSpecRuntimeLogLevel {\n  return TYPESPEC_RUNTIME_LOG_LEVELS.includes(level as any);\n}\n\n/**\n * Creates a logger context base on the provided options.\n * @param options - The options for creating a logger context.\n * @returns The logger context.\n */\nexport function createLoggerContext(options: CreateLoggerContextOptions): LoggerContext {\n  const registeredLoggers = new Set<DebuggerWithLogLevel>();\n  const logLevelFromEnv =\n    (typeof process !== \"undefined\" && process.env && process.env[options.logLevelEnvVarName]) ||\n    undefined;\n\n  let logLevel: TypeSpecRuntimeLogLevel | undefined;\n\n  const clientLogger: TypeSpecRuntimeClientLogger = debug(options.namespace);\n  clientLogger.log = (...args) => {\n    debug.log(...args);\n  };\n\n  function contextSetLogLevel(level?: TypeSpecRuntimeLogLevel): void {\n    if (level && !isTypeSpecRuntimeLogLevel(level)) {\n      throw new Error(\n        `Unknown log level '${level}'. Acceptable values: ${TYPESPEC_RUNTIME_LOG_LEVELS.join(\",\")}`,\n      );\n    }\n    logLevel = level;\n\n    const enabledNamespaces = [];\n    for (const logger of registeredLoggers) {\n      if (shouldEnable(logger)) {\n        enabledNamespaces.push(logger.namespace);\n      }\n    }\n\n    debug.enable(enabledNamespaces.join(\",\"));\n  }\n\n  if (logLevelFromEnv) {\n    // avoid calling setLogLevel because we don't want a mis-set environment variable to crash\n    if (isTypeSpecRuntimeLogLevel(logLevelFromEnv)) {\n      contextSetLogLevel(logLevelFromEnv);\n    } else {\n      console.error(\n        `${options.logLevelEnvVarName} set to unknown log level '${logLevelFromEnv}'; logging is not enabled. Acceptable values: ${TYPESPEC_RUNTIME_LOG_LEVELS.join(\n          \", \",\n        )}.`,\n      );\n    }\n  }\n\n  function shouldEnable(logger: DebuggerWithLogLevel): boolean {\n    return Boolean(logLevel && levelMap[logger.level] <= levelMap[logLevel]);\n  }\n\n  function createLogger(\n    parent: TypeSpecRuntimeClientLogger,\n    level: TypeSpecRuntimeLogLevel,\n  ): DebuggerWithLogLevel {\n    const logger: DebuggerWithLogLevel = Object.assign(parent.extend(level), {\n      level,\n    });\n\n    patchLogMethod(parent, logger);\n\n    if (shouldEnable(logger)) {\n      const enabledNamespaces = debug.disable();\n      debug.enable(enabledNamespaces + \",\" + logger.namespace);\n    }\n\n    registeredLoggers.add(logger);\n\n    return logger;\n  }\n\n  function contextGetLogLevel(): TypeSpecRuntimeLogLevel | undefined {\n    return logLevel;\n  }\n\n  function contextCreateClientLogger(namespace: string): TypeSpecRuntimeLogger {\n    const clientRootLogger: TypeSpecRuntimeClientLogger = clientLogger.extend(namespace);\n    patchLogMethod(clientLogger, clientRootLogger);\n    return {\n      error: createLogger(clientRootLogger, \"error\"),\n      warning: createLogger(clientRootLogger, \"warning\"),\n      info: createLogger(clientRootLogger, \"info\"),\n      verbose: createLogger(clientRootLogger, \"verbose\"),\n    };\n  }\n\n  return {\n    setLogLevel: contextSetLogLevel,\n    getLogLevel: contextGetLogLevel,\n    createClientLogger: contextCreateClientLogger,\n    logger: clientLogger,\n  };\n}\n\nconst context = createLoggerContext({\n  logLevelEnvVarName: \"TYPESPEC_RUNTIME_LOG_LEVEL\",\n  namespace: \"typeSpecRuntime\",\n});\n\n/**\n * Immediately enables logging at the specified log level. If no level is specified, logging is disabled.\n * @param level - The log level to enable for logging.\n * Options from most verbose to least verbose are:\n * - verbose\n * - info\n * - warning\n * - error\n */\n// eslint-disable-next-line @typescript-eslint/no-redeclare\nexport const TypeSpecRuntimeLogger: TypeSpecRuntimeClientLogger = context.logger;\n\n/**\n * Retrieves the currently specified log level.\n */\nexport function setLogLevel(logLevel?: TypeSpecRuntimeLogLevel): void {\n  context.setLogLevel(logLevel);\n}\n\n/**\n * Retrieves the currently specified log level.\n */\nexport function getLogLevel(): TypeSpecRuntimeLogLevel | undefined {\n  return context.getLogLevel();\n}\n\n/**\n * Creates a logger for use by the SDKs that inherits from `TypeSpecRuntimeLogger`.\n * @param namespace - The name of the SDK package.\n * @hidden\n */\nexport function createClientLogger(namespace: string): TypeSpecRuntimeLogger {\n  return context.createClientLogger(namespace);\n}\n"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;;;;;;;;AAElC,OAAO,KAAK,MAAM,YAAY,CAAC;;AAiG/B,MAAM,2BAA2B,GAAG;IAAC,SAAS;IAAE,MAAM;IAAE,SAAS;IAAE,OAAO;CAAC,CAAC;AAI5E,MAAM,QAAQ,GAAG;IACf,OAAO,EAAE,GAAG;IACZ,IAAI,EAAE,GAAG;IACT,OAAO,EAAE,GAAG;IACZ,KAAK,EAAE,GAAG;CACX,CAAC;AAEF,SAAS,cAAc,CACrB,MAAmC,EACnC,KAAyD;IAEzD,KAAK,CAAC,GAAG,GAAG,CAAC,GAAG,IAAI,EAAE,EAAE;QACtB,MAAM,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,CAAC;IACtB,CAAC,CAAC;AACJ,CAAC;AAED,SAAS,yBAAyB,CAAC,KAAa;IAC9C,OAAO,2BAA2B,CAAC,QAAQ,CAAC,KAAY,CAAC,CAAC;AAC5D,CAAC;AAOK,SAAU,mBAAmB,CAAC,OAAmC;IACrE,MAAM,iBAAiB,GAAG,IAAI,GAAG,EAAwB,CAAC;IAC1D,MAAM,eAAe,GACnB,AAAC,OAAO,OAAO,KAAK,WAAW,IAAI,OAAO,CAAC,GAAG,IAAI,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,kBAAkB,CAAC,CAAC,GAC1F,SAAS,CAAC;IAEZ,IAAI,QAA6C,CAAC;IAElD,MAAM,YAAY,+LAAgC,UAAA,AAAK,EAAC,OAAO,CAAC,SAAS,CAAC,CAAC;IAC3E,YAAY,CAAC,GAAG,GAAG,CAAC,GAAG,IAAI,EAAE,EAAE;gMAC7B,UAAK,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,CAAC;IACrB,CAAC,CAAC;IAEF,SAAS,kBAAkB,CAAC,KAA+B;QACzD,IAAI,KAAK,IAAI,CAAC,yBAAyB,CAAC,KAAK,CAAC,EAAE,CAAC;YAC/C,MAAM,IAAI,KAAK,CACb,CAAA,mBAAA,EAAsB,KAAK,CAAA,sBAAA,EAAyB,2BAA2B,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAC5F,CAAC;QACJ,CAAC;QACD,QAAQ,GAAG,KAAK,CAAC;QAEjB,MAAM,iBAAiB,GAAG,EAAE,CAAC;QAC7B,KAAK,MAAM,MAAM,IAAI,iBAAiB,CAAE,CAAC;YACvC,IAAI,YAAY,CAAC,MAAM,CAAC,EAAE,CAAC;gBACzB,iBAAiB,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;YAC3C,CAAC;QACH,CAAC;gMAED,UAAK,CAAC,MAAM,CAAC,iBAAiB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;IAC5C,CAAC;IAED,IAAI,eAAe,EAAE,CAAC;QACpB,0FAA0F;QAC1F,IAAI,yBAAyB,CAAC,eAAe,CAAC,EAAE,CAAC;YAC/C,kBAAkB,CAAC,eAAe,CAAC,CAAC;QACtC,CAAC,MAAM,CAAC;YACN,OAAO,CAAC,KAAK,CACX,GAAG,OAAO,CAAC,kBAAkB,CAAA,2BAAA,EAA8B,eAAe,CAAA,8CAAA,EAAiD,2BAA2B,CAAC,IAAI,CACzJ,IAAI,CACL,CAAA,CAAA,CAAG,CACL,CAAC;QACJ,CAAC;IACH,CAAC;IAED,SAAS,YAAY,CAAC,MAA4B;QAChD,OAAO,OAAO,CAAC,QAAQ,IAAI,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC;IAC3E,CAAC;IAED,SAAS,YAAY,CACnB,MAAmC,EACnC,KAA8B;QAE9B,MAAM,MAAM,GAAyB,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE;YACvE,KAAK;SACN,CAAC,CAAC;QAEH,cAAc,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;QAE/B,IAAI,YAAY,CAAC,MAAM,CAAC,EAAE,CAAC;YACzB,MAAM,iBAAiB,2LAAG,UAAK,CAAC,OAAO,EAAE,CAAC;mMAC1C,WAAK,CAAC,MAAM,CAAC,iBAAiB,GAAG,GAAG,GAAG,MAAM,CAAC,SAAS,CAAC,CAAC;QAC3D,CAAC;QAED,iBAAiB,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QAE9B,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,SAAS,kBAAkB;QACzB,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED,SAAS,yBAAyB,CAAC,SAAiB;QAClD,MAAM,gBAAgB,GAAgC,YAAY,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;QACrF,cAAc,CAAC,YAAY,EAAE,gBAAgB,CAAC,CAAC;QAC/C,OAAO;YACL,KAAK,EAAE,YAAY,CAAC,gBAAgB,EAAE,OAAO,CAAC;YAC9C,OAAO,EAAE,YAAY,CAAC,gBAAgB,EAAE,SAAS,CAAC;YAClD,IAAI,EAAE,YAAY,CAAC,gBAAgB,EAAE,MAAM,CAAC;YAC5C,OAAO,EAAE,YAAY,CAAC,gBAAgB,EAAE,SAAS,CAAC;SACnD,CAAC;IACJ,CAAC;IAED,OAAO;QACL,WAAW,EAAE,kBAAkB;QAC/B,WAAW,EAAE,kBAAkB;QAC/B,kBAAkB,EAAE,yBAAyB;QAC7C,MAAM,EAAE,YAAY;KACrB,CAAC;AACJ,CAAC;AAED,MAAM,OAAO,GAAG,mBAAmB,CAAC;IAClC,kBAAkB,EAAE,4BAA4B;IAChD,SAAS,EAAE,iBAAiB;CAC7B,CAAC,CAAC;AAYI,MAAM,qBAAqB,GAAgC,OAAO,CAAC,MAAM,CAAC;AAK3E,SAAU,WAAW,CAAC,QAAkC;IAC5D,OAAO,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;AAChC,CAAC;AAKK,SAAU,WAAW;IACzB,OAAO,OAAO,CAAC,WAAW,EAAE,CAAC;AAC/B,CAAC;AAOK,SAAU,kBAAkB,CAAC,SAAiB;IAClD,OAAO,OAAO,CAAC,kBAAkB,CAAC,SAAS,CAAC,CAAC;AAC/C,CAAC", "debugId": null}}, {"offset": {"line": 630, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@typespec/ts-http-runtime/dist/esm/httpHeaders.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/node_modules/%40typespec/ts-http-runtime/src/httpHeaders.ts"], "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type { HttpHeaders, RawHttpHeaders, RawHttpHeadersInput } from \"./interfaces.js\";\n\ninterface HeaderEntry {\n  name: string;\n  value: string;\n}\n\nfunction normalizeName(name: string): string {\n  return name.toLowerCase();\n}\n\nfunction* headerIterator(map: Map<string, HeaderEntry>): IterableIterator<[string, string]> {\n  for (const entry of map.values()) {\n    yield [entry.name, entry.value];\n  }\n}\n\nclass HttpHeadersImpl implements HttpHeaders {\n  private readonly _headersMap: Map<string, HeaderEntry>;\n\n  constructor(rawHeaders?: RawHttpHeaders | RawHttpHeadersInput) {\n    this._headersMap = new Map<string, HeaderEntry>();\n    if (rawHeaders) {\n      for (const headerName of Object.keys(rawHeaders)) {\n        this.set(headerName, rawHeaders[headerName]);\n      }\n    }\n  }\n\n  /**\n   * Set a header in this collection with the provided name and value. The name is\n   * case-insensitive.\n   * @param name - The name of the header to set. This value is case-insensitive.\n   * @param value - The value of the header to set.\n   */\n  public set(name: string, value: string | number | boolean): void {\n    this._headersMap.set(normalizeName(name), { name, value: String(value).trim() });\n  }\n\n  /**\n   * Get the header value for the provided header name, or undefined if no header exists in this\n   * collection with the provided name.\n   * @param name - The name of the header. This value is case-insensitive.\n   */\n  public get(name: string): string | undefined {\n    return this._headersMap.get(normalizeName(name))?.value;\n  }\n\n  /**\n   * Get whether or not this header collection contains a header entry for the provided header name.\n   * @param name - The name of the header to set. This value is case-insensitive.\n   */\n  public has(name: string): boolean {\n    return this._headersMap.has(normalizeName(name));\n  }\n\n  /**\n   * Remove the header with the provided headerName.\n   * @param name - The name of the header to remove.\n   */\n  public delete(name: string): void {\n    this._headersMap.delete(normalizeName(name));\n  }\n\n  /**\n   * Get the JSON object representation of this HTTP header collection.\n   */\n  public toJSON(options: { preserveCase?: boolean } = {}): RawHttpHeaders {\n    const result: RawHttpHeaders = {};\n    if (options.preserveCase) {\n      for (const entry of this._headersMap.values()) {\n        result[entry.name] = entry.value;\n      }\n    } else {\n      for (const [normalizedName, entry] of this._headersMap) {\n        result[normalizedName] = entry.value;\n      }\n    }\n\n    return result;\n  }\n\n  /**\n   * Get the string representation of this HTTP header collection.\n   */\n  public toString(): string {\n    return JSON.stringify(this.toJSON({ preserveCase: true }));\n  }\n\n  /**\n   * Iterate over tuples of header [name, value] pairs.\n   */\n  [Symbol.iterator](): Iterator<[string, string]> {\n    return headerIterator(this._headersMap);\n  }\n}\n\n/**\n * Creates an object that satisfies the `HttpHeaders` interface.\n * @param rawHeaders - A simple object representing initial headers\n */\nexport function createHttpHeaders(rawHeaders?: RawHttpHeadersInput): HttpHeaders {\n  return new HttpHeadersImpl(rawHeaders);\n}\n"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;;;;AASlC,SAAS,aAAa,CAAC,IAAY;IACjC,OAAO,IAAI,CAAC,WAAW,EAAE,CAAC;AAC5B,CAAC;AAED,QAAQ,CAAC,CAAC,cAAc,CAAC,GAA6B;IACpD,KAAK,MAAM,KAAK,IAAI,GAAG,CAAC,MAAM,EAAE,CAAE,CAAC;QACjC,MAAM;YAAC,KAAK,CAAC,IAAI;YAAE,KAAK,CAAC,KAAK;SAAC,CAAC;IAClC,CAAC;AACH,CAAC;AAED,MAAM,eAAe;IAGnB,YAAY,UAAiD,CAAA;QAC3D,IAAI,CAAC,WAAW,GAAG,IAAI,GAAG,EAAuB,CAAC;QAClD,IAAI,UAAU,EAAE,CAAC;YACf,KAAK,MAAM,UAAU,IAAI,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAE,CAAC;gBACjD,IAAI,CAAC,GAAG,CAAC,UAAU,EAAE,UAAU,CAAC,UAAU,CAAC,CAAC,CAAC;YAC/C,CAAC;QACH,CAAC;IACH,CAAC;IAED;;;;;OAKG,CACI,GAAG,CAAC,IAAY,EAAE,KAAgC,EAAA;QACvD,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,aAAa,CAAC,IAAI,CAAC,EAAE;YAAE,IAAI;YAAE,KAAK,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC,IAAI,EAAE;QAAA,CAAE,CAAC,CAAC;IACnF,CAAC;IAED;;;;OAIG,CACI,GAAG,CAAC,IAAY,EAAA;;QACrB,OAAO,CAAA,KAAA,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAE,KAAK,CAAC;IAC1D,CAAC;IAED;;;OAGG,CACI,GAAG,CAAC,IAAY,EAAA;QACrB,OAAO,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC;IACnD,CAAC;IAED;;;OAGG,CACI,MAAM,CAAC,IAAY,EAAA;QACxB,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC;IAC/C,CAAC;IAED;;OAEG,CACI,MAAM,CAAC,UAAsC,CAAA,CAAE,EAAA;QACpD,MAAM,MAAM,GAAmB,CAAA,CAAE,CAAC;QAClC,IAAI,OAAO,CAAC,YAAY,EAAE,CAAC;YACzB,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,CAAE,CAAC;gBAC9C,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC,KAAK,CAAC;YACnC,CAAC;QACH,CAAC,MAAM,CAAC;YACN,KAAK,MAAM,CAAC,cAAc,EAAE,KAAK,CAAC,IAAI,IAAI,CAAC,WAAW,CAAE,CAAC;gBACvD,MAAM,CAAC,cAAc,CAAC,GAAG,KAAK,CAAC,KAAK,CAAC;YACvC,CAAC;QACH,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;OAEG,CACI,QAAQ,GAAA;QACb,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC;YAAE,YAAY,EAAE,IAAI;QAAA,CAAE,CAAC,CAAC,CAAC;IAC7D,CAAC;IAED;;OAEG,CACH,CAAC,MAAM,CAAC,QAAQ,CAAC,GAAA;QACf,OAAO,cAAc,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;IAC1C,CAAC;CACF;AAMK,SAAU,iBAAiB,CAAC,UAAgC;IAChE,OAAO,IAAI,eAAe,CAAC,UAAU,CAAC,CAAC;AACzC,CAAC", "debugId": null}}, {"offset": {"line": 721, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@typespec/ts-http-runtime/dist/esm/auth/schemes.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/node_modules/%40typespec/ts-http-runtime/src/auth/schemes.ts"], "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type { OAuth2Flow } from \"./oauth2Flows.js\";\n\n/**\n * Represents HTTP Basic authentication scheme.\n * Basic authentication scheme requires a username and password to be provided with each request.\n * The credentials are encoded using Base64 and included in the Authorization header.\n */\nexport interface BasicAuthScheme {\n  /** Type of auth scheme */\n  kind: \"http\";\n  /** Basic authentication scheme */\n  scheme: \"basic\";\n}\n\n/**\n * Represents HTTP Bearer authentication scheme.\n * Bearer authentication scheme requires a bearer token to be provided with each request.\n * The token is included in the Authorization header with the \"Bearer\" prefix.\n */\nexport interface BearerAuthScheme {\n  /** Type of auth scheme */\n  kind: \"http\";\n  /** Bearer authentication scheme */\n  scheme: \"bearer\";\n}\n\n/**\n * Represents an endpoint or operation that requires no authentication.\n */\nexport interface NoAuthAuthScheme {\n  /** Type of auth scheme */\n  kind: \"noAuth\";\n}\n\n/**\n * Represents API Key authentication scheme.\n * API Key authentication requires a key to be provided with each request.\n * The key can be provided in different locations: query parameter, header, or cookie.\n */\nexport interface ApiKeyAuthScheme {\n  /** Type of auth scheme */\n  kind: \"apiKey\";\n  /** Location of the API key */\n  apiKeyLocation: \"query\" | \"header\" | \"cookie\";\n  /** Name of the API key parameter */\n  name: string;\n}\n\n/** Represents OAuth2 authentication scheme with specified flows */\nexport interface OAuth2AuthScheme<TFlows extends OAuth2Flow[]> {\n  /** Type of auth scheme */\n  kind: \"oauth2\";\n  /** Supported OAuth2 flows */\n  flows: TFlows;\n}\n\n/** Union type of all supported authentication schemes */\nexport type AuthScheme =\n  | BasicAuthScheme\n  | BearerAuthScheme\n  | NoAuthAuthScheme\n  | ApiKeyAuthScheme\n  | OAuth2AuthScheme<OAuth2Flow[]>;\n"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC", "debugId": null}}, {"offset": {"line": 730, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@typespec/ts-http-runtime/dist/esm/auth/oauth2Flows.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/node_modules/%40typespec/ts-http-runtime/src/auth/oauth2Flows.ts"], "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\n/**\n * Represents OAuth2 Authorization Code flow configuration.\n */\nexport interface AuthorizationCodeFlow {\n  /** Type of OAuth2 flow */\n  kind: \"authorizationCode\";\n  /** Authorization endpoint */\n  authorizationUrl: string;\n  /** Token endpoint */\n  tokenUrl: string;\n  /** Refresh token endpoint */\n  refreshUrl?: string;\n  /** OAuth2 scopes */\n  scopes?: string[];\n}\n\n/**\n * Represents OAuth2 Client Credentials flow configuration.\n */\nexport interface ClientCredentialsFlow {\n  /** Type of OAuth2 flow */\n  kind: \"clientCredentials\";\n  /** Token endpoint */\n  tokenUrl: string;\n  /** Refresh token endpoints */\n  refreshUrl?: string[];\n  /** OAuth2 scopes */\n  scopes?: string[];\n}\n\n/**\n * Represents OAuth2 Implicit flow configuration.\n */\nexport interface ImplicitFlow {\n  /** Type of OAuth2 flow */\n  kind: \"implicit\";\n  /** Authorization endpoint */\n  authorizationUrl: string;\n  /** Refresh token endpoint */\n  refreshUrl?: string;\n  /** OAuth2 scopes */\n  scopes?: string[];\n}\n\n/**\n * Represents OAuth2 Password flow configuration.\n */\nexport interface PasswordFlow {\n  /** Type of OAuth2 flow */\n  kind: \"password\";\n  /** Token endpoint */\n  tokenUrl: string;\n  /** Refresh token endpoint */\n  refreshUrl?: string;\n  /** OAuth2 scopes */\n  scopes?: string[];\n}\n\n/** Union type of all supported OAuth2 flows */\nexport type OAuth2Flow =\n  | AuthorizationCodeFlow\n  | ClientCredentialsFlow\n  | ImplicitFlow\n  | PasswordFlow;\n"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC", "debugId": null}}, {"offset": {"line": 739, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@typespec/ts-http-runtime/dist/esm/pipelineRequest.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/node_modules/%40typespec/ts-http-runtime/src/pipelineRequest.ts"], "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type {\n  FormDataMap,\n  HttpHeaders,\n  HttpMethods,\n  MultipartRequestBody,\n  PipelineRequest,\n  ProxySettings,\n  RequestBodyType,\n  TransferProgressEvent,\n} from \"./interfaces.js\";\nimport { createHttpHeaders } from \"./httpHeaders.js\";\nimport { randomUUID } from \"./util/uuidUtils.js\";\nimport { AuthScheme } from \"./auth/schemes.js\";\n\n/**\n * Settings to initialize a request.\n * Almost equivalent to Partial<PipelineRequest>, but url is mandatory.\n */\nexport interface PipelineRequestOptions {\n  /**\n   * The URL to make the request to.\n   */\n  url: string;\n\n  /**\n   * The HTTP method to use when making the request.\n   */\n  method?: HttpMethods;\n\n  /**\n   * The HTTP headers to use when making the request.\n   */\n  headers?: HttpHeaders;\n\n  /**\n   * The number of milliseconds a request can take before automatically being terminated.\n   * If the request is terminated, an `AbortError` is thrown.\n   * Defaults to 0, which disables the timeout.\n   */\n  timeout?: number;\n\n  /**\n   * If credentials (cookies) should be sent along during an XHR.\n   * Defaults to false.\n   */\n  withCredentials?: boolean;\n\n  /**\n   * A unique identifier for the request. Used for logging and tracing.\n   */\n  requestId?: string;\n\n  /**\n   * The HTTP body content (if any)\n   */\n  body?: RequestBodyType;\n\n  /**\n   * Body for a multipart request.\n   */\n  multipartBody?: MultipartRequestBody;\n\n  /**\n   * To simulate a browser form post\n   */\n  formData?: FormDataMap;\n\n  /**\n   * A list of response status codes whose corresponding PipelineResponse body should be treated as a stream.\n   */\n  streamResponseStatusCodes?: Set<number>;\n\n  /**\n   * BROWSER ONLY\n   *\n   * A browser only option to enable use of the Streams API. If this option is set and streaming is used\n   * (see `streamResponseStatusCodes`), the response will have a property `browserStream` instead of\n   * `blobBody` which will be undefined.\n   *\n   * Default value is false\n   */\n  enableBrowserStreams?: boolean;\n\n  /**\n   * Proxy configuration.\n   */\n  proxySettings?: ProxySettings;\n\n  /**\n   * If the connection should not be reused.\n   */\n  disableKeepAlive?: boolean;\n\n  /**\n   * Used to abort the request later.\n   */\n  abortSignal?: AbortSignal;\n\n  /**\n   * Callback which fires upon upload progress.\n   */\n  onUploadProgress?: (progress: TransferProgressEvent) => void;\n\n  /** Callback which fires upon download progress. */\n  onDownloadProgress?: (progress: TransferProgressEvent) => void;\n\n  /** Set to true if the request is sent over HTTP instead of HTTPS */\n  allowInsecureConnection?: boolean;\n\n  /**\n   * List of authentication schemes used for this specific request.\n   * These schemes define how the request will be authenticated.\n   *\n   * If values are provided, these schemes override the client level authentication schemes.\n   * If an empty array is provided, it explicitly specifies no authentication for the request.\n   * If not provided at the request level, the client level authentication schemes will be used.\n   */\n  authSchemes?: AuthScheme[];\n\n  /**\n   * Additional options to set on the request. This provides a way to override\n   * existing ones or provide request properties that are not declared.\n   *\n   * For possible valid properties, see\n   *   - NodeJS https.request options:  https://nodejs.org/api/http.html#httprequestoptions-callback\n   *   - Browser RequestInit: https://developer.mozilla.org/en-US/docs/Web/API/RequestInit\n   *\n   * WARNING: Options specified here will override any properties of same names when request is sent by {@link HttpClient}.\n   */\n  requestOverrides?: Record<string, unknown>;\n}\n\nclass PipelineRequestImpl implements PipelineRequest {\n  public url: string;\n  public method: HttpMethods;\n  public headers: HttpHeaders;\n  public timeout: number;\n  public withCredentials: boolean;\n  public body?: RequestBodyType;\n  public multipartBody?: MultipartRequestBody;\n  public formData?: FormDataMap;\n  public streamResponseStatusCodes?: Set<number>;\n  public enableBrowserStreams: boolean;\n\n  public proxySettings?: ProxySettings;\n  public disableKeepAlive: boolean;\n  public abortSignal?: AbortSignal;\n  public requestId: string;\n  public allowInsecureConnection?: boolean;\n  public onUploadProgress?: (progress: TransferProgressEvent) => void;\n  public onDownloadProgress?: (progress: TransferProgressEvent) => void;\n  public requestOverrides?: Record<string, unknown>;\n  public authSchemes?: AuthScheme[];\n\n  constructor(options: PipelineRequestOptions) {\n    this.url = options.url;\n    this.body = options.body;\n    this.headers = options.headers ?? createHttpHeaders();\n    this.method = options.method ?? \"GET\";\n    this.timeout = options.timeout ?? 0;\n    this.multipartBody = options.multipartBody;\n    this.formData = options.formData;\n    this.disableKeepAlive = options.disableKeepAlive ?? false;\n    this.proxySettings = options.proxySettings;\n    this.streamResponseStatusCodes = options.streamResponseStatusCodes;\n    this.withCredentials = options.withCredentials ?? false;\n    this.abortSignal = options.abortSignal;\n    this.onUploadProgress = options.onUploadProgress;\n    this.onDownloadProgress = options.onDownloadProgress;\n    this.requestId = options.requestId || randomUUID();\n    this.allowInsecureConnection = options.allowInsecureConnection ?? false;\n    this.enableBrowserStreams = options.enableBrowserStreams ?? false;\n    this.requestOverrides = options.requestOverrides;\n    this.authSchemes = options.authSchemes;\n  }\n}\n\n/**\n * Creates a new pipeline request with the given options.\n * This method is to allow for the easy setting of default values and not required.\n * @param options - The options to create the request with.\n */\nexport function createPipelineRequest(options: PipelineRequestOptions): PipelineRequest {\n  return new PipelineRequestImpl(options);\n}\n"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;;;;AAYlC,OAAO,EAAE,iBAAiB,EAAE,MAAM,kBAAkB,CAAC;AACrD,OAAO,EAAE,UAAU,EAAE,MAAM,qBAAqB,CAAC;;;AAyHjD,MAAM,mBAAmB;IAsBvB,YAAY,OAA+B,CAAA;;QACzC,IAAI,CAAC,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC;QACvB,IAAI,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC;QACzB,IAAI,CAAC,OAAO,GAAG,CAAA,KAAA,OAAO,CAAC,OAAO,MAAA,QAAA,OAAA,KAAA,IAAA,6LAAI,oBAAA,AAAiB,EAAE,CAAC;QACtD,IAAI,CAAC,MAAM,GAAG,CAAA,KAAA,OAAO,CAAC,MAAM,MAAA,QAAA,OAAA,KAAA,IAAA,KAAI,KAAK,CAAC;QACtC,IAAI,CAAC,OAAO,GAAG,CAAA,KAAA,OAAO,CAAC,OAAO,MAAA,QAAA,OAAA,KAAA,IAAA,KAAI,CAAC,CAAC;QACpC,IAAI,CAAC,aAAa,GAAG,OAAO,CAAC,aAAa,CAAC;QAC3C,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC;QACjC,IAAI,CAAC,gBAAgB,GAAG,CAAA,KAAA,OAAO,CAAC,gBAAgB,MAAA,QAAA,OAAA,KAAA,IAAA,KAAI,KAAK,CAAC;QAC1D,IAAI,CAAC,aAAa,GAAG,OAAO,CAAC,aAAa,CAAC;QAC3C,IAAI,CAAC,yBAAyB,GAAG,OAAO,CAAC,yBAAyB,CAAC;QACnE,IAAI,CAAC,eAAe,GAAG,CAAA,KAAA,OAAO,CAAC,eAAe,MAAA,QAAA,OAAA,KAAA,IAAA,KAAI,KAAK,CAAC;QACxD,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC,WAAW,CAAC;QACvC,IAAI,CAAC,gBAAgB,GAAG,OAAO,CAAC,gBAAgB,CAAC;QACjD,IAAI,CAAC,kBAAkB,GAAG,OAAO,CAAC,kBAAkB,CAAC;QACrD,IAAI,CAAC,SAAS,GAAG,OAAO,CAAC,SAAS,kMAAI,aAAA,AAAU,EAAE,CAAC;QACnD,IAAI,CAAC,uBAAuB,GAAG,CAAA,KAAA,OAAO,CAAC,uBAAuB,MAAA,QAAA,OAAA,KAAA,IAAA,KAAI,KAAK,CAAC;QACxE,IAAI,CAAC,oBAAoB,GAAG,CAAA,KAAA,OAAO,CAAC,oBAAoB,MAAA,QAAA,OAAA,KAAA,IAAA,KAAI,KAAK,CAAC;QAClE,IAAI,CAAC,gBAAgB,GAAG,OAAO,CAAC,gBAAgB,CAAC;QACjD,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC,WAAW,CAAC;IACzC,CAAC;CACF;AAOK,SAAU,qBAAqB,CAAC,OAA+B;IACnE,OAAO,IAAI,mBAAmB,CAAC,OAAO,CAAC,CAAC;AAC1C,CAAC", "debugId": null}}, {"offset": {"line": 779, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@typespec/ts-http-runtime/dist/esm/pipeline.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/node_modules/%40typespec/ts-http-runtime/src/pipeline.ts"], "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type { HttpClient, PipelineRequest, PipelineResponse, SendRequest } from \"./interfaces.js\";\n\n/**\n * Policies are executed in phases.\n * The execution order is:\n * 1. Serialize Phase\n * 2. Policies not in a phase\n * 3. Deserialize Phase\n * 4. Retry Phase\n * 5. Sign Phase\n */\nexport type PipelinePhase = \"Deserialize\" | \"Serialize\" | \"Retry\" | \"Sign\";\n\nconst ValidPhaseNames = new Set<PipelinePhase>([\"Deserialize\", \"Serialize\", \"Retry\", \"Sign\"]);\n\n/**\n * Options when adding a policy to the pipeline.\n * Used to express dependencies on other policies.\n */\nexport interface AddPolicyOptions {\n  /**\n   * Policies that this policy must come before.\n   */\n  beforePolicies?: string[];\n  /**\n   * Policies that this policy must come after.\n   */\n  afterPolicies?: string[];\n  /**\n   * The phase that this policy must come after.\n   */\n  afterPhase?: PipelinePhase;\n  /**\n   * The phase this policy belongs to.\n   */\n  phase?: PipelinePhase;\n}\n\n/**\n * A pipeline policy manipulates a request as it travels through the pipeline.\n * It is conceptually a middleware that is allowed to modify the request before\n * it is made as well as the response when it is received.\n */\nexport interface PipelinePolicy {\n  /**\n   * The policy name. Must be a unique string in the pipeline.\n   */\n  name: string;\n  /**\n   * The main method to implement that manipulates a request/response.\n   * @param request - The request being performed.\n   * @param next - The next policy in the pipeline. Must be called to continue the pipeline.\n   */\n  sendRequest(request: PipelineRequest, next: SendRequest): Promise<PipelineResponse>;\n}\n\n/**\n * Represents a pipeline for making a HTTP request to a URL.\n * Pipelines can have multiple policies to manage manipulating each request\n * before and after it is made to the server.\n */\nexport interface Pipeline {\n  /**\n   * Add a new policy to the pipeline.\n   * @param policy - A policy that manipulates a request.\n   * @param options - A set of options for when the policy should run.\n   */\n  addPolicy(policy: PipelinePolicy, options?: AddPolicyOptions): void;\n  /**\n   * Remove a policy from the pipeline.\n   * @param options - Options that let you specify which policies to remove.\n   */\n  removePolicy(options: { name?: string; phase?: PipelinePhase }): PipelinePolicy[];\n  /**\n   * Uses the pipeline to make a HTTP request.\n   * @param httpClient - The HttpClient that actually performs the request.\n   * @param request - The request to be made.\n   */\n  sendRequest(httpClient: HttpClient, request: PipelineRequest): Promise<PipelineResponse>;\n  /**\n   * Returns the current set of policies in the pipeline in the order in which\n   * they will be applied to the request. Later in the list is closer to when\n   * the request is performed.\n   */\n  getOrderedPolicies(): PipelinePolicy[];\n  /**\n   * Duplicates this pipeline to allow for modifying an existing one without mutating it.\n   */\n  clone(): Pipeline;\n}\n\ninterface PipelineDescriptor {\n  policy: PipelinePolicy;\n  options: AddPolicyOptions;\n}\n\ninterface PolicyGraphNode {\n  policy: PipelinePolicy;\n  dependsOn: Set<PolicyGraphNode>;\n  dependants: Set<PolicyGraphNode>;\n  afterPhase?: Phase;\n}\n\ninterface Phase {\n  name: PipelinePhase | \"None\";\n  policies: Set<PolicyGraphNode>;\n  hasRun: boolean;\n  hasAfterPolicies: boolean;\n}\n\n/**\n * A private implementation of Pipeline.\n * Do not export this class from the package.\n * @internal\n */\nclass HttpPipeline implements Pipeline {\n  private _policies: PipelineDescriptor[] = [];\n  private _orderedPolicies?: PipelinePolicy[];\n\n  private constructor(policies?: PipelineDescriptor[]) {\n    this._policies = policies?.slice(0) ?? [];\n    this._orderedPolicies = undefined;\n  }\n\n  public addPolicy(policy: PipelinePolicy, options: AddPolicyOptions = {}): void {\n    if (options.phase && options.afterPhase) {\n      throw new Error(\"Policies inside a phase cannot specify afterPhase.\");\n    }\n    if (options.phase && !ValidPhaseNames.has(options.phase)) {\n      throw new Error(`Invalid phase name: ${options.phase}`);\n    }\n    if (options.afterPhase && !ValidPhaseNames.has(options.afterPhase)) {\n      throw new Error(`Invalid afterPhase name: ${options.afterPhase}`);\n    }\n    this._policies.push({\n      policy,\n      options,\n    });\n    this._orderedPolicies = undefined;\n  }\n\n  public removePolicy(options: { name?: string; phase?: string }): PipelinePolicy[] {\n    const removedPolicies: PipelinePolicy[] = [];\n\n    this._policies = this._policies.filter((policyDescriptor) => {\n      if (\n        (options.name && policyDescriptor.policy.name === options.name) ||\n        (options.phase && policyDescriptor.options.phase === options.phase)\n      ) {\n        removedPolicies.push(policyDescriptor.policy);\n        return false;\n      } else {\n        return true;\n      }\n    });\n    this._orderedPolicies = undefined;\n\n    return removedPolicies;\n  }\n\n  public sendRequest(httpClient: HttpClient, request: PipelineRequest): Promise<PipelineResponse> {\n    const policies = this.getOrderedPolicies();\n\n    const pipeline = policies.reduceRight<SendRequest>(\n      (next, policy) => {\n        return (req: PipelineRequest) => {\n          return policy.sendRequest(req, next);\n        };\n      },\n      (req: PipelineRequest) => httpClient.sendRequest(req),\n    );\n\n    return pipeline(request);\n  }\n\n  public getOrderedPolicies(): PipelinePolicy[] {\n    if (!this._orderedPolicies) {\n      this._orderedPolicies = this.orderPolicies();\n    }\n    return this._orderedPolicies;\n  }\n\n  public clone(): Pipeline {\n    return new HttpPipeline(this._policies);\n  }\n\n  public static create(): Pipeline {\n    return new HttpPipeline();\n  }\n\n  private orderPolicies(): PipelinePolicy[] {\n    /**\n     * The goal of this method is to reliably order pipeline policies\n     * based on their declared requirements when they were added.\n     *\n     * Order is first determined by phase:\n     *\n     * 1. Serialize Phase\n     * 2. Policies not in a phase\n     * 3. Deserialize Phase\n     * 4. Retry Phase\n     * 5. Sign Phase\n     *\n     * Within each phase, policies are executed in the order\n     * they were added unless they were specified to execute\n     * before/after other policies or after a particular phase.\n     *\n     * To determine the final order, we will walk the policy list\n     * in phase order multiple times until all dependencies are\n     * satisfied.\n     *\n     * `afterPolicies` are the set of policies that must be\n     * executed before a given policy. This requirement is\n     * considered satisfied when each of the listed policies\n     * have been scheduled.\n     *\n     * `beforePolicies` are the set of policies that must be\n     * executed after a given policy. Since this dependency\n     * can be expressed by converting it into a equivalent\n     * `afterPolicies` declarations, they are normalized\n     * into that form for simplicity.\n     *\n     * An `afterPhase` dependency is considered satisfied when all\n     * policies in that phase have scheduled.\n     *\n     */\n    const result: PipelinePolicy[] = [];\n\n    // Track all policies we know about.\n    const policyMap: Map<string, PolicyGraphNode> = new Map<string, PolicyGraphNode>();\n\n    function createPhase(name: PipelinePhase | \"None\"): Phase {\n      return {\n        name,\n        policies: new Set<PolicyGraphNode>(),\n        hasRun: false,\n        hasAfterPolicies: false,\n      };\n    }\n\n    // Track policies for each phase.\n    const serializePhase = createPhase(\"Serialize\");\n    const noPhase = createPhase(\"None\");\n    const deserializePhase = createPhase(\"Deserialize\");\n    const retryPhase = createPhase(\"Retry\");\n    const signPhase = createPhase(\"Sign\");\n\n    // a list of phases in order\n    const orderedPhases = [serializePhase, noPhase, deserializePhase, retryPhase, signPhase];\n\n    // Small helper function to map phase name to each Phase\n    function getPhase(phase: PipelinePhase | undefined): Phase {\n      if (phase === \"Retry\") {\n        return retryPhase;\n      } else if (phase === \"Serialize\") {\n        return serializePhase;\n      } else if (phase === \"Deserialize\") {\n        return deserializePhase;\n      } else if (phase === \"Sign\") {\n        return signPhase;\n      } else {\n        return noPhase;\n      }\n    }\n\n    // First walk each policy and create a node to track metadata.\n    for (const descriptor of this._policies) {\n      const policy = descriptor.policy;\n      const options = descriptor.options;\n      const policyName = policy.name;\n      if (policyMap.has(policyName)) {\n        throw new Error(\"Duplicate policy names not allowed in pipeline\");\n      }\n      const node: PolicyGraphNode = {\n        policy,\n        dependsOn: new Set<PolicyGraphNode>(),\n        dependants: new Set<PolicyGraphNode>(),\n      };\n      if (options.afterPhase) {\n        node.afterPhase = getPhase(options.afterPhase);\n        node.afterPhase.hasAfterPolicies = true;\n      }\n      policyMap.set(policyName, node);\n      const phase = getPhase(options.phase);\n      phase.policies.add(node);\n    }\n\n    // Now that each policy has a node, connect dependency references.\n    for (const descriptor of this._policies) {\n      const { policy, options } = descriptor;\n      const policyName = policy.name;\n      const node = policyMap.get(policyName);\n      if (!node) {\n        throw new Error(`Missing node for policy ${policyName}`);\n      }\n\n      if (options.afterPolicies) {\n        for (const afterPolicyName of options.afterPolicies) {\n          const afterNode = policyMap.get(afterPolicyName);\n          if (afterNode) {\n            // Linking in both directions helps later\n            // when we want to notify dependants.\n            node.dependsOn.add(afterNode);\n            afterNode.dependants.add(node);\n          }\n        }\n      }\n      if (options.beforePolicies) {\n        for (const beforePolicyName of options.beforePolicies) {\n          const beforeNode = policyMap.get(beforePolicyName);\n          if (beforeNode) {\n            // To execute before another node, make it\n            // depend on the current node.\n            beforeNode.dependsOn.add(node);\n            node.dependants.add(beforeNode);\n          }\n        }\n      }\n    }\n\n    function walkPhase(phase: Phase): void {\n      phase.hasRun = true;\n      // Sets iterate in insertion order\n      for (const node of phase.policies) {\n        if (node.afterPhase && (!node.afterPhase.hasRun || node.afterPhase.policies.size)) {\n          // If this node is waiting on a phase to complete,\n          // we need to skip it for now.\n          // Even if the phase is empty, we should wait for it\n          // to be walked to avoid re-ordering policies.\n          continue;\n        }\n        if (node.dependsOn.size === 0) {\n          // If there's nothing else we're waiting for, we can\n          // add this policy to the result list.\n          result.push(node.policy);\n          // Notify anything that depends on this policy that\n          // the policy has been scheduled.\n          for (const dependant of node.dependants) {\n            dependant.dependsOn.delete(node);\n          }\n          policyMap.delete(node.policy.name);\n          phase.policies.delete(node);\n        }\n      }\n    }\n\n    function walkPhases(): void {\n      for (const phase of orderedPhases) {\n        walkPhase(phase);\n        // if the phase isn't complete\n        if (phase.policies.size > 0 && phase !== noPhase) {\n          if (!noPhase.hasRun) {\n            // Try running noPhase to see if that unblocks this phase next tick.\n            // This can happen if a phase that happens before noPhase\n            // is waiting on a noPhase policy to complete.\n            walkPhase(noPhase);\n          }\n          // Don't proceed to the next phase until this phase finishes.\n          return;\n        }\n\n        if (phase.hasAfterPolicies) {\n          // Run any policies unblocked by this phase\n          walkPhase(noPhase);\n        }\n      }\n    }\n\n    // Iterate until we've put every node in the result list.\n    let iteration = 0;\n    while (policyMap.size > 0) {\n      iteration++;\n      const initialResultLength = result.length;\n      // Keep walking each phase in order until we can order every node.\n      walkPhases();\n      // The result list *should* get at least one larger each time\n      // after the first full pass.\n      // Otherwise, we're going to loop forever.\n      if (result.length <= initialResultLength && iteration > 1) {\n        throw new Error(\"Cannot satisfy policy dependencies due to requirements cycle.\");\n      }\n    }\n\n    return result;\n  }\n}\n\n/**\n * Creates a totally empty pipeline.\n * Useful for testing or creating a custom one.\n */\nexport function createEmptyPipeline(): Pipeline {\n  return HttpPipeline.create();\n}\n"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;;;;AAelC,MAAM,eAAe,GAAG,IAAI,GAAG,CAAgB;IAAC,aAAa;IAAE,WAAW;IAAE,OAAO;IAAE,MAAM;CAAC,CAAC,CAAC;AAiG9F;;;;GAIG,CACH,MAAM,YAAY;IAIhB,YAAoB,QAA+B,CAAA;;QAH3C,IAAA,CAAA,SAAS,GAAyB,EAAE,CAAC;QAI3C,IAAI,CAAC,SAAS,GAAG,CAAA,KAAA,QAAQ,KAAA,QAAR,QAAQ,KAAA,KAAA,IAAA,KAAA,IAAR,QAAQ,CAAE,KAAK,CAAC,CAAC,CAAC,MAAA,QAAA,OAAA,KAAA,IAAA,KAAI,EAAE,CAAC;QAC1C,IAAI,CAAC,gBAAgB,GAAG,SAAS,CAAC;IACpC,CAAC;IAEM,SAAS,CAAC,MAAsB,EAAE,UAA4B,CAAA,CAAE,EAAA;QACrE,IAAI,OAAO,CAAC,KAAK,IAAI,OAAO,CAAC,UAAU,EAAE,CAAC;YACxC,MAAM,IAAI,KAAK,CAAC,oDAAoD,CAAC,CAAC;QACxE,CAAC;QACD,IAAI,OAAO,CAAC,KAAK,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC;YACzD,MAAM,IAAI,KAAK,CAAC,CAAA,oBAAA,EAAuB,OAAO,CAAC,KAAK,EAAE,CAAC,CAAC;QAC1D,CAAC;QACD,IAAI,OAAO,CAAC,UAAU,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE,CAAC;YACnE,MAAM,IAAI,KAAK,CAAC,CAAA,yBAAA,EAA4B,OAAO,CAAC,UAAU,EAAE,CAAC,CAAC;QACpE,CAAC;QACD,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;YAClB,MAAM;YACN,OAAO;SACR,CAAC,CAAC;QACH,IAAI,CAAC,gBAAgB,GAAG,SAAS,CAAC;IACpC,CAAC;IAEM,YAAY,CAAC,OAA0C,EAAA;QAC5D,MAAM,eAAe,GAAqB,EAAE,CAAC;QAE7C,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,gBAAgB,EAAE,EAAE;YAC1D,IACE,AAAC,OAAO,CAAC,IAAI,IAAI,gBAAgB,CAAC,MAAM,CAAC,IAAI,KAAK,OAAO,CAAC,IAAI,CAAC,GAC9D,OAAO,CAAC,KAAK,IAAI,gBAAgB,CAAC,OAAO,CAAC,KAAK,KAAK,OAAO,CAAC,KAAK,CAAC,CACnE,CAAC;gBACD,eAAe,CAAC,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC;gBAC9C,OAAO,KAAK,CAAC;YACf,CAAC,MAAM,CAAC;gBACN,OAAO,IAAI,CAAC;YACd,CAAC;QACH,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,gBAAgB,GAAG,SAAS,CAAC;QAElC,OAAO,eAAe,CAAC;IACzB,CAAC;IAEM,WAAW,CAAC,UAAsB,EAAE,OAAwB,EAAA;QACjE,MAAM,QAAQ,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAE3C,MAAM,QAAQ,GAAG,QAAQ,CAAC,WAAW,CACnC,CAAC,IAAI,EAAE,MAAM,EAAE,EAAE;YACf,OAAO,CAAC,GAAoB,EAAE,EAAE;gBAC9B,OAAO,MAAM,CAAC,WAAW,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;YACvC,CAAC,CAAC;QACJ,CAAC,EACD,CAAC,GAAoB,EAAE,CAAG,CAAD,SAAW,CAAC,WAAW,CAAC,GAAG,CAAC,CACtD,CAAC;QAEF,OAAO,QAAQ,CAAC,OAAO,CAAC,CAAC;IAC3B,CAAC;IAEM,kBAAkB,GAAA;QACvB,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAC3B,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,aAAa,EAAE,CAAC;QAC/C,CAAC;QACD,OAAO,IAAI,CAAC,gBAAgB,CAAC;IAC/B,CAAC;IAEM,KAAK,GAAA;QACV,OAAO,IAAI,YAAY,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;IAC1C,CAAC;IAEM,MAAM,CAAC,MAAM,GAAA;QAClB,OAAO,IAAI,YAAY,EAAE,CAAC;IAC5B,CAAC;IAEO,aAAa,GAAA;QACnB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;WAkCG,CACH,MAAM,MAAM,GAAqB,EAAE,CAAC;QAEpC,oCAAoC;QACpC,MAAM,SAAS,GAAiC,IAAI,GAAG,EAA2B,CAAC;QAEnF,SAAS,WAAW,CAAC,IAA4B;YAC/C,OAAO;gBACL,IAAI;gBACJ,QAAQ,EAAE,IAAI,GAAG,EAAmB;gBACpC,MAAM,EAAE,KAAK;gBACb,gBAAgB,EAAE,KAAK;aACxB,CAAC;QACJ,CAAC;QAED,iCAAiC;QACjC,MAAM,cAAc,GAAG,WAAW,CAAC,WAAW,CAAC,CAAC;QAChD,MAAM,OAAO,GAAG,WAAW,CAAC,MAAM,CAAC,CAAC;QACpC,MAAM,gBAAgB,GAAG,WAAW,CAAC,aAAa,CAAC,CAAC;QACpD,MAAM,UAAU,GAAG,WAAW,CAAC,OAAO,CAAC,CAAC;QACxC,MAAM,SAAS,GAAG,WAAW,CAAC,MAAM,CAAC,CAAC;QAEtC,4BAA4B;QAC5B,MAAM,aAAa,GAAG;YAAC,cAAc;YAAE,OAAO;YAAE,gBAAgB;YAAE,UAAU;YAAE,SAAS;SAAC,CAAC;QAEzF,wDAAwD;QACxD,SAAS,QAAQ,CAAC,KAAgC;YAChD,IAAI,KAAK,KAAK,OAAO,EAAE,CAAC;gBACtB,OAAO,UAAU,CAAC;YACpB,CAAC,MAAM,IAAI,KAAK,KAAK,WAAW,EAAE,CAAC;gBACjC,OAAO,cAAc,CAAC;YACxB,CAAC,MAAM,IAAI,KAAK,KAAK,aAAa,EAAE,CAAC;gBACnC,OAAO,gBAAgB,CAAC;YAC1B,CAAC,MAAM,IAAI,KAAK,KAAK,MAAM,EAAE,CAAC;gBAC5B,OAAO,SAAS,CAAC;YACnB,CAAC,MAAM,CAAC;gBACN,OAAO,OAAO,CAAC;YACjB,CAAC;QACH,CAAC;QAED,8DAA8D;QAC9D,KAAK,MAAM,UAAU,IAAI,IAAI,CAAC,SAAS,CAAE,CAAC;YACxC,MAAM,MAAM,GAAG,UAAU,CAAC,MAAM,CAAC;YACjC,MAAM,OAAO,GAAG,UAAU,CAAC,OAAO,CAAC;YACnC,MAAM,UAAU,GAAG,MAAM,CAAC,IAAI,CAAC;YAC/B,IAAI,SAAS,CAAC,GAAG,CAAC,UAAU,CAAC,EAAE,CAAC;gBAC9B,MAAM,IAAI,KAAK,CAAC,gDAAgD,CAAC,CAAC;YACpE,CAAC;YACD,MAAM,IAAI,GAAoB;gBAC5B,MAAM;gBACN,SAAS,EAAE,IAAI,GAAG,EAAmB;gBACrC,UAAU,EAAE,IAAI,GAAG,EAAmB;aACvC,CAAC;YACF,IAAI,OAAO,CAAC,UAAU,EAAE,CAAC;gBACvB,IAAI,CAAC,UAAU,GAAG,QAAQ,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;gBAC/C,IAAI,CAAC,UAAU,CAAC,gBAAgB,GAAG,IAAI,CAAC;YAC1C,CAAC;YACD,SAAS,CAAC,GAAG,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;YAChC,MAAM,KAAK,GAAG,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;YACtC,KAAK,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QAC3B,CAAC;QAED,kEAAkE;QAClE,KAAK,MAAM,UAAU,IAAI,IAAI,CAAC,SAAS,CAAE,CAAC;YACxC,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,GAAG,UAAU,CAAC;YACvC,MAAM,UAAU,GAAG,MAAM,CAAC,IAAI,CAAC;YAC/B,MAAM,IAAI,GAAG,SAAS,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;YACvC,IAAI,CAAC,IAAI,EAAE,CAAC;gBACV,MAAM,IAAI,KAAK,CAAC,CAAA,wBAAA,EAA2B,UAAU,EAAE,CAAC,CAAC;YAC3D,CAAC;YAED,IAAI,OAAO,CAAC,aAAa,EAAE,CAAC;gBAC1B,KAAK,MAAM,eAAe,IAAI,OAAO,CAAC,aAAa,CAAE,CAAC;oBACpD,MAAM,SAAS,GAAG,SAAS,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC;oBACjD,IAAI,SAAS,EAAE,CAAC;wBACd,yCAAyC;wBACzC,qCAAqC;wBACrC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;wBAC9B,SAAS,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;oBACjC,CAAC;gBACH,CAAC;YACH,CAAC;YACD,IAAI,OAAO,CAAC,cAAc,EAAE,CAAC;gBAC3B,KAAK,MAAM,gBAAgB,IAAI,OAAO,CAAC,cAAc,CAAE,CAAC;oBACtD,MAAM,UAAU,GAAG,SAAS,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC;oBACnD,IAAI,UAAU,EAAE,CAAC;wBACf,0CAA0C;wBAC1C,8BAA8B;wBAC9B,UAAU,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;wBAC/B,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;oBAClC,CAAC;gBACH,CAAC;YACH,CAAC;QACH,CAAC;QAED,SAAS,SAAS,CAAC,KAAY;YAC7B,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC;YACpB,kCAAkC;YAClC,KAAK,MAAM,IAAI,IAAI,KAAK,CAAC,QAAQ,CAAE,CAAC;gBAClC,IAAI,IAAI,CAAC,UAAU,IAAI,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,IAAI,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;oBAKlF,SAAS;gBACX,CAAC;gBACD,IAAI,IAAI,CAAC,SAAS,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC;oBAC9B,oDAAoD;oBACpD,sCAAsC;oBACtC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;oBACzB,mDAAmD;oBACnD,iCAAiC;oBACjC,KAAK,MAAM,SAAS,IAAI,IAAI,CAAC,UAAU,CAAE,CAAC;wBACxC,SAAS,CAAC,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;oBACnC,CAAC;oBACD,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;oBACnC,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;gBAC9B,CAAC;YACH,CAAC;QACH,CAAC;QAED,SAAS,UAAU;YACjB,KAAK,MAAM,KAAK,IAAI,aAAa,CAAE,CAAC;gBAClC,SAAS,CAAC,KAAK,CAAC,CAAC;gBACjB,8BAA8B;gBAC9B,IAAI,KAAK,CAAC,QAAQ,CAAC,IAAI,GAAG,CAAC,IAAI,KAAK,KAAK,OAAO,EAAE,CAAC;oBACjD,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC;wBACpB,oEAAoE;wBACpE,yDAAyD;wBACzD,8CAA8C;wBAC9C,SAAS,CAAC,OAAO,CAAC,CAAC;oBACrB,CAAC;oBACD,6DAA6D;oBAC7D,OAAO;gBACT,CAAC;gBAED,IAAI,KAAK,CAAC,gBAAgB,EAAE,CAAC;oBAC3B,2CAA2C;oBAC3C,SAAS,CAAC,OAAO,CAAC,CAAC;gBACrB,CAAC;YACH,CAAC;QACH,CAAC;QAED,yDAAyD;QACzD,IAAI,SAAS,GAAG,CAAC,CAAC;QAClB,MAAO,SAAS,CAAC,IAAI,GAAG,CAAC,CAAE,CAAC;YAC1B,SAAS,EAAE,CAAC;YACZ,MAAM,mBAAmB,GAAG,MAAM,CAAC,MAAM,CAAC;YAC1C,kEAAkE;YAClE,UAAU,EAAE,CAAC;YACb,6DAA6D;YAC7D,6BAA6B;YAC7B,0CAA0C;YAC1C,IAAI,MAAM,CAAC,MAAM,IAAI,mBAAmB,IAAI,SAAS,GAAG,CAAC,EAAE,CAAC;gBAC1D,MAAM,IAAI,KAAK,CAAC,+DAA+D,CAAC,CAAC;YACnF,CAAC;QACH,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;CACF;AAMK,SAAU,mBAAmB;IACjC,OAAO,YAAY,CAAC,MAAM,EAAE,CAAC;AAC/B,CAAC", "debugId": null}}, {"offset": {"line": 1042, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@typespec/ts-http-runtime/dist/esm/util/inspect.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/node_modules/%40typespec/ts-http-runtime/src/util/inspect.ts"], "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport { inspect } from \"node:util\";\n\nexport const custom = inspect.custom;\n"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;;;;AAElC,OAAO,EAAE,OAAO,EAAE,MAAM,WAAW,CAAC;;AAE7B,MAAM,MAAM,qHAAG,UAAO,CAAC,MAAM,CAAC", "debugId": null}}, {"offset": {"line": 1054, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@typespec/ts-http-runtime/dist/esm/restError.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/node_modules/%40typespec/ts-http-runtime/src/restError.ts"], "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport { isError } from \"./util/error.js\";\nimport type { PipelineRequest, PipelineResponse } from \"./interfaces.js\";\nimport { custom } from \"./util/inspect.js\";\nimport { Sanitizer } from \"./util/sanitizer.js\";\n\nconst errorSanitizer = new Sanitizer();\n\n/**\n * The options supported by RestError.\n */\nexport interface RestErrorOptions {\n  /**\n   * The code of the error itself (use statics on RestError if possible.)\n   */\n  code?: string;\n  /**\n   * The HTTP status code of the request (if applicable.)\n   */\n  statusCode?: number;\n  /**\n   * The request that was made.\n   */\n  request?: PipelineRequest;\n  /**\n   * The response received (if any.)\n   */\n  response?: PipelineResponse;\n}\n\n/**\n * A custom error type for failed pipeline requests.\n */\nexport class RestError extends Error {\n  /**\n   * Something went wrong when making the request.\n   * This means the actual request failed for some reason,\n   * such as a DNS issue or the connection being lost.\n   */\n  static readonly REQUEST_SEND_ERROR: string = \"REQUEST_SEND_ERROR\";\n  /**\n   * This means that parsing the response from the server failed.\n   * It may have been malformed.\n   */\n  static readonly PARSE_ERROR: string = \"PARSE_ERROR\";\n\n  /**\n   * The code of the error itself (use statics on RestError if possible.)\n   */\n  public code?: string;\n  /**\n   * The HTTP status code of the request (if applicable.)\n   */\n  public statusCode?: number;\n  /**\n   * The request that was made.\n   * This property is non-enumerable.\n   */\n  public request?: PipelineRequest;\n  /**\n   * The response received (if any.)\n   * This property is non-enumerable.\n   */\n  public response?: PipelineResponse;\n  /**\n   * Bonus property set by the throw site.\n   */\n  public details?: unknown;\n\n  constructor(message: string, options: RestErrorOptions = {}) {\n    super(message);\n    this.name = \"RestError\";\n    this.code = options.code;\n    this.statusCode = options.statusCode;\n\n    // The request and response may contain sensitive information in the headers or body.\n    // To help prevent this sensitive information being accidentally logged, the request and response\n    // properties are marked as non-enumerable here. This prevents them showing up in the output of\n    // JSON.stringify and console.log.\n    Object.defineProperty(this, \"request\", { value: options.request, enumerable: false });\n    Object.defineProperty(this, \"response\", { value: options.response, enumerable: false });\n\n    // Logging method for util.inspect in Node\n    Object.defineProperty(this, custom, {\n      value: () => {\n        // Extract non-enumerable properties and add them back. This is OK since in this output the request and\n        // response get sanitized.\n        return `RestError: ${this.message} \\n ${errorSanitizer.sanitize({\n          ...this,\n          request: this.request,\n          response: this.response,\n        })}`;\n      },\n      enumerable: false,\n    });\n\n    Object.setPrototypeOf(this, RestError.prototype);\n  }\n}\n\n/**\n * Typeguard for RestError\n * @param e - Something caught by a catch clause.\n */\nexport function isRestError(e: unknown): e is RestError {\n  if (e instanceof RestError) {\n    return true;\n  }\n  return isError(e) && e.name === \"RestError\";\n}\n"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;;;;;AAElC,OAAO,EAAE,OAAO,EAAE,MAAM,iBAAiB,CAAC;AAE1C,OAAO,EAAE,MAAM,EAAE,MAAM,mBAAmB,CAAC;AAC3C,OAAO,EAAE,SAAS,EAAE,MAAM,qBAAqB,CAAC;;;;AAEhD,MAAM,cAAc,GAAG,8LAAI,YAAS,EAAE,CAAC;AA2BjC,MAAO,SAAU,SAAQ,KAAK;IAoClC,YAAY,OAAe,EAAE,UAA4B,CAAA,CAAE,CAAA;QACzD,KAAK,CAAC,OAAO,CAAC,CAAC;QACf,IAAI,CAAC,IAAI,GAAG,WAAW,CAAC;QACxB,IAAI,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC;QACzB,IAAI,CAAC,UAAU,GAAG,OAAO,CAAC,UAAU,CAAC;QAErC,qFAAqF;QACrF,iGAAiG;QACjG,+FAA+F;QAC/F,kCAAkC;QAClC,MAAM,CAAC,cAAc,CAAC,IAAI,EAAE,SAAS,EAAE;YAAE,KAAK,EAAE,OAAO,CAAC,OAAO;YAAE,UAAU,EAAE,KAAK;QAAA,CAAE,CAAC,CAAC;QACtF,MAAM,CAAC,cAAc,CAAC,IAAI,EAAE,UAAU,EAAE;YAAE,KAAK,EAAE,OAAO,CAAC,QAAQ;YAAE,UAAU,EAAE,KAAK;QAAA,CAAE,CAAC,CAAC;QAExF,0CAA0C;QAC1C,MAAM,CAAC,cAAc,CAAC,IAAI,0LAAE,SAAM,EAAE;YAClC,KAAK,EAAE,GAAG,EAAE;gBACV,uGAAuG;gBACvG,0BAA0B;gBAC1B,OAAO,CAAA,WAAA,EAAc,IAAI,CAAC,OAAO,CAAA,IAAA,EAAO,cAAc,CAAC,QAAQ,CAAA,OAAA,MAAA,CAAA,OAAA,MAAA,CAAA,CAAA,GAC1D,IAAI,GAAA;oBACP,OAAO,EAAE,IAAI,CAAC,OAAO;oBACrB,QAAQ,EAAE,IAAI,CAAC,QAAQ;gBAAA,GACvB,EAAE,CAAC;YACP,CAAC;YACD,UAAU,EAAE,KAAK;SAClB,CAAC,CAAC;QAEH,MAAM,CAAC,cAAc,CAAC,IAAI,EAAE,SAAS,CAAC,SAAS,CAAC,CAAC;IACnD,CAAC;;AA/DD;;;;GAIG,CACa,UAAA,kBAAkB,GAAW,oBAAoB,CAAC;AAClE;;;GAGG,CACa,UAAA,WAAW,GAAW,aAAa,CAAC;AA4DhD,SAAU,WAAW,CAAC,CAAU;IACpC,IAAI,CAAC,YAAY,SAAS,EAAE,CAAC;QAC3B,OAAO,IAAI,CAAC;IACd,CAAC;IACD,iMAAO,UAAA,AAAO,EAAC,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,KAAK,WAAW,CAAC;AAC9C,CAAC", "debugId": null}}, {"offset": {"line": 1119, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@typespec/ts-http-runtime/dist/esm/log.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/node_modules/%40typespec/ts-http-runtime/src/log.ts"], "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport { createClientLogger } from \"./logger/logger.js\";\nexport const logger = createClientLogger(\"ts-http-runtime\");\n"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;;;;AAElC,OAAO,EAAE,kBAAkB,EAAE,MAAM,oBAAoB,CAAC;;AACjD,MAAM,MAAM,gMAAG,qBAAA,AAAkB,EAAC,iBAAiB,CAAC,CAAC", "debugId": null}}, {"offset": {"line": 1131, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@typespec/ts-http-runtime/dist/esm/nodeHttpClient.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/node_modules/%40typespec/ts-http-runtime/src/nodeHttpClient.ts"], "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport * as http from \"node:http\";\nimport * as https from \"node:https\";\nimport * as zlib from \"node:zlib\";\nimport { Transform } from \"node:stream\";\nimport { AbortError } from \"./abort-controller/AbortError.js\";\nimport type {\n  HttpClient,\n  HttpHeaders,\n  PipelineRequest,\n  PipelineResponse,\n  RequestBodyType,\n  TlsSettings,\n  TransferProgressEvent,\n} from \"./interfaces.js\";\nimport { createHttpHeaders } from \"./httpHeaders.js\";\nimport { RestError } from \"./restError.js\";\nimport type { IncomingMessage } from \"node:http\";\nimport { logger } from \"./log.js\";\nimport { Sanitizer } from \"./util/sanitizer.js\";\n\nconst DEFAULT_TLS_SETTINGS = {};\n\nfunction isReadableStream(body: any): body is NodeJS.ReadableStream {\n  return body && typeof body.pipe === \"function\";\n}\n\nfunction isStreamComplete(stream: NodeJS.ReadableStream): Promise<void> {\n  if (stream.readable === false) {\n    return Promise.resolve();\n  }\n\n  return new Promise((resolve) => {\n    const handler = (): void => {\n      resolve();\n      stream.removeListener(\"close\", handler);\n      stream.removeListener(\"end\", handler);\n      stream.removeListener(\"error\", handler);\n    };\n\n    stream.on(\"close\", handler);\n    stream.on(\"end\", handler);\n    stream.on(\"error\", handler);\n  });\n}\n\nfunction isArrayBuffer(body: any): body is ArrayBuffer | ArrayBufferView {\n  return body && typeof body.byteLength === \"number\";\n}\n\nclass ReportTransform extends Transform {\n  private loadedBytes = 0;\n  private progressCallback: (progress: TransferProgressEvent) => void;\n\n  // eslint-disable-next-line @typescript-eslint/no-unsafe-function-type\n  _transform(chunk: string | Buffer, _encoding: string, callback: Function): void {\n    this.push(chunk);\n    this.loadedBytes += chunk.length;\n    try {\n      this.progressCallback({ loadedBytes: this.loadedBytes });\n      callback();\n    } catch (e: any) {\n      callback(e);\n    }\n  }\n\n  constructor(progressCallback: (progress: TransferProgressEvent) => void) {\n    super();\n    this.progressCallback = progressCallback;\n  }\n}\n\n/**\n * A HttpClient implementation that uses Node's \"https\" module to send HTTPS requests.\n * @internal\n */\nclass NodeHttpClient implements HttpClient {\n  private cachedHttpAgent?: http.Agent;\n  private cachedHttpsAgents: WeakMap<TlsSettings, https.Agent> = new WeakMap();\n\n  /**\n   * Makes a request over an underlying transport layer and returns the response.\n   * @param request - The request to be made.\n   */\n  public async sendRequest(request: PipelineRequest): Promise<PipelineResponse> {\n    const abortController = new AbortController();\n    let abortListener: ((event: any) => void) | undefined;\n    if (request.abortSignal) {\n      if (request.abortSignal.aborted) {\n        throw new AbortError(\"The operation was aborted. Request has already been canceled.\");\n      }\n\n      abortListener = (event: Event) => {\n        if (event.type === \"abort\") {\n          abortController.abort();\n        }\n      };\n      request.abortSignal.addEventListener(\"abort\", abortListener);\n    }\n\n    let timeoutId: ReturnType<typeof setTimeout> | undefined;\n    if (request.timeout > 0) {\n      timeoutId = setTimeout(() => {\n        const sanitizer = new Sanitizer();\n        logger.info(`request to '${sanitizer.sanitizeUrl(request.url)}' timed out. canceling...`);\n        abortController.abort();\n      }, request.timeout);\n    }\n\n    const acceptEncoding = request.headers.get(\"Accept-Encoding\");\n    const shouldDecompress =\n      acceptEncoding?.includes(\"gzip\") || acceptEncoding?.includes(\"deflate\");\n\n    let body = typeof request.body === \"function\" ? request.body() : request.body;\n    if (body && !request.headers.has(\"Content-Length\")) {\n      const bodyLength = getBodyLength(body);\n      if (bodyLength !== null) {\n        request.headers.set(\"Content-Length\", bodyLength);\n      }\n    }\n\n    let responseStream: NodeJS.ReadableStream | undefined;\n    try {\n      if (body && request.onUploadProgress) {\n        const onUploadProgress = request.onUploadProgress;\n        const uploadReportStream = new ReportTransform(onUploadProgress);\n        uploadReportStream.on(\"error\", (e) => {\n          logger.error(\"Error in upload progress\", e);\n        });\n        if (isReadableStream(body)) {\n          body.pipe(uploadReportStream);\n        } else {\n          uploadReportStream.end(body);\n        }\n\n        body = uploadReportStream;\n      }\n\n      const res = await this.makeRequest(request, abortController, body);\n\n      if (timeoutId !== undefined) {\n        clearTimeout(timeoutId);\n      }\n\n      const headers = getResponseHeaders(res);\n\n      const status = res.statusCode ?? 0;\n      const response: PipelineResponse = {\n        status,\n        headers,\n        request,\n      };\n\n      // Responses to HEAD must not have a body.\n      // If they do return a body, that body must be ignored.\n      if (request.method === \"HEAD\") {\n        // call resume() and not destroy() to avoid closing the socket\n        // and losing keep alive\n        res.resume();\n        return response;\n      }\n\n      responseStream = shouldDecompress ? getDecodedResponseStream(res, headers) : res;\n\n      const onDownloadProgress = request.onDownloadProgress;\n      if (onDownloadProgress) {\n        const downloadReportStream = new ReportTransform(onDownloadProgress);\n        downloadReportStream.on(\"error\", (e) => {\n          logger.error(\"Error in download progress\", e);\n        });\n        responseStream.pipe(downloadReportStream);\n        responseStream = downloadReportStream;\n      }\n\n      if (\n        // Value of POSITIVE_INFINITY in streamResponseStatusCodes is considered as any status code\n        request.streamResponseStatusCodes?.has(Number.POSITIVE_INFINITY) ||\n        request.streamResponseStatusCodes?.has(response.status)\n      ) {\n        response.readableStreamBody = responseStream;\n      } else {\n        response.bodyAsText = await streamToText(responseStream);\n      }\n\n      return response;\n    } finally {\n      // clean up event listener\n      if (request.abortSignal && abortListener) {\n        let uploadStreamDone = Promise.resolve();\n        if (isReadableStream(body)) {\n          uploadStreamDone = isStreamComplete(body);\n        }\n        let downloadStreamDone = Promise.resolve();\n        if (isReadableStream(responseStream)) {\n          downloadStreamDone = isStreamComplete(responseStream);\n        }\n        Promise.all([uploadStreamDone, downloadStreamDone])\n          .then(() => {\n            // eslint-disable-next-line promise/always-return\n            if (abortListener) {\n              request.abortSignal?.removeEventListener(\"abort\", abortListener);\n            }\n          })\n          .catch((e) => {\n            logger.warning(\"Error when cleaning up abortListener on httpRequest\", e);\n          });\n      }\n    }\n  }\n\n  private makeRequest(\n    request: PipelineRequest,\n    abortController: AbortController,\n    body?: RequestBodyType,\n  ): Promise<http.IncomingMessage> {\n    const url = new URL(request.url);\n\n    const isInsecure = url.protocol !== \"https:\";\n\n    if (isInsecure && !request.allowInsecureConnection) {\n      throw new Error(`Cannot connect to ${request.url} while allowInsecureConnection is false.`);\n    }\n\n    const agent = (request.agent as http.Agent) ?? this.getOrCreateAgent(request, isInsecure);\n    const options: http.RequestOptions = {\n      agent,\n      hostname: url.hostname,\n      path: `${url.pathname}${url.search}`,\n      port: url.port,\n      method: request.method,\n      headers: request.headers.toJSON({ preserveCase: true }),\n      ...request.requestOverrides,\n    };\n\n    return new Promise<http.IncomingMessage>((resolve, reject) => {\n      const req = isInsecure ? http.request(options, resolve) : https.request(options, resolve);\n\n      req.once(\"error\", (err: Error & { code?: string }) => {\n        reject(\n          new RestError(err.message, { code: err.code ?? RestError.REQUEST_SEND_ERROR, request }),\n        );\n      });\n\n      abortController.signal.addEventListener(\"abort\", () => {\n        const abortError = new AbortError(\n          \"The operation was aborted. Rejecting from abort signal callback while making request.\",\n        );\n        req.destroy(abortError);\n        reject(abortError);\n      });\n      if (body && isReadableStream(body)) {\n        body.pipe(req);\n      } else if (body) {\n        if (typeof body === \"string\" || Buffer.isBuffer(body)) {\n          req.end(body);\n        } else if (isArrayBuffer(body)) {\n          req.end(ArrayBuffer.isView(body) ? Buffer.from(body.buffer) : Buffer.from(body));\n        } else {\n          logger.error(\"Unrecognized body type\", body);\n          reject(new RestError(\"Unrecognized body type\"));\n        }\n      } else {\n        // streams don't like \"undefined\" being passed as data\n        req.end();\n      }\n    });\n  }\n\n  private getOrCreateAgent(request: PipelineRequest, isInsecure: boolean): http.Agent {\n    const disableKeepAlive = request.disableKeepAlive;\n\n    // Handle Insecure requests first\n    if (isInsecure) {\n      if (disableKeepAlive) {\n        // keepAlive:false is the default so we don't need a custom Agent\n        return http.globalAgent;\n      }\n\n      if (!this.cachedHttpAgent) {\n        // If there is no cached agent create a new one and cache it.\n        this.cachedHttpAgent = new http.Agent({ keepAlive: true });\n      }\n      return this.cachedHttpAgent;\n    } else {\n      if (disableKeepAlive && !request.tlsSettings) {\n        // When there are no tlsSettings and keepAlive is false\n        // we don't need a custom agent\n        return https.globalAgent;\n      }\n\n      // We use the tlsSettings to index cached clients\n      const tlsSettings = request.tlsSettings ?? DEFAULT_TLS_SETTINGS;\n\n      // Get the cached agent or create a new one with the\n      // provided values for keepAlive and tlsSettings\n      let agent = this.cachedHttpsAgents.get(tlsSettings);\n\n      if (agent && agent.options.keepAlive === !disableKeepAlive) {\n        return agent;\n      }\n\n      logger.info(\"No cached TLS Agent exist, creating a new Agent\");\n      agent = new https.Agent({\n        // keepAlive is true if disableKeepAlive is false.\n        keepAlive: !disableKeepAlive,\n        // Since we are spreading, if no tslSettings were provided, nothing is added to the agent options.\n        ...tlsSettings,\n      });\n\n      this.cachedHttpsAgents.set(tlsSettings, agent);\n      return agent;\n    }\n  }\n}\n\nfunction getResponseHeaders(res: IncomingMessage): HttpHeaders {\n  const headers = createHttpHeaders();\n  for (const header of Object.keys(res.headers)) {\n    const value = res.headers[header];\n    if (Array.isArray(value)) {\n      if (value.length > 0) {\n        headers.set(header, value[0]);\n      }\n    } else if (value) {\n      headers.set(header, value);\n    }\n  }\n  return headers;\n}\n\nfunction getDecodedResponseStream(\n  stream: IncomingMessage,\n  headers: HttpHeaders,\n): NodeJS.ReadableStream {\n  const contentEncoding = headers.get(\"Content-Encoding\");\n  if (contentEncoding === \"gzip\") {\n    const unzip = zlib.createGunzip();\n    stream.pipe(unzip);\n    return unzip;\n  } else if (contentEncoding === \"deflate\") {\n    const inflate = zlib.createInflate();\n    stream.pipe(inflate);\n    return inflate;\n  }\n\n  return stream;\n}\n\nfunction streamToText(stream: NodeJS.ReadableStream): Promise<string> {\n  return new Promise<string>((resolve, reject) => {\n    const buffer: Buffer[] = [];\n\n    stream.on(\"data\", (chunk) => {\n      if (Buffer.isBuffer(chunk)) {\n        buffer.push(chunk);\n      } else {\n        buffer.push(Buffer.from(chunk));\n      }\n    });\n    stream.on(\"end\", () => {\n      resolve(Buffer.concat(buffer).toString(\"utf8\"));\n    });\n    stream.on(\"error\", (e) => {\n      if (e && e?.name === \"AbortError\") {\n        reject(e);\n      } else {\n        reject(\n          new RestError(`Error reading response as text: ${e.message}`, {\n            code: RestError.PARSE_ERROR,\n          }),\n        );\n      }\n    });\n  });\n}\n\n/** @internal */\nexport function getBodyLength(body: RequestBodyType): number | null {\n  if (!body) {\n    return 0;\n  } else if (Buffer.isBuffer(body)) {\n    return body.length;\n  } else if (isReadableStream(body)) {\n    return null;\n  } else if (isArrayBuffer(body)) {\n    return body.byteLength;\n  } else if (typeof body === \"string\") {\n    return Buffer.from(body).length;\n  } else {\n    return null;\n  }\n}\n\n/**\n * Create a new HttpClient instance for the NodeJS environment.\n * @internal\n */\nexport function createNodeHttpClient(): HttpClient {\n  return new NodeHttpClient();\n}\n"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;;;;;AAElC,OAAO,KAAK,IAAI,MAAM,WAAW,CAAC;AAClC,OAAO,KAAK,KAAK,MAAM,YAAY,CAAC;AACpC,OAAO,KAAK,IAAI,MAAM,WAAW,CAAC;AAClC,OAAO,EAAE,SAAS,EAAE,MAAM,aAAa,CAAC;AACxC,OAAO,EAAE,UAAU,EAAE,MAAM,kCAAkC,CAAC;AAU9D,OAAO,EAAE,iBAAiB,EAAE,MAAM,kBAAkB,CAAC;AACrD,OAAO,EAAE,SAAS,EAAE,MAAM,gBAAgB,CAAC;AAE3C,OAAO,EAAE,MAAM,EAAE,MAAM,UAAU,CAAC;AAClC,OAAO,EAAE,SAAS,EAAE,MAAM,qBAAqB,CAAC;;;;;;;;;;AAEhD,MAAM,oBAAoB,GAAG,CAAA,CAAE,CAAC;AAEhC,SAAS,gBAAgB,CAAC,IAAS;IACjC,OAAO,IAAI,IAAI,OAAO,IAAI,CAAC,IAAI,KAAK,UAAU,CAAC;AACjD,CAAC;AAED,SAAS,gBAAgB,CAAC,MAA6B;IACrD,IAAI,MAAM,CAAC,QAAQ,KAAK,KAAK,EAAE,CAAC;QAC9B,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;IAC3B,CAAC;IAED,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;QAC7B,MAAM,OAAO,GAAG,GAAS,EAAE;YACzB,OAAO,EAAE,CAAC;YACV,MAAM,CAAC,cAAc,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;YACxC,MAAM,CAAC,cAAc,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;YACtC,MAAM,CAAC,cAAc,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;QAC1C,CAAC,CAAC;QAEF,MAAM,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;QAC5B,MAAM,CAAC,EAAE,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;QAC1B,MAAM,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;IAC9B,CAAC,CAAC,CAAC;AACL,CAAC;AAED,SAAS,aAAa,CAAC,IAAS;IAC9B,OAAO,IAAI,IAAI,OAAO,IAAI,CAAC,UAAU,KAAK,QAAQ,CAAC;AACrD,CAAC;AAED,MAAM,eAAgB,+HAAQ,YAAS;IAIrC,sEAAsE;IACtE,UAAU,CAAC,KAAsB,EAAE,SAAiB,EAAE,QAAkB,EAAA;QACtE,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACjB,IAAI,CAAC,WAAW,IAAI,KAAK,CAAC,MAAM,CAAC;QACjC,IAAI,CAAC;YACH,IAAI,CAAC,gBAAgB,CAAC;gBAAE,WAAW,EAAE,IAAI,CAAC,WAAW;YAAA,CAAE,CAAC,CAAC;YACzD,QAAQ,EAAE,CAAC;QACb,CAAC,CAAC,OAAO,CAAM,EAAE,CAAC;YAChB,QAAQ,CAAC,CAAC,CAAC,CAAC;QACd,CAAC;IACH,CAAC;IAED,YAAY,gBAA2D,CAAA;QACrE,KAAK,EAAE,CAAC;QAhBF,IAAA,CAAA,WAAW,GAAG,CAAC,CAAC;QAiBtB,IAAI,CAAC,gBAAgB,GAAG,gBAAgB,CAAC;IAC3C,CAAC;CACF;AAED;;;GAGG,CACH,MAAM,cAAc;IAApB,aAAA;QAEU,IAAA,CAAA,iBAAiB,GAAsC,IAAI,OAAO,EAAE,CAAC;IA2O/E,CAAC;IAzOC;;;OAGG,CACI,KAAK,CAAC,WAAW,CAAC,OAAwB,EAAA;;QAC/C,MAAM,eAAe,GAAG,IAAI,eAAe,EAAE,CAAC;QAC9C,IAAI,aAAiD,CAAC;QACtD,IAAI,OAAO,CAAC,WAAW,EAAE,CAAC;YACxB,IAAI,OAAO,CAAC,WAAW,CAAC,OAAO,EAAE,CAAC;gBAChC,MAAM,8MAAI,aAAU,CAAC,+DAA+D,CAAC,CAAC;YACxF,CAAC;YAED,aAAa,GAAG,CAAC,KAAY,EAAE,EAAE;gBAC/B,IAAI,KAAK,CAAC,IAAI,KAAK,OAAO,EAAE,CAAC;oBAC3B,eAAe,CAAC,KAAK,EAAE,CAAC;gBAC1B,CAAC;YACH,CAAC,CAAC;YACF,OAAO,CAAC,WAAW,CAAC,gBAAgB,CAAC,OAAO,EAAE,aAAa,CAAC,CAAC;QAC/D,CAAC;QAED,IAAI,SAAoD,CAAC;QACzD,IAAI,OAAO,CAAC,OAAO,GAAG,CAAC,EAAE,CAAC;YACxB,SAAS,GAAG,UAAU,CAAC,GAAG,EAAE;gBAC1B,MAAM,SAAS,GAAG,8LAAI,YAAS,EAAE,CAAC;4LAClC,SAAM,CAAC,IAAI,CAAC,CAAA,YAAA,EAAe,SAAS,CAAC,WAAW,CAAC,OAAO,CAAC,GAAG,CAAC,CAAA,yBAAA,CAA2B,CAAC,CAAC;gBAC1F,eAAe,CAAC,KAAK,EAAE,CAAC;YAC1B,CAAC,EAAE,OAAO,CAAC,OAAO,CAAC,CAAC;QACtB,CAAC;QAED,MAAM,cAAc,GAAG,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAC;QAC9D,MAAM,gBAAgB,GACpB,CAAA,cAAc,KAAA,QAAd,cAAc,KAAA,KAAA,IAAA,KAAA,IAAd,cAAc,CAAE,QAAQ,CAAC,MAAM,CAAC,KAAA,CAAI,cAAc,KAAA,QAAd,cAAc,KAAA,KAAA,IAAA,KAAA,IAAd,cAAc,CAAE,QAAQ,CAAC,SAAS,CAAC,CAAA,CAAC;QAE1E,IAAI,IAAI,GAAG,OAAO,OAAO,CAAC,IAAI,KAAK,UAAU,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC;QAC9E,IAAI,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,gBAAgB,CAAC,EAAE,CAAC;YACnD,MAAM,UAAU,GAAG,aAAa,CAAC,IAAI,CAAC,CAAC;YACvC,IAAI,UAAU,KAAK,IAAI,EAAE,CAAC;gBACxB,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,gBAAgB,EAAE,UAAU,CAAC,CAAC;YACpD,CAAC;QACH,CAAC;QAED,IAAI,cAAiD,CAAC;QACtD,IAAI,CAAC;YACH,IAAI,IAAI,IAAI,OAAO,CAAC,gBAAgB,EAAE,CAAC;gBACrC,MAAM,gBAAgB,GAAG,OAAO,CAAC,gBAAgB,CAAC;gBAClD,MAAM,kBAAkB,GAAG,IAAI,eAAe,CAAC,gBAAgB,CAAC,CAAC;gBACjE,kBAAkB,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE;gMACnC,SAAM,CAAC,KAAK,CAAC,0BAA0B,EAAE,CAAC,CAAC,CAAC;gBAC9C,CAAC,CAAC,CAAC;gBACH,IAAI,gBAAgB,CAAC,IAAI,CAAC,EAAE,CAAC;oBAC3B,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;gBAChC,CAAC,MAAM,CAAC;oBACN,kBAAkB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;gBAC/B,CAAC;gBAED,IAAI,GAAG,kBAAkB,CAAC;YAC5B,CAAC;YAED,MAAM,GAAG,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,eAAe,EAAE,IAAI,CAAC,CAAC;YAEnE,IAAI,SAAS,KAAK,SAAS,EAAE,CAAC;gBAC5B,YAAY,CAAC,SAAS,CAAC,CAAC;YAC1B,CAAC;YAED,MAAM,OAAO,GAAG,kBAAkB,CAAC,GAAG,CAAC,CAAC;YAExC,MAAM,MAAM,GAAG,CAAA,KAAA,GAAG,CAAC,UAAU,MAAA,QAAA,OAAA,KAAA,IAAA,KAAI,CAAC,CAAC;YACnC,MAAM,QAAQ,GAAqB;gBACjC,MAAM;gBACN,OAAO;gBACP,OAAO;aACR,CAAC;YAEF,0CAA0C;YAC1C,uDAAuD;YACvD,IAAI,OAAO,CAAC,MAAM,KAAK,MAAM,EAAE,CAAC;gBAC9B,8DAA8D;gBAC9D,wBAAwB;gBACxB,GAAG,CAAC,MAAM,EAAE,CAAC;gBACb,OAAO,QAAQ,CAAC;YAClB,CAAC;YAED,cAAc,GAAG,gBAAgB,CAAC,CAAC,CAAC,wBAAwB,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;YAEjF,MAAM,kBAAkB,GAAG,OAAO,CAAC,kBAAkB,CAAC;YACtD,IAAI,kBAAkB,EAAE,CAAC;gBACvB,MAAM,oBAAoB,GAAG,IAAI,eAAe,CAAC,kBAAkB,CAAC,CAAC;gBACrE,oBAAoB,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE;oBACrC,qLAAM,CAAC,KAAK,CAAC,4BAA4B,EAAE,CAAC,CAAC,CAAC;gBAChD,CAAC,CAAC,CAAC;gBACH,cAAc,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;gBAC1C,cAAc,GAAG,oBAAoB,CAAC;YACxC,CAAC;YAED,IACE,2FAA2F;YAC3F,CAAA,CAAA,KAAA,OAAO,CAAC,yBAAyB,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAE,GAAG,CAAC,MAAM,CAAC,iBAAiB,CAAC,MAChE,CAAA,KAAA,OAAO,CAAC,yBAAyB,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAE,GAAG,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAA,EACvD,CAAC;gBACD,QAAQ,CAAC,kBAAkB,GAAG,cAAc,CAAC;YAC/C,CAAC,MAAM,CAAC;gBACN,QAAQ,CAAC,UAAU,GAAG,MAAM,YAAY,CAAC,cAAc,CAAC,CAAC;YAC3D,CAAC;YAED,OAAO,QAAQ,CAAC;QAClB,CAAC,QAAS,CAAC;YACT,0BAA0B;YAC1B,IAAI,OAAO,CAAC,WAAW,IAAI,aAAa,EAAE,CAAC;gBACzC,IAAI,gBAAgB,GAAG,OAAO,CAAC,OAAO,EAAE,CAAC;gBACzC,IAAI,gBAAgB,CAAC,IAAI,CAAC,EAAE,CAAC;oBAC3B,gBAAgB,GAAG,gBAAgB,CAAC,IAAI,CAAC,CAAC;gBAC5C,CAAC;gBACD,IAAI,kBAAkB,GAAG,OAAO,CAAC,OAAO,EAAE,CAAC;gBAC3C,IAAI,gBAAgB,CAAC,cAAc,CAAC,EAAE,CAAC;oBACrC,kBAAkB,GAAG,gBAAgB,CAAC,cAAc,CAAC,CAAC;gBACxD,CAAC;gBACD,OAAO,CAAC,GAAG,CAAC;oBAAC,gBAAgB;oBAAE,kBAAkB;iBAAC,CAAC,CAChD,IAAI,CAAC,GAAG,EAAE;;oBACT,iDAAiD;oBACjD,IAAI,aAAa,EAAE,CAAC;wBAClB,CAAA,KAAA,OAAO,CAAC,WAAW,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAE,mBAAmB,CAAC,OAAO,EAAE,aAAa,CAAC,CAAC;oBACnE,CAAC;gBACH,CAAC,CAAC,CACD,KAAK,CAAC,CAAC,CAAC,EAAE,EAAE;oBACX,qLAAM,CAAC,OAAO,CAAC,qDAAqD,EAAE,CAAC,CAAC,CAAC;gBAC3E,CAAC,CAAC,CAAC;YACP,CAAC;QACH,CAAC;IACH,CAAC;IAEO,WAAW,CACjB,OAAwB,EACxB,eAAgC,EAChC,IAAsB,EAAA;;QAEtB,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;QAEjC,MAAM,UAAU,GAAG,GAAG,CAAC,QAAQ,KAAK,QAAQ,CAAC;QAE7C,IAAI,UAAU,IAAI,CAAC,OAAO,CAAC,uBAAuB,EAAE,CAAC;YACnD,MAAM,IAAI,KAAK,CAAC,CAAA,kBAAA,EAAqB,OAAO,CAAC,GAAG,CAAA,wCAAA,CAA0C,CAAC,CAAC;QAC9F,CAAC;QAED,MAAM,KAAK,GAAG,CAAA,KAAC,OAAO,CAAC,KAAoB,MAAA,QAAA,OAAA,KAAA,IAAA,KAAI,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC;QAC1F,MAAM,OAAO,GAAA,OAAA,MAAA,CAAA;YACX,KAAK;YACL,QAAQ,EAAE,GAAG,CAAC,QAAQ;YACtB,IAAI,EAAE,GAAG,GAAG,CAAC,QAAQ,GAAG,GAAG,CAAC,MAAM,EAAE;YACpC,IAAI,EAAE,GAAG,CAAC,IAAI;YACd,MAAM,EAAE,OAAO,CAAC,MAAM;YACtB,OAAO,EAAE,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC;gBAAE,YAAY,EAAE,IAAI;YAAA,CAAE,CAAC;QAAA,GACpD,OAAO,CAAC,gBAAgB,CAC5B,CAAC;QAEF,OAAO,IAAI,OAAO,CAAuB,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YAC3D,MAAM,GAAG,GAAG,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,uHAAO,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC,CAAC,qHAAC,KAAK,CAAC,IAAO,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;YAE1F,GAAG,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC,GAA8B,EAAE,EAAE;;gBACnD,MAAM,CACJ,sLAAI,YAAS,CAAC,GAAG,CAAC,OAAO,EAAE;oBAAE,IAAI,EAAE,CAAA,KAAA,GAAG,CAAC,IAAI,MAAA,QAAA,OAAA,KAAA,IAAA,uLAAI,YAAS,CAAC,kBAAkB;oBAAE,OAAO;gBAAA,CAAE,CAAC,CACxF,CAAC;YACJ,CAAC,CAAC,CAAC;YAEH,eAAe,CAAC,MAAM,CAAC,gBAAgB,CAAC,OAAO,EAAE,GAAG,EAAE;gBACpD,MAAM,UAAU,GAAG,8MAAI,aAAU,CAC/B,uFAAuF,CACxF,CAAC;gBACF,GAAG,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;gBACxB,MAAM,CAAC,UAAU,CAAC,CAAC;YACrB,CAAC,CAAC,CAAC;YACH,IAAI,IAAI,IAAI,gBAAgB,CAAC,IAAI,CAAC,EAAE,CAAC;gBACnC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YACjB,CAAC,MAAM,IAAI,IAAI,EAAE,CAAC;gBAChB,IAAI,OAAO,IAAI,KAAK,QAAQ,IAAI,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;oBACtD,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;gBAChB,CAAC,MAAM,IAAI,aAAa,CAAC,IAAI,CAAC,EAAE,CAAC;oBAC/B,GAAG,CAAC,GAAG,CAAC,WAAW,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;gBACnF,CAAC,MAAM,CAAC;gMACN,SAAM,CAAC,KAAK,CAAC,wBAAwB,EAAE,IAAI,CAAC,CAAC;oBAC7C,MAAM,CAAC,sLAAI,YAAS,CAAC,wBAAwB,CAAC,CAAC,CAAC;gBAClD,CAAC;YACH,CAAC,MAAM,CAAC;gBACN,sDAAsD;gBACtD,GAAG,CAAC,GAAG,EAAE,CAAC;YACZ,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,gBAAgB,CAAC,OAAwB,EAAE,UAAmB,EAAA;;QACpE,MAAM,gBAAgB,GAAG,OAAO,CAAC,gBAAgB,CAAC;QAElD,iCAAiC;QACjC,IAAI,UAAU,EAAE,CAAC;YACf,IAAI,gBAAgB,EAAE,CAAC;gBACrB,iEAAiE;gBACjE,yHAAO,IAAI,CAAC,SAAW,CAAC;YAC1B,CAAC;YAED,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC;gBAC1B,6DAA6D;gBAC7D,IAAI,CAAC,eAAe,GAAG,sHAAI,IAAI,CAAC,GAAK,CAAC;oBAAE,SAAS,EAAE,IAAI;gBAAA,CAAE,CAAC,CAAC;YAC7D,CAAC;YACD,OAAO,IAAI,CAAC,eAAe,CAAC;QAC9B,CAAC,MAAM,CAAC;YACN,IAAI,gBAAgB,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC;gBAC7C,uDAAuD;gBACvD,+BAA+B;gBAC/B,2HAAO,KAAK,CAAC,QAAW,CAAC;YAC3B,CAAC;YAED,iDAAiD;YACjD,MAAM,WAAW,GAAG,CAAA,KAAA,OAAO,CAAC,WAAW,MAAA,QAAA,OAAA,KAAA,IAAA,KAAI,oBAAoB,CAAC;YAEhE,oDAAoD;YACpD,gDAAgD;YAChD,IAAI,KAAK,GAAG,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;YAEpD,IAAI,KAAK,IAAI,KAAK,CAAC,OAAO,CAAC,SAAS,KAAK,CAAC,gBAAgB,EAAE,CAAC;gBAC3D,OAAO,KAAK,CAAC;YACf,CAAC;uLAED,UAAM,CAAC,IAAI,CAAC,iDAAiD,CAAC,CAAC;YAC/D,KAAK,GAAG,wHAAI,KAAK,CAAC,EAAK,CAAA,OAAA,MAAA,CAAA;gBACrB,kDAAkD;gBAClD,SAAS,EAAE,CAAC,gBAAgB;YAAA,GAEzB,WAAW,EACd,CAAC;YAEH,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC;YAC/C,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;CACF;AAED,SAAS,kBAAkB,CAAC,GAAoB;IAC9C,MAAM,OAAO,2LAAG,oBAAA,AAAiB,EAAE,CAAC;IACpC,KAAK,MAAM,MAAM,IAAI,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,CAAE,CAAC;QAC9C,MAAM,KAAK,GAAG,GAAG,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;QAClC,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC;YACzB,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACrB,OAAO,CAAC,GAAG,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;YAChC,CAAC;QACH,CAAC,MAAM,IAAI,KAAK,EAAE,CAAC;YACjB,OAAO,CAAC,GAAG,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;QAC7B,CAAC;IACH,CAAC;IACD,OAAO,OAAO,CAAC;AACjB,CAAC;AAED,SAAS,wBAAwB,CAC/B,MAAuB,EACvB,OAAoB;IAEpB,MAAM,eAAe,GAAG,OAAO,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAC;IACxD,IAAI,eAAe,KAAK,MAAM,EAAE,CAAC;QAC/B,MAAM,KAAK,qHAAG,IAAI,CAAC,UAAY,EAAE,CAAC;QAClC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACnB,OAAO,KAAK,CAAC;IACf,CAAC,MAAM,IAAI,eAAe,KAAK,SAAS,EAAE,CAAC;QACzC,MAAM,OAAO,qHAAG,IAAI,CAAC,WAAa,EAAE,CAAC;QACrC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACrB,OAAO,OAAO,CAAC;IACjB,CAAC;IAED,OAAO,MAAM,CAAC;AAChB,CAAC;AAED,SAAS,YAAY,CAAC,MAA6B;IACjD,OAAO,IAAI,OAAO,CAAS,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;QAC7C,MAAM,MAAM,GAAa,EAAE,CAAC;QAE5B,MAAM,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,KAAK,EAAE,EAAE;YAC1B,IAAI,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC;gBAC3B,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACrB,CAAC,MAAM,CAAC;gBACN,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;YAClC,CAAC;QACH,CAAC,CAAC,CAAC;QACH,MAAM,CAAC,EAAE,CAAC,KAAK,EAAE,GAAG,EAAE;YACpB,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC;QAClD,CAAC,CAAC,CAAC;QACH,MAAM,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE;YACvB,IAAI,CAAC,IAAI,CAAA,CAAC,KAAA,QAAD,CAAC,KAAA,KAAA,IAAA,KAAA,IAAD,CAAC,CAAE,IAAI,MAAK,YAAY,EAAE,CAAC;gBAClC,MAAM,CAAC,CAAC,CAAC,CAAC;YACZ,CAAC,MAAM,CAAC;gBACN,MAAM,CACJ,IAAI,8LAAS,CAAC,CAAA,gCAAA,EAAmC,CAAC,CAAC,OAAO,EAAE,EAAE;oBAC5D,IAAI,oLAAE,YAAS,CAAC,WAAW;iBAC5B,CAAC,CACH,CAAC;YACJ,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC;AAGK,SAAU,aAAa,CAAC,IAAqB;IACjD,IAAI,CAAC,IAAI,EAAE,CAAC;QACV,OAAO,CAAC,CAAC;IACX,CAAC,MAAM,IAAI,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;QACjC,OAAO,IAAI,CAAC,MAAM,CAAC;IACrB,CAAC,MAAM,IAAI,gBAAgB,CAAC,IAAI,CAAC,EAAE,CAAC;QAClC,OAAO,IAAI,CAAC;IACd,CAAC,MAAM,IAAI,aAAa,CAAC,IAAI,CAAC,EAAE,CAAC;QAC/B,OAAO,IAAI,CAAC,UAAU,CAAC;IACzB,CAAC,MAAM,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE,CAAC;QACpC,OAAO,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC;IAClC,CAAC,MAAM,CAAC;QACN,OAAO,IAAI,CAAC;IACd,CAAC;AACH,CAAC;AAMK,SAAU,oBAAoB;IAClC,OAAO,IAAI,cAAc,EAAE,CAAC;AAC9B,CAAC", "debugId": null}}, {"offset": {"line": 1479, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@typespec/ts-http-runtime/dist/esm/defaultHttpClient.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/node_modules/%40typespec/ts-http-runtime/src/defaultHttpClient.ts"], "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type { HttpClient } from \"./interfaces.js\";\nimport { createNodeHttpClient } from \"./nodeHttpClient.js\";\n\n/**\n * Create the correct HttpClient for the current environment.\n */\nexport function createDefaultHttpClient(): HttpClient {\n  return createNodeHttpClient();\n}\n"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;;;;AAGlC,OAAO,EAAE,oBAAoB,EAAE,MAAM,qBAAqB,CAAC;;AAKrD,SAAU,uBAAuB;IACrC,kMAAO,uBAAA,AAAoB,EAAE,CAAC;AAChC,CAAC", "debugId": null}}, {"offset": {"line": 1493, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@typespec/ts-http-runtime/dist/esm/policies/logPolicy.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/node_modules/%40typespec/ts-http-runtime/src/policies/logPolicy.ts"], "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type { Debugger } from \"../logger/logger.js\";\nimport type { PipelineRequest, PipelineResponse, SendRequest } from \"../interfaces.js\";\nimport type { PipelinePolicy } from \"../pipeline.js\";\nimport { logger as coreLogger } from \"../log.js\";\nimport { Sanitizer } from \"../util/sanitizer.js\";\n\n/**\n * The programmatic identifier of the logPolicy.\n */\nexport const logPolicyName = \"logPolicy\";\n\n/**\n * Options to configure the logPolicy.\n */\nexport interface LogPolicyOptions {\n  /**\n   * Header names whose values will be logged when logging is enabled.\n   * Defaults include a list of well-known safe headers. Any headers\n   * specified in this field will be added to that list.  Any other values will\n   * be written to logs as \"REDACTED\".\n   */\n  additionalAllowedHeaderNames?: string[];\n\n  /**\n   * Query string names whose values will be logged when logging is enabled. By default no\n   * query string values are logged.\n   */\n  additionalAllowedQueryParameters?: string[];\n\n  /**\n   * The log function to use for writing pipeline logs.\n   * Defaults to core-http's built-in logger.\n   * Compatible with the `debug` library.\n   */\n  logger?: Debugger;\n}\n\n/**\n * A policy that logs all requests and responses.\n * @param options - Options to configure logPolicy.\n */\nexport function logPolicy(options: LogPolicyOptions = {}): PipelinePolicy {\n  const logger = options.logger ?? coreLogger.info;\n  const sanitizer = new Sanitizer({\n    additionalAllowedHeaderNames: options.additionalAllowedHeaderNames,\n    additionalAllowedQueryParameters: options.additionalAllowedQueryParameters,\n  });\n  return {\n    name: logPolicyName,\n    async sendRequest(request: PipelineRequest, next: SendRequest): Promise<PipelineResponse> {\n      if (!logger.enabled) {\n        return next(request);\n      }\n\n      logger(`Request: ${sanitizer.sanitize(request)}`);\n\n      const response = await next(request);\n\n      logger(`Response status code: ${response.status}`);\n      logger(`Headers: ${sanitizer.sanitize(response.headers)}`);\n\n      return response;\n    },\n  };\n}\n"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;;;;;AAKlC,OAAO,EAAE,MAAM,IAAI,UAAU,EAAE,MAAM,WAAW,CAAC;AACjD,OAAO,EAAE,SAAS,EAAE,MAAM,sBAAsB,CAAC;;;AAK1C,MAAM,aAAa,GAAG,WAAW,CAAC;AAgCnC,SAAU,SAAS,CAAC,UAA4B,CAAA,CAAE;;IACtD,MAAM,MAAM,GAAG,CAAA,KAAA,OAAO,CAAC,MAAM,MAAA,QAAA,OAAA,KAAA,IAAA,iLAAI,SAAU,CAAC,IAAI,CAAC;IACjD,MAAM,SAAS,GAAG,8LAAI,YAAS,CAAC;QAC9B,4BAA4B,EAAE,OAAO,CAAC,4BAA4B;QAClE,gCAAgC,EAAE,OAAO,CAAC,gCAAgC;KAC3E,CAAC,CAAC;IACH,OAAO;QACL,IAAI,EAAE,aAAa;QACnB,KAAK,CAAC,WAAW,EAAC,OAAwB,EAAE,IAAiB;YAC3D,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;gBACpB,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC;YACvB,CAAC;YAED,MAAM,CAAC,CAAA,SAAA,EAAY,SAAS,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;YAElD,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,CAAC;YAErC,MAAM,CAAC,CAAA,sBAAA,EAAyB,QAAQ,CAAC,MAAM,EAAE,CAAC,CAAC;YACnD,MAAM,CAAC,CAAA,SAAA,EAAY,SAAS,CAAC,QAAQ,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;YAE3D,OAAO,QAAQ,CAAC;QAClB,CAAC;KACF,CAAC;AACJ,CAAC", "debugId": null}}, {"offset": {"line": 1529, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@typespec/ts-http-runtime/dist/esm/policies/redirectPolicy.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/node_modules/%40typespec/ts-http-runtime/src/policies/redirectPolicy.ts"], "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type { PipelineRequest, PipelineResponse, SendRequest } from \"../interfaces.js\";\nimport type { PipelinePolicy } from \"../pipeline.js\";\n\n/**\n * The programmatic identifier of the redirectPolicy.\n */\nexport const redirectPolicyName = \"redirectPolicy\";\n\n/**\n * Methods that are allowed to follow redirects 301 and 302\n */\nconst allowedRedirect = [\"GET\", \"HEAD\"];\n\n/**\n * Options for how redirect responses are handled.\n */\nexport interface RedirectPolicyOptions {\n  /**\n   * The maximum number of times the redirect URL will be tried before\n   * failing.  Defaults to 20.\n   */\n  maxRetries?: number;\n}\n\n/**\n * A policy to follow Location headers from the server in order\n * to support server-side redirection.\n * In the browser, this policy is not used.\n * @param options - Options to control policy behavior.\n */\nexport function redirectPolicy(options: RedirectPolicyOptions = {}): PipelinePolicy {\n  const { maxRetries = 20 } = options;\n  return {\n    name: redirectPolicyName,\n    async sendRequest(request: PipelineRequest, next: SendRequest): Promise<PipelineResponse> {\n      const response = await next(request);\n      return handleRedirect(next, response, maxRetries);\n    },\n  };\n}\n\nasync function handleRedirect(\n  next: SendRequest,\n  response: PipelineResponse,\n  maxRetries: number,\n  currentRetries: number = 0,\n): Promise<PipelineResponse> {\n  const { request, status, headers } = response;\n  const locationHeader = headers.get(\"location\");\n  if (\n    locationHeader &&\n    (status === 300 ||\n      (status === 301 && allowedRedirect.includes(request.method)) ||\n      (status === 302 && allowedRedirect.includes(request.method)) ||\n      (status === 303 && request.method === \"POST\") ||\n      status === 307) &&\n    currentRetries < maxRetries\n  ) {\n    const url = new URL(locationHeader, request.url);\n    request.url = url.toString();\n\n    // POST request with Status code 303 should be converted into a\n    // redirected GET request if the redirect url is present in the location header\n    if (status === 303) {\n      request.method = \"GET\";\n      request.headers.delete(\"Content-Length\");\n      delete request.body;\n    }\n\n    request.headers.delete(\"Authorization\");\n\n    const res = await next(request);\n    return handleRedirect(next, res, maxRetries, currentRetries + 1);\n  }\n\n  return response;\n}\n"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAKlC;;GAEG;;;;AACI,MAAM,kBAAkB,GAAG,gBAAgB,CAAC;AAEnD;;GAEG,CACH,MAAM,eAAe,GAAG;IAAC,KAAK;IAAE,MAAM;CAAC,CAAC;AAmBlC,SAAU,cAAc,CAAC,UAAiC,CAAA,CAAE;IAChE,MAAM,EAAE,UAAU,GAAG,EAAE,EAAE,GAAG,OAAO,CAAC;IACpC,OAAO;QACL,IAAI,EAAE,kBAAkB;QACxB,KAAK,CAAC,WAAW,EAAC,OAAwB,EAAE,IAAiB;YAC3D,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,CAAC;YACrC,OAAO,cAAc,CAAC,IAAI,EAAE,QAAQ,EAAE,UAAU,CAAC,CAAC;QACpD,CAAC;KACF,CAAC;AACJ,CAAC;AAED,KAAK,UAAU,cAAc,CAC3B,IAAiB,EACjB,QAA0B,EAC1B,UAAkB,EAClB,iBAAyB,CAAC;IAE1B,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,GAAG,QAAQ,CAAC;IAC9C,MAAM,cAAc,GAAG,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;IAC/C,IACE,cAAc,IACd,CAAC,MAAM,KAAK,GAAG,IACZ,MAAM,KAAK,GAAG,IAAI,eAAe,CAAC,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,GAC3D,MAAM,KAAK,GAAG,IAAI,eAAe,CAAC,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,GAC3D,MAAM,KAAK,GAAG,IAAI,OAAO,CAAC,MAAM,KAAK,MAAM,CAAC,GAC7C,MAAM,KAAK,GAAG,CAAC,IACjB,cAAc,GAAG,UAAU,EAC3B,CAAC;QACD,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,cAAc,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;QACjD,OAAO,CAAC,GAAG,GAAG,GAAG,CAAC,QAAQ,EAAE,CAAC;QAE7B,+DAA+D;QAC/D,+EAA+E;QAC/E,IAAI,MAAM,KAAK,GAAG,EAAE,CAAC;YACnB,OAAO,CAAC,MAAM,GAAG,KAAK,CAAC;YACvB,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC;YACzC,OAAO,OAAO,CAAC,IAAI,CAAC;QACtB,CAAC;QAED,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC;QAExC,MAAM,GAAG,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,CAAC;QAChC,OAAO,cAAc,CAAC,IAAI,EAAE,GAAG,EAAE,UAAU,EAAE,cAAc,GAAG,CAAC,CAAC,CAAC;IACnE,CAAC;IAED,OAAO,QAAQ,CAAC;AAClB,CAAC", "debugId": null}}, {"offset": {"line": 1577, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@typespec/ts-http-runtime/dist/esm/util/userAgentPlatform.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/node_modules/%40typespec/ts-http-runtime/src/util/userAgentPlatform.ts"], "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport * as os from \"node:os\";\nimport * as process from \"node:process\";\n\n/**\n * @internal\n */\ninterface ExtendedPlatformVersions extends NodeJS.ProcessVersions {\n  bun?: string;\n  deno?: string;\n}\n\n/**\n * @internal\n */\nexport function getHeaderName(): string {\n  return \"User-Agent\";\n}\n\n/**\n * @internal\n */\nexport async function setPlatformSpecificData(map: Map<string, string>): Promise<void> {\n  if (process && process.versions) {\n    const versions = process.versions as ExtendedPlatformVersions;\n    if (versions.bun) {\n      map.set(\"Bun\", versions.bun);\n    } else if (versions.deno) {\n      map.set(\"Deno\", versions.deno);\n    } else if (versions.node) {\n      map.set(\"Node\", versions.node);\n    }\n  }\n\n  map.set(\"OS\", `(${os.arch()}-${os.type()}-${os.release()})`);\n}\n"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;;;;;AAElC,OAAO,KAAK,EAAE,MAAM,SAAS,CAAC;AAC9B,OAAO,KAAK,OAAO,MAAM,cAAc,CAAC;;;AAalC,SAAU,aAAa;IAC3B,OAAO,YAAY,CAAC;AACtB,CAAC;AAKM,KAAK,UAAU,uBAAuB,CAAC,GAAwB;IACpE,IAAI,OAAO,oHAAI,OAAO,iHAAC,QAAQ,EAAE,CAAC;QAChC,MAAM,QAAQ,GAAG,OAAO,iHAAC,QAAoC,CAAC;QAC9D,IAAI,QAAQ,CAAC,GAAG,EAAE,CAAC;YACjB,GAAG,CAAC,GAAG,CAAC,KAAK,EAAE,QAAQ,CAAC,GAAG,CAAC,CAAC;QAC/B,CAAC,MAAM,IAAI,QAAQ,CAAC,IAAI,EAAE,CAAC;YACzB,GAAG,CAAC,GAAG,CAAC,MAAM,EAAE,QAAQ,CAAC,IAAI,CAAC,CAAC;QACjC,CAAC,MAAM,IAAI,QAAQ,CAAC,IAAI,EAAE,CAAC;YACzB,GAAG,CAAC,GAAG,CAAC,MAAM,EAAE,QAAQ,CAAC,IAAI,CAAC,CAAC;QACjC,CAAC;IACH,CAAC;IAED,GAAG,CAAC,GAAG,CAAC,IAAI,EAAE,CAAA,CAAA,gHAAI,EAAE,CAAC,IAAI,EAAE,CAAA,CAAA,gHAAI,EAAE,CAAC,IAAI,EAAE,CAAA,CAAA,gHAAI,EAAE,CAAC,OAAO,EAAE,CAAA,CAAA,CAAG,CAAC,CAAC;AAC/D,CAAC", "debugId": null}}, {"offset": {"line": 1607, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@typespec/ts-http-runtime/dist/esm/constants.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/node_modules/%40typespec/ts-http-runtime/src/constants.ts"], "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nexport const SDK_VERSION: string = \"0.3.0\";\n\nexport const DEFAULT_RETRY_POLICY_COUNT = 3;\n"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;;;;;AAE3B,MAAM,WAAW,GAAW,OAAO,CAAC;AAEpC,MAAM,0BAA0B,GAAG,CAAC,CAAC", "debugId": null}}, {"offset": {"line": 1619, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@typespec/ts-http-runtime/dist/esm/util/userAgent.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/node_modules/%40typespec/ts-http-runtime/src/util/userAgent.ts"], "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport { getHeaderName, setPlatformSpecificData } from \"./userAgentPlatform.js\";\nimport { SDK_VERSION } from \"../constants.js\";\n\nfunction getUserAgentString(telemetryInfo: Map<string, string>): string {\n  const parts: string[] = [];\n  for (const [key, value] of telemetryInfo) {\n    const token = value ? `${key}/${value}` : key;\n    parts.push(token);\n  }\n  return parts.join(\" \");\n}\n\n/**\n * @internal\n */\nexport function getUserAgentHeaderName(): string {\n  return getHeaderName();\n}\n\n/**\n * @internal\n */\nexport async function getUserAgentValue(prefix?: string): Promise<string> {\n  const runtimeInfo = new Map<string, string>();\n  runtimeInfo.set(\"ts-http-runtime\", SDK_VERSION);\n  await setPlatformSpecificData(runtimeInfo);\n  const defaultAgent = getUserAgentString(runtimeInfo);\n  const userAgentValue = prefix ? `${prefix} ${defaultAgent}` : defaultAgent;\n  return userAgentValue;\n}\n"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;;;;;AAElC,OAAO,EAAE,aAAa,EAAE,uBAAuB,EAAE,MAAM,wBAAwB,CAAC;AAChF,OAAO,EAAE,WAAW,EAAE,MAAM,iBAAiB,CAAC;;;AAE9C,SAAS,kBAAkB,CAAC,aAAkC;IAC5D,MAAM,KAAK,GAAa,EAAE,CAAC;IAC3B,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,aAAa,CAAE,CAAC;QACzC,MAAM,KAAK,GAAG,KAAK,CAAC,CAAC,CAAC,GAAG,GAAG,CAAA,CAAA,EAAI,KAAK,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC;QAC9C,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IACpB,CAAC;IACD,OAAO,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AACzB,CAAC;AAKK,SAAU,sBAAsB;IACpC,6MAAO,gBAAA,AAAa,EAAE,CAAC;AACzB,CAAC;AAKM,KAAK,UAAU,iBAAiB,CAAC,MAAe;IACrD,MAAM,WAAW,GAAG,IAAI,GAAG,EAAkB,CAAC;IAC9C,WAAW,CAAC,GAAG,CAAC,iBAAiB,oLAAE,cAAW,CAAC,CAAC;IAChD,4MAAM,0BAAA,AAAuB,EAAC,WAAW,CAAC,CAAC;IAC3C,MAAM,YAAY,GAAG,kBAAkB,CAAC,WAAW,CAAC,CAAC;IACrD,MAAM,cAAc,GAAG,MAAM,CAAC,CAAC,CAAC,GAAG,MAAM,CAAA,CAAA,EAAI,YAAY,EAAE,CAAC,CAAC,CAAC,YAAY,CAAC;IAC3E,OAAO,cAAc,CAAC;AACxB,CAAC", "debugId": null}}, {"offset": {"line": 1652, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@typespec/ts-http-runtime/dist/esm/policies/userAgentPolicy.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/node_modules/%40typespec/ts-http-runtime/src/policies/userAgentPolicy.ts"], "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type { PipelineRequest, PipelineResponse, SendRequest } from \"../interfaces.js\";\nimport type { PipelinePolicy } from \"../pipeline.js\";\nimport { getUserAgentHeaderName, getUserAgentValue } from \"../util/userAgent.js\";\n\nconst UserAgentHeaderName = getUserAgentHeaderName();\n\n/**\n * The programmatic identifier of the userAgentPolicy.\n */\nexport const userAgentPolicyName = \"userAgentPolicy\";\n\n/**\n * Options for adding user agent details to outgoing requests.\n */\nexport interface UserAgentPolicyOptions {\n  /**\n   * String prefix to add to the user agent for outgoing requests.\n   * Defaults to an empty string.\n   */\n  userAgentPrefix?: string;\n}\n\n/**\n * A policy that sets the User-Agent header (or equivalent) to reflect\n * the library version.\n * @param options - Options to customize the user agent value.\n */\nexport function userAgentPolicy(options: UserAgentPolicyOptions = {}): PipelinePolicy {\n  const userAgentValue = getUserAgentValue(options.userAgentPrefix);\n  return {\n    name: userAgentPolicyName,\n    async sendRequest(request: PipelineRequest, next: SendRequest): Promise<PipelineResponse> {\n      if (!request.headers.has(UserAgentHeaderName)) {\n        request.headers.set(UserAgentHeaderName, await userAgentValue);\n      }\n      return next(request);\n    },\n  };\n}\n"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;;;;;AAIlC,OAAO,EAAE,sBAAsB,EAAE,iBAAiB,EAAE,MAAM,sBAAsB,CAAC;;AAEjF,MAAM,mBAAmB,iMAAG,yBAAA,AAAsB,EAAE,CAAC;AAK9C,MAAM,mBAAmB,GAAG,iBAAiB,CAAC;AAkB/C,SAAU,eAAe,CAAC,UAAkC,CAAA,CAAE;IAClE,MAAM,cAAc,iMAAG,oBAAA,AAAiB,EAAC,OAAO,CAAC,eAAe,CAAC,CAAC;IAClE,OAAO;QACL,IAAI,EAAE,mBAAmB;QACzB,KAAK,CAAC,WAAW,EAAC,OAAwB,EAAE,IAAiB;YAC3D,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,mBAAmB,CAAC,EAAE,CAAC;gBAC9C,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,mBAAmB,EAAE,MAAM,cAAc,CAAC,CAAC;YACjE,CAAC;YACD,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC;QACvB,CAAC;KACF,CAAC;AACJ,CAAC", "debugId": null}}, {"offset": {"line": 1678, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@typespec/ts-http-runtime/dist/esm/policies/decompressResponsePolicy.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/node_modules/%40typespec/ts-http-runtime/src/policies/decompressResponsePolicy.ts"], "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type { PipelineRequest, PipelineResponse, SendRequest } from \"../interfaces.js\";\nimport type { PipelinePolicy } from \"../pipeline.js\";\n\n/**\n * The programmatic identifier of the decompressResponsePolicy.\n */\nexport const decompressResponsePolicyName = \"decompressResponsePolicy\";\n\n/**\n * A policy to enable response decompression according to Accept-Encoding header\n * https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Accept-Encoding\n */\nexport function decompressResponsePolicy(): PipelinePolicy {\n  return {\n    name: decompressResponsePolicyName,\n    async sendRequest(request: PipelineRequest, next: SendRequest): Promise<PipelineResponse> {\n      // HEAD requests have no body\n      if (request.method !== \"HEAD\") {\n        request.headers.set(\"Accept-Encoding\", \"gzip,deflate\");\n      }\n      return next(request);\n    },\n  };\n}\n"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAKlC;;GAEG;;;;AACI,MAAM,4BAA4B,GAAG,0BAA0B,CAAC;AAMjE,SAAU,wBAAwB;IACtC,OAAO;QACL,IAAI,EAAE,4BAA4B;QAClC,KAAK,CAAC,WAAW,EAAC,OAAwB,EAAE,IAAiB;YAC3D,6BAA6B;YAC7B,IAAI,OAAO,CAAC,MAAM,KAAK,MAAM,EAAE,CAAC;gBAC9B,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,iBAAiB,EAAE,cAAc,CAAC,CAAC;YACzD,CAAC;YACD,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC;QACvB,CAAC;KACF,CAAC;AACJ,CAAC", "debugId": null}}, {"offset": {"line": 1703, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@typespec/ts-http-runtime/dist/esm/util/helpers.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/node_modules/%40typespec/ts-http-runtime/src/util/helpers.ts"], "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport { AbortError } from \"../abort-controller/AbortError.js\";\nimport type { PipelineResponse } from \"../interfaces.js\";\n\nconst StandardAbortMessage = \"The operation was aborted.\";\n\n/**\n * A wrapper for setTimeout that resolves a promise after delayInMs milliseconds.\n * @param delayInMs - The number of milliseconds to be delayed.\n * @param value - The value to be resolved with after a timeout of t milliseconds.\n * @param options - The options for delay - currently abort options\n *                  - abortSignal - The abortSignal associated with containing operation.\n *                  - abortErrorMsg - The abort error message associated with containing operation.\n * @returns Resolved promise\n */\nexport function delay<T>(\n  delayInMs: number,\n  value?: T,\n  options?: {\n    abortSignal?: AbortSignal;\n    abortErrorMsg?: string;\n  },\n): Promise<T | void> {\n  return new Promise((resolve, reject) => {\n    let timer: ReturnType<typeof setTimeout> | undefined = undefined;\n    let onAborted: (() => void) | undefined = undefined;\n\n    const rejectOnAbort = (): void => {\n      return reject(\n        new AbortError(options?.abortErrorMsg ? options?.abortErrorMsg : StandardAbortMessage),\n      );\n    };\n\n    const removeListeners = (): void => {\n      if (options?.abortSignal && onAborted) {\n        options.abortSignal.removeEventListener(\"abort\", onAborted);\n      }\n    };\n\n    onAborted = (): void => {\n      if (timer) {\n        clearTimeout(timer);\n      }\n      removeListeners();\n      return rejectOnAbort();\n    };\n\n    if (options?.abortSignal && options.abortSignal.aborted) {\n      return rejectOnAbort();\n    }\n\n    timer = setTimeout(() => {\n      removeListeners();\n      resolve(value);\n    }, delayInMs);\n\n    if (options?.abortSignal) {\n      options.abortSignal.addEventListener(\"abort\", onAborted);\n    }\n  });\n}\n\n/**\n * @internal\n * @returns the parsed value or undefined if the parsed value is invalid.\n */\nexport function parseHeaderValueAsNumber(\n  response: PipelineResponse,\n  headerName: string,\n): number | undefined {\n  const value = response.headers.get(headerName);\n  if (!value) return;\n  const valueAsNum = Number(value);\n  if (Number.isNaN(valueAsNum)) return;\n  return valueAsNum;\n}\n"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;;;;;AAElC,OAAO,EAAE,UAAU,EAAE,MAAM,mCAAmC,CAAC;;AAG/D,MAAM,oBAAoB,GAAG,4BAA4B,CAAC;AAWpD,SAAU,KAAK,CACnB,SAAiB,EACjB,KAAS,EACT,OAGC;IAED,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;QACrC,IAAI,KAAK,GAA8C,SAAS,CAAC;QACjE,IAAI,SAAS,GAA6B,SAAS,CAAC;QAEpD,MAAM,aAAa,GAAG,GAAS,EAAE;YAC/B,OAAO,MAAM,CACX,8MAAI,aAAU,CAAC,CAAA,OAAO,KAAA,QAAP,OAAO,KAAA,KAAA,IAAA,KAAA,IAAP,OAAO,CAAE,aAAa,EAAC,CAAC,CAAC,OAAO,KAAA,QAAP,OAAO,KAAA,KAAA,IAAA,KAAA,IAAP,OAAO,CAAE,aAAa,CAAC,CAAC,CAAC,oBAAoB,CAAC,CACvF,CAAC;QACJ,CAAC,CAAC;QAEF,MAAM,eAAe,GAAG,GAAS,EAAE;YACjC,IAAI,CAAA,OAAO,KAAA,QAAP,OAAO,KAAA,KAAA,IAAA,KAAA,IAAP,OAAO,CAAE,WAAW,KAAI,SAAS,EAAE,CAAC;gBACtC,OAAO,CAAC,WAAW,CAAC,mBAAmB,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC;YAC9D,CAAC;QACH,CAAC,CAAC;QAEF,SAAS,GAAG,GAAS,EAAE;YACrB,IAAI,KAAK,EAAE,CAAC;gBACV,YAAY,CAAC,KAAK,CAAC,CAAC;YACtB,CAAC;YACD,eAAe,EAAE,CAAC;YAClB,OAAO,aAAa,EAAE,CAAC;QACzB,CAAC,CAAC;QAEF,IAAI,CAAA,OAAO,KAAA,QAAP,OAAO,KAAA,KAAA,IAAA,KAAA,IAAP,OAAO,CAAE,WAAW,KAAI,OAAO,CAAC,WAAW,CAAC,OAAO,EAAE,CAAC;YACxD,OAAO,aAAa,EAAE,CAAC;QACzB,CAAC;QAED,KAAK,GAAG,UAAU,CAAC,GAAG,EAAE;YACtB,eAAe,EAAE,CAAC;YAClB,OAAO,CAAC,KAAK,CAAC,CAAC;QACjB,CAAC,EAAE,SAAS,CAAC,CAAC;QAEd,IAAI,OAAO,KAAA,QAAP,OAAO,KAAA,KAAA,IAAA,KAAA,IAAP,OAAO,CAAE,WAAW,EAAE,CAAC;YACzB,OAAO,CAAC,WAAW,CAAC,gBAAgB,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC;QAC3D,CAAC;IACH,CAAC,CAAC,CAAC;AACL,CAAC;AAMK,SAAU,wBAAwB,CACtC,QAA0B,EAC1B,UAAkB;IAElB,MAAM,KAAK,GAAG,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;IAC/C,IAAI,CAAC,KAAK,EAAE,OAAO;IACnB,MAAM,UAAU,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC;IACjC,IAAI,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC,EAAE,OAAO;IACrC,OAAO,UAAU,CAAC;AACpB,CAAC", "debugId": null}}, {"offset": {"line": 1754, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@typespec/ts-http-runtime/dist/esm/retryStrategies/throttlingRetryStrategy.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/node_modules/%40typespec/ts-http-runtime/src/retryStrategies/throttlingRetryStrategy.ts"], "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type { PipelineResponse } from \"../interfaces.js\";\nimport { parseHeaderValueAsNumber } from \"../util/helpers.js\";\nimport type { RetryStrategy } from \"./retryStrategy.js\";\n\n/**\n * The header that comes back from services representing\n * the amount of time (minimum) to wait to retry (in seconds or timestamp after which we can retry).\n */\nconst RetryAfterHeader = \"Retry-After\";\n/**\n * The headers that come back from services representing\n * the amount of time (minimum) to wait to retry.\n *\n * \"retry-after-ms\", \"x-ms-retry-after-ms\" : milliseconds\n * \"Retry-After\" : seconds or timestamp\n */\nconst AllRetryAfterHeaders: string[] = [\"retry-after-ms\", \"x-ms-retry-after-ms\", RetryAfterHeader];\n\n/**\n * A response is a throttling retry response if it has a throttling status code (429 or 503),\n * as long as one of the [ \"Retry-After\" or \"retry-after-ms\" or \"x-ms-retry-after-ms\" ] headers has a valid value.\n *\n * Returns the `retryAfterInMs` value if the response is a throttling retry response.\n * If not throttling retry response, returns `undefined`.\n *\n * @internal\n */\nfunction getRetryAfterInMs(response?: PipelineResponse): number | undefined {\n  if (!(response && [429, 503].includes(response.status))) return undefined;\n  try {\n    // Headers: \"retry-after-ms\", \"x-ms-retry-after-ms\", \"Retry-After\"\n    for (const header of AllRetryAfterHeaders) {\n      const retryAfterValue = parseHeaderValueAsNumber(response, header);\n      if (retryAfterValue === 0 || retryAfterValue) {\n        // \"Retry-After\" header ==> seconds\n        // \"retry-after-ms\", \"x-ms-retry-after-ms\" headers ==> milli-seconds\n        const multiplyingFactor = header === RetryAfterHeader ? 1000 : 1;\n        return retryAfterValue * multiplyingFactor; // in milli-seconds\n      }\n    }\n\n    // RetryAfterHeader (\"Retry-After\") has a special case where it might be formatted as a date instead of a number of seconds\n    const retryAfterHeader = response.headers.get(RetryAfterHeader);\n    if (!retryAfterHeader) return;\n\n    const date = Date.parse(retryAfterHeader);\n    const diff = date - Date.now();\n    // negative diff would mean a date in the past, so retry asap with 0 milliseconds\n    return Number.isFinite(diff) ? Math.max(0, diff) : undefined;\n  } catch {\n    return undefined;\n  }\n}\n\n/**\n * A response is a retry response if it has a throttling status code (429 or 503),\n * as long as one of the [ \"Retry-After\" or \"retry-after-ms\" or \"x-ms-retry-after-ms\" ] headers has a valid value.\n */\nexport function isThrottlingRetryResponse(response?: PipelineResponse): boolean {\n  return Number.isFinite(getRetryAfterInMs(response));\n}\n\nexport function throttlingRetryStrategy(): RetryStrategy {\n  return {\n    name: \"throttlingRetryStrategy\",\n    retry({ response }) {\n      const retryAfterInMs = getRetryAfterInMs(response);\n      if (!Number.isFinite(retryAfterInMs)) {\n        return { skipStrategy: true };\n      }\n      return {\n        retryAfterInMs,\n      };\n    },\n  };\n}\n"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;;;;;AAGlC,OAAO,EAAE,wBAAwB,EAAE,MAAM,oBAAoB,CAAC;;AAG9D;;;GAGG,CACH,MAAM,gBAAgB,GAAG,aAAa,CAAC;AACvC;;;;;;GAMG,CACH,MAAM,oBAAoB,GAAa;IAAC,gBAAgB;IAAE,qBAAqB;IAAE,gBAAgB;CAAC,CAAC;AAEnG;;;;;;;;GAQG,CACH,SAAS,iBAAiB,CAAC,QAA2B;IACpD,IAAI,CAAC,CAAC,QAAQ,IAAI;QAAC,GAAG;QAAE,GAAG;KAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,EAAE,OAAO,SAAS,CAAC;IAC1E,IAAI,CAAC;QACH,kEAAkE;QAClE,KAAK,MAAM,MAAM,IAAI,oBAAoB,CAAE,CAAC;YAC1C,MAAM,eAAe,IAAG,sNAAA,AAAwB,EAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;YACnE,IAAI,eAAe,KAAK,CAAC,IAAI,eAAe,EAAE,CAAC;gBAC7C,mCAAmC;gBACnC,oEAAoE;gBACpE,MAAM,iBAAiB,GAAG,MAAM,KAAK,gBAAgB,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;gBACjE,OAAO,eAAe,GAAG,iBAAiB,CAAC,CAAC,mBAAmB;YACjE,CAAC;QACH,CAAC;QAED,2HAA2H;QAC3H,MAAM,gBAAgB,GAAG,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC;QAChE,IAAI,CAAC,gBAAgB,EAAE,OAAO;QAE9B,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAC;QAC1C,MAAM,IAAI,GAAG,IAAI,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAC/B,iFAAiF;QACjF,OAAO,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;IAC/D,CAAC,CAAC,OAAA,IAAM,CAAC;QACP,OAAO,SAAS,CAAC;IACnB,CAAC;AACH,CAAC;AAMK,SAAU,yBAAyB,CAAC,QAA2B;IACnE,OAAO,MAAM,CAAC,QAAQ,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC,CAAC;AACtD,CAAC;AAEK,SAAU,uBAAuB;IACrC,OAAO;QACL,IAAI,EAAE,yBAAyB;QAC/B,KAAK,EAAC,EAAE,QAAQ,EAAE;YAChB,MAAM,cAAc,GAAG,iBAAiB,CAAC,QAAQ,CAAC,CAAC;YACnD,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,EAAE,CAAC;gBACrC,OAAO;oBAAE,YAAY,EAAE,IAAI;gBAAA,CAAE,CAAC;YAChC,CAAC;YACD,OAAO;gBACL,cAAc;aACf,CAAC;QACJ,CAAC;KACF,CAAC;AACJ,CAAC", "debugId": null}}, {"offset": {"line": 1835, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@typespec/ts-http-runtime/dist/esm/retryStrategies/exponentialRetryStrategy.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/node_modules/%40typespec/ts-http-runtime/src/retryStrategies/exponentialRetryStrategy.ts"], "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type { PipelineResponse } from \"../interfaces.js\";\nimport type { RestError } from \"../restError.js\";\nimport { calculateRetryDelay } from \"../util/delay.js\";\nimport type { RetryStrategy } from \"./retryStrategy.js\";\nimport { isThrottlingRetryResponse } from \"./throttlingRetryStrategy.js\";\n\n// intervals are in milliseconds\nconst DEFAULT_CLIENT_RETRY_INTERVAL = 1000;\nconst DEFAULT_CLIENT_MAX_RETRY_INTERVAL = 1000 * 64;\n\n/**\n * A retry strategy that retries with an exponentially increasing delay in these two cases:\n * - When there are errors in the underlying transport layer (e.g. DNS lookup failures).\n * - Or otherwise if the outgoing request fails (408, greater or equal than 500, except for 501 and 505).\n */\nexport function exponentialRetryStrategy(\n  options: {\n    /**\n     * The amount of delay in milliseconds between retry attempts. Defaults to 1000\n     * (1 second.) The delay increases exponentially with each retry up to a maximum\n     * specified by maxRetryDelayInMs.\n     */\n    retryDelayInMs?: number;\n\n    /**\n     * The maximum delay in milliseconds allowed before retrying an operation. Defaults\n     * to 64000 (64 seconds).\n     */\n    maxRetryDelayInMs?: number;\n\n    /**\n     * If true it won't retry if it received a system error.\n     */\n    ignoreSystemErrors?: boolean;\n\n    /**\n     * If true it won't retry if it received a non-fatal HTTP status code.\n     */\n    ignoreHttpStatusCodes?: boolean;\n  } = {},\n): RetryStrategy {\n  const retryInterval = options.retryDelayInMs ?? DEFAULT_CLIENT_RETRY_INTERVAL;\n  const maxRetryInterval = options.maxRetryDelayInMs ?? DEFAULT_CLIENT_MAX_RETRY_INTERVAL;\n\n  return {\n    name: \"exponentialRetryStrategy\",\n    retry({ retryCount, response, responseError }) {\n      const matchedSystemError = isSystemError(responseError);\n      const ignoreSystemErrors = matchedSystemError && options.ignoreSystemErrors;\n\n      const isExponential = isExponentialRetryResponse(response);\n      const ignoreExponentialResponse = isExponential && options.ignoreHttpStatusCodes;\n      const unknownResponse = response && (isThrottlingRetryResponse(response) || !isExponential);\n\n      if (unknownResponse || ignoreExponentialResponse || ignoreSystemErrors) {\n        return { skipStrategy: true };\n      }\n\n      if (responseError && !matchedSystemError && !isExponential) {\n        return { errorToThrow: responseError };\n      }\n\n      return calculateRetryDelay(retryCount, {\n        retryDelayInMs: retryInterval,\n        maxRetryDelayInMs: maxRetryInterval,\n      });\n    },\n  };\n}\n\n/**\n * A response is a retry response if it has status codes:\n * - 408, or\n * - Greater or equal than 500, except for 501 and 505.\n */\nexport function isExponentialRetryResponse(response?: PipelineResponse): boolean {\n  return Boolean(\n    response &&\n      response.status !== undefined &&\n      (response.status >= 500 || response.status === 408) &&\n      response.status !== 501 &&\n      response.status !== 505,\n  );\n}\n\n/**\n * Determines whether an error from a pipeline response was triggered in the network layer.\n */\nexport function isSystemError(err?: RestError): boolean {\n  if (!err) {\n    return false;\n  }\n  return (\n    err.code === \"ETIMEDOUT\" ||\n    err.code === \"ESOCKETTIMEDOUT\" ||\n    err.code === \"ECONNREFUSED\" ||\n    err.code === \"ECONNRESET\" ||\n    err.code === \"ENOENT\" ||\n    err.code === \"ENOTFOUND\"\n  );\n}\n"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;;;;;;AAIlC,OAAO,EAAE,mBAAmB,EAAE,MAAM,kBAAkB,CAAC;AAEvD,OAAO,EAAE,yBAAyB,EAAE,MAAM,8BAA8B,CAAC;;;AAEzE,gCAAgC;AAChC,MAAM,6BAA6B,GAAG,IAAI,CAAC;AAC3C,MAAM,iCAAiC,GAAG,IAAI,GAAG,EAAE,CAAC;AAO9C,SAAU,wBAAwB,CACtC,UAuBI,CAAA,CAAE;;IAEN,MAAM,aAAa,GAAG,CAAA,KAAA,OAAO,CAAC,cAAc,MAAA,QAAA,OAAA,KAAA,IAAA,KAAI,6BAA6B,CAAC;IAC9E,MAAM,gBAAgB,GAAG,CAAA,KAAA,OAAO,CAAC,iBAAiB,MAAA,QAAA,OAAA,KAAA,IAAA,KAAI,iCAAiC,CAAC;IAExF,OAAO;QACL,IAAI,EAAE,0BAA0B;QAChC,KAAK,EAAC,EAAE,UAAU,EAAE,QAAQ,EAAE,aAAa,EAAE;YAC3C,MAAM,kBAAkB,GAAG,aAAa,CAAC,aAAa,CAAC,CAAC;YACxD,MAAM,kBAAkB,GAAG,kBAAkB,IAAI,OAAO,CAAC,kBAAkB,CAAC;YAE5E,MAAM,aAAa,GAAG,0BAA0B,CAAC,QAAQ,CAAC,CAAC;YAC3D,MAAM,yBAAyB,GAAG,aAAa,IAAI,OAAO,CAAC,qBAAqB,CAAC;YACjF,MAAM,eAAe,GAAG,QAAQ,IAAI,EAAC,kPAAA,AAAyB,EAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YAE5F,IAAI,eAAe,IAAI,yBAAyB,IAAI,kBAAkB,EAAE,CAAC;gBACvE,OAAO;oBAAE,YAAY,EAAE,IAAI;gBAAA,CAAE,CAAC;YAChC,CAAC;YAED,IAAI,aAAa,IAAI,CAAC,kBAAkB,IAAI,CAAC,aAAa,EAAE,CAAC;gBAC3D,OAAO;oBAAE,YAAY,EAAE,aAAa;gBAAA,CAAE,CAAC;YACzC,CAAC;YAED,iMAAO,sBAAA,AAAmB,EAAC,UAAU,EAAE;gBACrC,cAAc,EAAE,aAAa;gBAC7B,iBAAiB,EAAE,gBAAgB;aACpC,CAAC,CAAC;QACL,CAAC;KACF,CAAC;AACJ,CAAC;AAOK,SAAU,0BAA0B,CAAC,QAA2B;IACpE,OAAO,OAAO,CACZ,QAAQ,IACN,QAAQ,CAAC,MAAM,KAAK,SAAS,IAC7B,CAAC,QAAQ,CAAC,MAAM,IAAI,GAAG,IAAI,QAAQ,CAAC,MAAM,KAAK,GAAG,CAAC,IACnD,QAAQ,CAAC,MAAM,KAAK,GAAG,IACvB,QAAQ,CAAC,MAAM,KAAK,GAAG,CAC1B,CAAC;AACJ,CAAC;AAKK,SAAU,aAAa,CAAC,GAAe;IAC3C,IAAI,CAAC,GAAG,EAAE,CAAC;QACT,OAAO,KAAK,CAAC;IACf,CAAC;IACD,OAAO,AACL,GAAG,CAAC,IAAI,KAAK,WAAW,IACxB,GAAG,CAAC,IAAI,KAAK,iBAAiB,IAC9B,GAAG,CAAC,IAAI,KAAK,cAAc,IAC3B,GAAG,CAAC,IAAI,KAAK,YAAY,IACzB,GAAG,CAAC,IAAI,KAAK,QAAQ,IACrB,GAAG,CAAC,IAAI,KAAK,WAAW,CACzB,CAAC;AACJ,CAAC", "debugId": null}}, {"offset": {"line": 1891, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@typespec/ts-http-runtime/dist/esm/policies/retryPolicy.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/node_modules/%40typespec/ts-http-runtime/src/policies/retryPolicy.ts"], "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type { PipelineRequest, PipelineResponse, SendRequest } from \"../interfaces.js\";\nimport type { PipelinePolicy } from \"../pipeline.js\";\nimport { delay } from \"../util/helpers.js\";\nimport type { RetryStrategy } from \"../retryStrategies/retryStrategy.js\";\nimport type { RestError } from \"../restError.js\";\nimport { AbortError } from \"../abort-controller/AbortError.js\";\nimport type { TypeSpecRuntimeLogger } from \"../logger/logger.js\";\nimport { createClientLogger } from \"../logger/logger.js\";\nimport { DEFAULT_RETRY_POLICY_COUNT } from \"../constants.js\";\n\nconst retryPolicyLogger = createClientLogger(\"ts-http-runtime retryPolicy\");\n\n/**\n * The programmatic identifier of the retryPolicy.\n */\nconst retryPolicyName = \"retryPolicy\";\n\n/**\n * Options to the {@link retryPolicy}\n */\nexport interface RetryPolicyOptions {\n  /**\n   * Maximum number of retries. If not specified, it will limit to 3 retries.\n   */\n  maxRetries?: number;\n  /**\n   * Logger. If it's not provided, a default logger is used.\n   */\n  logger?: TypeSpecRuntimeLogger;\n}\n\n/**\n * retryPolicy is a generic policy to enable retrying requests when certain conditions are met\n */\nexport function retryPolicy(\n  strategies: RetryStrategy[],\n  options: RetryPolicyOptions = { maxRetries: DEFAULT_RETRY_POLICY_COUNT },\n): PipelinePolicy {\n  const logger = options.logger || retryPolicyLogger;\n  return {\n    name: retryPolicyName,\n    async sendRequest(request: PipelineRequest, next: SendRequest): Promise<PipelineResponse> {\n      let response: PipelineResponse | undefined;\n      let responseError: RestError | undefined;\n      let retryCount = -1;\n\n      retryRequest: while (true) {\n        retryCount += 1;\n        response = undefined;\n        responseError = undefined;\n\n        try {\n          logger.info(`Retry ${retryCount}: Attempting to send request`, request.requestId);\n          response = await next(request);\n          logger.info(`Retry ${retryCount}: Received a response from request`, request.requestId);\n        } catch (e: any) {\n          logger.error(`Retry ${retryCount}: Received an error from request`, request.requestId);\n\n          // RestErrors are valid targets for the retry strategies.\n          // If none of the retry strategies can work with them, they will be thrown later in this policy.\n          // If the received error is not a RestError, it is immediately thrown.\n          responseError = e as RestError;\n          if (!e || responseError.name !== \"RestError\") {\n            throw e;\n          }\n\n          response = responseError.response;\n        }\n\n        if (request.abortSignal?.aborted) {\n          logger.error(`Retry ${retryCount}: Request aborted.`);\n          const abortError = new AbortError();\n          throw abortError;\n        }\n\n        if (retryCount >= (options.maxRetries ?? DEFAULT_RETRY_POLICY_COUNT)) {\n          logger.info(\n            `Retry ${retryCount}: Maximum retries reached. Returning the last received response, or throwing the last received error.`,\n          );\n          if (responseError) {\n            throw responseError;\n          } else if (response) {\n            return response;\n          } else {\n            throw new Error(\"Maximum retries reached with no response or error to throw\");\n          }\n        }\n\n        logger.info(`Retry ${retryCount}: Processing ${strategies.length} retry strategies.`);\n\n        strategiesLoop: for (const strategy of strategies) {\n          const strategyLogger = strategy.logger || logger;\n          strategyLogger.info(`Retry ${retryCount}: Processing retry strategy ${strategy.name}.`);\n\n          const modifiers = strategy.retry({\n            retryCount,\n            response,\n            responseError,\n          });\n\n          if (modifiers.skipStrategy) {\n            strategyLogger.info(`Retry ${retryCount}: Skipped.`);\n            continue strategiesLoop;\n          }\n\n          const { errorToThrow, retryAfterInMs, redirectTo } = modifiers;\n\n          if (errorToThrow) {\n            strategyLogger.error(\n              `Retry ${retryCount}: Retry strategy ${strategy.name} throws error:`,\n              errorToThrow,\n            );\n            throw errorToThrow;\n          }\n\n          if (retryAfterInMs || retryAfterInMs === 0) {\n            strategyLogger.info(\n              `Retry ${retryCount}: Retry strategy ${strategy.name} retries after ${retryAfterInMs}`,\n            );\n            await delay(retryAfterInMs, undefined, { abortSignal: request.abortSignal });\n            continue retryRequest;\n          }\n\n          if (redirectTo) {\n            strategyLogger.info(\n              `Retry ${retryCount}: Retry strategy ${strategy.name} redirects to ${redirectTo}`,\n            );\n            request.url = redirectTo;\n            continue retryRequest;\n          }\n        }\n\n        if (responseError) {\n          logger.info(\n            `None of the retry strategies could work with the received error. Throwing it.`,\n          );\n          throw responseError;\n        }\n        if (response) {\n          logger.info(\n            `None of the retry strategies could work with the received response. Returning it.`,\n          );\n          return response;\n        }\n\n        // If all the retries skip and there's no response,\n        // we're still in the retry loop, so a new request will be sent\n        // until `maxRetries` is reached.\n      }\n    },\n  };\n}\n"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;;;;AAIlC,OAAO,EAAE,KAAK,EAAE,MAAM,oBAAoB,CAAC;AAG3C,OAAO,EAAE,UAAU,EAAE,MAAM,mCAAmC,CAAC;AAE/D,OAAO,EAAE,kBAAkB,EAAE,MAAM,qBAAqB,CAAC;AACzD,OAAO,EAAE,0BAA0B,EAAE,MAAM,iBAAiB,CAAC;;;;;AAE7D,MAAM,iBAAiB,gMAAG,qBAAA,AAAkB,EAAC,6BAA6B,CAAC,CAAC;AAE5E;;GAEG,CACH,MAAM,eAAe,GAAG,aAAa,CAAC;AAmBhC,SAAU,WAAW,CACzB,UAA2B,EAC3B,UAA8B;IAAE,UAAU,oLAAE,6BAA0B;AAAA,CAAE;IAExE,MAAM,MAAM,GAAG,OAAO,CAAC,MAAM,IAAI,iBAAiB,CAAC;IACnD,OAAO;QACL,IAAI,EAAE,eAAe;QACrB,KAAK,CAAC,WAAW,EAAC,OAAwB,EAAE,IAAiB;;YAC3D,IAAI,QAAsC,CAAC;YAC3C,IAAI,aAAoC,CAAC;YACzC,IAAI,UAAU,GAAG,CAAC,CAAC,CAAC;YAEpB,YAAY,EAAE,MAAO,IAAI,CAAE,CAAC;gBAC1B,UAAU,IAAI,CAAC,CAAC;gBAChB,QAAQ,GAAG,SAAS,CAAC;gBACrB,aAAa,GAAG,SAAS,CAAC;gBAE1B,IAAI,CAAC;oBACH,MAAM,CAAC,IAAI,CAAC,CAAA,MAAA,EAAS,UAAU,CAAA,4BAAA,CAA8B,EAAE,OAAO,CAAC,SAAS,CAAC,CAAC;oBAClF,QAAQ,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,CAAC;oBAC/B,MAAM,CAAC,IAAI,CAAC,CAAA,MAAA,EAAS,UAAU,CAAA,kCAAA,CAAoC,EAAE,OAAO,CAAC,SAAS,CAAC,CAAC;gBAC1F,CAAC,CAAC,OAAO,CAAM,EAAE,CAAC;oBAChB,MAAM,CAAC,KAAK,CAAC,CAAA,MAAA,EAAS,UAAU,CAAA,gCAAA,CAAkC,EAAE,OAAO,CAAC,SAAS,CAAC,CAAC;oBAEvF,yDAAyD;oBACzD,gGAAgG;oBAChG,sEAAsE;oBACtE,aAAa,GAAG,CAAc,CAAC;oBAC/B,IAAI,CAAC,CAAC,IAAI,aAAa,CAAC,IAAI,KAAK,WAAW,EAAE,CAAC;wBAC7C,MAAM,CAAC,CAAC;oBACV,CAAC;oBAED,QAAQ,GAAG,aAAa,CAAC,QAAQ,CAAC;gBACpC,CAAC;gBAED,IAAI,CAAA,KAAA,OAAO,CAAC,WAAW,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAE,OAAO,EAAE,CAAC;oBACjC,MAAM,CAAC,KAAK,CAAC,CAAA,MAAA,EAAS,UAAU,CAAA,kBAAA,CAAoB,CAAC,CAAC;oBACtD,MAAM,UAAU,GAAG,8MAAI,aAAU,EAAE,CAAC;oBACpC,MAAM,UAAU,CAAC;gBACnB,CAAC;gBAED,IAAI,UAAU,IAAI,CAAC,CAAA,KAAA,OAAO,CAAC,UAAU,MAAA,QAAA,OAAA,KAAA,IAAA,uLAAI,6BAA0B,CAAC,EAAE,CAAC;oBACrE,MAAM,CAAC,IAAI,CACT,CAAA,MAAA,EAAS,UAAU,CAAA,qGAAA,CAAuG,CAC3H,CAAC;oBACF,IAAI,aAAa,EAAE,CAAC;wBAClB,MAAM,aAAa,CAAC;oBACtB,CAAC,MAAM,IAAI,QAAQ,EAAE,CAAC;wBACpB,OAAO,QAAQ,CAAC;oBAClB,CAAC,MAAM,CAAC;wBACN,MAAM,IAAI,KAAK,CAAC,4DAA4D,CAAC,CAAC;oBAChF,CAAC;gBACH,CAAC;gBAED,MAAM,CAAC,IAAI,CAAC,CAAA,MAAA,EAAS,UAAU,CAAA,aAAA,EAAgB,UAAU,CAAC,MAAM,CAAA,kBAAA,CAAoB,CAAC,CAAC;gBAEtF,cAAc,EAAE,KAAK,MAAM,QAAQ,IAAI,UAAU,CAAE,CAAC;oBAClD,MAAM,cAAc,GAAG,QAAQ,CAAC,MAAM,IAAI,MAAM,CAAC;oBACjD,cAAc,CAAC,IAAI,CAAC,CAAA,MAAA,EAAS,UAAU,CAAA,4BAAA,EAA+B,QAAQ,CAAC,IAAI,CAAA,CAAA,CAAG,CAAC,CAAC;oBAExF,MAAM,SAAS,GAAG,QAAQ,CAAC,KAAK,CAAC;wBAC/B,UAAU;wBACV,QAAQ;wBACR,aAAa;qBACd,CAAC,CAAC;oBAEH,IAAI,SAAS,CAAC,YAAY,EAAE,CAAC;wBAC3B,cAAc,CAAC,IAAI,CAAC,CAAA,MAAA,EAAS,UAAU,CAAA,UAAA,CAAY,CAAC,CAAC;wBACrD,SAAS,cAAc,CAAC;oBAC1B,CAAC;oBAED,MAAM,EAAE,YAAY,EAAE,cAAc,EAAE,UAAU,EAAE,GAAG,SAAS,CAAC;oBAE/D,IAAI,YAAY,EAAE,CAAC;wBACjB,cAAc,CAAC,KAAK,CAClB,CAAA,MAAA,EAAS,UAAU,CAAA,iBAAA,EAAoB,QAAQ,CAAC,IAAI,CAAA,cAAA,CAAgB,EACpE,YAAY,CACb,CAAC;wBACF,MAAM,YAAY,CAAC;oBACrB,CAAC;oBAED,IAAI,cAAc,IAAI,cAAc,KAAK,CAAC,EAAE,CAAC;wBAC3C,cAAc,CAAC,IAAI,CACjB,CAAA,MAAA,EAAS,UAAU,CAAA,iBAAA,EAAoB,QAAQ,CAAC,IAAI,CAAA,eAAA,EAAkB,cAAc,EAAE,CACvF,CAAC;wBACF,kMAAM,QAAA,AAAK,EAAC,cAAc,EAAE,SAAS,EAAE;4BAAE,WAAW,EAAE,OAAO,CAAC,WAAW;wBAAA,CAAE,CAAC,CAAC;wBAC7E,SAAS,YAAY,CAAC;oBACxB,CAAC;oBAED,IAAI,UAAU,EAAE,CAAC;wBACf,cAAc,CAAC,IAAI,CACjB,CAAA,MAAA,EAAS,UAAU,CAAA,iBAAA,EAAoB,QAAQ,CAAC,IAAI,CAAA,cAAA,EAAiB,UAAU,EAAE,CAClF,CAAC;wBACF,OAAO,CAAC,GAAG,GAAG,UAAU,CAAC;wBACzB,SAAS,YAAY,CAAC;oBACxB,CAAC;gBACH,CAAC;gBAED,IAAI,aAAa,EAAE,CAAC;oBAClB,MAAM,CAAC,IAAI,CACT,CAAA,6EAAA,CAA+E,CAChF,CAAC;oBACF,MAAM,aAAa,CAAC;gBACtB,CAAC;gBACD,IAAI,QAAQ,EAAE,CAAC;oBACb,MAAM,CAAC,IAAI,CACT,CAAA,iFAAA,CAAmF,CACpF,CAAC;oBACF,OAAO,QAAQ,CAAC;gBAClB,CAAC;YAED,mDAAmD;YACnD,+DAA+D;YAC/D,iCAAiC;YACnC,CAAC;QACH,CAAC;KACF,CAAC;AACJ,CAAC", "debugId": null}}, {"offset": {"line": 2003, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@typespec/ts-http-runtime/dist/esm/policies/defaultRetryPolicy.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/node_modules/%40typespec/ts-http-runtime/src/policies/defaultRetryPolicy.ts"], "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type { PipelineRetryOptions } from \"../interfaces.js\";\nimport type { PipelinePolicy } from \"../pipeline.js\";\nimport { exponentialRetryStrategy } from \"../retryStrategies/exponentialRetryStrategy.js\";\nimport { throttlingRetryStrategy } from \"../retryStrategies/throttlingRetryStrategy.js\";\nimport { retryPolicy } from \"./retryPolicy.js\";\nimport { DEFAULT_RETRY_POLICY_COUNT } from \"../constants.js\";\n\n/**\n * Name of the {@link defaultRetryPolicy}\n */\nexport const defaultRetryPolicyName = \"defaultRetryPolicy\";\n\n/**\n * Options that control how to retry failed requests.\n */\nexport interface DefaultRetryPolicyOptions extends PipelineRetryOptions {}\n\n/**\n * A policy that retries according to three strategies:\n * - When the server sends a 429 response with a Retry-After header.\n * - When there are errors in the underlying transport layer (e.g. DNS lookup failures).\n * - Or otherwise if the outgoing request fails, it will retry with an exponentially increasing delay.\n */\nexport function defaultRetryPolicy(options: DefaultRetryPolicyOptions = {}): PipelinePolicy {\n  return {\n    name: defaultRetryPolicyName,\n    sendRequest: retryPolicy([throttlingRetryStrategy(), exponentialRetryStrategy(options)], {\n      maxRetries: options.maxRetries ?? DEFAULT_RETRY_POLICY_COUNT,\n    }).sendRequest,\n  };\n}\n"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;;;;;AAIlC,OAAO,EAAE,wBAAwB,EAAE,MAAM,gDAAgD,CAAC;AAC1F,OAAO,EAAE,uBAAuB,EAAE,MAAM,+CAA+C,CAAC;AACxF,OAAO,EAAE,WAAW,EAAE,MAAM,kBAAkB,CAAC;AAC/C,OAAO,EAAE,0BAA0B,EAAE,MAAM,iBAAiB,CAAC;;;;;AAKtD,MAAM,sBAAsB,GAAG,oBAAoB,CAAC;AAarD,SAAU,kBAAkB,CAAC,UAAqC,CAAA,CAAE;;IACxE,OAAO;QACL,IAAI,EAAE,sBAAsB;QAC5B,WAAW,sMAAE,cAAA,AAAW,EAAC;aAAC,gPAAA,AAAuB,EAAE;oOAAE,2BAAA,AAAwB,EAAC,OAAO,CAAC;SAAC,EAAE;YACvF,UAAU,EAAE,CAAA,KAAA,OAAO,CAAC,UAAU,MAAA,QAAA,OAAA,KAAA,IAAA,uLAAI,6BAA0B;SAC7D,CAAC,CAAC,WAAW;KACf,CAAC;AACJ,CAAC", "debugId": null}}, {"offset": {"line": 2034, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@typespec/ts-http-runtime/dist/esm/policies/formDataPolicy.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/node_modules/%40typespec/ts-http-runtime/src/policies/formDataPolicy.ts"], "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport { stringToUint8Array } from \"../util/bytesEncoding.js\";\nimport { isNodeLike } from \"../util/checkEnvironment.js\";\nimport { createHttpHeaders } from \"../httpHeaders.js\";\nimport type {\n  BodyPart,\n  FormDataMap,\n  FormDataValue,\n  PipelineRequest,\n  PipelineResponse,\n  SendRequest,\n} from \"../interfaces.js\";\nimport type { PipelinePolicy } from \"../pipeline.js\";\n\n/**\n * The programmatic identifier of the formDataPolicy.\n */\nexport const formDataPolicyName = \"formDataPolicy\";\n\nfunction formDataToFormDataMap(formData: FormData): FormDataMap {\n  const formDataMap: FormDataMap = {};\n  for (const [key, value] of formData.entries()) {\n    formDataMap[key] ??= [];\n    (formDataMap[key] as FormDataValue[]).push(value);\n  }\n  return formDataMap;\n}\n\n/**\n * A policy that encodes FormData on the request into the body.\n */\nexport function formDataPolicy(): PipelinePolicy {\n  return {\n    name: formDataPolicyName,\n    async sendRequest(request: PipelineRequest, next: SendRequest): Promise<PipelineResponse> {\n      if (isNodeLike && typeof FormData !== \"undefined\" && request.body instanceof FormData) {\n        request.formData = formDataToFormDataMap(request.body);\n        request.body = undefined;\n      }\n\n      if (request.formData) {\n        const contentType = request.headers.get(\"Content-Type\");\n        if (contentType && contentType.indexOf(\"application/x-www-form-urlencoded\") !== -1) {\n          request.body = wwwFormUrlEncode(request.formData);\n        } else {\n          await prepareFormData(request.formData, request);\n        }\n\n        request.formData = undefined;\n      }\n      return next(request);\n    },\n  };\n}\n\nfunction wwwFormUrlEncode(formData: FormDataMap): string {\n  const urlSearchParams = new URLSearchParams();\n  for (const [key, value] of Object.entries(formData)) {\n    if (Array.isArray(value)) {\n      for (const subValue of value) {\n        urlSearchParams.append(key, subValue.toString());\n      }\n    } else {\n      urlSearchParams.append(key, value.toString());\n    }\n  }\n  return urlSearchParams.toString();\n}\n\nasync function prepareFormData(formData: FormDataMap, request: PipelineRequest): Promise<void> {\n  // validate content type (multipart/form-data)\n  const contentType = request.headers.get(\"Content-Type\");\n  if (contentType && !contentType.startsWith(\"multipart/form-data\")) {\n    // content type is specified and is not multipart/form-data. Exit.\n    return;\n  }\n\n  request.headers.set(\"Content-Type\", contentType ?? \"multipart/form-data\");\n\n  // set body to MultipartRequestBody using content from FormDataMap\n  const parts: BodyPart[] = [];\n\n  for (const [fieldName, values] of Object.entries(formData)) {\n    for (const value of Array.isArray(values) ? values : [values]) {\n      if (typeof value === \"string\") {\n        parts.push({\n          headers: createHttpHeaders({\n            \"Content-Disposition\": `form-data; name=\"${fieldName}\"`,\n          }),\n          body: stringToUint8Array(value, \"utf-8\"),\n        });\n      } else if (value === undefined || value === null || typeof value !== \"object\") {\n        throw new Error(\n          `Unexpected value for key ${fieldName}: ${value}. Value should be serialized to string first.`,\n        );\n      } else {\n        // using || instead of ?? here since if value.name is empty we should create a file name\n        const fileName = (value as File).name || \"blob\";\n        const headers = createHttpHeaders();\n        headers.set(\n          \"Content-Disposition\",\n          `form-data; name=\"${fieldName}\"; filename=\"${fileName}\"`,\n        );\n\n        // again, || is used since an empty value.type means the content type is unset\n        headers.set(\"Content-Type\", value.type || \"application/octet-stream\");\n\n        parts.push({\n          headers,\n          body: value,\n        });\n      }\n    }\n  }\n  request.multipartBody = { parts };\n}\n"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;;;;;AAElC,OAAO,EAAE,kBAAkB,EAAE,MAAM,0BAA0B,CAAC;AAC9D,OAAO,EAAE,UAAU,EAAE,MAAM,6BAA6B,CAAC;AACzD,OAAO,EAAE,iBAAiB,EAAE,MAAM,mBAAmB,CAAC;;;;AAc/C,MAAM,kBAAkB,GAAG,gBAAgB,CAAC;AAEnD,SAAS,qBAAqB,CAAC,QAAkB;;IAC/C,MAAM,WAAW,GAAgB,CAAA,CAAE,CAAC;IACpC,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,QAAQ,CAAC,OAAO,EAAE,CAAE,CAAC;QAC9C,CAAA,KAAA,WAAW,CAAC,GAAG,CAAA,MAAA,QAAA,OAAA,KAAA,IAAA,KAAf,WAAW,CAAC,GAAG,CAAA,GAAM,EAAE,EAAC;QACvB,WAAW,CAAC,GAAG,CAAqB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IACpD,CAAC;IACD,OAAO,WAAW,CAAC;AACrB,CAAC;AAKK,SAAU,cAAc;IAC5B,OAAO;QACL,IAAI,EAAE,kBAAkB;QACxB,KAAK,CAAC,WAAW,EAAC,OAAwB,EAAE,IAAiB;YAC3D,qMAAI,aAAU,IAAI,OAAO,QAAQ,KAAK,WAAW,IAAI,OAAO,CAAC,IAAI,YAAY,QAAQ,EAAE,CAAC;gBACtF,OAAO,CAAC,QAAQ,GAAG,qBAAqB,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;gBACvD,OAAO,CAAC,IAAI,GAAG,SAAS,CAAC;YAC3B,CAAC;YAED,IAAI,OAAO,CAAC,QAAQ,EAAE,CAAC;gBACrB,MAAM,WAAW,GAAG,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;gBACxD,IAAI,WAAW,IAAI,WAAW,CAAC,OAAO,CAAC,mCAAmC,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC;oBACnF,OAAO,CAAC,IAAI,GAAG,gBAAgB,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;gBACpD,CAAC,MAAM,CAAC;oBACN,MAAM,eAAe,CAAC,OAAO,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;gBACnD,CAAC;gBAED,OAAO,CAAC,QAAQ,GAAG,SAAS,CAAC;YAC/B,CAAC;YACD,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC;QACvB,CAAC;KACF,CAAC;AACJ,CAAC;AAED,SAAS,gBAAgB,CAAC,QAAqB;IAC7C,MAAM,eAAe,GAAG,IAAI,eAAe,EAAE,CAAC;IAC9C,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAE,CAAC;QACpD,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC;YACzB,KAAK,MAAM,QAAQ,IAAI,KAAK,CAAE,CAAC;gBAC7B,eAAe,CAAC,MAAM,CAAC,GAAG,EAAE,QAAQ,CAAC,QAAQ,EAAE,CAAC,CAAC;YACnD,CAAC;QACH,CAAC,MAAM,CAAC;YACN,eAAe,CAAC,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,QAAQ,EAAE,CAAC,CAAC;QAChD,CAAC;IACH,CAAC;IACD,OAAO,eAAe,CAAC,QAAQ,EAAE,CAAC;AACpC,CAAC;AAED,KAAK,UAAU,eAAe,CAAC,QAAqB,EAAE,OAAwB;IAC5E,8CAA8C;IAC9C,MAAM,WAAW,GAAG,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;IACxD,IAAI,WAAW,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,qBAAqB,CAAC,EAAE,CAAC;QAClE,kEAAkE;QAClE,OAAO;IACT,CAAC;IAED,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,EAAE,WAAW,KAAA,QAAX,WAAW,KAAA,KAAA,IAAX,WAAW,GAAI,qBAAqB,CAAC,CAAC;IAE1E,kEAAkE;IAClE,MAAM,KAAK,GAAe,EAAE,CAAC;IAE7B,KAAK,MAAM,CAAC,SAAS,EAAE,MAAM,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAE,CAAC;QAC3D,KAAK,MAAM,KAAK,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC;YAAC,MAAM;SAAC,CAAE,CAAC;YAC9D,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;gBAC9B,KAAK,CAAC,IAAI,CAAC;oBACT,OAAO,0LAAE,oBAAA,AAAiB,EAAC;wBACzB,qBAAqB,EAAE,CAAA,iBAAA,EAAoB,SAAS,CAAA,CAAA,CAAG;qBACxD,CAAC;oBACF,IAAI,oMAAE,qBAAA,AAAkB,EAAC,KAAK,EAAE,OAAO,CAAC;iBACzC,CAAC,CAAC;YACL,CAAC,MAAM,IAAI,KAAK,KAAK,SAAS,IAAI,KAAK,KAAK,IAAI,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;gBAC9E,MAAM,IAAI,KAAK,CACb,CAAA,yBAAA,EAA4B,SAAS,CAAA,EAAA,EAAK,KAAK,CAAA,6CAAA,CAA+C,CAC/F,CAAC;YACJ,CAAC,MAAM,CAAC;gBACN,wFAAwF;gBACxF,MAAM,QAAQ,GAAI,KAAc,CAAC,IAAI,IAAI,MAAM,CAAC;gBAChD,MAAM,OAAO,2LAAG,oBAAA,AAAiB,EAAE,CAAC;gBACpC,OAAO,CAAC,GAAG,CACT,qBAAqB,EACrB,CAAA,iBAAA,EAAoB,SAAS,CAAA,aAAA,EAAgB,QAAQ,CAAA,CAAA,CAAG,CACzD,CAAC;gBAEF,8EAA8E;gBAC9E,OAAO,CAAC,GAAG,CAAC,cAAc,EAAE,KAAK,CAAC,IAAI,IAAI,0BAA0B,CAAC,CAAC;gBAEtE,KAAK,CAAC,IAAI,CAAC;oBACT,OAAO;oBACP,IAAI,EAAE,KAAK;iBACZ,CAAC,CAAC;YACL,CAAC;QACH,CAAC;IACH,CAAC;IACD,OAAO,CAAC,aAAa,GAAG;QAAE,KAAK;IAAA,CAAE,CAAC;AACpC,CAAC", "debugId": null}}, {"offset": {"line": 2135, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@typespec/ts-http-runtime/dist/esm/policies/proxyPolicy.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/node_modules/%40typespec/ts-http-runtime/src/policies/proxyPolicy.ts"], "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type * as http from \"http\";\nimport type * as https from \"https\";\nimport { HttpsProxyAgent } from \"https-proxy-agent\";\nimport { HttpProxyAgent } from \"http-proxy-agent\";\nimport type {\n  PipelineRequest,\n  PipelineResponse,\n  ProxySettings,\n  SendRequest,\n} from \"../interfaces.js\";\nimport type { PipelinePolicy } from \"../pipeline.js\";\nimport { logger } from \"../log.js\";\n\nconst HTTPS_PROXY = \"HTTPS_PROXY\";\nconst HTTP_PROXY = \"HTTP_PROXY\";\nconst ALL_PROXY = \"ALL_PROXY\";\nconst NO_PROXY = \"NO_PROXY\";\n\n/**\n * The programmatic identifier of the proxyPolicy.\n */\nexport const proxyPolicyName = \"proxyPolicy\";\n\n/**\n * Stores the patterns specified in NO_PROXY environment variable.\n * @internal\n */\nexport const globalNoProxyList: string[] = [];\nlet noProxyListLoaded: boolean = false;\n\n/** A cache of whether a host should bypass the proxy. */\nconst globalBypassedMap: Map<string, boolean> = new Map();\n\nfunction getEnvironmentValue(name: string): string | undefined {\n  if (process.env[name]) {\n    return process.env[name];\n  } else if (process.env[name.toLowerCase()]) {\n    return process.env[name.toLowerCase()];\n  }\n  return undefined;\n}\n\nfunction loadEnvironmentProxyValue(): string | undefined {\n  if (!process) {\n    return undefined;\n  }\n\n  const httpsProxy = getEnvironmentValue(HTTPS_PROXY);\n  const allProxy = getEnvironmentValue(ALL_PROXY);\n  const httpProxy = getEnvironmentValue(HTTP_PROXY);\n\n  return httpsProxy || allProxy || httpProxy;\n}\n\n/**\n * Check whether the host of a given `uri` matches any pattern in the no proxy list.\n * If there's a match, any request sent to the same host shouldn't have the proxy settings set.\n * This implementation is a port of https://github.com/Azure/azure-sdk-for-net/blob/8cca811371159e527159c7eb65602477898683e2/sdk/core/Azure.Core/src/Pipeline/Internal/HttpEnvironmentProxy.cs#L210\n */\nfunction isBypassed(\n  uri: string,\n  noProxyList: string[],\n  bypassedMap?: Map<string, boolean>,\n): boolean | undefined {\n  if (noProxyList.length === 0) {\n    return false;\n  }\n  const host = new URL(uri).hostname;\n  if (bypassedMap?.has(host)) {\n    return bypassedMap.get(host);\n  }\n  let isBypassedFlag = false;\n  for (const pattern of noProxyList) {\n    if (pattern[0] === \".\") {\n      // This should match either domain it self or any subdomain or host\n      // .foo.com will match foo.com it self or *.foo.com\n      if (host.endsWith(pattern)) {\n        isBypassedFlag = true;\n      } else {\n        if (host.length === pattern.length - 1 && host === pattern.slice(1)) {\n          isBypassedFlag = true;\n        }\n      }\n    } else {\n      if (host === pattern) {\n        isBypassedFlag = true;\n      }\n    }\n  }\n  bypassedMap?.set(host, isBypassedFlag);\n  return isBypassedFlag;\n}\n\nexport function loadNoProxy(): string[] {\n  const noProxy = getEnvironmentValue(NO_PROXY);\n  noProxyListLoaded = true;\n  if (noProxy) {\n    return noProxy\n      .split(\",\")\n      .map((item) => item.trim())\n      .filter((item) => item.length);\n  }\n\n  return [];\n}\n\n/**\n * This method converts a proxy url into `ProxySettings` for use with ProxyPolicy.\n * If no argument is given, it attempts to parse a proxy URL from the environment\n * variables `HTTPS_PROXY` or `HTTP_PROXY`.\n * @param proxyUrl - The url of the proxy to use. May contain authentication information.\n * @deprecated - Internally this method is no longer necessary when setting proxy information.\n */\nexport function getDefaultProxySettings(proxyUrl?: string): ProxySettings | undefined {\n  if (!proxyUrl) {\n    proxyUrl = loadEnvironmentProxyValue();\n    if (!proxyUrl) {\n      return undefined;\n    }\n  }\n\n  const parsedUrl = new URL(proxyUrl);\n  const schema = parsedUrl.protocol ? parsedUrl.protocol + \"//\" : \"\";\n  return {\n    host: schema + parsedUrl.hostname,\n    port: Number.parseInt(parsedUrl.port || \"80\"),\n    username: parsedUrl.username,\n    password: parsedUrl.password,\n  };\n}\n\n/**\n * This method attempts to parse a proxy URL from the environment\n * variables `HTTPS_PROXY` or `HTTP_PROXY`.\n */\nfunction getDefaultProxySettingsInternal(): URL | undefined {\n  const envProxy = loadEnvironmentProxyValue();\n  return envProxy ? new URL(envProxy) : undefined;\n}\n\nfunction getUrlFromProxySettings(settings: ProxySettings): URL {\n  let parsedProxyUrl: URL;\n  try {\n    parsedProxyUrl = new URL(settings.host);\n  } catch {\n    throw new Error(\n      `Expecting a valid host string in proxy settings, but found \"${settings.host}\".`,\n    );\n  }\n\n  parsedProxyUrl.port = String(settings.port);\n  if (settings.username) {\n    parsedProxyUrl.username = settings.username;\n  }\n  if (settings.password) {\n    parsedProxyUrl.password = settings.password;\n  }\n\n  return parsedProxyUrl;\n}\n\nfunction setProxyAgentOnRequest(\n  request: PipelineRequest,\n  cachedAgents: CachedAgents,\n  proxyUrl: URL,\n): void {\n  // Custom Agent should take precedence so if one is present\n  // we should skip to avoid overwriting it.\n  if (request.agent) {\n    return;\n  }\n\n  const url = new URL(request.url);\n\n  const isInsecure = url.protocol !== \"https:\";\n\n  if (request.tlsSettings) {\n    logger.warning(\n      \"TLS settings are not supported in combination with custom Proxy, certificates provided to the client will be ignored.\",\n    );\n  }\n\n  const headers = request.headers.toJSON();\n\n  if (isInsecure) {\n    if (!cachedAgents.httpProxyAgent) {\n      cachedAgents.httpProxyAgent = new HttpProxyAgent(proxyUrl, { headers });\n    }\n    request.agent = cachedAgents.httpProxyAgent;\n  } else {\n    if (!cachedAgents.httpsProxyAgent) {\n      cachedAgents.httpsProxyAgent = new HttpsProxyAgent(proxyUrl, { headers });\n    }\n    request.agent = cachedAgents.httpsProxyAgent;\n  }\n}\n\ninterface CachedAgents {\n  httpsProxyAgent?: https.Agent;\n  httpProxyAgent?: http.Agent;\n}\n\n/**\n * A policy that allows one to apply proxy settings to all requests.\n * If not passed static settings, they will be retrieved from the HTTPS_PROXY\n * or HTTP_PROXY environment variables.\n * @param proxySettings - ProxySettings to use on each request.\n * @param options - additional settings, for example, custom NO_PROXY patterns\n */\nexport function proxyPolicy(\n  proxySettings?: ProxySettings,\n  options?: {\n    /** a list of patterns to override those loaded from NO_PROXY environment variable. */\n    customNoProxyList?: string[];\n  },\n): PipelinePolicy {\n  if (!noProxyListLoaded) {\n    globalNoProxyList.push(...loadNoProxy());\n  }\n\n  const defaultProxy = proxySettings\n    ? getUrlFromProxySettings(proxySettings)\n    : getDefaultProxySettingsInternal();\n\n  const cachedAgents: CachedAgents = {};\n\n  return {\n    name: proxyPolicyName,\n    async sendRequest(request: PipelineRequest, next: SendRequest): Promise<PipelineResponse> {\n      if (\n        !request.proxySettings &&\n        defaultProxy &&\n        !isBypassed(\n          request.url,\n          options?.customNoProxyList ?? globalNoProxyList,\n          options?.customNoProxyList ? undefined : globalBypassedMap,\n        )\n      ) {\n        setProxyAgentOnRequest(request, cachedAgents, defaultProxy);\n      } else if (request.proxySettings) {\n        setProxyAgentOnRequest(\n          request,\n          cachedAgents,\n          getUrlFromProxySettings(request.proxySettings),\n        );\n      }\n      return next(request);\n    },\n  };\n}\n"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;;;;;;;;AAIlC,OAAO,EAAE,eAAe,EAAE,MAAM,mBAAmB,CAAC;AACpD,OAAO,EAAE,cAAc,EAAE,MAAM,kBAAkB,CAAC;AAQlD,OAAO,EAAE,MAAM,EAAE,MAAM,WAAW,CAAC;;;;AAEnC,MAAM,WAAW,GAAG,aAAa,CAAC;AAClC,MAAM,UAAU,GAAG,YAAY,CAAC;AAChC,MAAM,SAAS,GAAG,WAAW,CAAC;AAC9B,MAAM,QAAQ,GAAG,UAAU,CAAC;AAKrB,MAAM,eAAe,GAAG,aAAa,CAAC;AAMtC,MAAM,iBAAiB,GAAa,EAAE,CAAC;AAC9C,IAAI,iBAAiB,GAAY,KAAK,CAAC;AAEvC,uDAAA,EAAyD,CACzD,MAAM,iBAAiB,GAAyB,IAAI,GAAG,EAAE,CAAC;AAE1D,SAAS,mBAAmB,CAAC,IAAY;IACvC,IAAI,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC;QACtB,OAAO,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;IAC3B,CAAC,MAAM,IAAI,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,EAAE,CAAC;QAC3C,OAAO,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC;IACzC,CAAC;IACD,OAAO,SAAS,CAAC;AACnB,CAAC;AAED,SAAS,yBAAyB;IAChC,IAAI,CAAC,OAAO,EAAE,CAAC;;IAIf,MAAM,UAAU,GAAG,mBAAmB,CAAC,WAAW,CAAC,CAAC;IACpD,MAAM,QAAQ,GAAG,mBAAmB,CAAC,SAAS,CAAC,CAAC;IAChD,MAAM,SAAS,GAAG,mBAAmB,CAAC,UAAU,CAAC,CAAC;IAElD,OAAO,UAAU,IAAI,QAAQ,IAAI,SAAS,CAAC;AAC7C,CAAC;AAED;;;;GAIG,CACH,SAAS,UAAU,CACjB,GAAW,EACX,WAAqB,EACrB,WAAkC;IAElC,IAAI,WAAW,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;QAC7B,OAAO,KAAK,CAAC;IACf,CAAC;IACD,MAAM,IAAI,GAAG,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC,QAAQ,CAAC;IACnC,IAAI,WAAW,KAAA,QAAX,WAAW,KAAA,KAAA,IAAA,KAAA,IAAX,WAAW,CAAE,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC;QAC3B,OAAO,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;IAC/B,CAAC;IACD,IAAI,cAAc,GAAG,KAAK,CAAC;IAC3B,KAAK,MAAM,OAAO,IAAI,WAAW,CAAE,CAAC;QAClC,IAAI,OAAO,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE,CAAC;YACvB,mEAAmE;YACnE,mDAAmD;YACnD,IAAI,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;gBAC3B,cAAc,GAAG,IAAI,CAAC;YACxB,CAAC,MAAM,CAAC;gBACN,IAAI,IAAI,CAAC,MAAM,KAAK,OAAO,CAAC,MAAM,GAAG,CAAC,IAAI,IAAI,KAAK,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC;oBACpE,cAAc,GAAG,IAAI,CAAC;gBACxB,CAAC;YACH,CAAC;QACH,CAAC,MAAM,CAAC;YACN,IAAI,IAAI,KAAK,OAAO,EAAE,CAAC;gBACrB,cAAc,GAAG,IAAI,CAAC;YACxB,CAAC;QACH,CAAC;IACH,CAAC;IACD,WAAW,KAAA,QAAX,WAAW,KAAA,KAAA,IAAA,KAAA,IAAX,WAAW,CAAE,GAAG,CAAC,IAAI,EAAE,cAAc,CAAC,CAAC;IACvC,OAAO,cAAc,CAAC;AACxB,CAAC;AAEK,SAAU,WAAW;IACzB,MAAM,OAAO,GAAG,mBAAmB,CAAC,QAAQ,CAAC,CAAC;IAC9C,iBAAiB,GAAG,IAAI,CAAC;IACzB,IAAI,OAAO,EAAE,CAAC;QACZ,OAAO,OAAO,CACX,KAAK,CAAC,GAAG,CAAC,CACV,GAAG,CAAC,CAAC,IAAI,EAAE,CAAG,CAAD,GAAK,CAAC,IAAI,EAAE,CAAC,CAC1B,MAAM,CAAC,CAAC,IAAI,EAAE,CAAG,CAAD,GAAK,CAAC,MAAM,CAAC,CAAC;IACnC,CAAC;IAED,OAAO,EAAE,CAAC;AACZ,CAAC;AASK,SAAU,uBAAuB,CAAC,QAAiB;IACvD,IAAI,CAAC,QAAQ,EAAE,CAAC;QACd,QAAQ,GAAG,yBAAyB,EAAE,CAAC;QACvC,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,OAAO,SAAS,CAAC;QACnB,CAAC;IACH,CAAC;IAED,MAAM,SAAS,GAAG,IAAI,GAAG,CAAC,QAAQ,CAAC,CAAC;IACpC,MAAM,MAAM,GAAG,SAAS,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,QAAQ,GAAG,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC;IACnE,OAAO;QACL,IAAI,EAAE,MAAM,GAAG,SAAS,CAAC,QAAQ;QACjC,IAAI,EAAE,MAAM,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,IAAI,IAAI,CAAC;QAC7C,QAAQ,EAAE,SAAS,CAAC,QAAQ;QAC5B,QAAQ,EAAE,SAAS,CAAC,QAAQ;KAC7B,CAAC;AACJ,CAAC;AAED;;;GAGG,CACH,SAAS,+BAA+B;IACtC,MAAM,QAAQ,GAAG,yBAAyB,EAAE,CAAC;IAC7C,OAAO,QAAQ,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;AAClD,CAAC;AAED,SAAS,uBAAuB,CAAC,QAAuB;IACtD,IAAI,cAAmB,CAAC;IACxB,IAAI,CAAC;QACH,cAAc,GAAG,IAAI,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;IAC1C,CAAC,CAAC,OAAA,IAAM,CAAC;QACP,MAAM,IAAI,KAAK,CACb,CAAA,4DAAA,EAA+D,QAAQ,CAAC,IAAI,CAAA,EAAA,CAAI,CACjF,CAAC;IACJ,CAAC;IAED,cAAc,CAAC,IAAI,GAAG,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;IAC5C,IAAI,QAAQ,CAAC,QAAQ,EAAE,CAAC;QACtB,cAAc,CAAC,QAAQ,GAAG,QAAQ,CAAC,QAAQ,CAAC;IAC9C,CAAC;IACD,IAAI,QAAQ,CAAC,QAAQ,EAAE,CAAC;QACtB,cAAc,CAAC,QAAQ,GAAG,QAAQ,CAAC,QAAQ,CAAC;IAC9C,CAAC;IAED,OAAO,cAAc,CAAC;AACxB,CAAC;AAED,SAAS,sBAAsB,CAC7B,OAAwB,EACxB,YAA0B,EAC1B,QAAa;IAEb,2DAA2D;IAC3D,0CAA0C;IAC1C,IAAI,OAAO,CAAC,KAAK,EAAE,CAAC;QAClB,OAAO;IACT,CAAC;IAED,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;IAEjC,MAAM,UAAU,GAAG,GAAG,CAAC,QAAQ,KAAK,QAAQ,CAAC;IAE7C,IAAI,OAAO,CAAC,WAAW,EAAE,CAAC;oLACxB,SAAM,CAAC,OAAO,CACZ,uHAAuH,CACxH,CAAC;IACJ,CAAC;IAED,MAAM,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC;IAEzC,IAAI,UAAU,EAAE,CAAC;QACf,IAAI,CAAC,YAAY,CAAC,cAAc,EAAE,CAAC;YACjC,YAAY,CAAC,cAAc,GAAG,8JAAI,iBAAc,CAAC,QAAQ,EAAE;gBAAE,OAAO;YAAA,CAAE,CAAC,CAAC;QAC1E,CAAC;QACD,OAAO,CAAC,KAAK,GAAG,YAAY,CAAC,cAAc,CAAC;IAC9C,CAAC,MAAM,CAAC;QACN,IAAI,CAAC,YAAY,CAAC,eAAe,EAAE,CAAC;YAClC,YAAY,CAAC,eAAe,GAAG,+JAAI,kBAAe,CAAC,QAAQ,EAAE;gBAAE,OAAO;YAAA,CAAE,CAAC,CAAC;QAC5E,CAAC;QACD,OAAO,CAAC,KAAK,GAAG,YAAY,CAAC,eAAe,CAAC;IAC/C,CAAC;AACH,CAAC;AAcK,SAAU,WAAW,CACzB,aAA6B,EAC7B,OAGC;IAED,IAAI,CAAC,iBAAiB,EAAE,CAAC;QACvB,iBAAiB,CAAC,IAAI,CAAC,GAAG,WAAW,EAAE,CAAC,CAAC;IAC3C,CAAC;IAED,MAAM,YAAY,GAAG,aAAa,GAC9B,uBAAuB,CAAC,aAAa,CAAC,GACtC,+BAA+B,EAAE,CAAC;IAEtC,MAAM,YAAY,GAAiB,CAAA,CAAE,CAAC;IAEtC,OAAO;QACL,IAAI,EAAE,eAAe;QACrB,KAAK,CAAC,WAAW,EAAC,OAAwB,EAAE,IAAiB;;YAC3D,IACE,CAAC,OAAO,CAAC,aAAa,IACtB,YAAY,IACZ,CAAC,UAAU,CACT,OAAO,CAAC,GAAG,EACX,CAAA,KAAA,OAAO,KAAA,QAAP,OAAO,KAAA,KAAA,IAAA,KAAA,IAAP,OAAO,CAAE,iBAAiB,MAAA,QAAA,OAAA,KAAA,IAAA,KAAI,iBAAiB,EAC/C,CAAA,OAAO,KAAA,QAAP,OAAO,KAAA,KAAA,IAAA,KAAA,IAAP,OAAO,CAAE,iBAAiB,EAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,iBAAiB,CAC3D,EACD,CAAC;gBACD,sBAAsB,CAAC,OAAO,EAAE,YAAY,EAAE,YAAY,CAAC,CAAC;YAC9D,CAAC,MAAM,IAAI,OAAO,CAAC,aAAa,EAAE,CAAC;gBACjC,sBAAsB,CACpB,OAAO,EACP,YAAY,EACZ,uBAAuB,CAAC,OAAO,CAAC,aAAa,CAAC,CAC/C,CAAC;YACJ,CAAC;YACD,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC;QACvB,CAAC;KACF,CAAC;AACJ,CAAC", "debugId": null}}, {"offset": {"line": 2305, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@typespec/ts-http-runtime/dist/esm/policies/agentPolicy.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/node_modules/%40typespec/ts-http-runtime/src/policies/agentPolicy.ts"], "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type { PipelinePolicy } from \"../pipeline.js\";\nimport type { Agent } from \"../interfaces.js\";\n\n/**\n * Name of the Agent Policy\n */\nexport const agentPolicyName = \"agentPolicy\";\n\n/**\n * Gets a pipeline policy that sets http.agent\n */\nexport function agentPolicy(agent?: Agent): PipelinePolicy {\n  return {\n    name: agentPolicyName,\n    sendRequest: async (req, next) => {\n      // Users may define an agent on the request, honor it over the client level one\n      if (!req.agent) {\n        req.agent = agent;\n      }\n      return next(req);\n    },\n  };\n}\n"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAKlC;;GAEG;;;;AACI,MAAM,eAAe,GAAG,aAAa,CAAC;AAKvC,SAAU,WAAW,CAAC,KAAa;IACvC,OAAO;QACL,IAAI,EAAE,eAAe;QACrB,WAAW,EAAE,KAAK,EAAE,GAAG,EAAE,IAAI,EAAE,EAAE;YAC/B,+EAA+E;YAC/E,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,CAAC;gBACf,GAAG,CAAC,KAAK,GAAG,KAAK,CAAC;YACpB,CAAC;YACD,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC;QACnB,CAAC;KACF,CAAC;AACJ,CAAC", "debugId": null}}, {"offset": {"line": 2330, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@typespec/ts-http-runtime/dist/esm/policies/tlsPolicy.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/node_modules/%40typespec/ts-http-runtime/src/policies/tlsPolicy.ts"], "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type { PipelinePolicy } from \"../pipeline.js\";\nimport type { TlsSettings } from \"../interfaces.js\";\n\n/**\n * Name of the TLS Policy\n */\nexport const tlsPolicyName = \"tlsPolicy\";\n\n/**\n * Gets a pipeline policy that adds the client certificate to the HttpClient agent for authentication.\n */\nexport function tlsPolicy(tlsSettings?: TlsSettings): PipelinePolicy {\n  return {\n    name: tlsPolicyName,\n    sendRequest: async (req, next) => {\n      // Users may define a request tlsSettings, honor those over the client level one\n      if (!req.tlsSettings) {\n        req.tlsSettings = tlsSettings;\n      }\n      return next(req);\n    },\n  };\n}\n"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAKlC;;GAEG;;;;AACI,MAAM,aAAa,GAAG,WAAW,CAAC;AAKnC,SAAU,SAAS,CAAC,WAAyB;IACjD,OAAO;QACL,IAAI,EAAE,aAAa;QACnB,WAAW,EAAE,KAAK,EAAE,GAAG,EAAE,IAAI,EAAE,EAAE;YAC/B,gFAAgF;YAChF,IAAI,CAAC,GAAG,CAAC,WAAW,EAAE,CAAC;gBACrB,GAAG,CAAC,WAAW,GAAG,WAAW,CAAC;YAChC,CAAC;YACD,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC;QACnB,CAAC;KACF,CAAC;AACJ,CAAC", "debugId": null}}, {"offset": {"line": 2355, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@typespec/ts-http-runtime/dist/esm/util/typeGuards.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/node_modules/%40typespec/ts-http-runtime/src/util/typeGuards.ts"], "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nexport function isNodeReadableStream(x: unknown): x is NodeJS.ReadableStream {\n  return Boolean(x && typeof (x as NodeJS.ReadableStream)[\"pipe\"] === \"function\");\n}\n\nexport function isWebReadableStream(x: unknown): x is ReadableStream {\n  return Boolean(\n    x &&\n      typeof (x as ReadableStream).getReader === \"function\" &&\n      typeof (x as ReadableStream).tee === \"function\",\n  );\n}\n\nexport function isBinaryBody(\n  body: unknown,\n): body is\n  | Uint8Array\n  | NodeJS.ReadableStream\n  | ReadableStream<Uint8Array>\n  | (() => NodeJS.ReadableStream)\n  | (() => ReadableStream<Uint8Array>)\n  | Blob {\n  return (\n    body !== undefined &&\n    (body instanceof Uint8Array ||\n      isReadableStream(body) ||\n      typeof body === \"function\" ||\n      body instanceof Blob)\n  );\n}\n\nexport function isReadableStream(x: unknown): x is ReadableStream | NodeJS.ReadableStream {\n  return isNodeReadableStream(x) || isWebReadableStream(x);\n}\n\nexport function isBlob(x: unknown): x is Blob {\n  return typeof (x as Blob).stream === \"function\";\n}\n"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;;;;;;;;AAE5B,SAAU,oBAAoB,CAAC,CAAU;IAC7C,OAAO,OAAO,CAAC,CAAC,IAAI,OAAQ,CAA2B,CAAC,MAAM,CAAC,KAAK,UAAU,CAAC,CAAC;AAClF,CAAC;AAEK,SAAU,mBAAmB,CAAC,CAAU;IAC5C,OAAO,OAAO,CACZ,CAAC,IACC,OAAQ,CAAoB,CAAC,SAAS,KAAK,UAAU,IACrD,OAAQ,CAAoB,CAAC,GAAG,KAAK,UAAU,CAClD,CAAC;AACJ,CAAC;AAEK,SAAU,YAAY,CAC1B,IAAa;IAQb,OAAO,AACL,IAAI,KAAK,SAAS,IAClB,CAAC,IAAI,YAAY,UAAU,IACzB,gBAAgB,CAAC,IAAI,CAAC,IACtB,OAAO,IAAI,KAAK,UAAU,IAC1B,IAAI,YAAY,IAAI,CAAC,CACxB,CAAC;AACJ,CAAC;AAEK,SAAU,gBAAgB,CAAC,CAAU;IACzC,OAAO,oBAAoB,CAAC,CAAC,CAAC,IAAI,mBAAmB,CAAC,CAAC,CAAC,CAAC;AAC3D,CAAC;AAEK,SAAU,MAAM,CAAC,CAAU;IAC/B,OAAO,OAAQ,CAAU,CAAC,MAAM,KAAK,UAAU,CAAC;AAClD,CAAC", "debugId": null}}, {"offset": {"line": 2383, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@typespec/ts-http-runtime/dist/esm/util/concat.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/node_modules/%40typespec/ts-http-runtime/src/util/concat.ts"], "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport { Readable } from \"stream\";\nimport type { ReadableStream as AsyncIterableReadableStream } from \"stream/web\";\nimport { isBlob } from \"./typeGuards.js\";\n\nasync function* streamAsyncIterator(\n  this: ReadableStream<Uint8Array>,\n): AsyncIterableIterator<Uint8Array> {\n  const reader = this.getReader();\n  try {\n    while (true) {\n      const { done, value } = await reader.read();\n      if (done) {\n        return;\n      }\n\n      yield value;\n    }\n  } finally {\n    reader.releaseLock();\n  }\n}\n\nfunction makeAsyncIterable<T>(webStream: any): asserts webStream is AsyncIterableReadableStream<T> {\n  if (!webStream[Symbol.asyncIterator]) {\n    webStream[Symbol.asyncIterator] = streamAsyncIterator.bind(webStream);\n  }\n\n  if (!webStream.values) {\n    webStream.values = streamAsyncIterator.bind(webStream);\n  }\n}\n\nfunction ensureNodeStream(\n  stream: ReadableStream<Uint8Array> | NodeJS.ReadableStream,\n): NodeJS.ReadableStream {\n  if (stream instanceof ReadableStream) {\n    makeAsyncIterable<Uint8Array>(stream);\n    return Readable.fromWeb(stream);\n  } else {\n    return stream;\n  }\n}\n\nfunction toStream(\n  source: ReadableStream<Uint8Array> | NodeJS.ReadableStream | Uint8Array | Blob,\n): NodeJS.ReadableStream {\n  if (source instanceof Uint8Array) {\n    return Readable.from(Buffer.from(source));\n  } else if (isBlob(source)) {\n    return ensureNodeStream(source.stream());\n  } else {\n    return ensureNodeStream(source);\n  }\n}\n\n/**\n * Accepted binary data types for concat\n *\n * @internal\n */\nexport type ConcatSource = ReadableStream<Uint8Array> | NodeJS.ReadableStream | Uint8Array | Blob;\n\n/**\n * Utility function that concatenates a set of binary inputs into one combined output.\n *\n * @param sources - array of sources for the concatenation\n * @returns - in Node, a (() =\\> NodeJS.ReadableStream) which, when read, produces a concatenation of all the inputs.\n *           In browser, returns a `Blob` representing all the concatenated inputs.\n *\n * @internal\n */\nexport async function concat(\n  sources: (ConcatSource | (() => ConcatSource))[],\n): Promise<(() => NodeJS.ReadableStream) | Blob> {\n  return function () {\n    const streams = sources.map((x) => (typeof x === \"function\" ? x() : x)).map(toStream);\n\n    return Readable.from(\n      (async function* () {\n        for (const stream of streams as NodeJS.ReadableStream[]) {\n          for await (const chunk of stream) {\n            yield chunk;\n          }\n        }\n      })(),\n    );\n  };\n}\n"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;;;;;AAElC,OAAO,EAAE,QAAQ,EAAE,MAAM,QAAQ,CAAC;AAElC,OAAO,EAAE,MAAM,EAAE,MAAM,iBAAiB,CAAC;;;;AAEzC,SAAgB,mBAAmB;;QAGjC,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;QAChC,IAAI,CAAC;YACH,MAAO,IAAI,CAAE,CAAC;gBACZ,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAA,CAAA,GAAA,wIAAA,CAAA,UAAA,EAAM,MAAM,CAAC,IAAI,EAAE,CAAA,CAAC;gBAC5C,IAAI,IAAI,EAAE,CAAC;oBACT,OAAA,MAAA,CAAA,GAAA,wIAAA,CAAA,UAAA,EAAA,KAAA,GAAO;gBACT,CAAC;gBAED,MAAA,MAAA,CAAA,GAAA,wIAAA,CAAA,UAAA,EAAM,KAAK,CAAA,CAAC;YACd,CAAC;QACH,CAAC,QAAS,CAAC;YACT,MAAM,CAAC,WAAW,EAAE,CAAC;QACvB,CAAC;IACH,CAAC;CAAA;AAED,SAAS,iBAAiB,CAAI,SAAc;IAC1C,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,aAAa,CAAC,EAAE,CAAC;QACrC,SAAS,CAAC,MAAM,CAAC,aAAa,CAAC,GAAG,mBAAmB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;IACxE,CAAC;IAED,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC;QACtB,SAAS,CAAC,MAAM,GAAG,mBAAmB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;IACzD,CAAC;AACH,CAAC;AAED,SAAS,gBAAgB,CACvB,MAA0D;IAE1D,IAAI,MAAM,YAAY,cAAc,EAAE,CAAC;QACrC,iBAAiB,CAAa,MAAM,CAAC,CAAC;QACtC,6GAAO,WAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;IAClC,CAAC,MAAM,CAAC;QACN,OAAO,MAAM,CAAC;IAChB,CAAC;AACH,CAAC;AAED,SAAS,QAAQ,CACf,MAA8E;IAE9E,IAAI,MAAM,YAAY,UAAU,EAAE,CAAC;QACjC,OAAO,iHAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;IAC5C,CAAC,MAAM,mMAAI,SAAA,AAAM,EAAC,MAAM,CAAC,EAAE,CAAC;QAC1B,OAAO,gBAAgB,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC;IAC3C,CAAC,MAAM,CAAC;QACN,OAAO,gBAAgB,CAAC,MAAM,CAAC,CAAC;IAClC,CAAC;AACH,CAAC;AAkBM,KAAK,UAAU,MAAM,CAC1B,OAAgD;IAEhD,OAAO;QACL,MAAM,OAAO,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAI,CAAF,CAAC,KAAQ,CAAC,KAAK,UAAU,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAE,AAAD,GAAI,CAAC,QAAQ,CAAC,CAAC;QAEtF,6GAAO,WAAQ,CAAC,IAAI,CAClB,AAAC;;;gBACC,KAAK,MAAM,MAAM,IAAI,OAAkC,CAAE,CAAC;;wBACxD,IAA0B,IAAA,KAAA,MAAA,WAAA,CAAA,MAAA,KAAA,gJAAA,gBAAA,EAAA,MAAM,CAAA,CAAA,EAAA,UAAA,EAAA,aAAA,MAAA,CAAA,GAAA,wIAAA,CAAA,UAAA,EAAA,SAAA,IAAA,KAAA,KAAA,WAAA,IAAA,EAAA,CAAA,IAAA,KAAA,KAAE,CAAC;4BAAT,KAAA,WAAA,KAAA,CAAM;4BAAN,KAAA,MAAM;4BAArB,MAAM,KAAK,GAAA,EAAA,CAAA;4BACpB,MAAA,MAAA,CAAA,GAAA,wIAAA,CAAA,UAAA,EAAM,KAAK,CAAA,CAAC;wBACd,CAAC;;;;;;;;;;;;gBACH,CAAC;YACH,CAAC;SAAA,CAAC,EAAE,CACL,CAAC;IACJ,CAAC,CAAC;AACJ,CAAC", "debugId": null}}, {"offset": {"line": 2469, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@typespec/ts-http-runtime/dist/esm/policies/multipartPolicy.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/node_modules/%40typespec/ts-http-runtime/src/policies/multipartPolicy.ts"], "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type { BodyPart, HttpHeaders, PipelineRequest, PipelineResponse } from \"../interfaces.js\";\nimport type { PipelinePolicy } from \"../pipeline.js\";\nimport { stringToUint8Array } from \"../util/bytesEncoding.js\";\nimport { isBlob } from \"../util/typeGuards.js\";\nimport { randomUUID } from \"../util/uuidUtils.js\";\nimport { concat } from \"../util/concat.js\";\n\nfunction generateBoundary(): string {\n  return `----AzSDKFormBoundary${randomUUID()}`;\n}\n\nfunction encodeHeaders(headers: HttpHeaders): string {\n  let result = \"\";\n  for (const [key, value] of headers) {\n    result += `${key}: ${value}\\r\\n`;\n  }\n  return result;\n}\n\nfunction getLength(\n  source:\n    | (() => ReadableStream<Uint8Array>)\n    | (() => NodeJS.ReadableStream)\n    | Uint8Array\n    | Blob\n    | ReadableStream\n    | NodeJS.ReadableStream,\n): number | undefined {\n  if (source instanceof Uint8Array) {\n    return source.byteLength;\n  } else if (isBlob(source)) {\n    // if was created using createFile then -1 means we have an unknown size\n    return source.size === -1 ? undefined : source.size;\n  } else {\n    return undefined;\n  }\n}\n\nfunction getTotalLength(\n  sources: (\n    | (() => ReadableStream<Uint8Array>)\n    | (() => NodeJS.ReadableStream)\n    | Uint8Array\n    | Blob\n    | ReadableStream\n    | NodeJS.ReadableStream\n  )[],\n): number | undefined {\n  let total = 0;\n  for (const source of sources) {\n    const partLength = getLength(source);\n    if (partLength === undefined) {\n      return undefined;\n    } else {\n      total += partLength;\n    }\n  }\n  return total;\n}\n\nasync function buildRequestBody(\n  request: PipelineRequest,\n  parts: BodyPart[],\n  boundary: string,\n): Promise<void> {\n  const sources = [\n    stringToUint8Array(`--${boundary}`, \"utf-8\"),\n    ...parts.flatMap((part) => [\n      stringToUint8Array(\"\\r\\n\", \"utf-8\"),\n      stringToUint8Array(encodeHeaders(part.headers), \"utf-8\"),\n      stringToUint8Array(\"\\r\\n\", \"utf-8\"),\n      part.body,\n      stringToUint8Array(`\\r\\n--${boundary}`, \"utf-8\"),\n    ]),\n    stringToUint8Array(\"--\\r\\n\\r\\n\", \"utf-8\"),\n  ];\n\n  const contentLength = getTotalLength(sources);\n  if (contentLength) {\n    request.headers.set(\"Content-Length\", contentLength);\n  }\n\n  request.body = await concat(sources);\n}\n\n/**\n * Name of multipart policy\n */\nexport const multipartPolicyName = \"multipartPolicy\";\n\nconst maxBoundaryLength = 70;\nconst validBoundaryCharacters = new Set(\n  `abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'()+,-./:=?`,\n);\n\nfunction assertValidBoundary(boundary: string): void {\n  if (boundary.length > maxBoundaryLength) {\n    throw new Error(`Multipart boundary \"${boundary}\" exceeds maximum length of 70 characters`);\n  }\n\n  if (Array.from(boundary).some((x) => !validBoundaryCharacters.has(x))) {\n    throw new Error(`Multipart boundary \"${boundary}\" contains invalid characters`);\n  }\n}\n\n/**\n * Pipeline policy for multipart requests\n */\nexport function multipartPolicy(): PipelinePolicy {\n  return {\n    name: multipartPolicyName,\n    async sendRequest(request, next): Promise<PipelineResponse> {\n      if (!request.multipartBody) {\n        return next(request);\n      }\n\n      if (request.body) {\n        throw new Error(\"multipartBody and regular body cannot be set at the same time\");\n      }\n\n      let boundary = request.multipartBody.boundary;\n\n      const contentTypeHeader = request.headers.get(\"Content-Type\") ?? \"multipart/mixed\";\n      const parsedHeader = contentTypeHeader.match(/^(multipart\\/[^ ;]+)(?:; *boundary=(.+))?$/);\n      if (!parsedHeader) {\n        throw new Error(\n          `Got multipart request body, but content-type header was not multipart: ${contentTypeHeader}`,\n        );\n      }\n\n      const [, contentType, parsedBoundary] = parsedHeader;\n      if (parsedBoundary && boundary && parsedBoundary !== boundary) {\n        throw new Error(\n          `Multipart boundary was specified as ${parsedBoundary} in the header, but got ${boundary} in the request body`,\n        );\n      }\n\n      boundary ??= parsedBoundary;\n      if (boundary) {\n        assertValidBoundary(boundary);\n      } else {\n        boundary = generateBoundary();\n      }\n      request.headers.set(\"Content-Type\", `${contentType}; boundary=${boundary}`);\n      await buildRequestBody(request, request.multipartBody.parts, boundary);\n\n      request.multipartBody = undefined;\n\n      return next(request);\n    },\n  };\n}\n"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;;;;;AAIlC,OAAO,EAAE,kBAAkB,EAAE,MAAM,0BAA0B,CAAC;AAC9D,OAAO,EAAE,MAAM,EAAE,MAAM,uBAAuB,CAAC;AAC/C,OAAO,EAAE,UAAU,EAAE,MAAM,sBAAsB,CAAC;AAClD,OAAO,EAAE,MAAM,EAAE,MAAM,mBAAmB,CAAC;;;;;AAE3C,SAAS,gBAAgB;IACvB,OAAO,CAAA,qBAAA,gMAAwB,aAAU,AAAV,EAAY,GAAE,CAAC;AAChD,CAAC;AAED,SAAS,aAAa,CAAC,OAAoB;IACzC,IAAI,MAAM,GAAG,EAAE,CAAC;IAChB,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,OAAO,CAAE,CAAC;QACnC,MAAM,IAAI,GAAG,GAAG,CAAA,EAAA,EAAK,KAAK,CAAA,IAAA,CAAM,CAAC;IACnC,CAAC;IACD,OAAO,MAAM,CAAC;AAChB,CAAC;AAED,SAAS,SAAS,CAChB,MAMyB;IAEzB,IAAI,MAAM,YAAY,UAAU,EAAE,CAAC;QACjC,OAAO,MAAM,CAAC,UAAU,CAAC;IAC3B,CAAC,MAAM,mMAAI,SAAA,AAAM,EAAC,MAAM,CAAC,EAAE,CAAC;QAC1B,wEAAwE;QACxE,OAAO,MAAM,CAAC,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC;IACtD,CAAC,MAAM,CAAC;QACN,OAAO,SAAS,CAAC;IACnB,CAAC;AACH,CAAC;AAED,SAAS,cAAc,CACrB,OAOG;IAEH,IAAI,KAAK,GAAG,CAAC,CAAC;IACd,KAAK,MAAM,MAAM,IAAI,OAAO,CAAE,CAAC;QAC7B,MAAM,UAAU,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC;QACrC,IAAI,UAAU,KAAK,SAAS,EAAE,CAAC;YAC7B,OAAO,SAAS,CAAC;QACnB,CAAC,MAAM,CAAC;YACN,KAAK,IAAI,UAAU,CAAC;QACtB,CAAC;IACH,CAAC;IACD,OAAO,KAAK,CAAC;AACf,CAAC;AAED,KAAK,UAAU,gBAAgB,CAC7B,OAAwB,EACxB,KAAiB,EACjB,QAAgB;IAEhB,MAAM,OAAO,GAAG;SACd,sNAAA,AAAkB,EAAC,CAAA,EAAA,EAAK,QAAQ,EAAE,EAAE,OAAO,CAAC;WACzC,KAAK,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,CAAG,CAAD;kNACxB,qBAAA,AAAkB,EAAC,MAAM,EAAE,OAAO,CAAC;oBACnC,mNAAA,AAAkB,EAAC,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,OAAO,CAAC;kNACxD,qBAAA,AAAkB,EAAC,MAAM,EAAE,OAAO,CAAC;gBACnC,IAAI,CAAC,IAAI;kNACT,qBAAA,AAAkB,EAAC,CAAA,MAAA,EAAS,QAAQ,EAAE,EAAE,OAAO,CAAC;aACjD,CAAC;0MACF,qBAAA,AAAkB,EAAC,YAAY,EAAE,OAAO,CAAC;KAC1C,CAAC;IAEF,MAAM,aAAa,GAAG,cAAc,CAAC,OAAO,CAAC,CAAC;IAC9C,IAAI,aAAa,EAAE,CAAC;QAClB,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,gBAAgB,EAAE,aAAa,CAAC,CAAC;IACvD,CAAC;IAED,OAAO,CAAC,IAAI,GAAG,iMAAM,SAAA,AAAM,EAAC,OAAO,CAAC,CAAC;AACvC,CAAC;AAKM,MAAM,mBAAmB,GAAG,iBAAiB,CAAC;AAErD,MAAM,iBAAiB,GAAG,EAAE,CAAC;AAC7B,MAAM,uBAAuB,GAAG,IAAI,GAAG,CACrC,CAAA,yEAAA,CAA2E,CAC5E,CAAC;AAEF,SAAS,mBAAmB,CAAC,QAAgB;IAC3C,IAAI,QAAQ,CAAC,MAAM,GAAG,iBAAiB,EAAE,CAAC;QACxC,MAAM,IAAI,KAAK,CAAC,CAAA,oBAAA,EAAuB,QAAQ,CAAA,yCAAA,CAA2C,CAAC,CAAC;IAC9F,CAAC;IAED,IAAI,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAG,CAAC,AAAF,uBAAyB,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACtE,MAAM,IAAI,KAAK,CAAC,CAAA,oBAAA,EAAuB,QAAQ,CAAA,6BAAA,CAA+B,CAAC,CAAC;IAClF,CAAC;AACH,CAAC;AAKK,SAAU,eAAe;IAC7B,OAAO;QACL,IAAI,EAAE,mBAAmB;QACzB,KAAK,CAAC,WAAW,EAAC,OAAO,EAAE,IAAI;;YAC7B,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,CAAC;gBAC3B,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC;YACvB,CAAC;YAED,IAAI,OAAO,CAAC,IAAI,EAAE,CAAC;gBACjB,MAAM,IAAI,KAAK,CAAC,+DAA+D,CAAC,CAAC;YACnF,CAAC;YAED,IAAI,QAAQ,GAAG,OAAO,CAAC,aAAa,CAAC,QAAQ,CAAC;YAE9C,MAAM,iBAAiB,GAAG,CAAA,KAAA,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,MAAA,QAAA,OAAA,KAAA,IAAA,KAAI,iBAAiB,CAAC;YACnF,MAAM,YAAY,GAAG,iBAAiB,CAAC,KAAK,CAAC,4CAA4C,CAAC,CAAC;YAC3F,IAAI,CAAC,YAAY,EAAE,CAAC;gBAClB,MAAM,IAAI,KAAK,CACb,CAAA,uEAAA,EAA0E,iBAAiB,EAAE,CAC9F,CAAC;YACJ,CAAC;YAED,MAAM,CAAC,EAAE,WAAW,EAAE,cAAc,CAAC,GAAG,YAAY,CAAC;YACrD,IAAI,cAAc,IAAI,QAAQ,IAAI,cAAc,KAAK,QAAQ,EAAE,CAAC;gBAC9D,MAAM,IAAI,KAAK,CACb,CAAA,oCAAA,EAAuC,cAAc,CAAA,wBAAA,EAA2B,QAAQ,CAAA,oBAAA,CAAsB,CAC/G,CAAC;YACJ,CAAC;YAED,QAAQ,KAAA,QAAR,QAAQ,KAAA,KAAA,IAAR,QAAQ,GAAR,QAAQ,GAAK,cAAc,EAAC;YAC5B,IAAI,QAAQ,EAAE,CAAC;gBACb,mBAAmB,CAAC,QAAQ,CAAC,CAAC;YAChC,CAAC,MAAM,CAAC;gBACN,QAAQ,GAAG,gBAAgB,EAAE,CAAC;YAChC,CAAC;YACD,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,EAAE,GAAG,WAAW,CAAA,WAAA,EAAc,QAAQ,EAAE,CAAC,CAAC;YAC5E,MAAM,gBAAgB,CAAC,OAAO,EAAE,OAAO,CAAC,aAAa,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;YAEvE,OAAO,CAAC,aAAa,GAAG,SAAS,CAAC;YAElC,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC;QACvB,CAAC;KACF,CAAC;AACJ,CAAC", "debugId": null}}, {"offset": {"line": 2582, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@typespec/ts-http-runtime/dist/esm/createPipelineFromOptions.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/node_modules/%40typespec/ts-http-runtime/src/createPipelineFromOptions.ts"], "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport { type LogPolicyOptions, logPolicy } from \"./policies/logPolicy.js\";\nimport { type Pipeline, createEmptyPipeline } from \"./pipeline.js\";\nimport type { Agent, PipelineRetryOptions, ProxySettings, TlsSettings } from \"./interfaces.js\";\nimport { type RedirectPolicyOptions, redirectPolicy } from \"./policies/redirectPolicy.js\";\nimport { type UserAgentPolicyOptions, userAgentPolicy } from \"./policies/userAgentPolicy.js\";\nimport { decompressResponsePolicy } from \"./policies/decompressResponsePolicy.js\";\nimport { defaultRetryPolicy } from \"./policies/defaultRetryPolicy.js\";\nimport { formDataPolicy } from \"./policies/formDataPolicy.js\";\nimport { isNodeLike } from \"./util/checkEnvironment.js\";\nimport { proxyPolicy } from \"./policies/proxyPolicy.js\";\nimport { agentPolicy } from \"./policies/agentPolicy.js\";\nimport { tlsPolicy } from \"./policies/tlsPolicy.js\";\nimport { multipartPolicy, multipartPolicyName } from \"./policies/multipartPolicy.js\";\n\n/**\n * Defines options that are used to configure the HTTP pipeline for\n * an SDK client.\n */\nexport interface PipelineOptions {\n  /**\n   * Options that control how to retry failed requests.\n   */\n  retryOptions?: PipelineRetryOptions;\n\n  /**\n   * Options to configure a proxy for outgoing requests.\n   */\n  proxyOptions?: ProxySettings;\n\n  /** Options for configuring Agent instance for outgoing requests */\n  agent?: Agent;\n\n  /** Options for configuring TLS authentication */\n  tlsOptions?: TlsSettings;\n\n  /**\n   * Options for how redirect responses are handled.\n   */\n  redirectOptions?: RedirectPolicyOptions;\n\n  /**\n   * Options for adding user agent details to outgoing requests.\n   */\n  userAgentOptions?: UserAgentPolicyOptions;\n\n  /**\n   * Options for setting common telemetry and tracing info to outgoing requests.\n   */\n  telemetryOptions?: TelemetryOptions;\n}\n\n/**\n * Defines options that are used to configure common telemetry and tracing info\n */\nexport interface TelemetryOptions {\n  /**\n   * The name of the header to pass the request ID to.\n   */\n  clientRequestIdHeaderName?: string;\n}\n\n/**\n * Defines options that are used to configure internal options of\n * the HTTP pipeline for an SDK client.\n */\nexport interface InternalPipelineOptions extends PipelineOptions {\n  /**\n   * Options to configure request/response logging.\n   */\n  loggingOptions?: LogPolicyOptions;\n}\n\n/**\n * Create a new pipeline with a default set of customizable policies.\n * @param options - Options to configure a custom pipeline.\n */\nexport function createPipelineFromOptions(options: InternalPipelineOptions): Pipeline {\n  const pipeline = createEmptyPipeline();\n\n  if (isNodeLike) {\n    if (options.agent) {\n      pipeline.addPolicy(agentPolicy(options.agent));\n    }\n    if (options.tlsOptions) {\n      pipeline.addPolicy(tlsPolicy(options.tlsOptions));\n    }\n    pipeline.addPolicy(proxyPolicy(options.proxyOptions));\n    pipeline.addPolicy(decompressResponsePolicy());\n  }\n\n  pipeline.addPolicy(formDataPolicy(), { beforePolicies: [multipartPolicyName] });\n  pipeline.addPolicy(userAgentPolicy(options.userAgentOptions));\n  // The multipart policy is added after policies with no phase, so that\n  // policies can be added between it and formDataPolicy to modify\n  // properties (e.g., making the boundary constant in recorded tests).\n  pipeline.addPolicy(multipartPolicy(), { afterPhase: \"Deserialize\" });\n  pipeline.addPolicy(defaultRetryPolicy(options.retryOptions), { phase: \"Retry\" });\n  if (isNodeLike) {\n    // Both XHR and Fetch expect to handle redirects automatically,\n    // so only include this policy when we're in Node.\n    pipeline.addPolicy(redirectPolicy(options.redirectOptions), { afterPhase: \"Retry\" });\n  }\n  pipeline.addPolicy(logPolicy(options.loggingOptions), { afterPhase: \"Sign\" });\n\n  return pipeline;\n}\n"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;;;;AAElC,OAAO,EAAyB,SAAS,EAAE,MAAM,yBAAyB,CAAC;AAC3E,OAAO,EAAiB,mBAAmB,EAAE,MAAM,eAAe,CAAC;AAEnE,OAAO,EAA8B,cAAc,EAAE,MAAM,8BAA8B,CAAC;AAC1F,OAAO,EAA+B,eAAe,EAAE,MAAM,+BAA+B,CAAC;AAC7F,OAAO,EAAE,wBAAwB,EAAE,MAAM,wCAAwC,CAAC;AAClF,OAAO,EAAE,kBAAkB,EAAE,MAAM,kCAAkC,CAAC;AACtE,OAAO,EAAE,cAAc,EAAE,MAAM,8BAA8B,CAAC;AAC9D,OAAO,EAAE,UAAU,EAAE,MAAM,4BAA4B,CAAC;AACxD,OAAO,EAAE,WAAW,EAAE,MAAM,2BAA2B,CAAC;AACxD,OAAO,EAAE,WAAW,EAAE,MAAM,2BAA2B,CAAC;AACxD,OAAO,EAAE,SAAS,EAAE,MAAM,yBAAyB,CAAC;AACpD,OAAO,EAAE,eAAe,EAAE,mBAAmB,EAAE,MAAM,+BAA+B,CAAC;;;;;;;;;;;;;AAgE/E,SAAU,yBAAyB,CAAC,OAAgC;IACxE,MAAM,QAAQ,wLAAG,sBAAA,AAAmB,EAAE,CAAC;IAEvC,qMAAI,aAAU,EAAE,CAAC;QACf,IAAI,OAAO,CAAC,KAAK,EAAE,CAAC;YAClB,QAAQ,CAAC,SAAS,qMAAC,cAAA,AAAW,EAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC;QACjD,CAAC;QACD,IAAI,OAAO,CAAC,UAAU,EAAE,CAAC;YACvB,QAAQ,CAAC,SAAS,CAAC,8MAAA,AAAS,EAAC,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC;QACpD,CAAC;QACD,QAAQ,CAAC,SAAS,qMAAC,cAAA,AAAW,EAAC,OAAO,CAAC,YAAY,CAAC,CAAC,CAAC;QACtD,QAAQ,CAAC,SAAS,EAAC,2OAAA,AAAwB,EAAE,CAAC,CAAC;IACjD,CAAC;IAED,QAAQ,CAAC,SAAS,wMAAC,iBAAA,AAAc,EAAE,GAAE;QAAE,cAAc,EAAE;gNAAC,sBAAmB;SAAC;IAAA,CAAE,CAAC,CAAC;IAChF,QAAQ,CAAC,SAAS,CAAC,0NAAA,AAAe,EAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC,CAAC;IAC9D,sEAAsE;IACtE,gEAAgE;IAChE,qEAAqE;IACrE,QAAQ,CAAC,SAAS,yMAAC,kBAAA,AAAe,EAAE,GAAE;QAAE,UAAU,EAAE,aAAa;IAAA,CAAE,CAAC,CAAC;IACrE,QAAQ,CAAC,SAAS,4MAAC,qBAAA,AAAkB,EAAC,OAAO,CAAC,YAAY,CAAC,EAAE;QAAE,KAAK,EAAE,OAAO;IAAA,CAAE,CAAC,CAAC;IACjF,qMAAI,aAAU,EAAE,CAAC;QACf,+DAA+D;QAC/D,kDAAkD;QAClD,QAAQ,CAAC,SAAS,wMAAC,iBAAA,AAAc,EAAC,OAAO,CAAC,eAAe,CAAC,EAAE;YAAE,UAAU,EAAE,OAAO;QAAA,CAAE,CAAC,CAAC;IACvF,CAAC;IACD,QAAQ,CAAC,SAAS,mMAAC,YAAA,AAAS,EAAC,OAAO,CAAC,cAAc,CAAC,EAAE;QAAE,UAAU,EAAE,MAAM;IAAA,CAAE,CAAC,CAAC;IAE9E,OAAO,QAAQ,CAAC;AAClB,CAAC", "debugId": null}}, {"offset": {"line": 2654, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@typespec/ts-http-runtime/dist/esm/client/apiVersionPolicy.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/node_modules/%40typespec/ts-http-runtime/src/client/apiVersionPolicy.ts"], "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type { PipelinePolicy } from \"../pipeline.js\";\nimport type { ClientOptions } from \"./common.js\";\n\nexport const apiVersionPolicyName = \"ApiVersionPolicy\";\n\n/**\n * Creates a policy that sets the apiVersion as a query parameter on every request\n * @param options - Client options\n * @returns Pipeline policy that sets the apiVersion as a query parameter on every request\n */\nexport function apiVersionPolicy(options: ClientOptions): PipelinePolicy {\n  return {\n    name: apiVersionPolicyName,\n    sendRequest: (req, next) => {\n      // Use the apiVesion defined in request url directly\n      // Append one if there is no apiVesion and we have one at client options\n      const url = new URL(req.url);\n      if (!url.searchParams.get(\"api-version\") && options.apiVersion) {\n        req.url = `${req.url}${\n          Array.from(url.searchParams.keys()).length > 0 ? \"&\" : \"?\"\n        }api-version=${options.apiVersion}`;\n      }\n\n      return next(req);\n    },\n  };\n}\n"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;;;;;AAK3B,MAAM,oBAAoB,GAAG,kBAAkB,CAAC;AAOjD,SAAU,gBAAgB,CAAC,OAAsB;IACrD,OAAO;QACL,IAAI,EAAE,oBAAoB;QAC1B,WAAW,EAAE,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE;YACzB,oDAAoD;YACpD,wEAAwE;YACxE,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;YAC7B,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,aAAa,CAAC,IAAI,OAAO,CAAC,UAAU,EAAE,CAAC;gBAC/D,GAAG,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC,GAAG,GAClB,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,IAAI,EAAE,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GACzD,CAAA,YAAA,EAAe,OAAO,CAAC,UAAU,EAAE,CAAC;YACtC,CAAC;YAED,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC;QACnB,CAAC;KACF,CAAC;AACJ,CAAC", "debugId": null}}, {"offset": {"line": 2679, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@typespec/ts-http-runtime/dist/esm/auth/credentials.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/node_modules/%40typespec/ts-http-runtime/src/auth/credentials.ts"], "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type { OAuth2Flow } from \"./oauth2Flows.js\";\n\n/**\n * Options used when creating and sending get OAuth 2 requests for this operation.\n */\nexport interface GetOAuth2TokenOptions {\n  /** Abort signal for the request */\n  abortSignal?: AbortSignal;\n}\n\n/**\n * Options used when creating and sending get bearer token requests for this operation.\n */\nexport interface GetBearerTokenOptions {\n  /** Abort signal for the request */\n  abortSignal?: AbortSignal;\n}\n\n/**\n * Credential for OAuth2 authentication flows.\n */\nexport interface OAuth2TokenCredential<TFlows extends OAuth2Flow> {\n  /**\n   * Gets an OAuth2 token for the specified flows.\n   * @param flows - The OAuth2 flows to use.\n   * @param options - Options for the request.\n   * @returns - a valid access token which was obtained through one of the flows specified in `flows`.\n   */\n  getOAuth2Token(flows: TFlows[], options?: GetOAuth2TokenOptions): Promise<string>;\n}\n\n/**\n * Credential for Bearer token authentication.\n */\nexport interface BearerTokenCredential {\n  /**\n   * Gets a Bearer token for the specified flows.\n   * @param options - Options for the request.\n   * @returns - a valid access token.\n   */\n  getBearerToken(options?: GetBearerTokenOptions): Promise<string>;\n}\n\n/**\n * Credential for HTTP Basic authentication.\n * Provides username and password for basic authentication headers.\n */\nexport interface BasicCredential {\n  /** The username for basic authentication. */\n  username: string;\n  /** The password for basic authentication. */\n  password: string;\n}\n\n/**\n * Credential for API Key authentication.\n * Provides an API key that will be used in the request headers.\n */\nexport interface ApiKeyCredential {\n  /** The API key for authentication. */\n  key: string;\n}\n\n/**\n * Union type of all supported authentication credentials.\n */\nexport type ClientCredential =\n  | OAuth2TokenCredential<OAuth2Flow>\n  | BearerTokenCredential\n  | BasicCredential\n  | ApiKeyCredential;\n\n/**\n * Type guard to check if a credential is an OAuth2 token credential.\n */\nexport function isOAuth2TokenCredential(\n  credential: ClientCredential,\n): credential is OAuth2TokenCredential<OAuth2Flow> {\n  return \"getOAuth2Token\" in credential;\n}\n\n/**\n * Type guard to check if a credential is a Bearer token credential.\n */\nexport function isBearerTokenCredential(\n  credential: ClientCredential,\n): credential is BearerTokenCredential {\n  return \"getBearerToken\" in credential;\n}\n\n/**\n * Type guard to check if a credential is a Basic auth credential.\n */\nexport function isBasicCredential(credential: ClientCredential): credential is BasicCredential {\n  return \"username\" in credential && \"password\" in credential;\n}\n\n/**\n * Type guard to check if a credential is an API key credential.\n */\nexport function isApiKeyCredential(credential: ClientCredential): credential is ApiKeyCredential {\n  return \"key\" in credential;\n}\n"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AA0ElC;;GAEG;;;;;;AACG,SAAU,uBAAuB,CACrC,UAA4B;IAE5B,OAAO,gBAAgB,IAAI,UAAU,CAAC;AACxC,CAAC;AAKK,SAAU,uBAAuB,CACrC,UAA4B;IAE5B,OAAO,gBAAgB,IAAI,UAAU,CAAC;AACxC,CAAC;AAKK,SAAU,iBAAiB,CAAC,UAA4B;IAC5D,OAAO,UAAU,IAAI,UAAU,IAAI,UAAU,IAAI,UAAU,CAAC;AAC9D,CAAC;AAKK,SAAU,kBAAkB,CAAC,UAA4B;IAC7D,OAAO,KAAK,IAAI,UAAU,CAAC;AAC7B,CAAC", "debugId": null}}, {"offset": {"line": 2705, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@typespec/ts-http-runtime/dist/esm/policies/auth/checkInsecureConnection.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/node_modules/%40typespec/ts-http-runtime/src/policies/auth/checkInsecureConnection.ts"], "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type { PipelineRequest } from \"../../interfaces.js\";\nimport { logger } from \"../../log.js\";\n\n// Ensure the warining is only emitted once\nlet insecureConnectionWarningEmmitted = false;\n\n/**\n * Checks if the request is allowed to be sent over an insecure connection.\n *\n * A request is allowed to be sent over an insecure connection when:\n * - The `allowInsecureConnection` option is set to `true`.\n * - The request has the `allowInsecureConnection` property set to `true`.\n * - The request is being sent to `localhost` or `127.0.0.1`\n */\nfunction allowInsecureConnection(\n  request: PipelineRequest,\n  options: { allowInsecureConnection?: boolean },\n): boolean {\n  if (options.allowInsecureConnection && request.allowInsecureConnection) {\n    const url = new URL(request.url);\n    if (url.hostname === \"localhost\" || url.hostname === \"127.0.0.1\") {\n      return true;\n    }\n  }\n\n  return false;\n}\n\n/**\n * Logs a warning about sending a token over an insecure connection.\n *\n * This function will emit a node warning once, but log the warning every time.\n */\nfunction emitInsecureConnectionWarning(): void {\n  const warning = \"Sending token over insecure transport. Assume any token issued is compromised.\";\n\n  logger.warning(warning);\n\n  if (typeof process?.emitWarning === \"function\" && !insecureConnectionWarningEmmitted) {\n    insecureConnectionWarningEmmitted = true;\n    process.emitWarning(warning);\n  }\n}\n\n/**\n * Ensures that authentication is only allowed over HTTPS unless explicitly allowed.\n * Throws an error if the connection is not secure and not explicitly allowed.\n */\nexport function ensureSecureConnection(\n  request: PipelineRequest,\n  options: { allowInsecureConnection?: boolean },\n): void {\n  if (!request.url.toLowerCase().startsWith(\"https://\")) {\n    if (allowInsecureConnection(request, options)) {\n      emitInsecureConnectionWarning();\n    } else {\n      throw new Error(\n        \"Authentication is not permitted for non-TLS protected (non-https) URLs when allowInsecureConnection is false.\",\n      );\n    }\n  }\n}\n"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;;;;AAGlC,OAAO,EAAE,MAAM,EAAE,MAAM,cAAc,CAAC;;AAEtC,2CAA2C;AAC3C,IAAI,iCAAiC,GAAG,KAAK,CAAC;AAE9C;;;;;;;GAOG,CACH,SAAS,uBAAuB,CAC9B,OAAwB,EACxB,OAA8C;IAE9C,IAAI,OAAO,CAAC,uBAAuB,IAAI,OAAO,CAAC,uBAAuB,EAAE,CAAC;QACvE,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;QACjC,IAAI,GAAG,CAAC,QAAQ,KAAK,WAAW,IAAI,GAAG,CAAC,QAAQ,KAAK,WAAW,EAAE,CAAC;YACjE,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC;IAED,OAAO,KAAK,CAAC;AACf,CAAC;AAED;;;;GAIG,CACH,SAAS,6BAA6B;IACpC,MAAM,OAAO,GAAG,gFAAgF,CAAC;gLAEjG,SAAM,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;IAExB,IAAI,OAAO,CAAA,OAAO,KAAA,QAAP,OAAO,KAAA,KAAA,IAAA,KAAA,IAAP,OAAO,CAAE,WAAW,CAAA,KAAK,UAAU,IAAI,CAAC,iCAAiC,EAAE,CAAC;QACrF,iCAAiC,GAAG,IAAI,CAAC;QACzC,OAAO,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;IAC/B,CAAC;AACH,CAAC;AAMK,SAAU,sBAAsB,CACpC,OAAwB,EACxB,OAA8C;IAE9C,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,WAAW,EAAE,CAAC,UAAU,CAAC,UAAU,CAAC,EAAE,CAAC;QACtD,IAAI,uBAAuB,CAAC,OAAO,EAAE,OAAO,CAAC,EAAE,CAAC;YAC9C,6BAA6B,EAAE,CAAC;QAClC,CAAC,MAAM,CAAC;YACN,MAAM,IAAI,KAAK,CACb,+GAA+G,CAChH,CAAC;QACJ,CAAC;IACH,CAAC;AACH,CAAC", "debugId": null}}, {"offset": {"line": 2755, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@typespec/ts-http-runtime/dist/esm/policies/auth/apiKeyAuthenticationPolicy.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/node_modules/%40typespec/ts-http-runtime/src/policies/auth/apiKeyAuthenticationPolicy.ts"], "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type { ApiKeyCredential } from \"../../auth/credentials.js\";\nimport type { AuthScheme } from \"../../auth/schemes.js\";\nimport type { PipelineRequest, PipelineResponse, SendRequest } from \"../../interfaces.js\";\nimport type { PipelinePolicy } from \"../../pipeline.js\";\nimport { ensureSecureConnection } from \"./checkInsecureConnection.js\";\n\n/**\n * Name of the API Key Authentication Policy\n */\nexport const apiKeyAuthenticationPolicyName = \"apiKeyAuthenticationPolicy\";\n\n/**\n * Options for configuring the API key authentication policy\n */\nexport interface ApiKeyAuthenticationPolicyOptions {\n  /**\n   * The credential used to authenticate requests\n   */\n  credential: ApiKeyCredential;\n  /**\n   * Optional authentication schemes to use. If `authSchemes` is provided in both request and policy options, the request options will take precedence.\n   */\n  authSchemes?: AuthScheme[];\n  /**\n   * Allows for connecting to HTTP endpoints instead of enforcing HTTPS.\n   * CAUTION: Never use this option in production.\n   */\n  allowInsecureConnection?: boolean;\n}\n\n/**\n * Gets a pipeline policy that adds API key authentication to requests\n */\nexport function apiKeyAuthenticationPolicy(\n  options: ApiKeyAuthenticationPolicyOptions,\n): PipelinePolicy {\n  return {\n    name: apiKeyAuthenticationPolicyName,\n    async sendRequest(request: PipelineRequest, next: SendRequest): Promise<PipelineResponse> {\n      // Ensure allowInsecureConnection is explicitly set when sending request to non-https URLs\n      ensureSecureConnection(request, options);\n\n      const scheme = (request.authSchemes ?? options.authSchemes)?.find((x) => x.kind === \"apiKey\");\n\n      // Skip adding authentication header if no API key authentication scheme is found\n      if (!scheme) {\n        return next(request);\n      }\n      if (scheme.apiKeyLocation !== \"header\") {\n        throw new Error(`Unsupported API key location: ${scheme.apiKeyLocation}`);\n      }\n\n      request.headers.set(scheme.name, options.credential.key);\n      return next(request);\n    },\n  };\n}\n"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;;;;;AAMlC,OAAO,EAAE,sBAAsB,EAAE,MAAM,8BAA8B,CAAC;;AAK/D,MAAM,8BAA8B,GAAG,4BAA4B,CAAC;AAwBrE,SAAU,0BAA0B,CACxC,OAA0C;IAE1C,OAAO;QACL,IAAI,EAAE,8BAA8B;QACpC,KAAK,CAAC,WAAW,EAAC,OAAwB,EAAE,IAAiB;;YAC3D,0FAA0F;aAC1F,gPAAA,AAAsB,EAAC,OAAO,EAAE,OAAO,CAAC,CAAC;YAEzC,MAAM,MAAM,GAAG,CAAA,KAAA,AAAC,CAAA,KAAA,OAAO,CAAC,WAAW,MAAA,QAAA,OAAA,KAAA,IAAA,KAAI,OAAO,CAAC,WAAY,AAAD,MAAC,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAE,IAAI,CAAC,CAAC,CAAC,EAAE,CAAG,CAAD,AAAE,CAAC,IAAI,KAAK,QAAQ,CAAC,CAAC;YAE9F,iFAAiF;YACjF,IAAI,CAAC,MAAM,EAAE,CAAC;gBACZ,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC;YACvB,CAAC;YACD,IAAI,MAAM,CAAC,cAAc,KAAK,QAAQ,EAAE,CAAC;gBACvC,MAAM,IAAI,KAAK,CAAC,CAAA,8BAAA,EAAiC,MAAM,CAAC,cAAc,EAAE,CAAC,CAAC;YAC5E,CAAC;YAED,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,EAAE,OAAO,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;YACzD,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC;QACvB,CAAC;KACF,CAAC;AACJ,CAAC", "debugId": null}}, {"offset": {"line": 2788, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@typespec/ts-http-runtime/dist/esm/policies/auth/basicAuthenticationPolicy.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/node_modules/%40typespec/ts-http-runtime/src/policies/auth/basicAuthenticationPolicy.ts"], "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type { BasicCredential } from \"../../auth/credentials.js\";\nimport type { AuthScheme } from \"../../auth/schemes.js\";\nimport type { PipelineRequest, PipelineResponse, SendRequest } from \"../../interfaces.js\";\nimport type { PipelinePolicy } from \"../../pipeline.js\";\nimport { stringToUint8Array, uint8ArrayToString } from \"../../util/bytesEncoding.js\";\nimport { ensureSecureConnection } from \"./checkInsecureConnection.js\";\n\n/**\n * Name of the Basic Authentication Policy\n */\nexport const basicAuthenticationPolicyName = \"bearerAuthenticationPolicy\";\n\n/**\n * Options for configuring the basic authentication policy\n */\nexport interface BasicAuthenticationPolicyOptions {\n  /**\n   * The credential used to authenticate requests\n   */\n  credential: BasicCredential;\n  /**\n   * Optional authentication schemes to use. If not provided, schemes from the request will be used.\n   */\n  authSchemes?: AuthScheme[];\n  /**\n   * Allows for connecting to HTTP endpoints instead of enforcing HTTPS.\n   * CAUTION: Never use this option in production.\n   */\n  allowInsecureConnection?: boolean;\n}\n\n/**\n * Gets a pipeline policy that adds basic authentication to requests\n */\nexport function basicAuthenticationPolicy(\n  options: BasicAuthenticationPolicyOptions,\n): PipelinePolicy {\n  return {\n    name: basicAuthenticationPolicyName,\n    async sendRequest(request: PipelineRequest, next: SendRequest): Promise<PipelineResponse> {\n      // Ensure allowInsecureConnection is explicitly set when sending request to non-https URLs\n      ensureSecureConnection(request, options);\n\n      const scheme = (request.authSchemes ?? options.authSchemes)?.find(\n        (x) => x.kind === \"http\" && x.scheme === \"basic\",\n      );\n\n      // Skip adding authentication header if no basic authentication scheme is found\n      if (!scheme) {\n        return next(request);\n      }\n\n      const { username, password } = options.credential;\n      const headerValue = uint8ArrayToString(\n        stringToUint8Array(`${username}:${password}`, \"utf-8\"),\n        \"base64\",\n      );\n      request.headers.set(\"Authorization\", `Basic ${headerValue}`);\n      return next(request);\n    },\n  };\n}\n"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;;;;;AAMlC,OAAO,EAAE,kBAAkB,EAAE,kBAAkB,EAAE,MAAM,6BAA6B,CAAC;AACrF,OAAO,EAAE,sBAAsB,EAAE,MAAM,8BAA8B,CAAC;;;AAK/D,MAAM,6BAA6B,GAAG,4BAA4B,CAAC;AAwBpE,SAAU,yBAAyB,CACvC,OAAyC;IAEzC,OAAO;QACL,IAAI,EAAE,6BAA6B;QACnC,KAAK,CAAC,WAAW,EAAC,OAAwB,EAAE,IAAiB;;YAC3D,0FAA0F;oOAC1F,yBAAA,AAAsB,EAAC,OAAO,EAAE,OAAO,CAAC,CAAC;YAEzC,MAAM,MAAM,GAAG,CAAA,KAAA,AAAC,CAAA,KAAA,OAAO,CAAC,WAAW,MAAA,QAAA,OAAA,KAAA,IAAA,KAAI,OAAO,CAAC,WAAW,AAAC,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAE,IAAI,CAC/D,CAAC,CAAC,EAAE,CAAG,CAAD,AAAE,CAAC,IAAI,KAAK,MAAM,IAAI,CAAC,CAAC,MAAM,KAAK,OAAO,CACjD,CAAC;YAEF,+EAA+E;YAC/E,IAAI,CAAC,MAAM,EAAE,CAAC;gBACZ,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC;YACvB,CAAC;YAED,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,OAAO,CAAC,UAAU,CAAC;YAClD,MAAM,WAAW,qMAAG,qBAAA,AAAkB,GACpC,sNAAA,AAAkB,EAAC,GAAG,QAAQ,CAAA,CAAA,EAAI,QAAQ,EAAE,EAAE,OAAO,CAAC,EACtD,QAAQ,CACT,CAAC;YACF,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,eAAe,EAAE,CAAA,MAAA,EAAS,WAAW,EAAE,CAAC,CAAC;YAC7D,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC;QACvB,CAAC;KACF,CAAC;AACJ,CAAC", "debugId": null}}, {"offset": {"line": 2822, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@typespec/ts-http-runtime/dist/esm/policies/auth/bearerAuthenticationPolicy.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/node_modules/%40typespec/ts-http-runtime/src/policies/auth/bearerAuthenticationPolicy.ts"], "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type { BearerTokenCredential } from \"../../auth/credentials.js\";\nimport type { AuthScheme } from \"../../auth/schemes.js\";\nimport type { PipelineRequest, PipelineResponse, SendRequest } from \"../../interfaces.js\";\nimport type { PipelinePolicy } from \"../../pipeline.js\";\nimport { ensureSecureConnection } from \"./checkInsecureConnection.js\";\n\n/**\n * Name of the Bearer Authentication Policy\n */\nexport const bearerAuthenticationPolicyName = \"bearerAuthenticationPolicy\";\n\n/**\n * Options for configuring the bearer authentication policy\n */\nexport interface BearerAuthenticationPolicyOptions {\n  /**\n   * The BearerTokenCredential implementation that can supply the bearer token.\n   */\n  credential: BearerTokenCredential;\n  /**\n   * Optional authentication schemes to use. If not provided, schemes from the request will be used.\n   */\n  authSchemes?: AuthScheme[];\n  /**\n   * Allows for connecting to HTTP endpoints instead of enforcing HTTPS.\n   * CAUTION: Never use this option in production.\n   */\n  allowInsecureConnection?: boolean;\n}\n\n/**\n * Gets a pipeline policy that adds bearer token authentication to requests\n */\nexport function bearerAuthenticationPolicy(\n  options: BearerAuthenticationPolicyOptions,\n): PipelinePolicy {\n  return {\n    name: bearerAuthenticationPolicyName,\n    async sendRequest(request: PipelineRequest, next: SendRequest): Promise<PipelineResponse> {\n      // Ensure allowInsecureConnection is explicitly set when sending request to non-https URLs\n      ensureSecureConnection(request, options);\n\n      const scheme = (request.authSchemes ?? options.authSchemes)?.find(\n        (x) => x.kind === \"http\" && x.scheme === \"bearer\",\n      );\n\n      // Skip adding authentication header if no bearer authentication scheme is found\n      if (!scheme) {\n        return next(request);\n      }\n\n      const token = await options.credential.getBearerToken({\n        abortSignal: request.abortSignal,\n      });\n      request.headers.set(\"Authorization\", `Bearer ${token}`);\n      return next(request);\n    },\n  };\n}\n"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;;;;;AAMlC,OAAO,EAAE,sBAAsB,EAAE,MAAM,8BAA8B,CAAC;;AAK/D,MAAM,8BAA8B,GAAG,4BAA4B,CAAC;AAwBrE,SAAU,0BAA0B,CACxC,OAA0C;IAE1C,OAAO;QACL,IAAI,EAAE,8BAA8B;QACpC,KAAK,CAAC,WAAW,EAAC,OAAwB,EAAE,IAAiB;;YAC3D,0FAA0F;aAC1F,gPAAA,AAAsB,EAAC,OAAO,EAAE,OAAO,CAAC,CAAC;YAEzC,MAAM,MAAM,GAAG,CAAA,KAAA,AAAC,CAAA,KAAA,OAAO,CAAC,WAAW,MAAA,QAAA,OAAA,KAAA,IAAA,KAAI,OAAO,CAAC,WAAY,AAAD,MAAC,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAE,IAAI,CAC/D,CAAC,CAAC,EAAE,CAAG,CAAD,AAAE,CAAC,IAAI,KAAK,MAAM,IAAI,CAAC,CAAC,MAAM,KAAK,QAAQ,CAClD,CAAC;YAEF,gFAAgF;YAChF,IAAI,CAAC,MAAM,EAAE,CAAC;gBACZ,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC;YACvB,CAAC;YAED,MAAM,KAAK,GAAG,MAAM,OAAO,CAAC,UAAU,CAAC,cAAc,CAAC;gBACpD,WAAW,EAAE,OAAO,CAAC,WAAW;aACjC,CAAC,CAAC;YACH,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,eAAe,EAAE,CAAA,OAAA,EAAU,KAAK,EAAE,CAAC,CAAC;YACxD,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC;QACvB,CAAC;KACF,CAAC;AACJ,CAAC", "debugId": null}}, {"offset": {"line": 2855, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@typespec/ts-http-runtime/dist/esm/policies/auth/oauth2AuthenticationPolicy.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/node_modules/%40typespec/ts-http-runtime/src/policies/auth/oauth2AuthenticationPolicy.ts"], "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type { OAuth2Flow } from \"../../auth/oauth2Flows.js\";\nimport type { OAuth2TokenCredential } from \"../../auth/credentials.js\";\nimport type { AuthScheme } from \"../../auth/schemes.js\";\nimport type { PipelineRequest, PipelineResponse, SendRequest } from \"../../interfaces.js\";\nimport type { PipelinePolicy } from \"../../pipeline.js\";\nimport { ensureSecureConnection } from \"./checkInsecureConnection.js\";\n\n/**\n * Name of the OAuth2 Authentication Policy\n */\nexport const oauth2AuthenticationPolicyName = \"oauth2AuthenticationPolicy\";\n\n/**\n * Options for configuring the OAuth2 authentication policy\n */\nexport interface OAuth2AuthenticationPolicyOptions<TFlows extends OAuth2Flow> {\n  /**\n   * The OAuth2TokenCredential implementation that can supply the bearer token.\n   */\n  credential: OAuth2TokenCredential<TFlows>;\n  /**\n   * Optional authentication schemes to use. If not provided, schemes from the request will be used.\n   */\n  authSchemes?: AuthScheme[];\n  /**\n   * Allows for connecting to HTTP endpoints instead of enforcing HTTPS.\n   * CAUTION: Never use this option in production.\n   */\n  allowInsecureConnection?: boolean;\n}\n\n/**\n * Gets a pipeline policy that adds authorization header from OAuth2 schemes\n */\nexport function oauth2AuthenticationPolicy<TFlows extends OAuth2Flow>(\n  options: OAuth2AuthenticationPolicyOptions<TFlows>,\n): PipelinePolicy {\n  return {\n    name: oauth2AuthenticationPolicyName,\n    async sendRequest(request: PipelineRequest, next: SendRequest): Promise<PipelineResponse> {\n      // Ensure allowInsecureConnection is explicitly set when sending request to non-https URLs\n      ensureSecureConnection(request, options);\n\n      const scheme = (request.authSchemes ?? options.authSchemes)?.find((x) => x.kind === \"oauth2\");\n\n      // Skip adding authentication header if no OAuth2 authentication scheme is found\n      if (!scheme) {\n        return next(request);\n      }\n      const token = await options.credential.getOAuth2Token(scheme.flows as TFlows[], {\n        abortSignal: request.abortSignal,\n      });\n      request.headers.set(\"Authorization\", `Bearer ${token}`);\n      return next(request);\n    },\n  };\n}\n"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;;;;;AAOlC,OAAO,EAAE,sBAAsB,EAAE,MAAM,8BAA8B,CAAC;;AAK/D,MAAM,8BAA8B,GAAG,4BAA4B,CAAC;AAwBrE,SAAU,0BAA0B,CACxC,OAAkD;IAElD,OAAO;QACL,IAAI,EAAE,8BAA8B;QACpC,KAAK,CAAC,WAAW,EAAC,OAAwB,EAAE,IAAiB;;YAC3D,0FAA0F;aAC1F,gPAAA,AAAsB,EAAC,OAAO,EAAE,OAAO,CAAC,CAAC;YAEzC,MAAM,MAAM,GAAG,CAAA,KAAA,AAAC,CAAA,KAAA,OAAO,CAAC,WAAW,MAAA,QAAA,OAAA,KAAA,IAAA,KAAI,OAAO,CAAC,WAAY,AAAD,MAAC,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAE,IAAI,CAAC,CAAC,CAAC,EAAE,CAAG,CAAD,AAAE,CAAC,IAAI,KAAK,QAAQ,CAAC,CAAC;YAE9F,gFAAgF;YAChF,IAAI,CAAC,MAAM,EAAE,CAAC;gBACZ,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC;YACvB,CAAC;YACD,MAAM,KAAK,GAAG,MAAM,OAAO,CAAC,UAAU,CAAC,cAAc,CAAC,MAAM,CAAC,KAAiB,EAAE;gBAC9E,WAAW,EAAE,OAAO,CAAC,WAAW;aACjC,CAAC,CAAC;YACH,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,eAAe,EAAE,CAAA,OAAA,EAAU,KAAK,EAAE,CAAC,CAAC;YACxD,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC;QACvB,CAAC;KACF,CAAC;AACJ,CAAC", "debugId": null}}, {"offset": {"line": 2888, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@typespec/ts-http-runtime/dist/esm/client/clientHelpers.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/node_modules/%40typespec/ts-http-runtime/src/client/clientHelpers.ts"], "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type { HttpClient } from \"../interfaces.js\";\nimport type { Pipeline } from \"../pipeline.js\";\nimport { createDefaultHttpClient } from \"../defaultHttpClient.js\";\nimport { createPipelineFromOptions } from \"../createPipelineFromOptions.js\";\nimport type { ClientOptions } from \"./common.js\";\nimport { apiVersionPolicy } from \"./apiVersionPolicy.js\";\nimport {\n  isApiKeyCredential,\n  isBasicCredential,\n  isBearerTokenCredential,\n  isOAuth2TokenCredential,\n} from \"../auth/credentials.js\";\nimport { apiKeyAuthenticationPolicy } from \"../policies/auth/apiKeyAuthenticationPolicy.js\";\nimport { basicAuthenticationPolicy } from \"../policies/auth/basicAuthenticationPolicy.js\";\nimport { bearerAuthenticationPolicy } from \"../policies/auth/bearerAuthenticationPolicy.js\";\nimport { oauth2AuthenticationPolicy } from \"../policies/auth/oauth2AuthenticationPolicy.js\";\n\nlet cachedHttpClient: HttpClient | undefined;\n\n/**\n * Creates a default rest pipeline to re-use accross Rest Level Clients\n */\nexport function createDefaultPipeline(options: ClientOptions = {}): Pipeline {\n  const pipeline = createPipelineFromOptions(options);\n\n  pipeline.addPolicy(apiVersionPolicy(options));\n\n  const { credential, authSchemes, allowInsecureConnection } = options;\n  if (credential) {\n    if (isApiKeyCredential(credential)) {\n      pipeline.addPolicy(\n        apiKeyAuthenticationPolicy({ authSchemes, credential, allowInsecureConnection }),\n      );\n    } else if (isBasicCredential(credential)) {\n      pipeline.addPolicy(\n        basicAuthenticationPolicy({ authSchemes, credential, allowInsecureConnection }),\n      );\n    } else if (isBearerTokenCredential(credential)) {\n      pipeline.addPolicy(\n        bearerAuthenticationPolicy({ authSchemes, credential, allowInsecureConnection }),\n      );\n    } else if (isOAuth2TokenCredential(credential)) {\n      pipeline.addPolicy(\n        oauth2AuthenticationPolicy({ authSchemes, credential, allowInsecureConnection }),\n      );\n    }\n  }\n\n  return pipeline;\n}\n\nexport function getCachedDefaultHttpsClient(): HttpClient {\n  if (!cachedHttpClient) {\n    cachedHttpClient = createDefaultHttpClient();\n  }\n\n  return cachedHttpClient;\n}\n"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;;;;;AAIlC,OAAO,EAAE,uBAAuB,EAAE,MAAM,yBAAyB,CAAC;AAClE,OAAO,EAAE,yBAAyB,EAAE,MAAM,iCAAiC,CAAC;AAE5E,OAAO,EAAE,gBAAgB,EAAE,MAAM,uBAAuB,CAAC;AACzD,OAAO,EACL,kBAAkB,EAClB,iBAAiB,EACjB,uBAAuB,EACvB,uBAAuB,GACxB,MAAM,wBAAwB,CAAC;AAChC,OAAO,EAAE,0BAA0B,EAAE,MAAM,gDAAgD,CAAC;AAC5F,OAAO,EAAE,yBAAyB,EAAE,MAAM,+CAA+C,CAAC;AAC1F,OAAO,EAAE,0BAA0B,EAAE,MAAM,gDAAgD,CAAC;AAC5F,OAAO,EAAE,0BAA0B,EAAE,MAAM,gDAAgD,CAAC;;;;;;;;;AAE5F,IAAI,gBAAwC,CAAC;AAKvC,SAAU,qBAAqB,CAAC,UAAyB,CAAA,CAAE;IAC/D,MAAM,QAAQ,OAAG,8NAAA,AAAyB,EAAC,OAAO,CAAC,CAAC;IAEpD,QAAQ,CAAC,SAAS,wMAAC,mBAAA,AAAgB,EAAC,OAAO,CAAC,CAAC,CAAC;IAE9C,MAAM,EAAE,UAAU,EAAE,WAAW,EAAE,uBAAuB,EAAE,GAAG,OAAO,CAAC;IACrE,IAAI,UAAU,EAAE,CAAC;QACf,oMAAI,qBAAA,AAAkB,EAAC,UAAU,CAAC,EAAE,CAAC;YACnC,QAAQ,CAAC,SAAS,EAChB,uPAAA,AAA0B,EAAC;gBAAE,WAAW;gBAAE,UAAU;gBAAE,uBAAuB;YAAA,CAAE,CAAC,CACjF,CAAC;QACJ,CAAC,MAAM,oMAAI,oBAAA,AAAiB,EAAC,UAAU,CAAC,EAAE,CAAC;YACzC,QAAQ,CAAC,SAAS,EAChB,qPAAA,AAAyB,EAAC;gBAAE,WAAW;gBAAE,UAAU;gBAAE,uBAAuB;YAAA,CAAE,CAAC,CAChF,CAAC;QACJ,CAAC,MAAM,oMAAI,0BAAA,AAAuB,EAAC,UAAU,CAAC,EAAE,CAAC;YAC/C,QAAQ,CAAC,SAAS,EAChB,uPAAA,AAA0B,EAAC;gBAAE,WAAW;gBAAE,UAAU;gBAAE,uBAAuB;YAAA,CAAE,CAAC,CACjF,CAAC;QACJ,CAAC,MAAM,oMAAI,0BAAA,AAAuB,EAAC,UAAU,CAAC,EAAE,CAAC;YAC/C,QAAQ,CAAC,SAAS,4NAChB,6BAAA,AAA0B,EAAC;gBAAE,WAAW;gBAAE,UAAU;gBAAE,uBAAuB;YAAA,CAAE,CAAC,CACjF,CAAC;QACJ,CAAC;IACH,CAAC;IAED,OAAO,QAAQ,CAAC;AAClB,CAAC;AAEK,SAAU,2BAA2B;IACzC,IAAI,CAAC,gBAAgB,EAAE,CAAC;QACtB,gBAAgB,iMAAG,0BAAA,AAAuB,EAAE,CAAC;IAC/C,CAAC;IAED,OAAO,gBAAgB,CAAC;AAC1B,CAAC", "debugId": null}}, {"offset": {"line": 2954, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@typespec/ts-http-runtime/dist/esm/client/multipart.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/node_modules/%40typespec/ts-http-runtime/src/client/multipart.ts"], "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type { BodyPart, MultipartRequestBody, RawHttpHeadersInput } from \"../interfaces.js\";\nimport { RestError } from \"../restError.js\";\nimport { createHttpHeaders } from \"../httpHeaders.js\";\nimport { stringToUint8Array } from \"../util/bytesEncoding.js\";\nimport { isBinaryBody } from \"../util/typeGuards.js\";\n\n/**\n * Describes a single part in a multipart body.\n */\nexport interface PartDescriptor {\n  /**\n   * Content type of this part. If set, this value will be used to set the Content-Type MIME header for this part, although explicitly\n   * setting the Content-Type header in the headers bag will override this value. If set to `null`, no content type will be inferred from\n   * the body field. Otherwise, the value of the Content-Type MIME header will be inferred based on the type of the body.\n   */\n  contentType?: string | null;\n\n  /**\n   * The disposition type of this part (for example, \"form-data\" for parts making up a multipart/form-data request). If set, this value\n   * will be used to set the Content-Disposition MIME header for this part, in addition to the `name` and `filename` properties.\n   * If the `name` or `filename` properties are set while `dispositionType` is left undefined, `dispositionType` will default to \"form-data\".\n   *\n   * Explicitly setting the Content-Disposition header in the headers bag will override this value.\n   */\n  dispositionType?: string;\n\n  /**\n   * The field name associated with this part. This value will be used to construct the Content-Disposition header,\n   * along with the `dispositionType` and `filename` properties, if the header has not been set in the `headers` bag.\n   */\n  name?: string;\n\n  /**\n   * The file name of the content if it is a file. This value will be used to construct the Content-Disposition header,\n   * along with the `dispositionType` and `name` properties, if the header has not been set in the `headers` bag.\n   */\n  filename?: string;\n\n  /**\n   * The multipart headers for this part of the multipart body. Values of the Content-Type and Content-Disposition headers set in the headers bag\n   * will take precedence over those computed from the request body or the contentType, dispositionType, name, and filename fields on this object.\n   */\n  headers?: RawHttpHeadersInput;\n\n  /**\n   * The body of this part of the multipart request.\n   */\n  body?: unknown;\n}\n\ntype MultipartBodyType = BodyPart[\"body\"];\n\ntype HeaderValue = RawHttpHeadersInput[string];\n\n/**\n * Get value of a header in the part descriptor ignoring case\n */\nfunction getHeaderValue(descriptor: PartDescriptor, headerName: string): HeaderValue | undefined {\n  if (descriptor.headers) {\n    const actualHeaderName = Object.keys(descriptor.headers).find(\n      (x) => x.toLowerCase() === headerName.toLowerCase(),\n    );\n    if (actualHeaderName) {\n      return descriptor.headers[actualHeaderName];\n    }\n  }\n\n  return undefined;\n}\n\nfunction getPartContentType(descriptor: PartDescriptor): HeaderValue | undefined {\n  const contentTypeHeader = getHeaderValue(descriptor, \"content-type\");\n  if (contentTypeHeader) {\n    return contentTypeHeader;\n  }\n\n  // Special value of null means content type is to be omitted\n  if (descriptor.contentType === null) {\n    return undefined;\n  }\n\n  if (descriptor.contentType) {\n    return descriptor.contentType;\n  }\n\n  const { body } = descriptor;\n\n  if (body === null || body === undefined) {\n    return undefined;\n  }\n\n  if (typeof body === \"string\" || typeof body === \"number\" || typeof body === \"boolean\") {\n    return \"text/plain; charset=UTF-8\";\n  }\n\n  if (body instanceof Blob) {\n    return body.type || \"application/octet-stream\";\n  }\n\n  if (isBinaryBody(body)) {\n    return \"application/octet-stream\";\n  }\n\n  // arbitrary non-text object -> generic JSON content type by default. We will try to JSON.stringify the body.\n  return \"application/json\";\n}\n\n/**\n * Enclose value in quotes and escape special characters, for use in the Content-Disposition header\n */\nfunction escapeDispositionField(value: string): string {\n  return JSON.stringify(value);\n}\n\nfunction getContentDisposition(descriptor: PartDescriptor): HeaderValue | undefined {\n  const contentDispositionHeader = getHeaderValue(descriptor, \"content-disposition\");\n  if (contentDispositionHeader) {\n    return contentDispositionHeader;\n  }\n\n  if (\n    descriptor.dispositionType === undefined &&\n    descriptor.name === undefined &&\n    descriptor.filename === undefined\n  ) {\n    return undefined;\n  }\n\n  const dispositionType = descriptor.dispositionType ?? \"form-data\";\n\n  let disposition = dispositionType;\n  if (descriptor.name) {\n    disposition += `; name=${escapeDispositionField(descriptor.name)}`;\n  }\n\n  let filename: string | undefined = undefined;\n  if (descriptor.filename) {\n    filename = descriptor.filename;\n  } else if (typeof File !== \"undefined\" && descriptor.body instanceof File) {\n    const filenameFromFile = (descriptor.body as File).name;\n    if (filenameFromFile !== \"\") {\n      filename = filenameFromFile;\n    }\n  }\n\n  if (filename) {\n    disposition += `; filename=${escapeDispositionField(filename)}`;\n  }\n\n  return disposition;\n}\n\nfunction normalizeBody(body?: unknown, contentType?: HeaderValue): MultipartBodyType {\n  if (body === undefined) {\n    // zero-length body\n    return new Uint8Array([]);\n  }\n\n  // binary and primitives should go straight on the wire regardless of content type\n  if (isBinaryBody(body)) {\n    return body;\n  }\n  if (typeof body === \"string\" || typeof body === \"number\" || typeof body === \"boolean\") {\n    return stringToUint8Array(String(body), \"utf-8\");\n  }\n\n  // stringify objects for JSON-ish content types e.g. application/json, application/merge-patch+json, application/vnd.oci.manifest.v1+json, application.json; charset=UTF-8\n  if (contentType && /application\\/(.+\\+)?json(;.+)?/i.test(String(contentType))) {\n    return stringToUint8Array(JSON.stringify(body), \"utf-8\");\n  }\n\n  throw new RestError(`Unsupported body/content-type combination: ${body}, ${contentType}`);\n}\n\nexport function buildBodyPart(descriptor: PartDescriptor): BodyPart {\n  const contentType = getPartContentType(descriptor);\n  const contentDisposition = getContentDisposition(descriptor);\n  const headers = createHttpHeaders(descriptor.headers ?? {});\n\n  if (contentType) {\n    headers.set(\"content-type\", contentType);\n  }\n  if (contentDisposition) {\n    headers.set(\"content-disposition\", contentDisposition);\n  }\n\n  const body = normalizeBody(descriptor.body, contentType);\n\n  return {\n    headers,\n    body,\n  };\n}\n\nexport function buildMultipartBody(parts: PartDescriptor[]): MultipartRequestBody {\n  return { parts: parts.map(buildBodyPart) };\n}\n"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;;;;;AAGlC,OAAO,EAAE,SAAS,EAAE,MAAM,iBAAiB,CAAC;AAC5C,OAAO,EAAE,iBAAiB,EAAE,MAAM,mBAAmB,CAAC;AACtD,OAAO,EAAE,kBAAkB,EAAE,MAAM,0BAA0B,CAAC;AAC9D,OAAO,EAAE,YAAY,EAAE,MAAM,uBAAuB,CAAC;;;;;AAkDrD;;GAEG,CACH,SAAS,cAAc,CAAC,UAA0B,EAAE,UAAkB;IACpE,IAAI,UAAU,CAAC,OAAO,EAAE,CAAC;QACvB,MAAM,gBAAgB,GAAG,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,IAAI,CAC3D,CAAC,CAAC,EAAE,CAAG,CAAC,AAAF,CAAG,WAAW,EAAE,KAAK,UAAU,CAAC,WAAW,EAAE,CACpD,CAAC;QACF,IAAI,gBAAgB,EAAE,CAAC;YACrB,OAAO,UAAU,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC;QAC9C,CAAC;IACH,CAAC;IAED,OAAO,SAAS,CAAC;AACnB,CAAC;AAED,SAAS,kBAAkB,CAAC,UAA0B;IACpD,MAAM,iBAAiB,GAAG,cAAc,CAAC,UAAU,EAAE,cAAc,CAAC,CAAC;IACrE,IAAI,iBAAiB,EAAE,CAAC;QACtB,OAAO,iBAAiB,CAAC;IAC3B,CAAC;IAED,4DAA4D;IAC5D,IAAI,UAAU,CAAC,WAAW,KAAK,IAAI,EAAE,CAAC;QACpC,OAAO,SAAS,CAAC;IACnB,CAAC;IAED,IAAI,UAAU,CAAC,WAAW,EAAE,CAAC;QAC3B,OAAO,UAAU,CAAC,WAAW,CAAC;IAChC,CAAC;IAED,MAAM,EAAE,IAAI,EAAE,GAAG,UAAU,CAAC;IAE5B,IAAI,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,SAAS,EAAE,CAAC;QACxC,OAAO,SAAS,CAAC;IACnB,CAAC;IAED,IAAI,OAAO,IAAI,KAAK,QAAQ,IAAI,OAAO,IAAI,KAAK,QAAQ,IAAI,OAAO,IAAI,KAAK,SAAS,EAAE,CAAC;QACtF,OAAO,2BAA2B,CAAC;IACrC,CAAC;IAED,IAAI,IAAI,YAAY,IAAI,EAAE,CAAC;QACzB,OAAO,IAAI,CAAC,IAAI,IAAI,0BAA0B,CAAC;IACjD,CAAC;IAED,mMAAI,eAAA,AAAY,EAAC,IAAI,CAAC,EAAE,CAAC;QACvB,OAAO,0BAA0B,CAAC;IACpC,CAAC;IAED,6GAA6G;IAC7G,OAAO,kBAAkB,CAAC;AAC5B,CAAC;AAED;;GAEG,CACH,SAAS,sBAAsB,CAAC,KAAa;IAC3C,OAAO,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;AAC/B,CAAC;AAED,SAAS,qBAAqB,CAAC,UAA0B;;IACvD,MAAM,wBAAwB,GAAG,cAAc,CAAC,UAAU,EAAE,qBAAqB,CAAC,CAAC;IACnF,IAAI,wBAAwB,EAAE,CAAC;QAC7B,OAAO,wBAAwB,CAAC;IAClC,CAAC;IAED,IACE,UAAU,CAAC,eAAe,KAAK,SAAS,IACxC,UAAU,CAAC,IAAI,KAAK,SAAS,IAC7B,UAAU,CAAC,QAAQ,KAAK,SAAS,EACjC,CAAC;QACD,OAAO,SAAS,CAAC;IACnB,CAAC;IAED,MAAM,eAAe,GAAG,CAAA,KAAA,UAAU,CAAC,eAAe,MAAA,QAAA,OAAA,KAAA,IAAA,KAAI,WAAW,CAAC;IAElE,IAAI,WAAW,GAAG,eAAe,CAAC;IAClC,IAAI,UAAU,CAAC,IAAI,EAAE,CAAC;QACpB,WAAW,IAAI,CAAA,OAAA,EAAU,sBAAsB,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC;IACrE,CAAC;IAED,IAAI,QAAQ,GAAuB,SAAS,CAAC;IAC7C,IAAI,UAAU,CAAC,QAAQ,EAAE,CAAC;QACxB,QAAQ,GAAG,UAAU,CAAC,QAAQ,CAAC;IACjC,CAAC,MAAM,IAAI,OAAO,IAAI,KAAK,WAAW,IAAI,UAAU,CAAC,IAAI,YAAY,IAAI,EAAE,CAAC;QAC1E,MAAM,gBAAgB,GAAI,UAAU,CAAC,IAAa,CAAC,IAAI,CAAC;QACxD,IAAI,gBAAgB,KAAK,EAAE,EAAE,CAAC;YAC5B,QAAQ,GAAG,gBAAgB,CAAC;QAC9B,CAAC;IACH,CAAC;IAED,IAAI,QAAQ,EAAE,CAAC;QACb,WAAW,IAAI,CAAA,WAAA,EAAc,sBAAsB,CAAC,QAAQ,CAAC,EAAE,CAAC;IAClE,CAAC;IAED,OAAO,WAAW,CAAC;AACrB,CAAC;AAED,SAAS,aAAa,CAAC,IAAc,EAAE,WAAyB;IAC9D,IAAI,IAAI,KAAK,SAAS,EAAE,CAAC;QACvB,mBAAmB;QACnB,OAAO,IAAI,UAAU,CAAC,EAAE,CAAC,CAAC;IAC5B,CAAC;IAED,kFAAkF;IAClF,mMAAI,eAAA,AAAY,EAAC,IAAI,CAAC,EAAE,CAAC;QACvB,OAAO,IAAI,CAAC;IACd,CAAC;IACD,IAAI,OAAO,IAAI,KAAK,QAAQ,IAAI,OAAO,IAAI,KAAK,QAAQ,IAAI,OAAO,IAAI,KAAK,SAAS,EAAE,CAAC;QACtF,yMAAO,qBAAA,AAAkB,EAAC,MAAM,CAAC,IAAI,CAAC,EAAE,OAAO,CAAC,CAAC;IACnD,CAAC;IAED,0KAA0K;IAC1K,IAAI,WAAW,IAAI,iCAAiC,CAAC,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,EAAE,CAAC;QAC/E,yMAAO,qBAAA,AAAkB,EAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,OAAO,CAAC,CAAC;IAC3D,CAAC;IAED,MAAM,sLAAI,YAAS,CAAC,CAAA,2CAAA,EAA8C,IAAI,CAAA,EAAA,EAAK,WAAW,EAAE,CAAC,CAAC;AAC5F,CAAC;AAEK,SAAU,aAAa,CAAC,UAA0B;;IACtD,MAAM,WAAW,GAAG,kBAAkB,CAAC,UAAU,CAAC,CAAC;IACnD,MAAM,kBAAkB,GAAG,qBAAqB,CAAC,UAAU,CAAC,CAAC;IAC7D,MAAM,OAAO,IAAG,2MAAA,AAAiB,EAAC,CAAA,KAAA,UAAU,CAAC,OAAO,MAAA,QAAA,OAAA,KAAA,IAAA,KAAI,CAAA,CAAE,CAAC,CAAC;IAE5D,IAAI,WAAW,EAAE,CAAC;QAChB,OAAO,CAAC,GAAG,CAAC,cAAc,EAAE,WAAW,CAAC,CAAC;IAC3C,CAAC;IACD,IAAI,kBAAkB,EAAE,CAAC;QACvB,OAAO,CAAC,GAAG,CAAC,qBAAqB,EAAE,kBAAkB,CAAC,CAAC;IACzD,CAAC;IAED,MAAM,IAAI,GAAG,aAAa,CAAC,UAAU,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC;IAEzD,OAAO;QACL,OAAO;QACP,IAAI;KACL,CAAC;AACJ,CAAC;AAEK,SAAU,kBAAkB,CAAC,KAAuB;IACxD,OAAO;QAAE,KAAK,EAAE,KAAK,CAAC,GAAG,CAAC,aAAa,CAAC;IAAA,CAAE,CAAC;AAC7C,CAAC", "debugId": null}}, {"offset": {"line": 3084, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@typespec/ts-http-runtime/dist/esm/client/sendRequest.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/node_modules/%40typespec/ts-http-runtime/src/client/sendRequest.ts"], "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type {\n  HttpClient,\n  HttpMethods,\n  MultipartRequestBody,\n  PipelineRequest,\n  PipelineResponse,\n  RequestBodyType,\n} from \"../interfaces.js\";\nimport { isRestError, RestError } from \"../restError.js\";\nimport type { Pipeline } from \"../pipeline.js\";\nimport { createHttpHeaders } from \"../httpHeaders.js\";\nimport { createPipelineRequest } from \"../pipelineRequest.js\";\nimport { getCachedDefaultHttpsClient } from \"./clientHelpers.js\";\nimport { isReadableStream } from \"../util/typeGuards.js\";\nimport type { HttpResponse, RequestParameters } from \"./common.js\";\nimport type { PartDescriptor } from \"./multipart.js\";\nimport { buildMultipartBody } from \"./multipart.js\";\n\n/**\n * Helper function to send request used by the client\n * @param method - method to use to send the request\n * @param url - url to send the request to\n * @param pipeline - pipeline with the policies to run when sending the request\n * @param options - request options\n * @param customHttpClient - a custom HttpClient to use when making the request\n * @returns returns and HttpResponse\n */\nexport async function sendRequest(\n  method: HttpMethods,\n  url: string,\n  pipeline: Pipeline,\n  options: InternalRequestParameters = {},\n  customHttpClient?: HttpClient,\n): Promise<HttpResponse> {\n  const httpClient = customHttpClient ?? getCachedDefaultHttpsClient();\n  const request = buildPipelineRequest(method, url, options);\n\n  try {\n    const response = await pipeline.sendRequest(httpClient, request);\n    const headers = response.headers.toJSON();\n    const stream = response.readableStreamBody ?? response.browserStreamBody;\n    const parsedBody =\n      options.responseAsStream || stream !== undefined ? undefined : getResponseBody(response);\n    const body = stream ?? parsedBody;\n\n    if (options?.onResponse) {\n      options.onResponse({ ...response, request, rawHeaders: headers, parsedBody });\n    }\n\n    return {\n      request,\n      headers,\n      status: `${response.status}`,\n      body,\n    };\n  } catch (e: unknown) {\n    if (isRestError(e) && e.response && options.onResponse) {\n      const { response } = e;\n      const rawHeaders = response.headers.toJSON();\n      // UNBRANDED DIFFERENCE: onResponse callback does not have a second __legacyError property\n      options?.onResponse({ ...response, request, rawHeaders }, e);\n    }\n\n    throw e;\n  }\n}\n\n/**\n * Function to determine the request content type\n * @param options - request options InternalRequestParameters\n * @returns returns the content-type\n */\nfunction getRequestContentType(options: InternalRequestParameters = {}): string {\n  return (\n    options.contentType ??\n    (options.headers?.[\"content-type\"] as string) ??\n    getContentType(options.body)\n  );\n}\n\n/**\n * Function to determine the content-type of a body\n * this is used if an explicit content-type is not provided\n * @param body - body in the request\n * @returns returns the content-type\n */\nfunction getContentType(body: any): string | undefined {\n  if (ArrayBuffer.isView(body)) {\n    return \"application/octet-stream\";\n  }\n\n  if (typeof body === \"string\") {\n    try {\n      JSON.parse(body);\n      return \"application/json\";\n    } catch (error: any) {\n      // If we fail to parse the body, it is not json\n      return undefined;\n    }\n  }\n  // By default return json\n  return \"application/json\";\n}\n\nexport interface InternalRequestParameters extends RequestParameters {\n  responseAsStream?: boolean;\n}\n\nfunction buildPipelineRequest(\n  method: HttpMethods,\n  url: string,\n  options: InternalRequestParameters = {},\n): PipelineRequest {\n  const requestContentType = getRequestContentType(options);\n  const { body, multipartBody } = getRequestBody(options.body, requestContentType);\n  const hasContent = body !== undefined || multipartBody !== undefined;\n\n  const headers = createHttpHeaders({\n    ...(options.headers ? options.headers : {}),\n    accept: options.accept ?? options.headers?.accept ?? \"application/json\",\n    ...(hasContent &&\n      requestContentType && {\n        \"content-type\": requestContentType,\n      }),\n  });\n\n  return createPipelineRequest({\n    url,\n    method,\n    body,\n    multipartBody,\n    headers,\n    allowInsecureConnection: options.allowInsecureConnection,\n    abortSignal: options.abortSignal,\n    onUploadProgress: options.onUploadProgress,\n    onDownloadProgress: options.onDownloadProgress,\n    timeout: options.timeout,\n    enableBrowserStreams: true,\n    streamResponseStatusCodes: options.responseAsStream\n      ? new Set([Number.POSITIVE_INFINITY])\n      : undefined,\n  });\n}\n\ninterface RequestBody {\n  body?: RequestBodyType;\n  multipartBody?: MultipartRequestBody;\n}\n\n/**\n * Prepares the body before sending the request\n */\nfunction getRequestBody(body?: unknown, contentType: string = \"\"): RequestBody {\n  if (body === undefined) {\n    return { body: undefined };\n  }\n\n  if (typeof FormData !== \"undefined\" && body instanceof FormData) {\n    return { body };\n  }\n\n  if (isReadableStream(body)) {\n    return { body };\n  }\n\n  if (ArrayBuffer.isView(body)) {\n    return { body: body instanceof Uint8Array ? body : JSON.stringify(body) };\n  }\n\n  const firstType = contentType.split(\";\")[0];\n\n  switch (firstType) {\n    case \"application/json\":\n      return { body: JSON.stringify(body) };\n    case \"multipart/form-data\":\n      if (Array.isArray(body)) {\n        return { multipartBody: buildMultipartBody(body as PartDescriptor[]) };\n      }\n      return { body: JSON.stringify(body) };\n    case \"text/plain\":\n      return { body: String(body) };\n    default:\n      if (typeof body === \"string\") {\n        return { body };\n      }\n      return { body: JSON.stringify(body) };\n  }\n}\n\n/**\n * Prepares the response body\n */\nfunction getResponseBody(response: PipelineResponse): RequestBodyType | undefined {\n  // Set the default response type\n  const contentType = response.headers.get(\"content-type\") ?? \"\";\n  const firstType = contentType.split(\";\")[0];\n  const bodyToParse = response.bodyAsText ?? \"\";\n\n  if (firstType === \"text/plain\") {\n    return String(bodyToParse);\n  }\n  // Default to \"application/json\" and fallback to string;\n  try {\n    return bodyToParse ? JSON.parse(bodyToParse) : undefined;\n  } catch (error: any) {\n    // If we were supposed to get a JSON object and failed to\n    // parse, throw a parse error\n    if (firstType === \"application/json\") {\n      throw createParseError(response, error);\n    }\n\n    // We are not sure how to handle the response so we return it as\n    // plain text.\n    return String(bodyToParse);\n  }\n}\n\nfunction createParseError(response: PipelineResponse, err: any): RestError {\n  const msg = `Error \"${err}\" occurred while parsing the response body - ${response.bodyAsText}.`;\n  const errCode = err.code ?? RestError.PARSE_ERROR;\n  return new RestError(msg, {\n    code: errCode,\n    statusCode: response.status,\n    request: response.request,\n    response: response,\n  });\n}\n"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;;;;AAUlC,OAAO,EAAE,WAAW,EAAE,SAAS,EAAE,MAAM,iBAAiB,CAAC;AAEzD,OAAO,EAAE,iBAAiB,EAAE,MAAM,mBAAmB,CAAC;AACtD,OAAO,EAAE,qBAAqB,EAAE,MAAM,uBAAuB,CAAC;AAC9D,OAAO,EAAE,2BAA2B,EAAE,MAAM,oBAAoB,CAAC;AACjE,OAAO,EAAE,gBAAgB,EAAE,MAAM,uBAAuB,CAAC;AAGzD,OAAO,EAAE,kBAAkB,EAAE,MAAM,gBAAgB,CAAC;;;;;;;AAW7C,KAAK,UAAU,WAAW,CAC/B,MAAmB,EACnB,GAAW,EACX,QAAkB,EAClB,UAAqC,CAAA,CAAE,EACvC,gBAA6B;;IAE7B,MAAM,UAAU,GAAG,gBAAgB,KAAA,QAAhB,gBAAgB,KAAA,KAAA,IAAhB,gBAAgB,uMAAI,8BAAA,AAA2B,EAAE,CAAC;IACrE,MAAM,OAAO,GAAG,oBAAoB,CAAC,MAAM,EAAE,GAAG,EAAE,OAAO,CAAC,CAAC;IAE3D,IAAI,CAAC;QACH,MAAM,QAAQ,GAAG,MAAM,QAAQ,CAAC,WAAW,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;QACjE,MAAM,OAAO,GAAG,QAAQ,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC;QAC1C,MAAM,MAAM,GAAG,CAAA,KAAA,QAAQ,CAAC,kBAAkB,MAAA,QAAA,OAAA,KAAA,IAAA,KAAI,QAAQ,CAAC,iBAAiB,CAAC;QACzE,MAAM,UAAU,GACd,OAAO,CAAC,gBAAgB,IAAI,MAAM,KAAK,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC;QAC3F,MAAM,IAAI,GAAG,MAAM,KAAA,QAAN,MAAM,KAAA,KAAA,IAAN,MAAM,GAAI,UAAU,CAAC;QAElC,IAAI,OAAO,KAAA,QAAP,OAAO,KAAA,KAAA,IAAA,KAAA,IAAP,OAAO,CAAE,UAAU,EAAE,CAAC;YACxB,OAAO,CAAC,UAAU,CAAA,OAAA,MAAA,CAAA,OAAA,MAAA,CAAA,CAAA,GAAM,QAAQ,GAAA;gBAAE,OAAO;gBAAE,UAAU,EAAE,OAAO;gBAAE,UAAU;YAAA,GAAG,CAAC;QAChF,CAAC;QAED,OAAO;YACL,OAAO;YACP,OAAO;YACP,MAAM,EAAE,GAAG,QAAQ,CAAC,MAAM,EAAE;YAC5B,IAAI;SACL,CAAC;IACJ,CAAC,CAAC,OAAO,CAAU,EAAE,CAAC;QACpB,0LAAI,cAAA,AAAW,EAAC,CAAC,CAAC,IAAI,CAAC,CAAC,QAAQ,IAAI,OAAO,CAAC,UAAU,EAAE,CAAC;YACvD,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAC,CAAC;YACvB,MAAM,UAAU,GAAG,QAAQ,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC;YAC7C,0FAA0F;YAC1F,OAAO,KAAA,QAAP,OAAO,KAAA,KAAA,IAAA,KAAA,IAAP,OAAO,CAAE,UAAU,CAAA,OAAA,MAAA,CAAA,OAAA,MAAA,CAAA,CAAA,GAAM,QAAQ,GAAA;gBAAE,OAAO;gBAAE,UAAU;YAAA,IAAI,CAAC,CAAC,CAAC;QAC/D,CAAC;QAED,MAAM,CAAC,CAAC;IACV,CAAC;AACH,CAAC;AAED;;;;GAIG,CACH,SAAS,qBAAqB,CAAC,UAAqC,CAAA,CAAE;;IACpE,OAAO,AACL,CAAA,KAAA,CAAA,KAAA,OAAO,CAAC,WAAW,MAAA,QAAA,OAAA,KAAA,IAAA,KAClB,CAAA,KAAA,OAAO,CAAC,OAAO,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,EAAA,CAAG,cAAc,CAAY,MAAA,QAAA,OAAA,KAAA,IAAA,KAC7C,cAAc,CAAC,OAAO,CAAC,IAAI,CAAC,CAC7B,CAAC;AACJ,CAAC;AAED;;;;;GAKG,CACH,SAAS,cAAc,CAAC,IAAS;IAC/B,IAAI,WAAW,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC;QAC7B,OAAO,0BAA0B,CAAC;IACpC,CAAC;IAED,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE,CAAC;QAC7B,IAAI,CAAC;YACH,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;YACjB,OAAO,kBAAkB,CAAC;QAC5B,CAAC,CAAC,OAAO,KAAU,EAAE,CAAC;YACpB,+CAA+C;YAC/C,OAAO,SAAS,CAAC;QACnB,CAAC;IACH,CAAC;IACD,yBAAyB;IACzB,OAAO,kBAAkB,CAAC;AAC5B,CAAC;AAMD,SAAS,oBAAoB,CAC3B,MAAmB,EACnB,GAAW,EACX,UAAqC,CAAA,CAAE;;IAEvC,MAAM,kBAAkB,GAAG,qBAAqB,CAAC,OAAO,CAAC,CAAC;IAC1D,MAAM,EAAE,IAAI,EAAE,aAAa,EAAE,GAAG,cAAc,CAAC,OAAO,CAAC,IAAI,EAAE,kBAAkB,CAAC,CAAC;IACjF,MAAM,UAAU,GAAG,IAAI,KAAK,SAAS,IAAI,aAAa,KAAK,SAAS,CAAC;IAErE,MAAM,OAAO,IAAG,2MAAA,AAAiB,EAAA,OAAA,MAAA,CAAA,OAAA,MAAA,CAAA,OAAA,MAAA,CAAA,CAAA,GAC5B,AAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,CAAA,CAAE,CAAC,EAAA;QAC3C,MAAM,EAAE,CAAA,KAAA,CAAA,KAAA,OAAO,CAAC,MAAM,MAAA,QAAA,OAAA,KAAA,IAAA,KAAI,CAAA,KAAA,OAAO,CAAC,OAAO,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAE,MAAM,MAAA,QAAA,OAAA,KAAA,IAAA,KAAI,kBAAkB;IAAA,IACpE,AAAC,UAAU,IACZ,kBAAkB,IAAI;QACpB,cAAc,EAAE,kBAAkB;KACnC,CAAC,EACJ,CAAC;IAEH,QAAO,mNAAA,AAAqB,EAAC;QAC3B,GAAG;QACH,MAAM;QACN,IAAI;QACJ,aAAa;QACb,OAAO;QACP,uBAAuB,EAAE,OAAO,CAAC,uBAAuB;QACxD,WAAW,EAAE,OAAO,CAAC,WAAW;QAChC,gBAAgB,EAAE,OAAO,CAAC,gBAAgB;QAC1C,kBAAkB,EAAE,OAAO,CAAC,kBAAkB;QAC9C,OAAO,EAAE,OAAO,CAAC,OAAO;QACxB,oBAAoB,EAAE,IAAI;QAC1B,yBAAyB,EAAE,OAAO,CAAC,gBAAgB,GAC/C,IAAI,GAAG,CAAC;YAAC,MAAM,CAAC,iBAAiB;SAAC,CAAC,GACnC,SAAS;KACd,CAAC,CAAC;AACL,CAAC;AAOD;;GAEG,CACH,SAAS,cAAc,CAAC,IAAc,EAAE,cAAsB,EAAE;IAC9D,IAAI,IAAI,KAAK,SAAS,EAAE,CAAC;QACvB,OAAO;YAAE,IAAI,EAAE,SAAS;QAAA,CAAE,CAAC;IAC7B,CAAC;IAED,IAAI,OAAO,QAAQ,KAAK,WAAW,IAAI,IAAI,YAAY,QAAQ,EAAE,CAAC;QAChE,OAAO;YAAE,IAAI;QAAA,CAAE,CAAC;IAClB,CAAC;IAED,mMAAI,mBAAA,AAAgB,EAAC,IAAI,CAAC,EAAE,CAAC;QAC3B,OAAO;YAAE,IAAI;QAAA,CAAE,CAAC;IAClB,CAAC;IAED,IAAI,WAAW,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC;QAC7B,OAAO;YAAE,IAAI,EAAE,IAAI,YAAY,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;QAAA,CAAE,CAAC;IAC5E,CAAC;IAED,MAAM,SAAS,GAAG,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;IAE5C,OAAQ,SAAS,EAAE,CAAC;QAClB,KAAK,kBAAkB;YACrB,OAAO;gBAAE,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;YAAA,CAAE,CAAC;QACxC,KAAK,qBAAqB;YACxB,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC;gBACxB,OAAO;oBAAE,aAAa,MAAE,iNAAA,AAAkB,EAAC,IAAwB,CAAC;gBAAA,CAAE,CAAC;YACzE,CAAC;YACD,OAAO;gBAAE,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;YAAA,CAAE,CAAC;QACxC,KAAK,YAAY;YACf,OAAO;gBAAE,IAAI,EAAE,MAAM,CAAC,IAAI,CAAC;YAAA,CAAE,CAAC;QAChC;YACE,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE,CAAC;gBAC7B,OAAO;oBAAE,IAAI;gBAAA,CAAE,CAAC;YAClB,CAAC;YACD,OAAO;gBAAE,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;YAAA,CAAE,CAAC;IAC1C,CAAC;AACH,CAAC;AAED;;GAEG,CACH,SAAS,eAAe,CAAC,QAA0B;;IACjD,gCAAgC;IAChC,MAAM,WAAW,GAAG,CAAA,KAAA,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,MAAA,QAAA,OAAA,KAAA,IAAA,KAAI,EAAE,CAAC;IAC/D,MAAM,SAAS,GAAG,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;IAC5C,MAAM,WAAW,GAAG,CAAA,KAAA,QAAQ,CAAC,UAAU,MAAA,QAAA,OAAA,KAAA,IAAA,KAAI,EAAE,CAAC;IAE9C,IAAI,SAAS,KAAK,YAAY,EAAE,CAAC;QAC/B,OAAO,MAAM,CAAC,WAAW,CAAC,CAAC;IAC7B,CAAC;IACD,wDAAwD;IACxD,IAAI,CAAC;QACH,OAAO,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;IAC3D,CAAC,CAAC,OAAO,KAAU,EAAE,CAAC;QACpB,yDAAyD;QACzD,6BAA6B;QAC7B,IAAI,SAAS,KAAK,kBAAkB,EAAE,CAAC;YACrC,MAAM,gBAAgB,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;QAC1C,CAAC;QAED,gEAAgE;QAChE,cAAc;QACd,OAAO,MAAM,CAAC,WAAW,CAAC,CAAC;IAC7B,CAAC;AACH,CAAC;AAED,SAAS,gBAAgB,CAAC,QAA0B,EAAE,GAAQ;;IAC5D,MAAM,GAAG,GAAG,CAAA,OAAA,EAAU,GAAG,CAAA,6CAAA,EAAgD,QAAQ,CAAC,UAAU,CAAA,CAAA,CAAG,CAAC;IAChG,MAAM,OAAO,GAAG,CAAA,KAAA,GAAG,CAAC,IAAI,MAAA,QAAA,OAAA,KAAA,IAAA,KAAI,8LAAS,CAAC,WAAW,CAAC;IAClD,OAAO,sLAAI,YAAS,CAAC,GAAG,EAAE;QACxB,IAAI,EAAE,OAAO;QACb,UAAU,EAAE,QAAQ,CAAC,MAAM;QAC3B,OAAO,EAAE,QAAQ,CAAC,OAAO;QACzB,QAAQ,EAAE,QAAQ;KACnB,CAAC,CAAC;AACL,CAAC", "debugId": null}}, {"offset": {"line": 3286, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@typespec/ts-http-runtime/dist/esm/client/urlHelpers.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/node_modules/%40typespec/ts-http-runtime/src/client/urlHelpers.ts"], "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type { PathParameterWithOptions, RequestParameters } from \"./common.js\";\n\ntype QueryParameterStyle = \"form\" | \"spaceDelimited\" | \"pipeDelimited\";\n\n/**\n * An object that can be passed as a query parameter, allowing for additional options to be set relating to how the parameter is encoded.\n */\ninterface QueryParameterWithOptions {\n  /**\n   * The value of the query parameter.\n   */\n  value: unknown;\n\n  /**\n   * If set to true, value must be an array. Setting this option to true will cause the array to be encoded as multiple query parameters.\n   * Setting it to false will cause the array values to be encoded as a single query parameter, with each value separated by a comma ','.\n   *\n   * For example, with `explode` set to true, a query parameter named \"foo\" with value [\"a\", \"b\", \"c\"] will be encoded as foo=a&foo=b&foo=c.\n   * If `explode` was set to false, the same example would instead be encouded as foo=a,b,c.\n   *\n   * Defaults to false.\n   */\n  explode?: boolean;\n\n  /**\n   * Style for encoding arrays. Three possible values:\n   * - \"form\": array values will be separated by a comma \",\" in the query parameter value.\n   * - \"spaceDelimited\": array values will be separated by a space (\" \", url-encoded to \"%20\").\n   * - \"pipeDelimited\": array values will be separated by a pipe (\"|\").\n   *\n   * Defaults to \"form\".\n   */\n  style?: QueryParameterStyle;\n}\n\nfunction isQueryParameterWithOptions(x: unknown): x is QueryParameterWithOptions {\n  const value = (x as QueryParameterWithOptions).value as any;\n  return (\n    value !== undefined && value.toString !== undefined && typeof value.toString === \"function\"\n  );\n}\n\n/**\n * Builds the request url, filling in query and path parameters\n * @param endpoint - base url which can be a template url\n * @param routePath - path to append to the endpoint\n * @param pathParameters - values of the path parameters\n * @param options - request parameters including query parameters\n * @returns a full url with path and query parameters\n */\nexport function buildRequestUrl(\n  endpoint: string,\n  routePath: string,\n  pathParameters: (string | number | PathParameterWithOptions)[],\n  options: RequestParameters = {},\n): string {\n  if (routePath.startsWith(\"https://\") || routePath.startsWith(\"http://\")) {\n    return routePath;\n  }\n  endpoint = buildBaseUrl(endpoint, options);\n  routePath = buildRoutePath(routePath, pathParameters, options);\n  const requestUrl = appendQueryParams(`${endpoint}/${routePath}`, options);\n  const url = new URL(requestUrl);\n\n  return (\n    url\n      .toString()\n      // Remove double forward slashes\n      .replace(/([^:]\\/)\\/+/g, \"$1\")\n  );\n}\n\nfunction getQueryParamValue(\n  key: string,\n  allowReserved: boolean,\n  style: QueryParameterStyle,\n  param: any,\n): string {\n  let separator: string;\n  if (style === \"pipeDelimited\") {\n    separator = \"|\";\n  } else if (style === \"spaceDelimited\") {\n    separator = \"%20\";\n  } else {\n    separator = \",\";\n  }\n\n  let paramValues: any[];\n  if (Array.isArray(param)) {\n    paramValues = param;\n  } else if (typeof param === \"object\" && param.toString === Object.prototype.toString) {\n    // If the parameter is an object without a custom toString implementation (e.g. a Date),\n    // then we should deconstruct the object into an array [key1, value1, key2, value2, ...].\n    paramValues = Object.entries(param).flat();\n  } else {\n    paramValues = [param];\n  }\n\n  const value = paramValues\n    .map((p) => {\n      if (p === null || p === undefined) {\n        return \"\";\n      }\n\n      if (!p.toString || typeof p.toString !== \"function\") {\n        throw new Error(`Query parameters must be able to be represented as string, ${key} can't`);\n      }\n\n      const rawValue = p.toISOString !== undefined ? p.toISOString() : p.toString();\n      return allowReserved ? rawValue : encodeURIComponent(rawValue);\n    })\n    .join(separator);\n\n  return `${allowReserved ? key : encodeURIComponent(key)}=${value}`;\n}\n\nfunction appendQueryParams(url: string, options: RequestParameters = {}): string {\n  if (!options.queryParameters) {\n    return url;\n  }\n  const parsedUrl = new URL(url);\n  const queryParams = options.queryParameters;\n\n  const paramStrings: string[] = [];\n  for (const key of Object.keys(queryParams)) {\n    const param = queryParams[key] as any;\n    if (param === undefined || param === null) {\n      continue;\n    }\n\n    const hasMetadata = isQueryParameterWithOptions(param);\n    const rawValue = hasMetadata ? param.value : param;\n    const explode = hasMetadata ? (param.explode ?? false) : false;\n    const style = hasMetadata && param.style ? param.style : \"form\";\n\n    if (explode) {\n      if (Array.isArray(rawValue)) {\n        for (const item of rawValue) {\n          paramStrings.push(getQueryParamValue(key, options.skipUrlEncoding ?? false, style, item));\n        }\n      } else if (typeof rawValue === \"object\") {\n        // For object explode, the name of the query parameter is ignored and we use the object key instead\n        for (const [actualKey, value] of Object.entries(rawValue)) {\n          paramStrings.push(\n            getQueryParamValue(actualKey, options.skipUrlEncoding ?? false, style, value),\n          );\n        }\n      } else {\n        // Explode doesn't really make sense for primitives\n        throw new Error(\"explode can only be set to true for objects and arrays\");\n      }\n    } else {\n      paramStrings.push(getQueryParamValue(key, options.skipUrlEncoding ?? false, style, rawValue));\n    }\n  }\n\n  if (parsedUrl.search !== \"\") {\n    parsedUrl.search += \"&\";\n  }\n  parsedUrl.search += paramStrings.join(\"&\");\n  return parsedUrl.toString();\n}\n\nexport function buildBaseUrl(endpoint: string, options: RequestParameters): string {\n  if (!options.pathParameters) {\n    return endpoint;\n  }\n  const pathParams = options.pathParameters;\n  for (const [key, param] of Object.entries(pathParams)) {\n    if (param === undefined || param === null) {\n      throw new Error(`Path parameters ${key} must not be undefined or null`);\n    }\n    if (!param.toString || typeof param.toString !== \"function\") {\n      throw new Error(`Path parameters must be able to be represented as string, ${key} can't`);\n    }\n    let value = param.toISOString !== undefined ? param.toISOString() : String(param);\n    if (!options.skipUrlEncoding) {\n      value = encodeURIComponent(param);\n    }\n    endpoint = replaceAll(endpoint, `{${key}}`, value) ?? \"\";\n  }\n  return endpoint;\n}\n\nfunction buildRoutePath(\n  routePath: string,\n  pathParameters: (string | number | PathParameterWithOptions)[],\n  options: RequestParameters = {},\n): string {\n  for (const pathParam of pathParameters) {\n    const allowReserved = typeof pathParam === \"object\" && (pathParam.allowReserved ?? false);\n    let value = typeof pathParam === \"object\" ? pathParam.value : pathParam;\n\n    if (!options.skipUrlEncoding && !allowReserved) {\n      value = encodeURIComponent(value);\n    }\n\n    routePath = routePath.replace(/\\{[\\w-]+\\}/, String(value));\n  }\n  return routePath;\n}\n\n/**\n * Replace all of the instances of searchValue in value with the provided replaceValue.\n * @param value - The value to search and replace in.\n * @param searchValue - The value to search for in the value argument.\n * @param replaceValue - The value to replace searchValue with in the value argument.\n * @returns The value where each instance of searchValue was replaced with replacedValue.\n */\nexport function replaceAll(\n  value: string | undefined,\n  searchValue: string,\n  replaceValue: string,\n): string | undefined {\n  return !value || !searchValue ? value : value.split(searchValue).join(replaceValue || \"\");\n}\n"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;;;;;;AAqClC,SAAS,2BAA2B,CAAC,CAAU;IAC7C,MAAM,KAAK,GAAI,CAA+B,CAAC,KAAY,CAAC;IAC5D,OAAO,AACL,KAAK,KAAK,SAAS,IAAI,KAAK,CAAC,QAAQ,KAAK,SAAS,IAAI,OAAO,KAAK,CAAC,QAAQ,KAAK,UAAU,CAC5F,CAAC;AACJ,CAAC;AAUK,SAAU,eAAe,CAC7B,QAAgB,EAChB,SAAiB,EACjB,cAA8D,EAC9D,UAA6B,CAAA,CAAE;IAE/B,IAAI,SAAS,CAAC,UAAU,CAAC,UAAU,CAAC,IAAI,SAAS,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE,CAAC;QACxE,OAAO,SAAS,CAAC;IACnB,CAAC;IACD,QAAQ,GAAG,YAAY,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;IAC3C,SAAS,GAAG,cAAc,CAAC,SAAS,EAAE,cAAc,EAAE,OAAO,CAAC,CAAC;IAC/D,MAAM,UAAU,GAAG,iBAAiB,CAAC,GAAG,QAAQ,CAAA,CAAA,EAAI,SAAS,EAAE,EAAE,OAAO,CAAC,CAAC;IAC1E,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,UAAU,CAAC,CAAC;IAEhC,OAAO,AACL,GAAG,CACA,QAAQ,EAAE,AACX,gCAAgC;KAC/B,OAAO,CAAC,cAAc,EAAE,IAAI,CAAC,CACjC,CAAC;AACJ,CAAC;AAED,SAAS,kBAAkB,CACzB,GAAW,EACX,aAAsB,EACtB,KAA0B,EAC1B,KAAU;IAEV,IAAI,SAAiB,CAAC;IACtB,IAAI,KAAK,KAAK,eAAe,EAAE,CAAC;QAC9B,SAAS,GAAG,GAAG,CAAC;IAClB,CAAC,MAAM,IAAI,KAAK,KAAK,gBAAgB,EAAE,CAAC;QACtC,SAAS,GAAG,KAAK,CAAC;IACpB,CAAC,MAAM,CAAC;QACN,SAAS,GAAG,GAAG,CAAC;IAClB,CAAC;IAED,IAAI,WAAkB,CAAC;IACvB,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC;QACzB,WAAW,GAAG,KAAK,CAAC;IACtB,CAAC,MAAM,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,KAAK,CAAC,QAAQ,KAAK,MAAM,CAAC,SAAS,CAAC,QAAQ,EAAE,CAAC;QACrF,wFAAwF;QACxF,yFAAyF;QACzF,WAAW,GAAG,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,IAAI,EAAE,CAAC;IAC7C,CAAC,MAAM,CAAC;QACN,WAAW,GAAG;YAAC,KAAK;SAAC,CAAC;IACxB,CAAC;IAED,MAAM,KAAK,GAAG,WAAW,CACtB,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE;QACT,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,SAAS,EAAE,CAAC;YAClC,OAAO,EAAE,CAAC;QACZ,CAAC;QAED,IAAI,CAAC,CAAC,CAAC,QAAQ,IAAI,OAAO,CAAC,CAAC,QAAQ,KAAK,UAAU,EAAE,CAAC;YACpD,MAAM,IAAI,KAAK,CAAC,CAAA,2DAAA,EAA8D,GAAG,CAAA,MAAA,CAAQ,CAAC,CAAC;QAC7F,CAAC;QAED,MAAM,QAAQ,GAAG,CAAC,CAAC,WAAW,KAAK,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC;QAC9E,OAAO,aAAa,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,kBAAkB,CAAC,QAAQ,CAAC,CAAC;IACjE,CAAC,CAAC,CACD,IAAI,CAAC,SAAS,CAAC,CAAC;IAEnB,OAAO,GAAG,aAAa,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAA,CAAA,EAAI,KAAK,EAAE,CAAC;AACrE,CAAC;AAED,SAAS,iBAAiB,CAAC,GAAW,EAAE,UAA6B,CAAA,CAAE;;IACrE,IAAI,CAAC,OAAO,CAAC,eAAe,EAAE,CAAC;QAC7B,OAAO,GAAG,CAAC;IACb,CAAC;IACD,MAAM,SAAS,GAAG,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC;IAC/B,MAAM,WAAW,GAAG,OAAO,CAAC,eAAe,CAAC;IAE5C,MAAM,YAAY,GAAa,EAAE,CAAC;IAClC,KAAK,MAAM,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,CAAE,CAAC;QAC3C,MAAM,KAAK,GAAG,WAAW,CAAC,GAAG,CAAQ,CAAC;QACtC,IAAI,KAAK,KAAK,SAAS,IAAI,KAAK,KAAK,IAAI,EAAE,CAAC;YAC1C,SAAS;QACX,CAAC;QAED,MAAM,WAAW,GAAG,2BAA2B,CAAC,KAAK,CAAC,CAAC;QACvD,MAAM,QAAQ,GAAG,WAAW,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC;QACnD,MAAM,OAAO,GAAG,WAAW,CAAC,CAAC,CAAC,AAAC,CAAA,KAAA,KAAK,CAAC,OAAO,MAAA,QAAA,OAAA,KAAA,IAAA,KAAI,KAAK,CAAC,CAAC,CAAC,AAAC,KAAK,CAAC;QAC/D,MAAM,KAAK,GAAG,WAAW,IAAI,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC;QAEhE,IAAI,OAAO,EAAE,CAAC;YACZ,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE,CAAC;gBAC5B,KAAK,MAAM,IAAI,IAAI,QAAQ,CAAE,CAAC;oBAC5B,YAAY,CAAC,IAAI,CAAC,kBAAkB,CAAC,GAAG,EAAE,CAAA,KAAA,OAAO,CAAC,eAAe,MAAA,QAAA,OAAA,KAAA,IAAA,KAAI,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC,CAAC;gBAC5F,CAAC;YACH,CAAC,MAAM,IAAI,OAAO,QAAQ,KAAK,QAAQ,EAAE,CAAC;gBACxC,mGAAmG;gBACnG,KAAK,MAAM,CAAC,SAAS,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAE,CAAC;oBAC1D,YAAY,CAAC,IAAI,CACf,kBAAkB,CAAC,SAAS,EAAE,CAAA,KAAA,OAAO,CAAC,eAAe,MAAA,QAAA,OAAA,KAAA,IAAA,KAAI,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,CAC9E,CAAC;gBACJ,CAAC;YACH,CAAC,MAAM,CAAC;gBACN,mDAAmD;gBACnD,MAAM,IAAI,KAAK,CAAC,wDAAwD,CAAC,CAAC;YAC5E,CAAC;QACH,CAAC,MAAM,CAAC;YACN,YAAY,CAAC,IAAI,CAAC,kBAAkB,CAAC,GAAG,EAAE,CAAA,KAAA,OAAO,CAAC,eAAe,MAAA,QAAA,OAAA,KAAA,IAAA,KAAI,KAAK,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAC,CAAC;QAChG,CAAC;IACH,CAAC;IAED,IAAI,SAAS,CAAC,MAAM,KAAK,EAAE,EAAE,CAAC;QAC5B,SAAS,CAAC,MAAM,IAAI,GAAG,CAAC;IAC1B,CAAC;IACD,SAAS,CAAC,MAAM,IAAI,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IAC3C,OAAO,SAAS,CAAC,QAAQ,EAAE,CAAC;AAC9B,CAAC;AAEK,SAAU,YAAY,CAAC,QAAgB,EAAE,OAA0B;;IACvE,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE,CAAC;QAC5B,OAAO,QAAQ,CAAC;IAClB,CAAC;IACD,MAAM,UAAU,GAAG,OAAO,CAAC,cAAc,CAAC;IAC1C,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC,CAAE,CAAC;QACtD,IAAI,KAAK,KAAK,SAAS,IAAI,KAAK,KAAK,IAAI,EAAE,CAAC;YAC1C,MAAM,IAAI,KAAK,CAAC,CAAA,gBAAA,EAAmB,GAAG,CAAA,8BAAA,CAAgC,CAAC,CAAC;QAC1E,CAAC;QACD,IAAI,CAAC,KAAK,CAAC,QAAQ,IAAI,OAAO,KAAK,CAAC,QAAQ,KAAK,UAAU,EAAE,CAAC;YAC5D,MAAM,IAAI,KAAK,CAAC,CAAA,0DAAA,EAA6D,GAAG,CAAA,MAAA,CAAQ,CAAC,CAAC;QAC5F,CAAC;QACD,IAAI,KAAK,GAAG,KAAK,CAAC,WAAW,KAAK,SAAS,CAAC,CAAC,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;QAClF,IAAI,CAAC,OAAO,CAAC,eAAe,EAAE,CAAC;YAC7B,KAAK,GAAG,kBAAkB,CAAC,KAAK,CAAC,CAAC;QACpC,CAAC;QACD,QAAQ,GAAG,CAAA,KAAA,UAAU,CAAC,QAAQ,EAAE,CAAA,CAAA,EAAI,GAAG,CAAA,CAAA,CAAG,EAAE,KAAK,CAAC,MAAA,QAAA,OAAA,KAAA,IAAA,KAAI,EAAE,CAAC;IAC3D,CAAC;IACD,OAAO,QAAQ,CAAC;AAClB,CAAC;AAED,SAAS,cAAc,CACrB,SAAiB,EACjB,cAA8D,EAC9D,UAA6B,CAAA,CAAE;;IAE/B,KAAK,MAAM,SAAS,IAAI,cAAc,CAAE,CAAC;QACvC,MAAM,aAAa,GAAG,OAAO,SAAS,KAAK,QAAQ,IAAI,CAAC,CAAA,KAAA,SAAS,CAAC,aAAa,MAAA,QAAA,OAAA,KAAA,IAAA,KAAI,KAAK,CAAC,CAAC;QAC1F,IAAI,KAAK,GAAG,OAAO,SAAS,KAAK,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,SAAS,CAAC;QAExE,IAAI,CAAC,OAAO,CAAC,eAAe,IAAI,CAAC,aAAa,EAAE,CAAC;YAC/C,KAAK,GAAG,kBAAkB,CAAC,KAAK,CAAC,CAAC;QACpC,CAAC;QAED,SAAS,GAAG,SAAS,CAAC,OAAO,CAAC,YAAY,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;IAC7D,CAAC;IACD,OAAO,SAAS,CAAC;AACnB,CAAC;AASK,SAAU,UAAU,CACxB,KAAyB,EACzB,WAAmB,EACnB,YAAoB;IAEpB,OAAO,CAAC,KAAK,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,YAAY,IAAI,EAAE,CAAC,CAAC;AAC5F,CAAC", "debugId": null}}, {"offset": {"line": 3422, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@typespec/ts-http-runtime/dist/esm/client/getClient.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/node_modules/%40typespec/ts-http-runtime/src/client/getClient.ts"], "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type { HttpClient, HttpMethods } from \"../interfaces.js\";\nimport type { Pipeline } from \"../pipeline.js\";\nimport { createDefaultPipeline } from \"./clientHelpers.js\";\nimport type {\n  Client,\n  ClientOptions,\n  HttpBrowserStreamResponse,\n  HttpNodeStreamResponse,\n  RequestParameters,\n  ResourceMethods,\n  StreamableMethod,\n} from \"./common.js\";\nimport { sendRequest } from \"./sendRequest.js\";\nimport { buildRequestUrl } from \"./urlHelpers.js\";\nimport { isNodeLike } from \"../util/checkEnvironment.js\";\n\n/**\n * Creates a client with a default pipeline\n * @param endpoint - Base endpoint for the client\n * @param credentials - Credentials to authenticate the requests\n * @param options - Client options\n */\nexport function getClient(endpoint: string, clientOptions: ClientOptions = {}): Client {\n  const pipeline = clientOptions.pipeline ?? createDefaultPipeline(clientOptions);\n  if (clientOptions.additionalPolicies?.length) {\n    for (const { policy, position } of clientOptions.additionalPolicies) {\n      // Sign happens after Retry and is commonly needed to occur\n      // before policies that intercept post-retry.\n      const afterPhase = position === \"perRetry\" ? \"Sign\" : undefined;\n      pipeline.addPolicy(policy, {\n        afterPhase,\n      });\n    }\n  }\n\n  const { allowInsecureConnection, httpClient } = clientOptions;\n  const endpointUrl = clientOptions.endpoint ?? endpoint;\n  const client = (path: string, ...args: Array<any>): ResourceMethods<StreamableMethod> => {\n    const getUrl = (requestOptions: RequestParameters): string =>\n      buildRequestUrl(endpointUrl, path, args, { allowInsecureConnection, ...requestOptions });\n\n    return {\n      get: (requestOptions: RequestParameters = {}): StreamableMethod => {\n        return buildOperation(\n          \"GET\",\n          getUrl(requestOptions),\n          pipeline,\n          requestOptions,\n          allowInsecureConnection,\n          httpClient,\n        );\n      },\n      post: (requestOptions: RequestParameters = {}): StreamableMethod => {\n        return buildOperation(\n          \"POST\",\n          getUrl(requestOptions),\n          pipeline,\n          requestOptions,\n          allowInsecureConnection,\n          httpClient,\n        );\n      },\n      put: (requestOptions: RequestParameters = {}): StreamableMethod => {\n        return buildOperation(\n          \"PUT\",\n          getUrl(requestOptions),\n          pipeline,\n          requestOptions,\n          allowInsecureConnection,\n          httpClient,\n        );\n      },\n      patch: (requestOptions: RequestParameters = {}): StreamableMethod => {\n        return buildOperation(\n          \"PATCH\",\n          getUrl(requestOptions),\n          pipeline,\n          requestOptions,\n          allowInsecureConnection,\n          httpClient,\n        );\n      },\n      delete: (requestOptions: RequestParameters = {}): StreamableMethod => {\n        return buildOperation(\n          \"DELETE\",\n          getUrl(requestOptions),\n          pipeline,\n          requestOptions,\n          allowInsecureConnection,\n          httpClient,\n        );\n      },\n      head: (requestOptions: RequestParameters = {}): StreamableMethod => {\n        return buildOperation(\n          \"HEAD\",\n          getUrl(requestOptions),\n          pipeline,\n          requestOptions,\n          allowInsecureConnection,\n          httpClient,\n        );\n      },\n      options: (requestOptions: RequestParameters = {}): StreamableMethod => {\n        return buildOperation(\n          \"OPTIONS\",\n          getUrl(requestOptions),\n          pipeline,\n          requestOptions,\n          allowInsecureConnection,\n          httpClient,\n        );\n      },\n      trace: (requestOptions: RequestParameters = {}): StreamableMethod => {\n        return buildOperation(\n          \"TRACE\",\n          getUrl(requestOptions),\n          pipeline,\n          requestOptions,\n          allowInsecureConnection,\n          httpClient,\n        );\n      },\n    };\n  };\n\n  return {\n    path: client,\n    pathUnchecked: client,\n    pipeline,\n  };\n}\n\nfunction buildOperation(\n  method: HttpMethods,\n  url: string,\n  pipeline: Pipeline,\n  options: RequestParameters,\n  allowInsecureConnection?: boolean,\n  httpClient?: HttpClient,\n): StreamableMethod {\n  allowInsecureConnection = options.allowInsecureConnection ?? allowInsecureConnection;\n  return {\n    then: function (onFulfilled, onrejected) {\n      return sendRequest(\n        method,\n        url,\n        pipeline,\n        { ...options, allowInsecureConnection },\n        httpClient,\n      ).then(onFulfilled, onrejected);\n    },\n    async asBrowserStream() {\n      if (isNodeLike) {\n        throw new Error(\n          \"`asBrowserStream` is supported only in the browser environment. Use `asNodeStream` instead to obtain the response body stream. If you require a Web stream of the response in Node, consider using `Readable.toWeb` on the result of `asNodeStream`.\",\n        );\n      } else {\n        return sendRequest(\n          method,\n          url,\n          pipeline,\n          { ...options, allowInsecureConnection, responseAsStream: true },\n          httpClient,\n        ) as Promise<HttpBrowserStreamResponse>;\n      }\n    },\n    async asNodeStream() {\n      if (isNodeLike) {\n        return sendRequest(\n          method,\n          url,\n          pipeline,\n          { ...options, allowInsecureConnection, responseAsStream: true },\n          httpClient,\n        ) as Promise<HttpNodeStreamResponse>;\n      } else {\n        throw new Error(\n          \"`isNodeStream` is not supported in the browser environment. Use `asBrowserStream` to obtain the response body stream.\",\n        );\n      }\n    },\n  };\n}\n"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;;;;AAIlC,OAAO,EAAE,qBAAqB,EAAE,MAAM,oBAAoB,CAAC;AAU3D,OAAO,EAAE,WAAW,EAAE,MAAM,kBAAkB,CAAC;AAC/C,OAAO,EAAE,eAAe,EAAE,MAAM,iBAAiB,CAAC;AAClD,OAAO,EAAE,UAAU,EAAE,MAAM,6BAA6B,CAAC;;;;;AAQnD,SAAU,SAAS,CAAC,QAAgB,EAAE,gBAA+B,CAAA,CAAE;;IAC3E,MAAM,QAAQ,GAAG,CAAA,KAAA,aAAa,CAAC,QAAQ,MAAA,QAAA,OAAA,KAAA,IAAA,yMAAI,wBAAA,AAAqB,EAAC,aAAa,CAAC,CAAC;IAChF,IAAI,CAAA,KAAA,aAAa,CAAC,kBAAkB,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAE,MAAM,EAAE,CAAC;QAC7C,KAAK,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,aAAa,CAAC,kBAAkB,CAAE,CAAC;YACpE,2DAA2D;YAC3D,6CAA6C;YAC7C,MAAM,UAAU,GAAG,QAAQ,KAAK,UAAU,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS,CAAC;YAChE,QAAQ,CAAC,SAAS,CAAC,MAAM,EAAE;gBACzB,UAAU;aACX,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAED,MAAM,EAAE,uBAAuB,EAAE,UAAU,EAAE,GAAG,aAAa,CAAC;IAC9D,MAAM,WAAW,GAAG,CAAA,KAAA,aAAa,CAAC,QAAQ,MAAA,QAAA,OAAA,KAAA,IAAA,KAAI,QAAQ,CAAC;IACvD,MAAM,MAAM,GAAG,CAAC,IAAY,EAAE,GAAG,IAAgB,EAAqC,EAAE;QACtF,MAAM,MAAM,GAAG,CAAC,cAAiC,EAAU,EAAE,gMAC3D,kBAAA,AAAe,EAAC,WAAW,EAAE,IAAI,EAAE,IAAI,EAAA,OAAA,MAAA,CAAA;gBAAI,uBAAuB;YAAA,GAAK,cAAc,EAAG,CAAC;QAE3F,OAAO;YACL,GAAG,EAAE,CAAC,iBAAoC,CAAA,CAAE,EAAoB,EAAE;gBAChE,OAAO,cAAc,CACnB,KAAK,EACL,MAAM,CAAC,cAAc,CAAC,EACtB,QAAQ,EACR,cAAc,EACd,uBAAuB,EACvB,UAAU,CACX,CAAC;YACJ,CAAC;YACD,IAAI,EAAE,CAAC,iBAAoC,CAAA,CAAE,EAAoB,EAAE;gBACjE,OAAO,cAAc,CACnB,MAAM,EACN,MAAM,CAAC,cAAc,CAAC,EACtB,QAAQ,EACR,cAAc,EACd,uBAAuB,EACvB,UAAU,CACX,CAAC;YACJ,CAAC;YACD,GAAG,EAAE,CAAC,iBAAoC,CAAA,CAAE,EAAoB,EAAE;gBAChE,OAAO,cAAc,CACnB,KAAK,EACL,MAAM,CAAC,cAAc,CAAC,EACtB,QAAQ,EACR,cAAc,EACd,uBAAuB,EACvB,UAAU,CACX,CAAC;YACJ,CAAC;YACD,KAAK,EAAE,CAAC,iBAAoC,CAAA,CAAE,EAAoB,EAAE;gBAClE,OAAO,cAAc,CACnB,OAAO,EACP,MAAM,CAAC,cAAc,CAAC,EACtB,QAAQ,EACR,cAAc,EACd,uBAAuB,EACvB,UAAU,CACX,CAAC;YACJ,CAAC;YACD,MAAM,EAAE,CAAC,iBAAoC,CAAA,CAAE,EAAoB,EAAE;gBACnE,OAAO,cAAc,CACnB,QAAQ,EACR,MAAM,CAAC,cAAc,CAAC,EACtB,QAAQ,EACR,cAAc,EACd,uBAAuB,EACvB,UAAU,CACX,CAAC;YACJ,CAAC;YACD,IAAI,EAAE,CAAC,iBAAoC,CAAA,CAAE,EAAoB,EAAE;gBACjE,OAAO,cAAc,CACnB,MAAM,EACN,MAAM,CAAC,cAAc,CAAC,EACtB,QAAQ,EACR,cAAc,EACd,uBAAuB,EACvB,UAAU,CACX,CAAC;YACJ,CAAC;YACD,OAAO,EAAE,CAAC,iBAAoC,CAAA,CAAE,EAAoB,EAAE;gBACpE,OAAO,cAAc,CACnB,SAAS,EACT,MAAM,CAAC,cAAc,CAAC,EACtB,QAAQ,EACR,cAAc,EACd,uBAAuB,EACvB,UAAU,CACX,CAAC;YACJ,CAAC;YACD,KAAK,EAAE,CAAC,iBAAoC,CAAA,CAAE,EAAoB,EAAE;gBAClE,OAAO,cAAc,CACnB,OAAO,EACP,MAAM,CAAC,cAAc,CAAC,EACtB,QAAQ,EACR,cAAc,EACd,uBAAuB,EACvB,UAAU,CACX,CAAC;YACJ,CAAC;SACF,CAAC;IACJ,CAAC,CAAC;IAEF,OAAO;QACL,IAAI,EAAE,MAAM;QACZ,aAAa,EAAE,MAAM;QACrB,QAAQ;KACT,CAAC;AACJ,CAAC;AAED,SAAS,cAAc,CACrB,MAAmB,EACnB,GAAW,EACX,QAAkB,EAClB,OAA0B,EAC1B,uBAAiC,EACjC,UAAuB;;IAEvB,uBAAuB,GAAG,CAAA,KAAA,OAAO,CAAC,uBAAuB,MAAA,QAAA,OAAA,KAAA,IAAA,KAAI,uBAAuB,CAAC;IACrF,OAAO;QACL,IAAI,EAAE,SAAU,WAAW,EAAE,UAAU;YACrC,yMAAO,cAAA,AAAW,EAChB,MAAM,EACN,GAAG,EACH,QAAQ,EAAA,OAAA,MAAA,CAAA,OAAA,MAAA,CAAA,CAAA,GACH,OAAO,GAAA;gBAAE,uBAAuB;YAAA,IACrC,UAAU,CACX,CAAC,IAAI,CAAC,WAAW,EAAE,UAAU,CAAC,CAAC;QAClC,CAAC;QACD,KAAK,CAAC,eAAe;YACnB,IAAI,8MAAU,EAAE,CAAC;gBACf,MAAM,IAAI,KAAK,CACb,sPAAsP,CACvP,CAAC;YACJ,CAAC,MAAM,CAAC;gBACN,yMAAO,cAAA,AAAW,EAChB,MAAM,EACN,GAAG,EACH,QAAQ,EAAA,OAAA,MAAA,CAAA,OAAA,MAAA,CAAA,CAAA,GACH,OAAO,GAAA;oBAAE,uBAAuB;oBAAE,gBAAgB,EAAE,IAAI;gBAAA,IAC7D,UAAU,CAC2B,CAAC;YAC1C,CAAC;QACH,CAAC;QACD,KAAK,CAAC,YAAY;YAChB,qMAAI,aAAU,EAAE,CAAC;gBACf,yMAAO,cAAA,AAAW,EAChB,MAAM,EACN,GAAG,EACH,QAAQ,EAAA,OAAA,MAAA,CAAA,OAAA,MAAA,CAAA,CAAA,GACH,OAAO,GAAA;oBAAE,uBAAuB;oBAAE,gBAAgB,EAAE,IAAI;gBAAA,IAC7D,UAAU,CACwB,CAAC;YACvC,CAAC,MAAM,CAAC;gBACN,MAAM,IAAI,KAAK,CACb,uHAAuH,CACxH,CAAC;YACJ,CAAC;QACH,CAAC;KACF,CAAC;AACJ,CAAC", "debugId": null}}, {"offset": {"line": 3522, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@typespec/ts-http-runtime/dist/esm/client/operationOptionHelpers.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/node_modules/%40typespec/ts-http-runtime/src/client/operationOptionHelpers.ts"], "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type { OperationOptions, RequestParameters } from \"./common.js\";\n\n/**\n * Helper function to convert OperationOptions to RequestParameters\n * @param options - the options that are used by Modular layer to send the request\n * @returns the result of the conversion in RequestParameters of RLC layer\n */\nexport function operationOptionsToRequestParameters(options: OperationOptions): RequestParameters {\n  return {\n    allowInsecureConnection: options.requestOptions?.allowInsecureConnection,\n    timeout: options.requestOptions?.timeout,\n    skipUrlEncoding: options.requestOptions?.skipUrlEncoding,\n    abortSignal: options.abortSignal,\n    onUploadProgress: options.requestOptions?.onUploadProgress,\n    onDownloadProgress: options.requestOptions?.onDownloadProgress,\n    headers: { ...options.requestOptions?.headers },\n    onResponse: options.onResponse,\n  };\n}\n"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAIlC;;;;GAIG;;;AACG,SAAU,mCAAmC,CAAC,OAAyB;;IAC3E,OAAO;QACL,uBAAuB,EAAE,CAAA,KAAA,OAAO,CAAC,cAAc,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAE,uBAAuB;QACxE,OAAO,EAAE,CAAA,KAAA,OAAO,CAAC,cAAc,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAE,OAAO;QACxC,eAAe,EAAE,CAAA,KAAA,OAAO,CAAC,cAAc,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAE,eAAe;QACxD,WAAW,EAAE,OAAO,CAAC,WAAW;QAChC,gBAAgB,EAAE,CAAA,KAAA,OAAO,CAAC,cAAc,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAE,gBAAgB;QAC1D,kBAAkB,EAAE,CAAA,KAAA,OAAO,CAAC,cAAc,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAE,kBAAkB;QAC9D,OAAO,EAAA,OAAA,MAAA,CAAA,CAAA,GAAO,CAAA,KAAA,OAAO,CAAC,cAAc,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAE,OAAO,CAAE;QAC/C,UAAU,EAAE,OAAO,CAAC,UAAU;KAC/B,CAAC;AACJ,CAAC", "debugId": null}}, {"offset": {"line": 3548, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@typespec/ts-http-runtime/dist/esm/client/restError.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/node_modules/%40typespec/ts-http-runtime/src/client/restError.ts"], "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type { PipelineResponse } from \"../interfaces.js\";\nimport { RestError } from \"../restError.js\";\nimport { createHttpHeaders } from \"../httpHeaders.js\";\nimport type { PathUncheckedResponse } from \"./common.js\";\n\n/**\n * Creates a rest error from a PathUnchecked response\n */\nexport function createRestError(response: PathUncheckedResponse): RestError;\n/**\n * Creates a rest error from an error message and a PathUnchecked response\n */\nexport function createRestError(message: string, response: PathUncheckedResponse): RestError;\nexport function createRestError(\n  messageOrResponse: string | PathUncheckedResponse,\n  response?: PathUncheckedResponse,\n): RestError {\n  const resp = typeof messageOrResponse === \"string\" ? response! : messageOrResponse;\n  const internalError = resp.body?.error ?? resp.body;\n  const message =\n    typeof messageOrResponse === \"string\"\n      ? messageOrResponse\n      : (internalError?.message ?? `Unexpected status code: ${resp.status}`);\n  return new RestError(message, {\n    statusCode: statusCodeToNumber(resp.status),\n    code: internalError?.code,\n    request: resp.request,\n    response: toPipelineResponse(resp),\n  });\n}\n\nfunction toPipelineResponse(response: PathUncheckedResponse): PipelineResponse {\n  return {\n    headers: createHttpHeaders(response.headers),\n    request: response.request,\n    status: statusCodeToNumber(response.status) ?? -1,\n  };\n}\n\nfunction statusCodeToNumber(statusCode: string): number | undefined {\n  const status = Number.parseInt(statusCode);\n\n  return Number.isNaN(status) ? undefined : status;\n}\n"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;;;;AAGlC,OAAO,EAAE,SAAS,EAAE,MAAM,iBAAiB,CAAC;AAC5C,OAAO,EAAE,iBAAiB,EAAE,MAAM,mBAAmB,CAAC;;;AAWhD,SAAU,eAAe,CAC7B,iBAAiD,EACjD,QAAgC;;IAEhC,MAAM,IAAI,GAAG,OAAO,iBAAiB,KAAK,QAAQ,CAAC,CAAC,CAAC,QAAS,CAAC,CAAC,CAAC,iBAAiB,CAAC;IACnF,MAAM,aAAa,GAAG,CAAA,KAAA,CAAA,KAAA,IAAI,CAAC,IAAI,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAE,KAAK,MAAA,QAAA,OAAA,KAAA,IAAA,KAAI,IAAI,CAAC,IAAI,CAAC;IACpD,MAAM,OAAO,GACX,OAAO,iBAAiB,KAAK,QAAQ,GACjC,iBAAiB,GAChB,CAAA,KAAA,aAAa,KAAA,QAAb,aAAa,KAAA,KAAA,IAAA,KAAA,IAAb,aAAa,CAAE,OAAO,MAAA,QAAA,OAAA,KAAA,IAAA,KAAI,CAAA,wBAAA,EAA2B,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC;IAC3E,OAAO,sLAAI,YAAS,CAAC,OAAO,EAAE;QAC5B,UAAU,EAAE,kBAAkB,CAAC,IAAI,CAAC,MAAM,CAAC;QAC3C,IAAI,EAAE,aAAa,KAAA,QAAb,aAAa,KAAA,KAAA,IAAA,KAAA,IAAb,aAAa,CAAE,IAAI;QACzB,OAAO,EAAE,IAAI,CAAC,OAAO;QACrB,QAAQ,EAAE,kBAAkB,CAAC,IAAI,CAAC;KACnC,CAAC,CAAC;AACL,CAAC;AAED,SAAS,kBAAkB,CAAC,QAA+B;;IACzD,OAAO;QACL,OAAO,0LAAE,oBAAA,AAAiB,EAAC,QAAQ,CAAC,OAAO,CAAC;QAC5C,OAAO,EAAE,QAAQ,CAAC,OAAO;QACzB,MAAM,EAAE,CAAA,KAAA,kBAAkB,CAAC,QAAQ,CAAC,MAAM,CAAC,MAAA,QAAA,OAAA,KAAA,IAAA,KAAI,CAAC,CAAC;KAClD,CAAC;AACJ,CAAC;AAED,SAAS,kBAAkB,CAAC,UAAkB;IAC5C,MAAM,MAAM,GAAG,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;IAE3C,OAAO,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,MAAM,CAAC;AACnD,CAAC", "debugId": null}}, {"offset": {"line": 3585, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@typespec/ts-http-runtime/dist/esm/index.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/node_modules/%40typespec/ts-http-runtime/src/index.ts"], "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\ndeclare global {\n  interface FormData {}\n  interface Blob {}\n  interface File {}\n  interface ReadableStream<R = any> {}\n  interface TransformStream<I = any, O = any> {}\n}\n\nexport { AbortError } from \"./abort-controller/AbortError.js\";\nexport {\n  createClientLogger,\n  getLogLevel,\n  setLogLevel,\n  TypeSpecRuntimeLogger,\n  type Debugger,\n  type TypeSpecRuntimeClientLogger,\n  type TypeSpecRuntimeLogLevel,\n} from \"./logger/logger.js\";\n\nexport type {\n  BodyPart,\n  FormDataValue,\n  RawHttpHeaders,\n  KeyObject,\n  PxfObject,\n  HttpClient,\n  PipelineRequest,\n  PipelineResponse,\n  SendRequest,\n  TlsSettings,\n  Agent,\n  RequestBodyType,\n  FormDataMap,\n  HttpHeaders,\n  HttpMethods,\n  MultipartRequestBody,\n  TransferProgressEvent,\n  ProxySettings,\n  RawHttpHeadersInput,\n  PipelineRetryOptions,\n} from \"./interfaces.js\";\nexport { createHttpHeaders } from \"./httpHeaders.js\";\nexport * from \"./auth/schemes.js\";\nexport * from \"./auth/oauth2Flows.js\";\nexport {\n  type BasicCredential,\n  type BearerTokenCredential,\n  type OAuth2TokenCredential,\n  type GetOAuth2TokenOptions,\n  type GetBearerTokenOptions,\n  type ApiKeyCredential,\n  type ClientCredential,\n} from \"./auth/credentials.js\";\nexport { createPipelineRequest, type PipelineRequestOptions } from \"./pipelineRequest.js\";\nexport {\n  type Pipeline,\n  type PipelinePolicy,\n  type AddPolicyOptions,\n  type PipelinePhase,\n  createEmptyPipeline,\n} from \"./pipeline.js\";\nexport { RestError, isRestError, type RestErrorOptions } from \"./restError.js\";\nexport { stringToUint8Array, uint8ArrayToString, type EncodingType } from \"./util/bytesEncoding.js\";\nexport { createDefaultHttpClient } from \"./defaultHttpClient.js\";\nexport { getClient } from \"./client/getClient.js\";\nexport { operationOptionsToRequestParameters } from \"./client/operationOptionHelpers.js\";\nexport { createRestError } from \"./client/restError.js\";\nexport type {\n  Client,\n  ClientOptions,\n  OperationOptions,\n  AdditionalPolicyConfig,\n  PathUnchecked,\n  PathUncheckedResponse,\n  HttpResponse,\n  RawResponseCallback,\n  OperationRequestOptions,\n  PathParameters,\n  ResourceMethods,\n  PathParameterWithOptions,\n  StreamableMethod,\n  RequestParameters,\n  HttpNodeStreamResponse,\n  HttpBrowserStreamResponse,\n  FullOperationResponse,\n} from \"./client/common.js\";\nexport type { PipelineOptions, TelemetryOptions } from \"./createPipelineFromOptions.js\";\nexport type { LogPolicyOptions } from \"./policies/logPolicy.js\";\nexport type { RedirectPolicyOptions } from \"./policies/redirectPolicy.js\";\nexport type { UserAgentPolicyOptions } from \"./policies/userAgentPolicy.js\";\n"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;;AAUlC,OAAO,EAAE,UAAU,EAAE,MAAM,kCAAkC,CAAC;AAC9D,OAAO,EACL,kBAAkB,EAClB,WAAW,EACX,WAAW,EACX,qBAAqB,GAItB,MAAM,oBAAoB,CAAC;AAwB5B,OAAO,EAAE,iBAAiB,EAAE,MAAM,kBAAkB,CAAC;AACrD,cAAc,mBAAmB,CAAC;AAClC,cAAc,uBAAuB,CAAC;AAUtC,OAAO,EAAE,qBAAqB,EAA+B,MAAM,sBAAsB,CAAC;AAC1F,OAAO,EAKL,mBAAmB,GACpB,MAAM,eAAe,CAAC;AACvB,OAAO,EAAE,SAAS,EAAE,WAAW,EAAyB,MAAM,gBAAgB,CAAC;AAC/E,OAAO,EAAE,kBAAkB,EAAE,kBAAkB,EAAqB,MAAM,yBAAyB,CAAC;AACpG,OAAO,EAAE,uBAAuB,EAAE,MAAM,wBAAwB,CAAC;AACjE,OAAO,EAAE,SAAS,EAAE,MAAM,uBAAuB,CAAC;AAClD,OAAO,EAAE,mCAAmC,EAAE,MAAM,oCAAoC,CAAC;AACzF,OAAO,EAAE,eAAe,EAAE,MAAM,uBAAuB,CAAC", "debugId": null}}, {"offset": {"line": 3637, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@typespec/ts-http-runtime/dist/esm/logger/internal.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/node_modules/%40typespec/ts-http-runtime/src/logger/internal.ts"], "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nexport {\n  createLoggerContext,\n  type CreateLoggerContextOptions,\n  type LoggerContext,\n} from \"./logger.js\";\n"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;;AAElC,OAAO,EACL,mBAAmB,GAGpB,MAAM,aAAa,CAAC", "debugId": null}}, {"offset": {"line": 3653, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@typespec/ts-http-runtime/dist/esm/policies/exponentialRetryPolicy.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/node_modules/%40typespec/ts-http-runtime/src/policies/exponentialRetryPolicy.ts"], "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type { PipelinePolicy } from \"../pipeline.js\";\nimport { exponentialRetryStrategy } from \"../retryStrategies/exponentialRetryStrategy.js\";\nimport { retryPolicy } from \"./retryPolicy.js\";\nimport { DEFAULT_RETRY_POLICY_COUNT } from \"../constants.js\";\n\n/**\n * The programmatic identifier of the exponentialRetryPolicy.\n */\nexport const exponentialRetryPolicyName = \"exponentialRetryPolicy\";\n\n/**\n * Options that control how to retry failed requests.\n */\nexport interface ExponentialRetryPolicyOptions {\n  /**\n   * The maximum number of retry attempts. Defaults to 3.\n   */\n  maxRetries?: number;\n\n  /**\n   * The amount of delay in milliseconds between retry attempts. Defaults to 1000\n   * (1 second.) The delay increases exponentially with each retry up to a maximum\n   * specified by maxRetryDelayInMs.\n   */\n  retryDelayInMs?: number;\n\n  /**\n   * The maximum delay in milliseconds allowed before retrying an operation. Defaults\n   * to 64000 (64 seconds).\n   */\n  maxRetryDelayInMs?: number;\n}\n\n/**\n * A policy that attempts to retry requests while introducing an exponentially increasing delay.\n * @param options - Options that configure retry logic.\n */\nexport function exponentialRetryPolicy(\n  options: ExponentialRetryPolicyOptions = {},\n): PipelinePolicy {\n  return retryPolicy(\n    [\n      exponentialRetryStrategy({\n        ...options,\n        ignoreSystemErrors: true,\n      }),\n    ],\n    {\n      maxRetries: options.maxRetries ?? DEFAULT_RETRY_POLICY_COUNT,\n    },\n  );\n}\n"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;;;;;AAGlC,OAAO,EAAE,wBAAwB,EAAE,MAAM,gDAAgD,CAAC;AAC1F,OAAO,EAAE,WAAW,EAAE,MAAM,kBAAkB,CAAC;AAC/C,OAAO,EAAE,0BAA0B,EAAE,MAAM,iBAAiB,CAAC;;;;AAKtD,MAAM,0BAA0B,GAAG,wBAAwB,CAAC;AA6B7D,SAAU,sBAAsB,CACpC,UAAyC,CAAA,CAAE;;IAE3C,2MAAO,cAAA,AAAW,EAChB;gOACE,2BAAA,AAAwB,EAAA,OAAA,MAAA,CAAA,OAAA,MAAA,CAAA,CAAA,GACnB,OAAO,GAAA;YACV,kBAAkB,EAAE,IAAI;QAAA,GACxB;KACH,EACD;QACE,UAAU,EAAE,CAAA,KAAA,OAAO,CAAC,UAAU,MAAA,QAAA,OAAA,KAAA,IAAA,uLAAI,6BAA0B;KAC7D,CACF,CAAC;AACJ,CAAC", "debugId": null}}, {"offset": {"line": 3680, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@typespec/ts-http-runtime/dist/esm/policies/systemErrorRetryPolicy.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/node_modules/%40typespec/ts-http-runtime/src/policies/systemErrorRetryPolicy.ts"], "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type { PipelinePolicy } from \"../pipeline.js\";\nimport { exponentialRetryStrategy } from \"../retryStrategies/exponentialRetryStrategy.js\";\nimport { retryPolicy } from \"./retryPolicy.js\";\nimport { DEFAULT_RETRY_POLICY_COUNT } from \"../constants.js\";\n\n/**\n * Name of the {@link systemErrorRetryPolicy}\n */\nexport const systemErrorRetryPolicyName = \"systemErrorRetryPolicy\";\n\n/**\n * Options that control how to retry failed requests.\n */\nexport interface SystemErrorRetryPolicyOptions {\n  /**\n   * The maximum number of retry attempts. Defaults to 3.\n   */\n  maxRetries?: number;\n\n  /**\n   * The amount of delay in milliseconds between retry attempts. Defaults to 1000\n   * (1 second.) The delay increases exponentially with each retry up to a maximum\n   * specified by maxRetryDelayInMs.\n   */\n  retryDelayInMs?: number;\n\n  /**\n   * The maximum delay in milliseconds allowed before retrying an operation. Defaults\n   * to 64000 (64 seconds).\n   */\n  maxRetryDelayInMs?: number;\n}\n\n/**\n * A retry policy that specifically seeks to handle errors in the\n * underlying transport layer (e.g. DNS lookup failures) rather than\n * retryable error codes from the server itself.\n * @param options - Options that customize the policy.\n */\nexport function systemErrorRetryPolicy(\n  options: SystemErrorRetryPolicyOptions = {},\n): PipelinePolicy {\n  return {\n    name: systemErrorRetryPolicyName,\n    sendRequest: retryPolicy(\n      [\n        exponentialRetryStrategy({\n          ...options,\n          ignoreHttpStatusCodes: true,\n        }),\n      ],\n      {\n        maxRetries: options.maxRetries ?? DEFAULT_RETRY_POLICY_COUNT,\n      },\n    ).sendRequest,\n  };\n}\n"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;;;;;AAGlC,OAAO,EAAE,wBAAwB,EAAE,MAAM,gDAAgD,CAAC;AAC1F,OAAO,EAAE,WAAW,EAAE,MAAM,kBAAkB,CAAC;AAC/C,OAAO,EAAE,0BAA0B,EAAE,MAAM,iBAAiB,CAAC;;;;AAKtD,MAAM,0BAA0B,GAAG,wBAAwB,CAAC;AA+B7D,SAAU,sBAAsB,CACpC,UAAyC,CAAA,CAAE;;IAE3C,OAAO;QACL,IAAI,EAAE,0BAA0B;QAChC,WAAW,sMAAE,cAAA,AAAW,EACtB;oOACE,2BAAA,AAAwB,EAAA,OAAA,MAAA,CAAA,OAAA,MAAA,CAAA,CAAA,GACnB,OAAO,GAAA;gBACV,qBAAqB,EAAE,IAAI;YAAA,GAC3B;SACH,EACD;YACE,UAAU,EAAE,CAAA,KAAA,OAAO,CAAC,UAAU,MAAA,QAAA,OAAA,KAAA,IAAA,uLAAI,6BAA0B;SAC7D,CACF,CAAC,WAAW;KACd,CAAC;AACJ,CAAC", "debugId": null}}, {"offset": {"line": 3710, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@typespec/ts-http-runtime/dist/esm/policies/throttlingRetryPolicy.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/node_modules/%40typespec/ts-http-runtime/src/policies/throttlingRetryPolicy.ts"], "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type { PipelinePolicy } from \"../pipeline.js\";\nimport { throttlingRetryStrategy } from \"../retryStrategies/throttlingRetryStrategy.js\";\nimport { retryPolicy } from \"./retryPolicy.js\";\nimport { DEFAULT_RETRY_POLICY_COUNT } from \"../constants.js\";\n\n/**\n * Name of the {@link throttlingRetryPolicy}\n */\nexport const throttlingRetryPolicyName = \"throttlingRetryPolicy\";\n\n/**\n * Options that control how to retry failed requests.\n */\nexport interface ThrottlingRetryPolicyOptions {\n  /**\n   * The maximum number of retry attempts. Defaults to 3.\n   */\n  maxRetries?: number;\n}\n\n/**\n * A policy that retries when the server sends a 429 response with a Retry-After header.\n *\n * To learn more, please refer to\n * https://learn.microsoft.com/azure/azure-resource-manager/resource-manager-request-limits,\n * https://learn.microsoft.com/azure/azure-subscription-service-limits and\n * https://learn.microsoft.com/azure/virtual-machines/troubleshooting/troubleshooting-throttling-errors\n *\n * @param options - Options that configure retry logic.\n */\nexport function throttlingRetryPolicy(options: ThrottlingRetryPolicyOptions = {}): PipelinePolicy {\n  return {\n    name: throttlingRetryPolicyName,\n    sendRequest: retryPolicy([throttlingRetryStrategy()], {\n      maxRetries: options.maxRetries ?? DEFAULT_RETRY_POLICY_COUNT,\n    }).sendRequest,\n  };\n}\n"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;;;;;AAGlC,OAAO,EAAE,uBAAuB,EAAE,MAAM,+CAA+C,CAAC;AACxF,OAAO,EAAE,WAAW,EAAE,MAAM,kBAAkB,CAAC;AAC/C,OAAO,EAAE,0BAA0B,EAAE,MAAM,iBAAiB,CAAC;;;;AAKtD,MAAM,yBAAyB,GAAG,uBAAuB,CAAC;AAsB3D,SAAU,qBAAqB,CAAC,UAAwC,CAAA,CAAE;;IAC9E,OAAO;QACL,IAAI,EAAE,yBAAyB;QAC/B,WAAW,sMAAE,cAAA,AAAW,EAAC;mOAAC,0BAAA,AAAuB,EAAE;SAAC,EAAE;YACpD,UAAU,EAAE,CAAA,KAAA,OAAO,CAAC,UAAU,MAAA,QAAA,OAAA,KAAA,IAAA,uLAAI,6BAA0B;SAC7D,CAAC,CAAC,WAAW;KACf,CAAC;AACJ,CAAC", "debugId": null}}, {"offset": {"line": 3738, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@typespec/ts-http-runtime/dist/esm/policies/internal.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/node_modules/%40typespec/ts-http-runtime/src/policies/internal.ts"], "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nexport { agentPolicy, agentPolicyName } from \"./agentPolicy.js\";\nexport {\n  decompressResponsePolicy,\n  decompressResponsePolicyName,\n} from \"./decompressResponsePolicy.js\";\nexport {\n  defaultRetryPolicy,\n  defaultRetryPolicyName,\n  DefaultRetryPolicyOptions,\n} from \"./defaultRetryPolicy.js\";\nexport {\n  exponentialRetryPolicy,\n  exponentialRetryPolicyName,\n  ExponentialRetryPolicyOptions,\n} from \"./exponentialRetryPolicy.js\";\nexport { retryPolicy, RetryPolicyOptions } from \"./retryPolicy.js\";\nexport {\n  RetryInformation,\n  RetryModifiers,\n  RetryStrategy,\n} from \"../retryStrategies/retryStrategy.js\";\nexport { systemErrorRetryPolicy, systemErrorRetryPolicyName } from \"./systemErrorRetryPolicy.js\";\nexport { throttlingRetryPolicy, throttlingRetryPolicyName } from \"./throttlingRetryPolicy.js\";\nexport { formDataPolicy, formDataPolicyName } from \"./formDataPolicy.js\";\nexport { logPolicy, logPolicyName, LogPolicyOptions } from \"./logPolicy.js\";\nexport { multipartPolicy, multipartPolicyName } from \"./multipartPolicy.js\";\nexport { proxyPolicy, proxyPolicyName, getDefaultProxySettings } from \"./proxyPolicy.js\";\nexport { redirectPolicy, redirectPolicyName, RedirectPolicyOptions } from \"./redirectPolicy.js\";\nexport { tlsPolicy, tlsPolicyName } from \"./tlsPolicy.js\";\nexport { userAgentPolicy, userAgentPolicyName, UserAgentPolicyOptions } from \"./userAgentPolicy.js\";\n"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;;AAElC,OAAO,EAAE,WAAW,EAAE,eAAe,EAAE,MAAM,kBAAkB,CAAC;AAChE,OAAO,EACL,wBAAwB,EACxB,4BAA4B,GAC7B,MAAM,+BAA+B,CAAC;AACvC,OAAO,EACL,kBAAkB,EAClB,sBAAsB,GAEvB,MAAM,yBAAyB,CAAC;AACjC,OAAO,EACL,sBAAsB,EACtB,0BAA0B,GAE3B,MAAM,6BAA6B,CAAC;AACrC,OAAO,EAAE,WAAW,EAAsB,MAAM,kBAAkB,CAAC;AAMnE,OAAO,EAAE,sBAAsB,EAAE,0BAA0B,EAAE,MAAM,6BAA6B,CAAC;AACjG,OAAO,EAAE,qBAAqB,EAAE,yBAAyB,EAAE,MAAM,4BAA4B,CAAC;AAC9F,OAAO,EAAE,cAAc,EAAE,kBAAkB,EAAE,MAAM,qBAAqB,CAAC;AACzE,OAAO,EAAE,SAAS,EAAE,aAAa,EAAoB,MAAM,gBAAgB,CAAC;AAC5E,OAAO,EAAE,eAAe,EAAE,mBAAmB,EAAE,MAAM,sBAAsB,CAAC;AAC5E,OAAO,EAAE,WAAW,EAAE,eAAe,EAAE,uBAAuB,EAAE,MAAM,kBAAkB,CAAC;AACzF,OAAO,EAAE,cAAc,EAAE,kBAAkB,EAAyB,MAAM,qBAAqB,CAAC;AAChG,OAAO,EAAE,SAAS,EAAE,aAAa,EAAE,MAAM,gBAAgB,CAAC;AAC1D,OAAO,EAAE,eAAe,EAAE,mBAAmB,EAA0B,MAAM,sBAAsB,CAAC", "debugId": null}}]}