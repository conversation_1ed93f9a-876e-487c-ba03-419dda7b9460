/* [next]/internal/font/google/poppins_571a6e72.module.css [app-client] (css) */
@font-face {
  font-family: Poppins;
  font-style: normal;
  font-weight: 100;
  font-display: swap;
  src: url("../media/3bffb43194241cce-s.477e9f30.woff2") format("woff2");
  unicode-range: U+900-97F, U+1CD0-1CF9, U+200C-200D, U+20A8, U+20B9, U+20F0, U+25CC, U+A830-A839, U+A8E0-A8FF, U+11B00-11B09;
}

@font-face {
  font-family: Poppins;
  font-style: normal;
  font-weight: 100;
  font-display: swap;
  src: url("../media/ff039fa6eaa5c1c6-s.f058f0cf.woff2") format("woff2");
  unicode-range: U+100-2BA, U+2BD-2C5, U+2C7-2CC, U+2CE-2D7, U+2DD-2FF, U+304, U+308, U+329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}

@font-face {
  font-family: Poppins;
  font-style: normal;
  font-weight: 100;
  font-display: swap;
  src: url("../media/7cd0eb8b4950cc84-s.p.65d31cf5.woff2") format("woff2");
  unicode-range: U+??, U+131, U+152-153, U+2BB-2BC, U+2C6, U+2DA, U+2DC, U+304, U+308, U+329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: Poppins;
  font-style: normal;
  font-weight: 200;
  font-display: swap;
  src: url("../media/d386bda3e023dcb1-s.796e930e.woff2") format("woff2");
  unicode-range: U+900-97F, U+1CD0-1CF9, U+200C-200D, U+20A8, U+20B9, U+20F0, U+25CC, U+A830-A839, U+A8E0-A8FF, U+11B00-11B09;
}

@font-face {
  font-family: Poppins;
  font-style: normal;
  font-weight: 200;
  font-display: swap;
  src: url("../media/5b0be09c114b4478-s.a000242e.woff2") format("woff2");
  unicode-range: U+100-2BA, U+2BD-2C5, U+2C7-2CC, U+2CE-2D7, U+2DD-2FF, U+304, U+308, U+329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}

@font-face {
  font-family: Poppins;
  font-style: normal;
  font-weight: 200;
  font-display: swap;
  src: url("../media/ab9c058895d1e899-s.p.79a0f3e8.woff2") format("woff2");
  unicode-range: U+??, U+131, U+152-153, U+2BB-2BC, U+2C6, U+2DA, U+2DC, U+304, U+308, U+329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: Poppins;
  font-style: normal;
  font-weight: 300;
  font-display: swap;
  src: url("../media/d8581755e6c30311-s.07792bab.woff2") format("woff2");
  unicode-range: U+900-97F, U+1CD0-1CF9, U+200C-200D, U+20A8, U+20B9, U+20F0, U+25CC, U+A830-A839, U+A8E0-A8FF, U+11B00-11B09;
}

@font-face {
  font-family: Poppins;
  font-style: normal;
  font-weight: 300;
  font-display: swap;
  src: url("../media/1c465740c14e2ea7-s.8da2481e.woff2") format("woff2");
  unicode-range: U+100-2BA, U+2BD-2C5, U+2C7-2CC, U+2CE-2D7, U+2DD-2FF, U+304, U+308, U+329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}

@font-face {
  font-family: Poppins;
  font-style: normal;
  font-weight: 300;
  font-display: swap;
  src: url("../media/34af13152f33a24c-s.p.11d83409.woff2") format("woff2");
  unicode-range: U+??, U+131, U+152-153, U+2BB-2BC, U+2C6, U+2DA, U+2DC, U+304, U+308, U+329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: Poppins;
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url("../media/7457c36979e2d949-s.f7608c0b.woff2") format("woff2");
  unicode-range: U+900-97F, U+1CD0-1CF9, U+200C-200D, U+20A8, U+20B9, U+20F0, U+25CC, U+A830-A839, U+A8E0-A8FF, U+11B00-11B09;
}

@font-face {
  font-family: Poppins;
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url("../media/f711ea5208caf77c-s.bfdffbb8.woff2") format("woff2");
  unicode-range: U+100-2BA, U+2BD-2C5, U+2C7-2CC, U+2CE-2D7, U+2DD-2FF, U+304, U+308, U+329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}

@font-face {
  font-family: Poppins;
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url("../media/bca38f7a2c3c301d-s.p.c336a9d1.woff2") format("woff2");
  unicode-range: U+??, U+131, U+152-153, U+2BB-2BC, U+2C6, U+2DA, U+2DC, U+304, U+308, U+329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: Poppins;
  font-style: normal;
  font-weight: 500;
  font-display: swap;
  src: url("../media/ecefa4aa01b6ffd1-s.a02d710f.woff2") format("woff2");
  unicode-range: U+900-97F, U+1CD0-1CF9, U+200C-200D, U+20A8, U+20B9, U+20F0, U+25CC, U+A830-A839, U+A8E0-A8FF, U+11B00-11B09;
}

@font-face {
  font-family: Poppins;
  font-style: normal;
  font-weight: 500;
  font-display: swap;
  src: url("../media/2127cdb37b33970d-s.87c6fae1.woff2") format("woff2");
  unicode-range: U+100-2BA, U+2BD-2C5, U+2C7-2CC, U+2CE-2D7, U+2DD-2FF, U+304, U+308, U+329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}

@font-face {
  font-family: Poppins;
  font-style: normal;
  font-weight: 500;
  font-display: swap;
  src: url("../media/f7bb5d5adbf9d12c-s.p.ab5dbd7e.woff2") format("woff2");
  unicode-range: U+??, U+131, U+152-153, U+2BB-2BC, U+2C6, U+2DA, U+2DC, U+304, U+308, U+329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: Poppins;
  font-style: normal;
  font-weight: 600;
  font-display: swap;
  src: url("../media/e46e47d6d5544f83-s.55ef469a.woff2") format("woff2");
  unicode-range: U+900-97F, U+1CD0-1CF9, U+200C-200D, U+20A8, U+20B9, U+20F0, U+25CC, U+A830-A839, U+A8E0-A8FF, U+11B00-11B09;
}

@font-face {
  font-family: Poppins;
  font-style: normal;
  font-weight: 600;
  font-display: swap;
  src: url("../media/651d350bd5dd4372-s.cc58d8dc.woff2") format("woff2");
  unicode-range: U+100-2BA, U+2BD-2C5, U+2C7-2CC, U+2CE-2D7, U+2DD-2FF, U+304, U+308, U+329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}

@font-face {
  font-family: Poppins;
  font-style: normal;
  font-weight: 600;
  font-display: swap;
  src: url("../media/113b2251aa49fd3e-s.p.dd0b3887.woff2") format("woff2");
  unicode-range: U+??, U+131, U+152-153, U+2BB-2BC, U+2C6, U+2DA, U+2DC, U+304, U+308, U+329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: Poppins;
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url("../media/dbf7ae5b97dd834f-s.45055564.woff2") format("woff2");
  unicode-range: U+900-97F, U+1CD0-1CF9, U+200C-200D, U+20A8, U+20B9, U+20F0, U+25CC, U+A830-A839, U+A8E0-A8FF, U+11B00-11B09;
}

@font-face {
  font-family: Poppins;
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url("../media/21a2a96dc1afeb07-s.0c442f40.woff2") format("woff2");
  unicode-range: U+100-2BA, U+2BD-2C5, U+2C7-2CC, U+2CE-2D7, U+2DD-2FF, U+304, U+308, U+329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}

@font-face {
  font-family: Poppins;
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url("../media/d492bb9c458a4d65-s.p.d95035ce.woff2") format("woff2");
  unicode-range: U+??, U+131, U+152-153, U+2BB-2BC, U+2C6, U+2DA, U+2DC, U+304, U+308, U+329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: Poppins;
  font-style: normal;
  font-weight: 800;
  font-display: swap;
  src: url("../media/367d29c7e3a2e400-s.8e4a632a.woff2") format("woff2");
  unicode-range: U+900-97F, U+1CD0-1CF9, U+200C-200D, U+20A8, U+20B9, U+20F0, U+25CC, U+A830-A839, U+A8E0-A8FF, U+11B00-11B09;
}

@font-face {
  font-family: Poppins;
  font-style: normal;
  font-weight: 800;
  font-display: swap;
  src: url("../media/a08be17b2fb7e083-s.65d48da8.woff2") format("woff2");
  unicode-range: U+100-2BA, U+2BD-2C5, U+2C7-2CC, U+2CE-2D7, U+2DD-2FF, U+304, U+308, U+329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}

@font-face {
  font-family: Poppins;
  font-style: normal;
  font-weight: 800;
  font-display: swap;
  src: url("../media/905898e29e555e94-s.p.d66bbad7.woff2") format("woff2");
  unicode-range: U+??, U+131, U+152-153, U+2BB-2BC, U+2C6, U+2DA, U+2DC, U+304, U+308, U+329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: Poppins;
  font-style: normal;
  font-weight: 900;
  font-display: swap;
  src: url("../media/7a1653613c192e0a-s.9d1b71df.woff2") format("woff2");
  unicode-range: U+900-97F, U+1CD0-1CF9, U+200C-200D, U+20A8, U+20B9, U+20F0, U+25CC, U+A830-A839, U+A8E0-A8FF, U+11B00-11B09;
}

@font-face {
  font-family: Poppins;
  font-style: normal;
  font-weight: 900;
  font-display: swap;
  src: url("../media/46699bd0dc0d2892-s.d42316f9.woff2") format("woff2");
  unicode-range: U+100-2BA, U+2BD-2C5, U+2C7-2CC, U+2CE-2D7, U+2DD-2FF, U+304, U+308, U+329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}

@font-face {
  font-family: Poppins;
  font-style: normal;
  font-weight: 900;
  font-display: swap;
  src: url("../media/a390b2a3673c72e3-s.p.c194becc.woff2") format("woff2");
  unicode-range: U+??, U+131, U+152-153, U+2BB-2BC, U+2C6, U+2DA, U+2DC, U+304, U+308, U+329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: Poppins Fallback;
  src: local(Arial);
  ascent-override: 93.62%;
  descent-override: 31.21%;
  line-gap-override: 8.92%;
  size-adjust: 112.16%;
}

.poppins_571a6e72-module__yksj5q__className {
  font-family: Poppins, Poppins Fallback;
  font-style: normal;
}

.poppins_571a6e72-module__yksj5q__variable {
  --font-poppins: "Poppins", "Poppins Fallback";
}

/* [next]/internal/font/google/roboto_ff541327.module.css [app-client] (css) */
@font-face {
  font-family: Roboto;
  font-style: normal;
  font-weight: 100;
  font-stretch: 100%;
  font-display: swap;
  src: url("../media/a49b7466783b98a7-s.37f0ec53.woff2") format("woff2");
  unicode-range: U+460-52F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
}

@font-face {
  font-family: Roboto;
  font-style: normal;
  font-weight: 100;
  font-stretch: 100%;
  font-display: swap;
  src: url("../media/a434524554b4cbdf-s.deaa4120.woff2") format("woff2");
  unicode-range: U+301, U+400-45F, U+490-491, U+4B0-4B1, U+2116;
}

@font-face {
  font-family: Roboto;
  font-style: normal;
  font-weight: 100;
  font-stretch: 100%;
  font-display: swap;
  src: url("../media/03566f5e37ffd2dc-s.377ca26f.woff2") format("woff2");
  unicode-range: U+1F??;
}

@font-face {
  font-family: Roboto;
  font-style: normal;
  font-weight: 100;
  font-stretch: 100%;
  font-display: swap;
  src: url("../media/c386a28af0fdd133-s.faccc9eb.woff2") format("woff2");
  unicode-range: U+370-377, U+37A-37F, U+384-38A, U+38C, U+38E-3A1, U+3A3-3FF;
}

@font-face {
  font-family: Roboto;
  font-style: normal;
  font-weight: 100;
  font-stretch: 100%;
  font-display: swap;
  src: url("../media/bebec4eee24d770e-s.e676bb0a.woff2") format("woff2");
  unicode-range: U+302-303, U+305, U+307-308, U+310, U+312, U+315, U+31A, U+326-327, U+32C, U+32F-330, U+332-333, U+338, U+33A, U+346, U+34D, U+391-3A1, U+3A3-3A9, U+3B1-3C9, U+3D1, U+3D5-3D6, U+3F0-3F1, U+3F4-3F5, U+2016-2017, U+2034-2038, U+203C, U+2040, U+2043, U+2047, U+2050, U+2057, U+205F, U+2070-2071, U+2074-208E, U+2090-209C, U+20D0-20DC, U+20E1, U+20E5-20EF, U+2100-2112, U+2114-2115, U+2117-2121, U+2123-214F, U+2190, U+2192, U+2194-21AE, U+21B0-21E5, U+21F1-21F2, U+21F4-2211, U+2213-2214, U+2216-22FF, U+2308-230B, U+2310, U+2319, U+231C-2321, U+2336-237A, U+237C, U+2395, U+239B-23B7, U+23D0, U+23DC-23E1, U+2474-2475, U+25AF, U+25B3, U+25B7, U+25BD, U+25C1, U+25CA, U+25CC, U+25FB, U+266D-266F, U+27C0-27FF, U+2900-2AFF, U+2B0E-2B11, U+2B30-2B4C, U+2BFE, U+3030, U+FF5B, U+FF5D, U+1D400-1D7FF, U+1EE??;
}

@font-face {
  font-family: Roboto;
  font-style: normal;
  font-weight: 100;
  font-stretch: 100%;
  font-display: swap;
  src: url("../media/d8fb98b47816a157-s.27669ba6.woff2") format("woff2");
  unicode-range: U+1-C, U+E-1F, U+7F-9F, U+20DD-20E0, U+20E2-20E4, U+2150-218F, U+2190, U+2192, U+2194-2199, U+21AF, U+21E6-21F0, U+21F3, U+2218-2219, U+2299, U+22C4-22C6, U+2300-243F, U+2440-244A, U+2460-24FF, U+25A0-27BF, U+28??, U+2921-2922, U+2981, U+29BF, U+29EB, U+2B??, U+4DC0-4DFF, U+FFF9-FFFB, U+10140-1018E, U+10190-1019C, U+101A0, U+101D0-101FD, U+102E0-102FB, U+10E60-10E7E, U+1D2C0-1D2D3, U+1D2E0-1D37F, U+1F0??, U+1F100-1F1AD, U+1F1E6-1F1FF, U+1F30D-1F30F, U+1F315, U+1F31C, U+1F31E, U+1F320-1F32C, U+1F336, U+1F378, U+1F37D, U+1F382, U+1F393-1F39F, U+1F3A7-1F3A8, U+1F3AC-1F3AF, U+1F3C2, U+1F3C4-1F3C6, U+1F3CA-1F3CE, U+1F3D4-1F3E0, U+1F3ED, U+1F3F1-1F3F3, U+1F3F5-1F3F7, U+1F408, U+1F415, U+1F41F, U+1F426, U+1F43F, U+1F441-1F442, U+1F444, U+1F446-1F449, U+1F44C-1F44E, U+1F453, U+1F46A, U+1F47D, U+1F4A3, U+1F4B0, U+1F4B3, U+1F4B9, U+1F4BB, U+1F4BF, U+1F4C8-1F4CB, U+1F4D6, U+1F4DA, U+1F4DF, U+1F4E3-1F4E6, U+1F4EA-1F4ED, U+1F4F7, U+1F4F9-1F4FB, U+1F4FD-1F4FE, U+1F503, U+1F507-1F50B, U+1F50D, U+1F512-1F513, U+1F53E-1F54A, U+1F54F-1F5FA, U+1F610, U+1F650-1F67F, U+1F687, U+1F68D, U+1F691, U+1F694, U+1F698, U+1F6AD, U+1F6B2, U+1F6B9-1F6BA, U+1F6BC, U+1F6C6-1F6CF, U+1F6D3-1F6D7, U+1F6E0-1F6EA, U+1F6F0-1F6F3, U+1F6F7-1F6FC, U+1F7??, U+1F800-1F80B, U+1F810-1F847, U+1F850-1F859, U+1F860-1F887, U+1F890-1F8AD, U+1F8B0-1F8BB, U+1F8C0-1F8C1, U+1F900-1F90B, U+1F93B, U+1F946, U+1F984, U+1F996, U+1F9E9, U+1FA00-1FA6F, U+1FA70-1FA7C, U+1FA80-1FA89, U+1FA8F-1FAC6, U+1FACE-1FADC, U+1FADF-1FAE9, U+1FAF0-1FAF8, U+1FB??;
}

@font-face {
  font-family: Roboto;
  font-style: normal;
  font-weight: 100;
  font-stretch: 100%;
  font-display: swap;
  src: url("../media/a47057a7a4d8f605-s.99770768.woff2") format("woff2");
  unicode-range: U+102-103, U+110-111, U+128-129, U+168-169, U+1A0-1A1, U+1AF-1B0, U+300-301, U+303-304, U+308-309, U+323, U+329, U+1EA0-1EF9, U+20AB;
}

@font-face {
  font-family: Roboto;
  font-style: normal;
  font-weight: 100;
  font-stretch: 100%;
  font-display: swap;
  src: url("../media/1ee7186e620cfacd-s.c479c32f.woff2") format("woff2");
  unicode-range: U+100-2BA, U+2BD-2C5, U+2C7-2CC, U+2CE-2D7, U+2DD-2FF, U+304, U+308, U+329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}

@font-face {
  font-family: Roboto;
  font-style: normal;
  font-weight: 100;
  font-stretch: 100%;
  font-display: swap;
  src: url("../media/4c650e1ac62b3415-s.p.a797655c.woff2") format("woff2");
  unicode-range: U+??, U+131, U+152-153, U+2BB-2BC, U+2C6, U+2DA, U+2DC, U+304, U+308, U+329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: Roboto;
  font-style: normal;
  font-weight: 300;
  font-stretch: 100%;
  font-display: swap;
  src: url("../media/a49b7466783b98a7-s.37f0ec53.woff2") format("woff2");
  unicode-range: U+460-52F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
}

@font-face {
  font-family: Roboto;
  font-style: normal;
  font-weight: 300;
  font-stretch: 100%;
  font-display: swap;
  src: url("../media/a434524554b4cbdf-s.deaa4120.woff2") format("woff2");
  unicode-range: U+301, U+400-45F, U+490-491, U+4B0-4B1, U+2116;
}

@font-face {
  font-family: Roboto;
  font-style: normal;
  font-weight: 300;
  font-stretch: 100%;
  font-display: swap;
  src: url("../media/03566f5e37ffd2dc-s.377ca26f.woff2") format("woff2");
  unicode-range: U+1F??;
}

@font-face {
  font-family: Roboto;
  font-style: normal;
  font-weight: 300;
  font-stretch: 100%;
  font-display: swap;
  src: url("../media/c386a28af0fdd133-s.faccc9eb.woff2") format("woff2");
  unicode-range: U+370-377, U+37A-37F, U+384-38A, U+38C, U+38E-3A1, U+3A3-3FF;
}

@font-face {
  font-family: Roboto;
  font-style: normal;
  font-weight: 300;
  font-stretch: 100%;
  font-display: swap;
  src: url("../media/bebec4eee24d770e-s.e676bb0a.woff2") format("woff2");
  unicode-range: U+302-303, U+305, U+307-308, U+310, U+312, U+315, U+31A, U+326-327, U+32C, U+32F-330, U+332-333, U+338, U+33A, U+346, U+34D, U+391-3A1, U+3A3-3A9, U+3B1-3C9, U+3D1, U+3D5-3D6, U+3F0-3F1, U+3F4-3F5, U+2016-2017, U+2034-2038, U+203C, U+2040, U+2043, U+2047, U+2050, U+2057, U+205F, U+2070-2071, U+2074-208E, U+2090-209C, U+20D0-20DC, U+20E1, U+20E5-20EF, U+2100-2112, U+2114-2115, U+2117-2121, U+2123-214F, U+2190, U+2192, U+2194-21AE, U+21B0-21E5, U+21F1-21F2, U+21F4-2211, U+2213-2214, U+2216-22FF, U+2308-230B, U+2310, U+2319, U+231C-2321, U+2336-237A, U+237C, U+2395, U+239B-23B7, U+23D0, U+23DC-23E1, U+2474-2475, U+25AF, U+25B3, U+25B7, U+25BD, U+25C1, U+25CA, U+25CC, U+25FB, U+266D-266F, U+27C0-27FF, U+2900-2AFF, U+2B0E-2B11, U+2B30-2B4C, U+2BFE, U+3030, U+FF5B, U+FF5D, U+1D400-1D7FF, U+1EE??;
}

@font-face {
  font-family: Roboto;
  font-style: normal;
  font-weight: 300;
  font-stretch: 100%;
  font-display: swap;
  src: url("../media/d8fb98b47816a157-s.27669ba6.woff2") format("woff2");
  unicode-range: U+1-C, U+E-1F, U+7F-9F, U+20DD-20E0, U+20E2-20E4, U+2150-218F, U+2190, U+2192, U+2194-2199, U+21AF, U+21E6-21F0, U+21F3, U+2218-2219, U+2299, U+22C4-22C6, U+2300-243F, U+2440-244A, U+2460-24FF, U+25A0-27BF, U+28??, U+2921-2922, U+2981, U+29BF, U+29EB, U+2B??, U+4DC0-4DFF, U+FFF9-FFFB, U+10140-1018E, U+10190-1019C, U+101A0, U+101D0-101FD, U+102E0-102FB, U+10E60-10E7E, U+1D2C0-1D2D3, U+1D2E0-1D37F, U+1F0??, U+1F100-1F1AD, U+1F1E6-1F1FF, U+1F30D-1F30F, U+1F315, U+1F31C, U+1F31E, U+1F320-1F32C, U+1F336, U+1F378, U+1F37D, U+1F382, U+1F393-1F39F, U+1F3A7-1F3A8, U+1F3AC-1F3AF, U+1F3C2, U+1F3C4-1F3C6, U+1F3CA-1F3CE, U+1F3D4-1F3E0, U+1F3ED, U+1F3F1-1F3F3, U+1F3F5-1F3F7, U+1F408, U+1F415, U+1F41F, U+1F426, U+1F43F, U+1F441-1F442, U+1F444, U+1F446-1F449, U+1F44C-1F44E, U+1F453, U+1F46A, U+1F47D, U+1F4A3, U+1F4B0, U+1F4B3, U+1F4B9, U+1F4BB, U+1F4BF, U+1F4C8-1F4CB, U+1F4D6, U+1F4DA, U+1F4DF, U+1F4E3-1F4E6, U+1F4EA-1F4ED, U+1F4F7, U+1F4F9-1F4FB, U+1F4FD-1F4FE, U+1F503, U+1F507-1F50B, U+1F50D, U+1F512-1F513, U+1F53E-1F54A, U+1F54F-1F5FA, U+1F610, U+1F650-1F67F, U+1F687, U+1F68D, U+1F691, U+1F694, U+1F698, U+1F6AD, U+1F6B2, U+1F6B9-1F6BA, U+1F6BC, U+1F6C6-1F6CF, U+1F6D3-1F6D7, U+1F6E0-1F6EA, U+1F6F0-1F6F3, U+1F6F7-1F6FC, U+1F7??, U+1F800-1F80B, U+1F810-1F847, U+1F850-1F859, U+1F860-1F887, U+1F890-1F8AD, U+1F8B0-1F8BB, U+1F8C0-1F8C1, U+1F900-1F90B, U+1F93B, U+1F946, U+1F984, U+1F996, U+1F9E9, U+1FA00-1FA6F, U+1FA70-1FA7C, U+1FA80-1FA89, U+1FA8F-1FAC6, U+1FACE-1FADC, U+1FADF-1FAE9, U+1FAF0-1FAF8, U+1FB??;
}

@font-face {
  font-family: Roboto;
  font-style: normal;
  font-weight: 300;
  font-stretch: 100%;
  font-display: swap;
  src: url("../media/a47057a7a4d8f605-s.99770768.woff2") format("woff2");
  unicode-range: U+102-103, U+110-111, U+128-129, U+168-169, U+1A0-1A1, U+1AF-1B0, U+300-301, U+303-304, U+308-309, U+323, U+329, U+1EA0-1EF9, U+20AB;
}

@font-face {
  font-family: Roboto;
  font-style: normal;
  font-weight: 300;
  font-stretch: 100%;
  font-display: swap;
  src: url("../media/1ee7186e620cfacd-s.c479c32f.woff2") format("woff2");
  unicode-range: U+100-2BA, U+2BD-2C5, U+2C7-2CC, U+2CE-2D7, U+2DD-2FF, U+304, U+308, U+329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}

@font-face {
  font-family: Roboto;
  font-style: normal;
  font-weight: 300;
  font-stretch: 100%;
  font-display: swap;
  src: url("../media/4c650e1ac62b3415-s.p.a797655c.woff2") format("woff2");
  unicode-range: U+??, U+131, U+152-153, U+2BB-2BC, U+2C6, U+2DA, U+2DC, U+304, U+308, U+329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: Roboto;
  font-style: normal;
  font-weight: 400;
  font-stretch: 100%;
  font-display: swap;
  src: url("../media/a49b7466783b98a7-s.37f0ec53.woff2") format("woff2");
  unicode-range: U+460-52F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
}

@font-face {
  font-family: Roboto;
  font-style: normal;
  font-weight: 400;
  font-stretch: 100%;
  font-display: swap;
  src: url("../media/a434524554b4cbdf-s.deaa4120.woff2") format("woff2");
  unicode-range: U+301, U+400-45F, U+490-491, U+4B0-4B1, U+2116;
}

@font-face {
  font-family: Roboto;
  font-style: normal;
  font-weight: 400;
  font-stretch: 100%;
  font-display: swap;
  src: url("../media/03566f5e37ffd2dc-s.377ca26f.woff2") format("woff2");
  unicode-range: U+1F??;
}

@font-face {
  font-family: Roboto;
  font-style: normal;
  font-weight: 400;
  font-stretch: 100%;
  font-display: swap;
  src: url("../media/c386a28af0fdd133-s.faccc9eb.woff2") format("woff2");
  unicode-range: U+370-377, U+37A-37F, U+384-38A, U+38C, U+38E-3A1, U+3A3-3FF;
}

@font-face {
  font-family: Roboto;
  font-style: normal;
  font-weight: 400;
  font-stretch: 100%;
  font-display: swap;
  src: url("../media/bebec4eee24d770e-s.e676bb0a.woff2") format("woff2");
  unicode-range: U+302-303, U+305, U+307-308, U+310, U+312, U+315, U+31A, U+326-327, U+32C, U+32F-330, U+332-333, U+338, U+33A, U+346, U+34D, U+391-3A1, U+3A3-3A9, U+3B1-3C9, U+3D1, U+3D5-3D6, U+3F0-3F1, U+3F4-3F5, U+2016-2017, U+2034-2038, U+203C, U+2040, U+2043, U+2047, U+2050, U+2057, U+205F, U+2070-2071, U+2074-208E, U+2090-209C, U+20D0-20DC, U+20E1, U+20E5-20EF, U+2100-2112, U+2114-2115, U+2117-2121, U+2123-214F, U+2190, U+2192, U+2194-21AE, U+21B0-21E5, U+21F1-21F2, U+21F4-2211, U+2213-2214, U+2216-22FF, U+2308-230B, U+2310, U+2319, U+231C-2321, U+2336-237A, U+237C, U+2395, U+239B-23B7, U+23D0, U+23DC-23E1, U+2474-2475, U+25AF, U+25B3, U+25B7, U+25BD, U+25C1, U+25CA, U+25CC, U+25FB, U+266D-266F, U+27C0-27FF, U+2900-2AFF, U+2B0E-2B11, U+2B30-2B4C, U+2BFE, U+3030, U+FF5B, U+FF5D, U+1D400-1D7FF, U+1EE??;
}

@font-face {
  font-family: Roboto;
  font-style: normal;
  font-weight: 400;
  font-stretch: 100%;
  font-display: swap;
  src: url("../media/d8fb98b47816a157-s.27669ba6.woff2") format("woff2");
  unicode-range: U+1-C, U+E-1F, U+7F-9F, U+20DD-20E0, U+20E2-20E4, U+2150-218F, U+2190, U+2192, U+2194-2199, U+21AF, U+21E6-21F0, U+21F3, U+2218-2219, U+2299, U+22C4-22C6, U+2300-243F, U+2440-244A, U+2460-24FF, U+25A0-27BF, U+28??, U+2921-2922, U+2981, U+29BF, U+29EB, U+2B??, U+4DC0-4DFF, U+FFF9-FFFB, U+10140-1018E, U+10190-1019C, U+101A0, U+101D0-101FD, U+102E0-102FB, U+10E60-10E7E, U+1D2C0-1D2D3, U+1D2E0-1D37F, U+1F0??, U+1F100-1F1AD, U+1F1E6-1F1FF, U+1F30D-1F30F, U+1F315, U+1F31C, U+1F31E, U+1F320-1F32C, U+1F336, U+1F378, U+1F37D, U+1F382, U+1F393-1F39F, U+1F3A7-1F3A8, U+1F3AC-1F3AF, U+1F3C2, U+1F3C4-1F3C6, U+1F3CA-1F3CE, U+1F3D4-1F3E0, U+1F3ED, U+1F3F1-1F3F3, U+1F3F5-1F3F7, U+1F408, U+1F415, U+1F41F, U+1F426, U+1F43F, U+1F441-1F442, U+1F444, U+1F446-1F449, U+1F44C-1F44E, U+1F453, U+1F46A, U+1F47D, U+1F4A3, U+1F4B0, U+1F4B3, U+1F4B9, U+1F4BB, U+1F4BF, U+1F4C8-1F4CB, U+1F4D6, U+1F4DA, U+1F4DF, U+1F4E3-1F4E6, U+1F4EA-1F4ED, U+1F4F7, U+1F4F9-1F4FB, U+1F4FD-1F4FE, U+1F503, U+1F507-1F50B, U+1F50D, U+1F512-1F513, U+1F53E-1F54A, U+1F54F-1F5FA, U+1F610, U+1F650-1F67F, U+1F687, U+1F68D, U+1F691, U+1F694, U+1F698, U+1F6AD, U+1F6B2, U+1F6B9-1F6BA, U+1F6BC, U+1F6C6-1F6CF, U+1F6D3-1F6D7, U+1F6E0-1F6EA, U+1F6F0-1F6F3, U+1F6F7-1F6FC, U+1F7??, U+1F800-1F80B, U+1F810-1F847, U+1F850-1F859, U+1F860-1F887, U+1F890-1F8AD, U+1F8B0-1F8BB, U+1F8C0-1F8C1, U+1F900-1F90B, U+1F93B, U+1F946, U+1F984, U+1F996, U+1F9E9, U+1FA00-1FA6F, U+1FA70-1FA7C, U+1FA80-1FA89, U+1FA8F-1FAC6, U+1FACE-1FADC, U+1FADF-1FAE9, U+1FAF0-1FAF8, U+1FB??;
}

@font-face {
  font-family: Roboto;
  font-style: normal;
  font-weight: 400;
  font-stretch: 100%;
  font-display: swap;
  src: url("../media/a47057a7a4d8f605-s.99770768.woff2") format("woff2");
  unicode-range: U+102-103, U+110-111, U+128-129, U+168-169, U+1A0-1A1, U+1AF-1B0, U+300-301, U+303-304, U+308-309, U+323, U+329, U+1EA0-1EF9, U+20AB;
}

@font-face {
  font-family: Roboto;
  font-style: normal;
  font-weight: 400;
  font-stretch: 100%;
  font-display: swap;
  src: url("../media/1ee7186e620cfacd-s.c479c32f.woff2") format("woff2");
  unicode-range: U+100-2BA, U+2BD-2C5, U+2C7-2CC, U+2CE-2D7, U+2DD-2FF, U+304, U+308, U+329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}

@font-face {
  font-family: Roboto;
  font-style: normal;
  font-weight: 400;
  font-stretch: 100%;
  font-display: swap;
  src: url("../media/4c650e1ac62b3415-s.p.a797655c.woff2") format("woff2");
  unicode-range: U+??, U+131, U+152-153, U+2BB-2BC, U+2C6, U+2DA, U+2DC, U+304, U+308, U+329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: Roboto;
  font-style: normal;
  font-weight: 500;
  font-stretch: 100%;
  font-display: swap;
  src: url("../media/a49b7466783b98a7-s.37f0ec53.woff2") format("woff2");
  unicode-range: U+460-52F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
}

@font-face {
  font-family: Roboto;
  font-style: normal;
  font-weight: 500;
  font-stretch: 100%;
  font-display: swap;
  src: url("../media/a434524554b4cbdf-s.deaa4120.woff2") format("woff2");
  unicode-range: U+301, U+400-45F, U+490-491, U+4B0-4B1, U+2116;
}

@font-face {
  font-family: Roboto;
  font-style: normal;
  font-weight: 500;
  font-stretch: 100%;
  font-display: swap;
  src: url("../media/03566f5e37ffd2dc-s.377ca26f.woff2") format("woff2");
  unicode-range: U+1F??;
}

@font-face {
  font-family: Roboto;
  font-style: normal;
  font-weight: 500;
  font-stretch: 100%;
  font-display: swap;
  src: url("../media/c386a28af0fdd133-s.faccc9eb.woff2") format("woff2");
  unicode-range: U+370-377, U+37A-37F, U+384-38A, U+38C, U+38E-3A1, U+3A3-3FF;
}

@font-face {
  font-family: Roboto;
  font-style: normal;
  font-weight: 500;
  font-stretch: 100%;
  font-display: swap;
  src: url("../media/bebec4eee24d770e-s.e676bb0a.woff2") format("woff2");
  unicode-range: U+302-303, U+305, U+307-308, U+310, U+312, U+315, U+31A, U+326-327, U+32C, U+32F-330, U+332-333, U+338, U+33A, U+346, U+34D, U+391-3A1, U+3A3-3A9, U+3B1-3C9, U+3D1, U+3D5-3D6, U+3F0-3F1, U+3F4-3F5, U+2016-2017, U+2034-2038, U+203C, U+2040, U+2043, U+2047, U+2050, U+2057, U+205F, U+2070-2071, U+2074-208E, U+2090-209C, U+20D0-20DC, U+20E1, U+20E5-20EF, U+2100-2112, U+2114-2115, U+2117-2121, U+2123-214F, U+2190, U+2192, U+2194-21AE, U+21B0-21E5, U+21F1-21F2, U+21F4-2211, U+2213-2214, U+2216-22FF, U+2308-230B, U+2310, U+2319, U+231C-2321, U+2336-237A, U+237C, U+2395, U+239B-23B7, U+23D0, U+23DC-23E1, U+2474-2475, U+25AF, U+25B3, U+25B7, U+25BD, U+25C1, U+25CA, U+25CC, U+25FB, U+266D-266F, U+27C0-27FF, U+2900-2AFF, U+2B0E-2B11, U+2B30-2B4C, U+2BFE, U+3030, U+FF5B, U+FF5D, U+1D400-1D7FF, U+1EE??;
}

@font-face {
  font-family: Roboto;
  font-style: normal;
  font-weight: 500;
  font-stretch: 100%;
  font-display: swap;
  src: url("../media/d8fb98b47816a157-s.27669ba6.woff2") format("woff2");
  unicode-range: U+1-C, U+E-1F, U+7F-9F, U+20DD-20E0, U+20E2-20E4, U+2150-218F, U+2190, U+2192, U+2194-2199, U+21AF, U+21E6-21F0, U+21F3, U+2218-2219, U+2299, U+22C4-22C6, U+2300-243F, U+2440-244A, U+2460-24FF, U+25A0-27BF, U+28??, U+2921-2922, U+2981, U+29BF, U+29EB, U+2B??, U+4DC0-4DFF, U+FFF9-FFFB, U+10140-1018E, U+10190-1019C, U+101A0, U+101D0-101FD, U+102E0-102FB, U+10E60-10E7E, U+1D2C0-1D2D3, U+1D2E0-1D37F, U+1F0??, U+1F100-1F1AD, U+1F1E6-1F1FF, U+1F30D-1F30F, U+1F315, U+1F31C, U+1F31E, U+1F320-1F32C, U+1F336, U+1F378, U+1F37D, U+1F382, U+1F393-1F39F, U+1F3A7-1F3A8, U+1F3AC-1F3AF, U+1F3C2, U+1F3C4-1F3C6, U+1F3CA-1F3CE, U+1F3D4-1F3E0, U+1F3ED, U+1F3F1-1F3F3, U+1F3F5-1F3F7, U+1F408, U+1F415, U+1F41F, U+1F426, U+1F43F, U+1F441-1F442, U+1F444, U+1F446-1F449, U+1F44C-1F44E, U+1F453, U+1F46A, U+1F47D, U+1F4A3, U+1F4B0, U+1F4B3, U+1F4B9, U+1F4BB, U+1F4BF, U+1F4C8-1F4CB, U+1F4D6, U+1F4DA, U+1F4DF, U+1F4E3-1F4E6, U+1F4EA-1F4ED, U+1F4F7, U+1F4F9-1F4FB, U+1F4FD-1F4FE, U+1F503, U+1F507-1F50B, U+1F50D, U+1F512-1F513, U+1F53E-1F54A, U+1F54F-1F5FA, U+1F610, U+1F650-1F67F, U+1F687, U+1F68D, U+1F691, U+1F694, U+1F698, U+1F6AD, U+1F6B2, U+1F6B9-1F6BA, U+1F6BC, U+1F6C6-1F6CF, U+1F6D3-1F6D7, U+1F6E0-1F6EA, U+1F6F0-1F6F3, U+1F6F7-1F6FC, U+1F7??, U+1F800-1F80B, U+1F810-1F847, U+1F850-1F859, U+1F860-1F887, U+1F890-1F8AD, U+1F8B0-1F8BB, U+1F8C0-1F8C1, U+1F900-1F90B, U+1F93B, U+1F946, U+1F984, U+1F996, U+1F9E9, U+1FA00-1FA6F, U+1FA70-1FA7C, U+1FA80-1FA89, U+1FA8F-1FAC6, U+1FACE-1FADC, U+1FADF-1FAE9, U+1FAF0-1FAF8, U+1FB??;
}

@font-face {
  font-family: Roboto;
  font-style: normal;
  font-weight: 500;
  font-stretch: 100%;
  font-display: swap;
  src: url("../media/a47057a7a4d8f605-s.99770768.woff2") format("woff2");
  unicode-range: U+102-103, U+110-111, U+128-129, U+168-169, U+1A0-1A1, U+1AF-1B0, U+300-301, U+303-304, U+308-309, U+323, U+329, U+1EA0-1EF9, U+20AB;
}

@font-face {
  font-family: Roboto;
  font-style: normal;
  font-weight: 500;
  font-stretch: 100%;
  font-display: swap;
  src: url("../media/1ee7186e620cfacd-s.c479c32f.woff2") format("woff2");
  unicode-range: U+100-2BA, U+2BD-2C5, U+2C7-2CC, U+2CE-2D7, U+2DD-2FF, U+304, U+308, U+329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}

@font-face {
  font-family: Roboto;
  font-style: normal;
  font-weight: 500;
  font-stretch: 100%;
  font-display: swap;
  src: url("../media/4c650e1ac62b3415-s.p.a797655c.woff2") format("woff2");
  unicode-range: U+??, U+131, U+152-153, U+2BB-2BC, U+2C6, U+2DA, U+2DC, U+304, U+308, U+329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: Roboto;
  font-style: normal;
  font-weight: 700;
  font-stretch: 100%;
  font-display: swap;
  src: url("../media/a49b7466783b98a7-s.37f0ec53.woff2") format("woff2");
  unicode-range: U+460-52F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
}

@font-face {
  font-family: Roboto;
  font-style: normal;
  font-weight: 700;
  font-stretch: 100%;
  font-display: swap;
  src: url("../media/a434524554b4cbdf-s.deaa4120.woff2") format("woff2");
  unicode-range: U+301, U+400-45F, U+490-491, U+4B0-4B1, U+2116;
}

@font-face {
  font-family: Roboto;
  font-style: normal;
  font-weight: 700;
  font-stretch: 100%;
  font-display: swap;
  src: url("../media/03566f5e37ffd2dc-s.377ca26f.woff2") format("woff2");
  unicode-range: U+1F??;
}

@font-face {
  font-family: Roboto;
  font-style: normal;
  font-weight: 700;
  font-stretch: 100%;
  font-display: swap;
  src: url("../media/c386a28af0fdd133-s.faccc9eb.woff2") format("woff2");
  unicode-range: U+370-377, U+37A-37F, U+384-38A, U+38C, U+38E-3A1, U+3A3-3FF;
}

@font-face {
  font-family: Roboto;
  font-style: normal;
  font-weight: 700;
  font-stretch: 100%;
  font-display: swap;
  src: url("../media/bebec4eee24d770e-s.e676bb0a.woff2") format("woff2");
  unicode-range: U+302-303, U+305, U+307-308, U+310, U+312, U+315, U+31A, U+326-327, U+32C, U+32F-330, U+332-333, U+338, U+33A, U+346, U+34D, U+391-3A1, U+3A3-3A9, U+3B1-3C9, U+3D1, U+3D5-3D6, U+3F0-3F1, U+3F4-3F5, U+2016-2017, U+2034-2038, U+203C, U+2040, U+2043, U+2047, U+2050, U+2057, U+205F, U+2070-2071, U+2074-208E, U+2090-209C, U+20D0-20DC, U+20E1, U+20E5-20EF, U+2100-2112, U+2114-2115, U+2117-2121, U+2123-214F, U+2190, U+2192, U+2194-21AE, U+21B0-21E5, U+21F1-21F2, U+21F4-2211, U+2213-2214, U+2216-22FF, U+2308-230B, U+2310, U+2319, U+231C-2321, U+2336-237A, U+237C, U+2395, U+239B-23B7, U+23D0, U+23DC-23E1, U+2474-2475, U+25AF, U+25B3, U+25B7, U+25BD, U+25C1, U+25CA, U+25CC, U+25FB, U+266D-266F, U+27C0-27FF, U+2900-2AFF, U+2B0E-2B11, U+2B30-2B4C, U+2BFE, U+3030, U+FF5B, U+FF5D, U+1D400-1D7FF, U+1EE??;
}

@font-face {
  font-family: Roboto;
  font-style: normal;
  font-weight: 700;
  font-stretch: 100%;
  font-display: swap;
  src: url("../media/d8fb98b47816a157-s.27669ba6.woff2") format("woff2");
  unicode-range: U+1-C, U+E-1F, U+7F-9F, U+20DD-20E0, U+20E2-20E4, U+2150-218F, U+2190, U+2192, U+2194-2199, U+21AF, U+21E6-21F0, U+21F3, U+2218-2219, U+2299, U+22C4-22C6, U+2300-243F, U+2440-244A, U+2460-24FF, U+25A0-27BF, U+28??, U+2921-2922, U+2981, U+29BF, U+29EB, U+2B??, U+4DC0-4DFF, U+FFF9-FFFB, U+10140-1018E, U+10190-1019C, U+101A0, U+101D0-101FD, U+102E0-102FB, U+10E60-10E7E, U+1D2C0-1D2D3, U+1D2E0-1D37F, U+1F0??, U+1F100-1F1AD, U+1F1E6-1F1FF, U+1F30D-1F30F, U+1F315, U+1F31C, U+1F31E, U+1F320-1F32C, U+1F336, U+1F378, U+1F37D, U+1F382, U+1F393-1F39F, U+1F3A7-1F3A8, U+1F3AC-1F3AF, U+1F3C2, U+1F3C4-1F3C6, U+1F3CA-1F3CE, U+1F3D4-1F3E0, U+1F3ED, U+1F3F1-1F3F3, U+1F3F5-1F3F7, U+1F408, U+1F415, U+1F41F, U+1F426, U+1F43F, U+1F441-1F442, U+1F444, U+1F446-1F449, U+1F44C-1F44E, U+1F453, U+1F46A, U+1F47D, U+1F4A3, U+1F4B0, U+1F4B3, U+1F4B9, U+1F4BB, U+1F4BF, U+1F4C8-1F4CB, U+1F4D6, U+1F4DA, U+1F4DF, U+1F4E3-1F4E6, U+1F4EA-1F4ED, U+1F4F7, U+1F4F9-1F4FB, U+1F4FD-1F4FE, U+1F503, U+1F507-1F50B, U+1F50D, U+1F512-1F513, U+1F53E-1F54A, U+1F54F-1F5FA, U+1F610, U+1F650-1F67F, U+1F687, U+1F68D, U+1F691, U+1F694, U+1F698, U+1F6AD, U+1F6B2, U+1F6B9-1F6BA, U+1F6BC, U+1F6C6-1F6CF, U+1F6D3-1F6D7, U+1F6E0-1F6EA, U+1F6F0-1F6F3, U+1F6F7-1F6FC, U+1F7??, U+1F800-1F80B, U+1F810-1F847, U+1F850-1F859, U+1F860-1F887, U+1F890-1F8AD, U+1F8B0-1F8BB, U+1F8C0-1F8C1, U+1F900-1F90B, U+1F93B, U+1F946, U+1F984, U+1F996, U+1F9E9, U+1FA00-1FA6F, U+1FA70-1FA7C, U+1FA80-1FA89, U+1FA8F-1FAC6, U+1FACE-1FADC, U+1FADF-1FAE9, U+1FAF0-1FAF8, U+1FB??;
}

@font-face {
  font-family: Roboto;
  font-style: normal;
  font-weight: 700;
  font-stretch: 100%;
  font-display: swap;
  src: url("../media/a47057a7a4d8f605-s.99770768.woff2") format("woff2");
  unicode-range: U+102-103, U+110-111, U+128-129, U+168-169, U+1A0-1A1, U+1AF-1B0, U+300-301, U+303-304, U+308-309, U+323, U+329, U+1EA0-1EF9, U+20AB;
}

@font-face {
  font-family: Roboto;
  font-style: normal;
  font-weight: 700;
  font-stretch: 100%;
  font-display: swap;
  src: url("../media/1ee7186e620cfacd-s.c479c32f.woff2") format("woff2");
  unicode-range: U+100-2BA, U+2BD-2C5, U+2C7-2CC, U+2CE-2D7, U+2DD-2FF, U+304, U+308, U+329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}

@font-face {
  font-family: Roboto;
  font-style: normal;
  font-weight: 700;
  font-stretch: 100%;
  font-display: swap;
  src: url("../media/4c650e1ac62b3415-s.p.a797655c.woff2") format("woff2");
  unicode-range: U+??, U+131, U+152-153, U+2BB-2BC, U+2C6, U+2DA, U+2DC, U+304, U+308, U+329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: Roboto;
  font-style: normal;
  font-weight: 900;
  font-stretch: 100%;
  font-display: swap;
  src: url("../media/a49b7466783b98a7-s.37f0ec53.woff2") format("woff2");
  unicode-range: U+460-52F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
}

@font-face {
  font-family: Roboto;
  font-style: normal;
  font-weight: 900;
  font-stretch: 100%;
  font-display: swap;
  src: url("../media/a434524554b4cbdf-s.deaa4120.woff2") format("woff2");
  unicode-range: U+301, U+400-45F, U+490-491, U+4B0-4B1, U+2116;
}

@font-face {
  font-family: Roboto;
  font-style: normal;
  font-weight: 900;
  font-stretch: 100%;
  font-display: swap;
  src: url("../media/03566f5e37ffd2dc-s.377ca26f.woff2") format("woff2");
  unicode-range: U+1F??;
}

@font-face {
  font-family: Roboto;
  font-style: normal;
  font-weight: 900;
  font-stretch: 100%;
  font-display: swap;
  src: url("../media/c386a28af0fdd133-s.faccc9eb.woff2") format("woff2");
  unicode-range: U+370-377, U+37A-37F, U+384-38A, U+38C, U+38E-3A1, U+3A3-3FF;
}

@font-face {
  font-family: Roboto;
  font-style: normal;
  font-weight: 900;
  font-stretch: 100%;
  font-display: swap;
  src: url("../media/bebec4eee24d770e-s.e676bb0a.woff2") format("woff2");
  unicode-range: U+302-303, U+305, U+307-308, U+310, U+312, U+315, U+31A, U+326-327, U+32C, U+32F-330, U+332-333, U+338, U+33A, U+346, U+34D, U+391-3A1, U+3A3-3A9, U+3B1-3C9, U+3D1, U+3D5-3D6, U+3F0-3F1, U+3F4-3F5, U+2016-2017, U+2034-2038, U+203C, U+2040, U+2043, U+2047, U+2050, U+2057, U+205F, U+2070-2071, U+2074-208E, U+2090-209C, U+20D0-20DC, U+20E1, U+20E5-20EF, U+2100-2112, U+2114-2115, U+2117-2121, U+2123-214F, U+2190, U+2192, U+2194-21AE, U+21B0-21E5, U+21F1-21F2, U+21F4-2211, U+2213-2214, U+2216-22FF, U+2308-230B, U+2310, U+2319, U+231C-2321, U+2336-237A, U+237C, U+2395, U+239B-23B7, U+23D0, U+23DC-23E1, U+2474-2475, U+25AF, U+25B3, U+25B7, U+25BD, U+25C1, U+25CA, U+25CC, U+25FB, U+266D-266F, U+27C0-27FF, U+2900-2AFF, U+2B0E-2B11, U+2B30-2B4C, U+2BFE, U+3030, U+FF5B, U+FF5D, U+1D400-1D7FF, U+1EE??;
}

@font-face {
  font-family: Roboto;
  font-style: normal;
  font-weight: 900;
  font-stretch: 100%;
  font-display: swap;
  src: url("../media/d8fb98b47816a157-s.27669ba6.woff2") format("woff2");
  unicode-range: U+1-C, U+E-1F, U+7F-9F, U+20DD-20E0, U+20E2-20E4, U+2150-218F, U+2190, U+2192, U+2194-2199, U+21AF, U+21E6-21F0, U+21F3, U+2218-2219, U+2299, U+22C4-22C6, U+2300-243F, U+2440-244A, U+2460-24FF, U+25A0-27BF, U+28??, U+2921-2922, U+2981, U+29BF, U+29EB, U+2B??, U+4DC0-4DFF, U+FFF9-FFFB, U+10140-1018E, U+10190-1019C, U+101A0, U+101D0-101FD, U+102E0-102FB, U+10E60-10E7E, U+1D2C0-1D2D3, U+1D2E0-1D37F, U+1F0??, U+1F100-1F1AD, U+1F1E6-1F1FF, U+1F30D-1F30F, U+1F315, U+1F31C, U+1F31E, U+1F320-1F32C, U+1F336, U+1F378, U+1F37D, U+1F382, U+1F393-1F39F, U+1F3A7-1F3A8, U+1F3AC-1F3AF, U+1F3C2, U+1F3C4-1F3C6, U+1F3CA-1F3CE, U+1F3D4-1F3E0, U+1F3ED, U+1F3F1-1F3F3, U+1F3F5-1F3F7, U+1F408, U+1F415, U+1F41F, U+1F426, U+1F43F, U+1F441-1F442, U+1F444, U+1F446-1F449, U+1F44C-1F44E, U+1F453, U+1F46A, U+1F47D, U+1F4A3, U+1F4B0, U+1F4B3, U+1F4B9, U+1F4BB, U+1F4BF, U+1F4C8-1F4CB, U+1F4D6, U+1F4DA, U+1F4DF, U+1F4E3-1F4E6, U+1F4EA-1F4ED, U+1F4F7, U+1F4F9-1F4FB, U+1F4FD-1F4FE, U+1F503, U+1F507-1F50B, U+1F50D, U+1F512-1F513, U+1F53E-1F54A, U+1F54F-1F5FA, U+1F610, U+1F650-1F67F, U+1F687, U+1F68D, U+1F691, U+1F694, U+1F698, U+1F6AD, U+1F6B2, U+1F6B9-1F6BA, U+1F6BC, U+1F6C6-1F6CF, U+1F6D3-1F6D7, U+1F6E0-1F6EA, U+1F6F0-1F6F3, U+1F6F7-1F6FC, U+1F7??, U+1F800-1F80B, U+1F810-1F847, U+1F850-1F859, U+1F860-1F887, U+1F890-1F8AD, U+1F8B0-1F8BB, U+1F8C0-1F8C1, U+1F900-1F90B, U+1F93B, U+1F946, U+1F984, U+1F996, U+1F9E9, U+1FA00-1FA6F, U+1FA70-1FA7C, U+1FA80-1FA89, U+1FA8F-1FAC6, U+1FACE-1FADC, U+1FADF-1FAE9, U+1FAF0-1FAF8, U+1FB??;
}

@font-face {
  font-family: Roboto;
  font-style: normal;
  font-weight: 900;
  font-stretch: 100%;
  font-display: swap;
  src: url("../media/a47057a7a4d8f605-s.99770768.woff2") format("woff2");
  unicode-range: U+102-103, U+110-111, U+128-129, U+168-169, U+1A0-1A1, U+1AF-1B0, U+300-301, U+303-304, U+308-309, U+323, U+329, U+1EA0-1EF9, U+20AB;
}

@font-face {
  font-family: Roboto;
  font-style: normal;
  font-weight: 900;
  font-stretch: 100%;
  font-display: swap;
  src: url("../media/1ee7186e620cfacd-s.c479c32f.woff2") format("woff2");
  unicode-range: U+100-2BA, U+2BD-2C5, U+2C7-2CC, U+2CE-2D7, U+2DD-2FF, U+304, U+308, U+329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}

@font-face {
  font-family: Roboto;
  font-style: normal;
  font-weight: 900;
  font-stretch: 100%;
  font-display: swap;
  src: url("../media/4c650e1ac62b3415-s.p.a797655c.woff2") format("woff2");
  unicode-range: U+??, U+131, U+152-153, U+2BB-2BC, U+2C6, U+2DA, U+2DC, U+304, U+308, U+329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: Roboto Fallback;
  src: local(Arial);
  ascent-override: 92.98%;
  descent-override: 24.47%;
  line-gap-override: 0.0%;
  size-adjust: 99.78%;
}

.roboto_ff541327-module__yBgLWW__className {
  font-family: Roboto, Roboto Fallback;
  font-style: normal;
}

.roboto_ff541327-module__yBgLWW__variable {
  --font-roboto: "Roboto", "Roboto Fallback";
}

/* [project]/src/app/globals.css [app-client] (css) */
@layer properties {
  @supports (((-webkit-hyphens: none)) and (not (margin-trim: inline))) or ((-moz-orient: inline) and (not (color: rgb(from red r g b)))) {
    *, :before, :after {
      --tw-translate-x: 0;
      --tw-translate-y: 0;
      --tw-translate-z: 0;
      --tw-scale-x: 1;
      --tw-scale-y: 1;
      --tw-scale-z: 1;
      --tw-rotate-x: initial;
      --tw-rotate-y: initial;
      --tw-rotate-z: initial;
      --tw-skew-x: initial;
      --tw-skew-y: initial;
      --tw-scroll-snap-strictness: proximity;
      --tw-space-y-reverse: 0;
      --tw-space-x-reverse: 0;
      --tw-border-style: solid;
      --tw-gradient-position: initial;
      --tw-gradient-from: rgba(0, 0, 0, 0);
      --tw-gradient-via: rgba(0, 0, 0, 0);
      --tw-gradient-to: rgba(0, 0, 0, 0);
      --tw-gradient-stops: initial;
      --tw-gradient-via-stops: initial;
      --tw-gradient-from-position: 0%;
      --tw-gradient-via-position: 50%;
      --tw-gradient-to-position: 100%;
      --tw-leading: initial;
      --tw-font-weight: initial;
      --tw-tracking: initial;
      --tw-shadow: 0 0 rgba(0, 0, 0, 0);
      --tw-shadow-color: initial;
      --tw-shadow-alpha: 100%;
      --tw-inset-shadow: 0 0 rgba(0, 0, 0, 0);
      --tw-inset-shadow-color: initial;
      --tw-inset-shadow-alpha: 100%;
      --tw-ring-color: initial;
      --tw-ring-shadow: 0 0 rgba(0, 0, 0, 0);
      --tw-inset-ring-color: initial;
      --tw-inset-ring-shadow: 0 0 rgba(0, 0, 0, 0);
      --tw-ring-inset: initial;
      --tw-ring-offset-width: 0px;
      --tw-ring-offset-color: #fff;
      --tw-ring-offset-shadow: 0 0 rgba(0, 0, 0, 0);
      --tw-blur: initial;
      --tw-brightness: initial;
      --tw-contrast: initial;
      --tw-grayscale: initial;
      --tw-hue-rotate: initial;
      --tw-invert: initial;
      --tw-opacity: initial;
      --tw-saturate: initial;
      --tw-sepia: initial;
      --tw-drop-shadow: initial;
      --tw-drop-shadow-color: initial;
      --tw-drop-shadow-alpha: 100%;
      --tw-drop-shadow-size: initial;
      --tw-backdrop-blur: initial;
      --tw-backdrop-brightness: initial;
      --tw-backdrop-contrast: initial;
      --tw-backdrop-grayscale: initial;
      --tw-backdrop-hue-rotate: initial;
      --tw-backdrop-invert: initial;
      --tw-backdrop-opacity: initial;
      --tw-backdrop-saturate: initial;
      --tw-backdrop-sepia: initial;
      --tw-duration: initial;
      --tw-ease: initial;
    }

    ::backdrop {
      --tw-translate-x: 0;
      --tw-translate-y: 0;
      --tw-translate-z: 0;
      --tw-scale-x: 1;
      --tw-scale-y: 1;
      --tw-scale-z: 1;
      --tw-rotate-x: initial;
      --tw-rotate-y: initial;
      --tw-rotate-z: initial;
      --tw-skew-x: initial;
      --tw-skew-y: initial;
      --tw-scroll-snap-strictness: proximity;
      --tw-space-y-reverse: 0;
      --tw-space-x-reverse: 0;
      --tw-border-style: solid;
      --tw-gradient-position: initial;
      --tw-gradient-from: rgba(0, 0, 0, 0);
      --tw-gradient-via: rgba(0, 0, 0, 0);
      --tw-gradient-to: rgba(0, 0, 0, 0);
      --tw-gradient-stops: initial;
      --tw-gradient-via-stops: initial;
      --tw-gradient-from-position: 0%;
      --tw-gradient-via-position: 50%;
      --tw-gradient-to-position: 100%;
      --tw-leading: initial;
      --tw-font-weight: initial;
      --tw-tracking: initial;
      --tw-shadow: 0 0 rgba(0, 0, 0, 0);
      --tw-shadow-color: initial;
      --tw-shadow-alpha: 100%;
      --tw-inset-shadow: 0 0 rgba(0, 0, 0, 0);
      --tw-inset-shadow-color: initial;
      --tw-inset-shadow-alpha: 100%;
      --tw-ring-color: initial;
      --tw-ring-shadow: 0 0 rgba(0, 0, 0, 0);
      --tw-inset-ring-color: initial;
      --tw-inset-ring-shadow: 0 0 rgba(0, 0, 0, 0);
      --tw-ring-inset: initial;
      --tw-ring-offset-width: 0px;
      --tw-ring-offset-color: #fff;
      --tw-ring-offset-shadow: 0 0 rgba(0, 0, 0, 0);
      --tw-blur: initial;
      --tw-brightness: initial;
      --tw-contrast: initial;
      --tw-grayscale: initial;
      --tw-hue-rotate: initial;
      --tw-invert: initial;
      --tw-opacity: initial;
      --tw-saturate: initial;
      --tw-sepia: initial;
      --tw-drop-shadow: initial;
      --tw-drop-shadow-color: initial;
      --tw-drop-shadow-alpha: 100%;
      --tw-drop-shadow-size: initial;
      --tw-backdrop-blur: initial;
      --tw-backdrop-brightness: initial;
      --tw-backdrop-contrast: initial;
      --tw-backdrop-grayscale: initial;
      --tw-backdrop-hue-rotate: initial;
      --tw-backdrop-invert: initial;
      --tw-backdrop-opacity: initial;
      --tw-backdrop-saturate: initial;
      --tw-backdrop-sepia: initial;
      --tw-duration: initial;
      --tw-ease: initial;
    }
  }
}

@layer theme {
  :root, :host {
    --font-sans: ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
    --font-mono: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
    --color-red-50: #fef2f2;
    --color-red-200: #ffcaca;
    --color-red-500: #fb2c36;
    --color-red-700: #bf000f;
    --color-red-800: #9f0712;
    --color-orange-500: #fe6e00;
    --color-yellow-400: #fac800;
    --color-yellow-500: #edb200;
    --color-green-50: #f0fdf4;
    --color-green-100: #dcfce7;
    --color-green-200: #b9f8cf;
    --color-green-400: #05df72;
    --color-green-500: #00c758;
    --color-green-600: #00a544;
    --color-green-700: #008138;
    --color-green-800: #016630;
    --color-emerald-400: #00d294;
    --color-cyan-300: #53eafd;
    --color-cyan-500: #00b7d7;
    --color-cyan-600: #0092b5;
    --color-sky-500: #00a5ef;
    --color-blue-50: #eff6ff;
    --color-blue-100: #dbeafe;
    --color-blue-300: #90c5ff;
    --color-blue-400: #54a2ff;
    --color-blue-500: #3080ff;
    --color-blue-600: #155dfc;
    --color-blue-700: #1447e6;
    --color-blue-800: #193cb8;
    --color-purple-500: #ac4bff;
    --color-slate-50: #f8fafc;
    --color-slate-300: #cad5e2;
    --color-slate-600: #45556c;
    --color-slate-800: #1d293d;
    --color-slate-900: #0f172b;
    --color-gray-50: #f9fafb;
    --color-gray-100: #f3f4f6;
    --color-gray-200: #e5e7eb;
    --color-gray-300: #d1d5dc;
    --color-gray-400: #99a1af;
    --color-gray-500: #6a7282;
    --color-gray-600: #4a5565;
    --color-gray-700: #364153;
    --color-gray-800: #1e2939;
    --color-gray-900: #101828;
    --color-zinc-950: #09090b;
    --color-neutral-200: #e5e5e5;
    --color-neutral-300: #d4d4d4;
    --color-neutral-600: #525252;
    --color-black: #000;
    --color-white: #fff;
    --spacing: .25rem;
    --breakpoint-2xl: 96rem;
    --container-sm: 24rem;
    --container-md: 28rem;
    --container-lg: 32rem;
    --container-xl: 36rem;
    --container-2xl: 42rem;
    --container-3xl: 48rem;
    --container-4xl: 56rem;
    --container-5xl: 64rem;
    --container-6xl: 72rem;
    --container-7xl: 80rem;
    --text-xs: .75rem;
    --text-xs--line-height: calc(1 / .75);
    --text-sm: .875rem;
    --text-sm--line-height: calc(1.25 / .875);
    --text-base: 1rem;
    --text-base--line-height: calc(1.5 / 1);
    --text-lg: 1.125rem;
    --text-lg--line-height: calc(1.75 / 1.125);
    --text-xl: 1.25rem;
    --text-xl--line-height: calc(1.75 / 1.25);
    --text-2xl: 1.5rem;
    --text-2xl--line-height: calc(2 / 1.5);
    --text-3xl: 1.875rem;
    --text-3xl--line-height: calc(2.25 / 1.875);
    --text-4xl: 2.25rem;
    --text-4xl--line-height: calc(2.5 / 2.25);
    --text-5xl: 3rem;
    --text-5xl--line-height: 1;
    --text-6xl: 3.75rem;
    --text-6xl--line-height: 1;
    --font-weight-normal: 400;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;
    --tracking-wide: .025em;
    --leading-tight: 1.25;
    --leading-snug: 1.375;
    --leading-relaxed: 1.625;
    --radius-md: .375rem;
    --radius-lg: .5rem;
    --radius-xl: .75rem;
    --radius-2xl: 1rem;
    --radius-3xl: 1.5rem;
    --radius-4xl: 2rem;
    --drop-shadow-lg: 0 4px 4px rgba(0, 0, 0, .15);
    --ease-out: cubic-bezier(0, 0, .2, 1);
    --ease-in-out: cubic-bezier(.4, 0, .2, 1);
    --animate-spin: spin 1s linear infinite;
    --blur-sm: 8px;
    --blur-xl: 24px;
    --blur-3xl: 64px;
    --default-transition-duration: .15s;
    --default-transition-timing-function: cubic-bezier(.4, 0, .2, 1);
    --default-font-family: var(--font-sans);
    --default-mono-font-family: var(--font-mono);
  }

  @supports (color: color(display-p3 0 0 0)) {
    :root, :host {
      --color-red-50: color(display-p3 .988669 .951204 .950419);
      --color-red-200: color(display-p3 .969562 .798149 .794299);
      --color-red-500: color(display-p3 .903738 .262579 .253307);
      --color-red-700: color(display-p3 .692737 .116232 .104679);
      --color-red-800: color(display-p3 .569606 .121069 .108493);
      --color-orange-500: color(display-p3 .946589 .449788 .0757345);
      --color-yellow-400: color(display-p3 .959941 .790171 .0585198);
      --color-yellow-500: color(display-p3 .903651 .703062 .0745389);
      --color-green-50: color(display-p3 .950677 .990571 .959366);
      --color-green-100: color(display-p3 .885269 .984329 .910368);
      --color-green-200: color(display-p3 .776442 .964383 .823412);
      --color-green-400: color(display-p3 .399536 .862346 .49324);
      --color-green-500: color(display-p3 .308734 .774754 .374307);
      --color-green-600: color(display-p3 .243882 .640824 .294808);
      --color-green-700: color(display-p3 .198355 .501799 .245335);
      --color-green-800: color(display-p3 .168568 .395123 .211217);
      --color-emerald-400: color(display-p3 .334701 .819603 .591575);
      --color-cyan-300: color(display-p3 .503734 .904871 .979358);
      --color-cyan-500: color(display-p3 .246703 .710032 .841444);
      --color-cyan-600: color(display-p3 .193249 .564651 .707197);
      --color-sky-500: color(display-p3 .219113 .639027 .931479);
      --color-blue-50: color(display-p3 .941826 .963151 .995385);
      --color-blue-100: color(display-p3 .869214 .915931 .989622);
      --color-blue-300: color(display-p3 .602559 .767214 .993938);
      --color-blue-400: color(display-p3 .397443 .62813 .992116);
      --color-blue-500: color(display-p3 .266422 .491219 .988624);
      --color-blue-600: color(display-p3 .174493 .358974 .950247);
      --color-blue-700: color(display-p3 .1379 .274983 .867624);
      --color-blue-800: color(display-p3 .134023 .230647 .695537);
      --color-purple-500: color(display-p3 .629519 .30089 .990817);
      --color-slate-50: color(display-p3 .974377 .979815 .986207);
      --color-slate-300: color(display-p3 .800294 .834432 .882803);
      --color-slate-600: color(display-p3 .283418 .332214 .416355);
      --color-slate-800: color(display-p3 .121994 .158688 .232363);
      --color-slate-900: color(display-p3 .0639692 .0891152 .163036);
      --color-gray-50: color(display-p3 .977213 .98084 .985102);
      --color-gray-100: color(display-p3 .953567 .956796 .964321);
      --color-gray-200: color(display-p3 .899787 .906171 .92106);
      --color-gray-300: color(display-p3 .822033 .835264 .858521);
      --color-gray-400: color(display-p3 .605734 .630385 .680158);
      --color-gray-500: color(display-p3 .421287 .446085 .504784);
      --color-gray-600: color(display-p3 .297358 .332176 .39043);
      --color-gray-700: color(display-p3 .219968 .253721 .318679);
      --color-gray-800: color(display-p3 .125854 .159497 .216835);
      --color-gray-900: color(display-p3 .070423 .0928982 .151928);
      --color-zinc-950: color(display-p3 .0353716 .0353595 .0435539);
      --color-neutral-200: color(display-p3 .898161 .898161 .898161);
      --color-neutral-300: color(display-p3 .831444 .831444 .831444);
      --color-neutral-600: color(display-p3 .321993 .321993 .321993);
    }
  }

  @supports (color: lab(0% 0 0)) {
    :root, :host {
      --color-red-50: lab(96.5005% 4.18511 1.52329);
      --color-red-200: lab(86.017% 19.8815 7.75869);
      --color-red-500: lab(55.4814% 75.0732 48.8528);
      --color-red-700: lab(40.4273% 67.2623 53.7441);
      --color-red-800: lab(33.7174% 55.8993 41.0293);
      --color-orange-500: lab(64.272% 57.1788 90.3583);
      --color-yellow-400: lab(83.2664% 8.65132 106.895);
      --color-yellow-500: lab(76.3898% 14.5258 98.4589);
      --color-green-50: lab(98.1563% -5.60117 2.75913);
      --color-green-100: lab(96.186% -13.8464 6.52362);
      --color-green-200: lab(92.4222% -26.4702 12.9427);
      --color-green-400: lab(78.503% -64.9265 39.7492);
      --color-green-500: lab(70.5521% -66.5147 45.8072);
      --color-green-600: lab(59.0978% -58.6621 41.2579);
      --color-green-700: lab(47.0329% -47.0239 31.4788);
      --color-green-800: lab(37.4616% -36.7971 22.9692);
      --color-emerald-400: lab(75.0771% -60.7313 19.4146);
      --color-cyan-300: lab(85.3886% -36.7635 -21.5717);
      --color-cyan-500: lab(67.805% -35.3952 -30.2018);
      --color-cyan-600: lab(55.1767% -26.7496 -30.5138);
      --color-sky-500: lab(63.3038% -18.433 -51.0407);
      --color-blue-50: lab(96.492% -1.14647 -5.11479);
      --color-blue-100: lab(92.0301% -2.24757 -11.6453);
      --color-blue-300: lab(77.5052% -6.4629 -36.42);
      --color-blue-400: lab(65.0361% -1.42062 -56.9803);
      --color-blue-500: lab(54.1736% 13.3368 -74.6839);
      --color-blue-600: lab(44.0605% 29.0279 -86.0352);
      --color-blue-700: lab(36.9089% 35.0961 -85.6872);
      --color-blue-800: lab(30.2514% 27.7854 -70.2699);
      --color-purple-500: lab(52.0183% 66.11 -78.2316);
      --color-slate-50: lab(98.1434% -.369549 -1.05968);
      --color-slate-300: lab(84.7652% -1.94535 -7.93337);
      --color-slate-600: lab(35.5623% -1.74978 -15.4316);
      --color-slate-800: lab(16.132% -.318021 -14.6672);
      --color-slate-900: lab(7.78673% 1.82346 -15.0537);
      --color-gray-50: lab(98.2596% -.247031 -.706708);
      --color-gray-100: lab(96.1596% -.082314 -1.13575);
      --color-gray-200: lab(91.6229% -.159085 -2.26791);
      --color-gray-300: lab(85.1236% -.612259 -3.7138);
      --color-gray-400: lab(65.9269% -.832707 -8.17474);
      --color-gray-500: lab(47.7841% -.393212 -10.0268);
      --color-gray-600: lab(35.6337% -1.58697 -10.8425);
      --color-gray-700: lab(27.1134% -.956401 -12.3224);
      --color-gray-800: lab(16.1051% -1.18239 -11.7533);
      --color-gray-900: lab(8.11897% .811279 -12.254);
      --color-zinc-950: lab(2.51107% .242703 -.886115);
      --color-neutral-200: lab(90.952% -.0000596046 0);
      --color-neutral-300: lab(84.92% 0 0);
      --color-neutral-600: lab(34.924% 0 0);
    }
  }
}

@layer base {
  *, :after, :before {
    box-sizing: border-box;
    border: 0 solid;
    margin: 0;
    padding: 0;
  }

  ::backdrop {
    box-sizing: border-box;
    border: 0 solid;
    margin: 0;
    padding: 0;
  }

  ::-webkit-file-upload-button {
    box-sizing: border-box;
    border: 0 solid;
    margin: 0;
    padding: 0;
  }

  ::file-selector-button {
    box-sizing: border-box;
    border: 0 solid;
    margin: 0;
    padding: 0;
  }

  html, :host {
    -webkit-text-size-adjust: 100%;
    -moz-tab-size: 4;
    tab-size: 4;
    line-height: 1.5;
    font-family: var(--default-font-family, ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji");
    font-feature-settings: var(--default-font-feature-settings, normal);
    font-variation-settings: var(--default-font-variation-settings, normal);
    -webkit-tap-highlight-color: transparent;
  }

  hr {
    height: 0;
    color: inherit;
    border-top-width: 1px;
  }

  abbr:where([title]) {
    -webkit-text-decoration: underline dotted;
    text-decoration: underline dotted;
  }

  h1, h2, h3, h4, h5, h6 {
    font-size: inherit;
    font-weight: inherit;
  }

  a {
    color: inherit;
    -webkit-text-decoration: inherit;
    -webkit-text-decoration: inherit;
    text-decoration: inherit;
  }

  b, strong {
    font-weight: bolder;
  }

  code, kbd, samp, pre {
    font-family: var(--default-mono-font-family, ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace);
    font-feature-settings: var(--default-mono-font-feature-settings, normal);
    font-variation-settings: var(--default-mono-font-variation-settings, normal);
    font-size: 1em;
  }

  small {
    font-size: 80%;
  }

  sub, sup {
    vertical-align: baseline;
    font-size: 75%;
    line-height: 0;
    position: relative;
  }

  sub {
    bottom: -.25em;
  }

  sup {
    top: -.5em;
  }

  table {
    text-indent: 0;
    border-color: inherit;
    border-collapse: collapse;
  }

  :-moz-focusring {
    outline: auto;
  }

  progress {
    vertical-align: baseline;
  }

  summary {
    display: list-item;
  }

  ol, ul, menu {
    list-style: none;
  }

  img, svg, video, canvas, audio, iframe, embed, object {
    vertical-align: middle;
    display: block;
  }

  img, video {
    max-width: 100%;
    height: auto;
  }

  button, input, select, optgroup, textarea {
    font: inherit;
    font-feature-settings: inherit;
    font-variation-settings: inherit;
    letter-spacing: inherit;
    color: inherit;
    opacity: 1;
    background-color: rgba(0, 0, 0, 0);
    border-radius: 0;
  }

  ::-webkit-file-upload-button {
    font: inherit;
    font-feature-settings: inherit;
    font-variation-settings: inherit;
    letter-spacing: inherit;
    color: inherit;
    opacity: 1;
    background-color: rgba(0, 0, 0, 0);
    border-radius: 0;
  }

  ::file-selector-button {
    font: inherit;
    font-feature-settings: inherit;
    font-variation-settings: inherit;
    letter-spacing: inherit;
    color: inherit;
    opacity: 1;
    background-color: rgba(0, 0, 0, 0);
    border-radius: 0;
  }

  :where(select:-webkit-any([multiple], [size])) optgroup {
    font-weight: bolder;
  }

  :where(select:-moz-any([multiple], [size])) optgroup {
    font-weight: bolder;
  }

  :where(select:is([multiple], [size])) optgroup {
    font-weight: bolder;
  }

  :where(select:-webkit-any([multiple], [size])) optgroup option:not(:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    padding-left: 20px;
  }

  :where(select:-moz-any([multiple], [size])) optgroup option:not(:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    padding-left: 20px;
  }

  :where(select:is([multiple], [size])) optgroup option:not(:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    padding-left: 20px;
  }

  :where(select:-webkit-any([multiple], [size])) optgroup option:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    padding-right: 20px;
  }

  :where(select:-moz-any([multiple], [size])) optgroup option:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    padding-right: 20px;
  }

  :where(select:is([multiple], [size])) optgroup option:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    padding-right: 20px;
  }

  :not(:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)))::-webkit-file-upload-button {
    margin-right: 4px;
  }

  :not(:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)))::file-selector-button {
    margin-right: 4px;
  }

  :not(:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)))::file-selector-button {
    margin-right: 4px;
  }

  :-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))::-webkit-file-upload-button {
    margin-left: 4px;
  }

  :-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))::file-selector-button {
    margin-left: 4px;
  }

  :is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))::file-selector-button {
    margin-left: 4px;
  }

  ::placeholder {
    opacity: 1;
  }

  @supports (not ((-webkit-appearance: -apple-pay-button))) or (contain-intrinsic-size: 1px) {
    ::placeholder {
      color: currentColor;
    }

    @supports (color: color-mix(in lab, red, red)) {
      ::placeholder {
        color: color-mix(in oklab, currentcolor 50%, transparent);
      }
    }
  }

  textarea {
    resize: vertical;
  }

  ::-webkit-search-decoration {
    -webkit-appearance: none;
  }

  ::-webkit-date-and-time-value {
    min-height: 1lh;
    text-align: inherit;
  }

  ::-webkit-datetime-edit {
    display: inline-flex;
  }

  ::-webkit-datetime-edit-fields-wrapper {
    padding: 0;
  }

  ::-webkit-datetime-edit {
    padding-top: 0;
    padding-bottom: 0;
  }

  ::-webkit-datetime-edit-year-field {
    padding-top: 0;
    padding-bottom: 0;
  }

  ::-webkit-datetime-edit-month-field {
    padding-top: 0;
    padding-bottom: 0;
  }

  ::-webkit-datetime-edit-day-field {
    padding-top: 0;
    padding-bottom: 0;
  }

  ::-webkit-datetime-edit-hour-field {
    padding-top: 0;
    padding-bottom: 0;
  }

  ::-webkit-datetime-edit-minute-field {
    padding-top: 0;
    padding-bottom: 0;
  }

  ::-webkit-datetime-edit-second-field {
    padding-top: 0;
    padding-bottom: 0;
  }

  ::-webkit-datetime-edit-millisecond-field {
    padding-top: 0;
    padding-bottom: 0;
  }

  ::-webkit-datetime-edit-meridiem-field {
    padding-top: 0;
    padding-bottom: 0;
  }

  :-moz-ui-invalid {
    box-shadow: none;
  }

  button {
    -webkit-appearance: button;
    -moz-appearance: button;
    appearance: button;
  }

  input:where([type="button"], [type="reset"], [type="submit"]) {
    -webkit-appearance: button;
    -moz-appearance: button;
    appearance: button;
  }

  ::-webkit-file-upload-button {
    -webkit-appearance: button;
    -moz-appearance: button;
    appearance: button;
  }

  ::file-selector-button {
    -webkit-appearance: button;
    -moz-appearance: button;
    appearance: button;
  }

  ::-webkit-inner-spin-button {
    height: auto;
  }

  ::-webkit-outer-spin-button {
    height: auto;
  }

  [hidden]:where(:not([hidden="until-found"])) {
    display: none !important;
  }
}

@layer components;

@layer utilities {
  .pointer-events-none {
    pointer-events: none;
  }

  .collapse {
    visibility: collapse;
  }

  .visible {
    visibility: visible;
  }

  .absolute {
    position: absolute;
  }

  .fixed {
    position: fixed;
  }

  .relative {
    position: relative;
  }

  .sticky {
    position: -webkit-sticky;
    position: sticky;
  }

  .inset-0 {
    inset: calc(var(--spacing) * 0);
  }

  .inset-x-0 {
    inset-inline: calc(var(--spacing) * 0);
  }

  .inset-y-0 {
    inset-block: calc(var(--spacing) * 0);
  }

  .-top-4 {
    top: calc(var(--spacing) * -4);
  }

  .top-0 {
    top: calc(var(--spacing) * 0);
  }

  .top-1\/2 {
    top: 50%;
  }

  .top-3 {
    top: calc(var(--spacing) * 3);
  }

  .top-4 {
    top: calc(var(--spacing) * 4);
  }

  .top-6 {
    top: calc(var(--spacing) * 6);
  }

  .top-20 {
    top: calc(var(--spacing) * 20);
  }

  .top-24 {
    top: calc(var(--spacing) * 24);
  }

  .top-32 {
    top: calc(var(--spacing) * 32);
  }

  .top-full {
    top: 100%;
  }

  .-right-4 {
    right: calc(var(--spacing) * -4);
  }

  .right-0 {
    right: calc(var(--spacing) * 0);
  }

  .right-1\/2 {
    right: 50%;
  }

  .right-1\/4 {
    right: 25%;
  }

  .right-3 {
    right: calc(var(--spacing) * 3);
  }

  .right-4 {
    right: calc(var(--spacing) * 4);
  }

  .right-6 {
    right: calc(var(--spacing) * 6);
  }

  .right-10 {
    right: calc(var(--spacing) * 10);
  }

  .-bottom-0 {
    bottom: calc(var(--spacing) * 0);
  }

  .-bottom-4 {
    bottom: calc(var(--spacing) * -4);
  }

  .bottom-0 {
    bottom: calc(var(--spacing) * 0);
  }

  .bottom-1\/2 {
    bottom: 50%;
  }

  .bottom-20 {
    bottom: calc(var(--spacing) * 20);
  }

  .-left-1 {
    left: calc(var(--spacing) * -1);
  }

  .-left-4 {
    left: calc(var(--spacing) * -4);
  }

  .left-0 {
    left: calc(var(--spacing) * 0);
  }

  .left-1\/2 {
    left: 50%;
  }

  .left-1\/4 {
    left: 25%;
  }

  .left-3 {
    left: calc(var(--spacing) * 3);
  }

  .left-4 {
    left: calc(var(--spacing) * 4);
  }

  .left-8 {
    left: calc(var(--spacing) * 8);
  }

  .left-10 {
    left: calc(var(--spacing) * 10);
  }

  .left-\[1\.125rem\] {
    left: 1.125rem;
  }

  .z-0 {
    z-index: 0;
  }

  .z-10 {
    z-index: 10;
  }

  .z-20 {
    z-index: 20;
  }

  .z-40 {
    z-index: 40;
  }

  .z-50 {
    z-index: 50;
  }

  .z-\[60\] {
    z-index: 60;
  }

  .z-\[9999\] {
    z-index: 9999;
  }

  .z-\[10000\] {
    z-index: 10000;
  }

  .col-span-full {
    grid-column: 1 / -1;
  }

  .row-span-1 {
    grid-row: span 1 / span 1;
  }

  .container {
    width: 100%;
  }

  @media (min-width: 40rem) {
    .container {
      max-width: 40rem;
    }
  }

  @media (min-width: 48rem) {
    .container {
      max-width: 48rem;
    }
  }

  @media (min-width: 64rem) {
    .container {
      max-width: 64rem;
    }
  }

  @media (min-width: 80rem) {
    .container {
      max-width: 80rem;
    }
  }

  @media (min-width: 96rem) {
    .container {
      max-width: 96rem;
    }
  }

  .mx-1 {
    margin-inline: calc(var(--spacing) * 1);
  }

  .mx-5 {
    margin-inline: calc(var(--spacing) * 5);
  }

  .mx-auto {
    margin-left: auto;
    margin-right: auto;
  }

  .my-6 {
    margin-block: calc(var(--spacing) * 6);
  }

  .my-8 {
    margin-block: calc(var(--spacing) * 8);
  }

  .-mt-6 {
    margin-top: calc(var(--spacing) * -6);
  }

  .-mt-7 {
    margin-top: calc(var(--spacing) * -7);
  }

  .mt-0 {
    margin-top: calc(var(--spacing) * 0);
  }

  .mt-0\.5 {
    margin-top: calc(var(--spacing) * .5);
  }

  .mt-1 {
    margin-top: calc(var(--spacing) * 1);
  }

  .mt-2 {
    margin-top: calc(var(--spacing) * 2);
  }

  .mt-4 {
    margin-top: calc(var(--spacing) * 4);
  }

  .mt-6 {
    margin-top: calc(var(--spacing) * 6);
  }

  .mt-8 {
    margin-top: calc(var(--spacing) * 8);
  }

  .mt-12 {
    margin-top: calc(var(--spacing) * 12);
  }

  .mt-16 {
    margin-top: calc(var(--spacing) * 16);
  }

  .mt-20 {
    margin-top: calc(var(--spacing) * 20);
  }

  .mt-24 {
    margin-top: calc(var(--spacing) * 24);
  }

  .mt-auto {
    margin-top: auto;
  }

  .mr-2 {
    margin-right: calc(var(--spacing) * 2);
  }

  .mr-3 {
    margin-right: calc(var(--spacing) * 3);
  }

  .mb-1 {
    margin-bottom: calc(var(--spacing) * 1);
  }

  .mb-2 {
    margin-bottom: calc(var(--spacing) * 2);
  }

  .mb-3 {
    margin-bottom: calc(var(--spacing) * 3);
  }

  .mb-4 {
    margin-bottom: calc(var(--spacing) * 4);
  }

  .mb-6 {
    margin-bottom: calc(var(--spacing) * 6);
  }

  .mb-8 {
    margin-bottom: calc(var(--spacing) * 8);
  }

  .mb-10 {
    margin-bottom: calc(var(--spacing) * 10);
  }

  .mb-12 {
    margin-bottom: calc(var(--spacing) * 12);
  }

  .mb-16 {
    margin-bottom: calc(var(--spacing) * 16);
  }

  .mb-20 {
    margin-bottom: calc(var(--spacing) * 20);
  }

  .ml-1 {
    margin-left: calc(var(--spacing) * 1);
  }

  .ml-2 {
    margin-left: calc(var(--spacing) * 2);
  }

  .ml-4 {
    margin-left: calc(var(--spacing) * 4);
  }

  .ml-\[0\.09375rem\] {
    margin-left: .09375rem;
  }

  .line-clamp-2 {
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    display: -webkit-box;
    overflow: hidden;
  }

  .line-clamp-3 {
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    display: -webkit-box;
    overflow: hidden;
  }

  .block {
    display: block;
  }

  .flex {
    display: flex;
  }

  .grid {
    display: grid;
  }

  .hidden {
    display: none;
  }

  .inline {
    display: inline;
  }

  .inline-block {
    display: inline-block;
  }

  .inline-flex {
    display: inline-flex;
  }

  .aspect-\[4\/3\] {
    aspect-ratio: 4 / 3;
  }

  .h-1 {
    height: calc(var(--spacing) * 1);
  }

  .h-2 {
    height: calc(var(--spacing) * 2);
  }

  .h-3 {
    height: calc(var(--spacing) * 3);
  }

  .h-3\.5 {
    height: calc(var(--spacing) * 3.5);
  }

  .h-4 {
    height: calc(var(--spacing) * 4);
  }

  .h-5 {
    height: calc(var(--spacing) * 5);
  }

  .h-6 {
    height: calc(var(--spacing) * 6);
  }

  .h-7 {
    height: calc(var(--spacing) * 7);
  }

  .h-8 {
    height: calc(var(--spacing) * 8);
  }

  .h-10 {
    height: calc(var(--spacing) * 10);
  }

  .h-12 {
    height: calc(var(--spacing) * 12);
  }

  .h-14 {
    height: calc(var(--spacing) * 14);
  }

  .h-16 {
    height: calc(var(--spacing) * 16);
  }

  .h-20 {
    height: calc(var(--spacing) * 20);
  }

  .h-24 {
    height: calc(var(--spacing) * 24);
  }

  .h-28 {
    height: calc(var(--spacing) * 28);
  }

  .h-32 {
    height: calc(var(--spacing) * 32);
  }

  .h-40 {
    height: calc(var(--spacing) * 40);
  }

  .h-48 {
    height: calc(var(--spacing) * 48);
  }

  .h-56 {
    height: calc(var(--spacing) * 56);
  }

  .h-64 {
    height: calc(var(--spacing) * 64);
  }

  .h-72 {
    height: calc(var(--spacing) * 72);
  }

  .h-80 {
    height: calc(var(--spacing) * 80);
  }

  .h-96 {
    height: calc(var(--spacing) * 96);
  }

  .h-\[2px\] {
    height: 2px;
  }

  .h-\[4px\] {
    height: 4px;
  }

  .h-\[11\.25rem\] {
    height: 11.25rem;
  }

  .h-\[60px\] {
    height: 60px;
  }

  .h-\[140px\] {
    height: 140px;
  }

  .h-\[400px\] {
    height: 400px;
  }

  .h-\[500px\] {
    height: 500px;
  }

  .h-auto {
    height: auto;
  }

  .h-fit {
    height: -moz-fit-content;
    height: fit-content;
  }

  .h-full {
    height: 100%;
  }

  .h-px {
    height: 1px;
  }

  .max-h-12 {
    max-height: calc(var(--spacing) * 12);
  }

  .max-h-36 {
    max-height: calc(var(--spacing) * 36);
  }

  .max-h-\[50vh\] {
    max-height: 50vh;
  }

  .max-h-\[60vh\] {
    max-height: 60vh;
  }

  .max-h-\[80vh\] {
    max-height: 80vh;
  }

  .max-h-\[400px\] {
    max-height: 400px;
  }

  .max-h-\[calc\(100vh-8rem\)\] {
    max-height: calc(100vh - 8rem);
  }

  .max-h-\[calc\(100vh-10rem\)\] {
    max-height: calc(100vh - 10rem);
  }

  .max-h-full {
    max-height: 100%;
  }

  .min-h-\[60vh\] {
    min-height: 60vh;
  }

  .min-h-\[200px\] {
    min-height: 200px;
  }

  .min-h-\[280px\] {
    min-height: 280px;
  }

  .min-h-\[300px\] {
    min-height: 300px;
  }

  .min-h-screen {
    min-height: 100vh;
  }

  .w-0 {
    width: calc(var(--spacing) * 0);
  }

  .w-1 {
    width: calc(var(--spacing) * 1);
  }

  .w-1\/3 {
    width: 33.3333%;
  }

  .w-2 {
    width: calc(var(--spacing) * 2);
  }

  .w-3 {
    width: calc(var(--spacing) * 3);
  }

  .w-3\.5 {
    width: calc(var(--spacing) * 3.5);
  }

  .w-4 {
    width: calc(var(--spacing) * 4);
  }

  .w-5 {
    width: calc(var(--spacing) * 5);
  }

  .w-6 {
    width: calc(var(--spacing) * 6);
  }

  .w-7 {
    width: calc(var(--spacing) * 7);
  }

  .w-8 {
    width: calc(var(--spacing) * 8);
  }

  .w-10 {
    width: calc(var(--spacing) * 10);
  }

  .w-11\/12 {
    width: 91.6667%;
  }

  .w-12 {
    width: calc(var(--spacing) * 12);
  }

  .w-14 {
    width: calc(var(--spacing) * 14);
  }

  .w-16 {
    width: calc(var(--spacing) * 16);
  }

  .w-20 {
    width: calc(var(--spacing) * 20);
  }

  .w-40 {
    width: calc(var(--spacing) * 40);
  }

  .w-44 {
    width: calc(var(--spacing) * 44);
  }

  .w-72 {
    width: calc(var(--spacing) * 72);
  }

  .w-80 {
    width: calc(var(--spacing) * 80);
  }

  .w-96 {
    width: calc(var(--spacing) * 96);
  }

  .w-\[2px\] {
    width: 2px;
  }

  .w-\[4px\] {
    width: 4px;
  }

  .w-\[11\.25rem\] {
    width: 11.25rem;
  }

  .w-\[90\%\] {
    width: 90%;
  }

  .w-\[250px\] {
    width: 250px;
  }

  .w-\[calc\(100\%-2\.25rem\)\] {
    width: calc(100% - 2.25rem);
  }

  .w-auto {
    width: auto;
  }

  .w-full {
    width: 100%;
  }

  .w-px {
    width: 1px;
  }

  .max-w-2xl {
    max-width: var(--container-2xl);
  }

  .max-w-3xl {
    max-width: var(--container-3xl);
  }

  .max-w-4xl {
    max-width: var(--container-4xl);
  }

  .max-w-5xl {
    max-width: var(--container-5xl);
  }

  .max-w-6xl {
    max-width: var(--container-6xl);
  }

  .max-w-7xl {
    max-width: var(--container-7xl);
  }

  .max-w-\[120px\] {
    max-width: 120px;
  }

  .max-w-\[280px\] {
    max-width: 280px;
  }

  .max-w-full {
    max-width: 100%;
  }

  .max-w-lg {
    max-width: var(--container-lg);
  }

  .max-w-md {
    max-width: var(--container-md);
  }

  .max-w-none {
    max-width: none;
  }

  .max-w-screen-2xl {
    max-width: var(--breakpoint-2xl);
  }

  .max-w-sm {
    max-width: var(--container-sm);
  }

  .max-w-xl {
    max-width: var(--container-xl);
  }

  .min-w-0 {
    min-width: calc(var(--spacing) * 0);
  }

  .min-w-full {
    min-width: 100%;
  }

  .flex-1 {
    flex: 1;
  }

  .flex-none {
    flex: none;
  }

  .flex-shrink-0, .shrink-0 {
    flex-shrink: 0;
  }

  .flex-grow, .grow {
    flex-grow: 1;
  }

  .border-collapse {
    border-collapse: collapse;
  }

  .-translate-x-1 {
    --tw-translate-x: calc(var(--spacing) * -1);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .-translate-x-1\/2 {
    --tw-translate-x: calc(calc(1 / 2 * 100%) * -1);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .translate-x-\[0\.5px\] {
    --tw-translate-x: .5px;
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .translate-x-\[1\.5px\] {
    --tw-translate-x: 1.5px;
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .-translate-y-1\/2 {
    --tw-translate-y: calc(calc(1 / 2 * 100%) * -1);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .-translate-y-full {
    --tw-translate-y: -100%;
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .translate-y-0 {
    --tw-translate-y: calc(var(--spacing) * 0);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .translate-y-1\/2 {
    --tw-translate-y: calc(1 / 2 * 100%);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .translate-y-4 {
    --tw-translate-y: calc(var(--spacing) * 4);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .translate-y-\[14px\] {
    --tw-translate-y: 14px;
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .scale-95 {
    --tw-scale-x: 95%;
    --tw-scale-y: 95%;
    --tw-scale-z: 95%;
    scale: var(--tw-scale-x) var(--tw-scale-y);
  }

  .scale-100 {
    --tw-scale-x: 100%;
    --tw-scale-y: 100%;
    --tw-scale-z: 100%;
    scale: var(--tw-scale-x) var(--tw-scale-y);
  }

  .scale-125 {
    --tw-scale-x: 125%;
    --tw-scale-y: 125%;
    --tw-scale-z: 125%;
    scale: var(--tw-scale-x) var(--tw-scale-y);
  }

  .rotate-180 {
    rotate: 180deg;
  }

  .transform {
    transform: var(--tw-rotate-x, ) var(--tw-rotate-y, ) var(--tw-rotate-z, ) var(--tw-skew-x, ) var(--tw-skew-y, );
  }

  .animate-spin {
    animation: var(--animate-spin);
  }

  .cursor-not-allowed {
    cursor: not-allowed;
  }

  .cursor-pointer {
    cursor: pointer;
  }

  .resize {
    resize: both;
  }

  .resize-none {
    resize: none;
  }

  .snap-x {
    scroll-snap-type: x var(--tw-scroll-snap-strictness);
  }

  .snap-mandatory {
    --tw-scroll-snap-strictness: mandatory;
  }

  .snap-center {
    scroll-snap-align: center;
  }

  .scroll-mt-32 {
    scroll-margin-top: calc(var(--spacing) * 32);
  }

  .list-inside {
    list-style-position: inside;
  }

  .list-disc {
    list-style-type: disc;
  }

  .appearance-none {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
  }

  .grid-cols-1 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }

  .grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .flex-col {
    flex-direction: column;
  }

  .flex-wrap {
    flex-wrap: wrap;
  }

  .items-center {
    align-items: center;
  }

  .items-end {
    align-items: flex-end;
  }

  .items-start {
    align-items: flex-start;
  }

  .items-stretch {
    align-items: stretch;
  }

  .justify-between {
    justify-content: space-between;
  }

  .justify-center {
    justify-content: center;
  }

  .justify-start {
    justify-content: flex-start;
  }

  .justify-items-start {
    justify-items: start;
  }

  .gap-0 {
    gap: calc(var(--spacing) * 0);
  }

  .gap-1 {
    gap: calc(var(--spacing) * 1);
  }

  .gap-1\.5 {
    gap: calc(var(--spacing) * 1.5);
  }

  .gap-2 {
    gap: calc(var(--spacing) * 2);
  }

  .gap-3 {
    gap: calc(var(--spacing) * 3);
  }

  .gap-4 {
    gap: calc(var(--spacing) * 4);
  }

  .gap-5 {
    gap: calc(var(--spacing) * 5);
  }

  .gap-6 {
    gap: calc(var(--spacing) * 6);
  }

  .gap-8 {
    gap: calc(var(--spacing) * 8);
  }

  .gap-10 {
    gap: calc(var(--spacing) * 10);
  }

  .gap-12 {
    gap: calc(var(--spacing) * 12);
  }

  .gap-16 {
    gap: calc(var(--spacing) * 16);
  }

  :where(.space-y-1 > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-top: calc(calc(var(--spacing) * 1) * var(--tw-space-y-reverse));
    margin-bottom: calc(calc(var(--spacing) * 1) * calc(1 - var(--tw-space-y-reverse)));
  }

  :where(.space-y-2 > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-top: calc(calc(var(--spacing) * 2) * var(--tw-space-y-reverse));
    margin-bottom: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-y-reverse)));
  }

  :where(.space-y-3 > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-top: calc(calc(var(--spacing) * 3) * var(--tw-space-y-reverse));
    margin-bottom: calc(calc(var(--spacing) * 3) * calc(1 - var(--tw-space-y-reverse)));
  }

  :where(.space-y-4 > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-top: calc(calc(var(--spacing) * 4) * var(--tw-space-y-reverse));
    margin-bottom: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-y-reverse)));
  }

  :where(.space-y-5 > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-top: calc(calc(var(--spacing) * 5) * var(--tw-space-y-reverse));
    margin-bottom: calc(calc(var(--spacing) * 5) * calc(1 - var(--tw-space-y-reverse)));
  }

  :where(.space-y-6 > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-top: calc(calc(var(--spacing) * 6) * var(--tw-space-y-reverse));
    margin-bottom: calc(calc(var(--spacing) * 6) * calc(1 - var(--tw-space-y-reverse)));
  }

  :where(.space-y-8 > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-top: calc(calc(var(--spacing) * 8) * var(--tw-space-y-reverse));
    margin-bottom: calc(calc(var(--spacing) * 8) * calc(1 - var(--tw-space-y-reverse)));
  }

  :where(.space-y-16 > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-top: calc(calc(var(--spacing) * 16) * var(--tw-space-y-reverse));
    margin-bottom: calc(calc(var(--spacing) * 16) * calc(1 - var(--tw-space-y-reverse)));
  }

  .gap-x-8 {
    column-gap: calc(var(--spacing) * 8);
  }

  :where(.space-x-1 > :not(:last-child)) {
    --tw-space-x-reverse: 0;
  }

  :where(.space-x-1 > :not(:last-child)):not(:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    margin-left: calc(calc(var(--spacing) * 1) * var(--tw-space-x-reverse));
    margin-right: calc(calc(var(--spacing) * 1) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-1 > :not(:last-child)):not(:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    margin-left: calc(calc(var(--spacing) * 1) * var(--tw-space-x-reverse));
    margin-right: calc(calc(var(--spacing) * 1) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-1 > :not(:last-child)):not(:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    margin-left: calc(calc(var(--spacing) * 1) * var(--tw-space-x-reverse));
    margin-right: calc(calc(var(--spacing) * 1) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-1 > :not(:last-child)):-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    margin-right: calc(calc(var(--spacing) * 1) * var(--tw-space-x-reverse));
    margin-left: calc(calc(var(--spacing) * 1) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-1 > :not(:last-child)):-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    margin-right: calc(calc(var(--spacing) * 1) * var(--tw-space-x-reverse));
    margin-left: calc(calc(var(--spacing) * 1) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-1 > :not(:last-child)):is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    margin-right: calc(calc(var(--spacing) * 1) * var(--tw-space-x-reverse));
    margin-left: calc(calc(var(--spacing) * 1) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-2 > :not(:last-child)) {
    --tw-space-x-reverse: 0;
  }

  :where(.space-x-2 > :not(:last-child)):not(:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    margin-left: calc(calc(var(--spacing) * 2) * var(--tw-space-x-reverse));
    margin-right: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-2 > :not(:last-child)):not(:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    margin-left: calc(calc(var(--spacing) * 2) * var(--tw-space-x-reverse));
    margin-right: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-2 > :not(:last-child)):not(:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    margin-left: calc(calc(var(--spacing) * 2) * var(--tw-space-x-reverse));
    margin-right: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-2 > :not(:last-child)):-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    margin-right: calc(calc(var(--spacing) * 2) * var(--tw-space-x-reverse));
    margin-left: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-2 > :not(:last-child)):-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    margin-right: calc(calc(var(--spacing) * 2) * var(--tw-space-x-reverse));
    margin-left: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-2 > :not(:last-child)):is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    margin-right: calc(calc(var(--spacing) * 2) * var(--tw-space-x-reverse));
    margin-left: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-3 > :not(:last-child)) {
    --tw-space-x-reverse: 0;
  }

  :where(.space-x-3 > :not(:last-child)):not(:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    margin-left: calc(calc(var(--spacing) * 3) * var(--tw-space-x-reverse));
    margin-right: calc(calc(var(--spacing) * 3) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-3 > :not(:last-child)):not(:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    margin-left: calc(calc(var(--spacing) * 3) * var(--tw-space-x-reverse));
    margin-right: calc(calc(var(--spacing) * 3) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-3 > :not(:last-child)):not(:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    margin-left: calc(calc(var(--spacing) * 3) * var(--tw-space-x-reverse));
    margin-right: calc(calc(var(--spacing) * 3) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-3 > :not(:last-child)):-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    margin-right: calc(calc(var(--spacing) * 3) * var(--tw-space-x-reverse));
    margin-left: calc(calc(var(--spacing) * 3) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-3 > :not(:last-child)):-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    margin-right: calc(calc(var(--spacing) * 3) * var(--tw-space-x-reverse));
    margin-left: calc(calc(var(--spacing) * 3) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-3 > :not(:last-child)):is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    margin-right: calc(calc(var(--spacing) * 3) * var(--tw-space-x-reverse));
    margin-left: calc(calc(var(--spacing) * 3) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-4 > :not(:last-child)) {
    --tw-space-x-reverse: 0;
  }

  :where(.space-x-4 > :not(:last-child)):not(:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    margin-left: calc(calc(var(--spacing) * 4) * var(--tw-space-x-reverse));
    margin-right: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-4 > :not(:last-child)):not(:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    margin-left: calc(calc(var(--spacing) * 4) * var(--tw-space-x-reverse));
    margin-right: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-4 > :not(:last-child)):not(:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    margin-left: calc(calc(var(--spacing) * 4) * var(--tw-space-x-reverse));
    margin-right: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-4 > :not(:last-child)):-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    margin-right: calc(calc(var(--spacing) * 4) * var(--tw-space-x-reverse));
    margin-left: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-4 > :not(:last-child)):-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    margin-right: calc(calc(var(--spacing) * 4) * var(--tw-space-x-reverse));
    margin-left: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-4 > :not(:last-child)):is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    margin-right: calc(calc(var(--spacing) * 4) * var(--tw-space-x-reverse));
    margin-left: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-6 > :not(:last-child)) {
    --tw-space-x-reverse: 0;
  }

  :where(.space-x-6 > :not(:last-child)):not(:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    margin-left: calc(calc(var(--spacing) * 6) * var(--tw-space-x-reverse));
    margin-right: calc(calc(var(--spacing) * 6) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-6 > :not(:last-child)):not(:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    margin-left: calc(calc(var(--spacing) * 6) * var(--tw-space-x-reverse));
    margin-right: calc(calc(var(--spacing) * 6) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-6 > :not(:last-child)):not(:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    margin-left: calc(calc(var(--spacing) * 6) * var(--tw-space-x-reverse));
    margin-right: calc(calc(var(--spacing) * 6) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-6 > :not(:last-child)):-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    margin-right: calc(calc(var(--spacing) * 6) * var(--tw-space-x-reverse));
    margin-left: calc(calc(var(--spacing) * 6) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-6 > :not(:last-child)):-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    margin-right: calc(calc(var(--spacing) * 6) * var(--tw-space-x-reverse));
    margin-left: calc(calc(var(--spacing) * 6) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-6 > :not(:last-child)):is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    margin-right: calc(calc(var(--spacing) * 6) * var(--tw-space-x-reverse));
    margin-left: calc(calc(var(--spacing) * 6) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-8 > :not(:last-child)) {
    --tw-space-x-reverse: 0;
  }

  :where(.space-x-8 > :not(:last-child)):not(:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    margin-left: calc(calc(var(--spacing) * 8) * var(--tw-space-x-reverse));
    margin-right: calc(calc(var(--spacing) * 8) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-8 > :not(:last-child)):not(:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    margin-left: calc(calc(var(--spacing) * 8) * var(--tw-space-x-reverse));
    margin-right: calc(calc(var(--spacing) * 8) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-8 > :not(:last-child)):not(:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    margin-left: calc(calc(var(--spacing) * 8) * var(--tw-space-x-reverse));
    margin-right: calc(calc(var(--spacing) * 8) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-8 > :not(:last-child)):-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    margin-right: calc(calc(var(--spacing) * 8) * var(--tw-space-x-reverse));
    margin-left: calc(calc(var(--spacing) * 8) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-8 > :not(:last-child)):-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    margin-right: calc(calc(var(--spacing) * 8) * var(--tw-space-x-reverse));
    margin-left: calc(calc(var(--spacing) * 8) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-8 > :not(:last-child)):is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    margin-right: calc(calc(var(--spacing) * 8) * var(--tw-space-x-reverse));
    margin-left: calc(calc(var(--spacing) * 8) * calc(1 - var(--tw-space-x-reverse)));
  }

  .gap-y-10 {
    row-gap: calc(var(--spacing) * 10);
  }

  .gap-y-12 {
    row-gap: calc(var(--spacing) * 12);
  }

  .self-start {
    align-self: flex-start;
  }

  .truncate {
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
  }

  .overflow-hidden {
    overflow: hidden;
  }

  .overflow-x-auto {
    overflow-x: auto;
  }

  .overflow-y-auto {
    overflow-y: auto;
  }

  .rounded {
    border-radius: .25rem;
  }

  .rounded-2xl {
    border-radius: var(--radius-2xl);
  }

  .rounded-3xl {
    border-radius: var(--radius-3xl);
  }

  .rounded-4xl {
    border-radius: var(--radius-4xl);
  }

  .rounded-\[50\%\] {
    border-radius: 50%;
  }

  .rounded-full {
    border-radius: 3.40282e38px;
  }

  .rounded-lg {
    border-radius: var(--radius-lg);
  }

  .rounded-md {
    border-radius: var(--radius-md);
  }

  .rounded-xl {
    border-radius: var(--radius-xl);
  }

  .rounded-t-2xl {
    border-top-left-radius: var(--radius-2xl);
    border-top-right-radius: var(--radius-2xl);
  }

  .border {
    border-style: var(--tw-border-style);
    border-width: 1px;
  }

  .border-2 {
    border-style: var(--tw-border-style);
    border-width: 2px;
  }

  .border-t {
    border-top-style: var(--tw-border-style);
    border-top-width: 1px;
  }

  .border-t-4 {
    border-top-style: var(--tw-border-style);
    border-top-width: 4px;
  }

  .border-r {
    border-right-style: var(--tw-border-style);
    border-right-width: 1px;
  }

  .border-b {
    border-bottom-style: var(--tw-border-style);
    border-bottom-width: 1px;
  }

  .border-b-2 {
    border-bottom-style: var(--tw-border-style);
    border-bottom-width: 2px;
  }

  .border-l-4 {
    border-left-style: var(--tw-border-style);
    border-left-width: 4px;
  }

  .border-dashed {
    --tw-border-style: dashed;
    border-style: dashed;
  }

  .border-\[var\(--color-blue\)\] {
    border-color: var(--color-blue);
  }

  .border-\[var\(--color-blue\)\]\/10 {
    border-color: var(--color-blue);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .border-\[var\(--color-blue\)\]\/10 {
      border-color: color-mix(in oklab, var(--color-blue) 10%, transparent);
    }
  }

  .border-\[var\(--color-blue\)\]\/20 {
    border-color: var(--color-blue);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .border-\[var\(--color-blue\)\]\/20 {
      border-color: color-mix(in oklab, var(--color-blue) 20%, transparent);
    }
  }

  .border-\[var\(--color-brand-blue\)\]\/20 {
    border-color: var(--color-brand-blue);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .border-\[var\(--color-brand-blue\)\]\/20 {
      border-color: color-mix(in oklab, var(--color-brand-blue) 20%, transparent);
    }
  }

  .border-\[var\(--color-yellow\)\] {
    border-color: var(--color-yellow);
  }

  .border-\[var\(--color-yellow\)\]\/30 {
    border-color: var(--color-yellow);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .border-\[var\(--color-yellow\)\]\/30 {
      border-color: color-mix(in oklab, var(--color-yellow) 30%, transparent);
    }
  }

  .border-gray-100 {
    border-color: var(--color-gray-100);
  }

  .border-gray-200 {
    border-color: var(--color-gray-200);
  }

  .border-gray-300 {
    border-color: var(--color-gray-300);
  }

  .border-gray-700 {
    border-color: var(--color-gray-700);
  }

  .border-gray-700\/50 {
    border-color: rgba(54, 65, 83, .5);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .border-gray-700\/50 {
      border-color: color-mix(in oklab, var(--color-gray-700) 50%, transparent);
    }
  }

  .border-green-200 {
    border-color: var(--color-green-200);
  }

  .border-green-500 {
    border-color: var(--color-green-500);
  }

  .border-purple-500 {
    border-color: var(--color-purple-500);
  }

  .border-red-200 {
    border-color: var(--color-red-200);
  }

  .border-red-500 {
    border-color: var(--color-red-500);
  }

  .border-transparent {
    border-color: rgba(0, 0, 0, 0);
  }

  .border-white {
    border-color: var(--color-white);
  }

  .border-white\/10 {
    border-color: rgba(255, 255, 255, .1);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .border-white\/10 {
      border-color: color-mix(in oklab, var(--color-white) 10%, transparent);
    }
  }

  .border-white\/15 {
    border-color: rgba(255, 255, 255, .15);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .border-white\/15 {
      border-color: color-mix(in oklab, var(--color-white) 15%, transparent);
    }
  }

  .border-white\/20 {
    border-color: rgba(255, 255, 255, .2);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .border-white\/20 {
      border-color: color-mix(in oklab, var(--color-white) 20%, transparent);
    }
  }

  .border-white\/\[0\.1\] {
    border-color: rgba(255, 255, 255, .1);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .border-white\/\[0\.1\] {
      border-color: color-mix(in oklab, var(--color-white) 10%, transparent);
    }
  }

  .border-yellow-500 {
    border-color: var(--color-yellow-500);
  }

  .border-t-\[var\(--color-blue\)\] {
    border-top-color: var(--color-blue);
  }

  .bg-\[\#1E40AF\]\/20 {
    background-color: rgba(30, 64, 175, .2);
    background-color: color(display-p3 .150755 .247797 .660864 / .2);
    background-color: lab(30.7907% 22.3499 -63.8416 / .2);
  }

  .bg-\[\#3265fe\] {
    background-color: #3265fe;
  }

  .bg-\[\#F8F8F8\] {
    background-color: #f8f8f8;
  }

  .bg-\[var\(--bg-light-blue\)\] {
    background-color: var(--bg-light-blue);
  }

  .bg-\[var\(--color-blue\)\] {
    background-color: var(--color-blue);
  }

  .bg-\[var\(--color-blue\)\]\/5 {
    background-color: var(--color-blue);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-\[var\(--color-blue\)\]\/5 {
      background-color: color-mix(in oklab, var(--color-blue) 5%, transparent);
    }
  }

  .bg-\[var\(--color-blue\)\]\/10 {
    background-color: var(--color-blue);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-\[var\(--color-blue\)\]\/10 {
      background-color: color-mix(in oklab, var(--color-blue) 10%, transparent);
    }
  }

  .bg-\[var\(--color-blue\)\]\/20 {
    background-color: var(--color-blue);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-\[var\(--color-blue\)\]\/20 {
      background-color: color-mix(in oklab, var(--color-blue) 20%, transparent);
    }
  }

  .bg-\[var\(--color-blue\)\]\/80 {
    background-color: var(--color-blue);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-\[var\(--color-blue\)\]\/80 {
      background-color: color-mix(in oklab, var(--color-blue) 80%, transparent);
    }
  }

  .bg-\[var\(--color-blue-secondary\)\] {
    background-color: var(--color-blue-secondary);
  }

  .bg-\[var\(--color-blue-secondary\)\]\/5 {
    background-color: var(--color-blue-secondary);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-\[var\(--color-blue-secondary\)\]\/5 {
      background-color: color-mix(in oklab, var(--color-blue-secondary) 5%, transparent);
    }
  }

  .bg-\[var\(--color-blue-secondary\)\]\/20 {
    background-color: var(--color-blue-secondary);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-\[var\(--color-blue-secondary\)\]\/20 {
      background-color: color-mix(in oklab, var(--color-blue-secondary) 20%, transparent);
    }
  }

  .bg-\[var\(--color-brand-blue\)\] {
    background-color: var(--color-brand-blue);
  }

  .bg-\[var\(--color-brand-navy\)\] {
    background-color: var(--color-brand-navy);
  }

  .bg-\[var\(--color-dark-blue\)\] {
    background-color: var(--color-dark-blue);
  }

  .bg-\[var\(--color-dark-blue\)\]\/80 {
    background-color: var(--color-dark-blue);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-\[var\(--color-dark-blue\)\]\/80 {
      background-color: color-mix(in oklab, var(--color-dark-blue) 80%, transparent);
    }
  }

  .bg-\[var\(--color-dark-blue\)\]\/90 {
    background-color: var(--color-dark-blue);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-\[var\(--color-dark-blue\)\]\/90 {
      background-color: color-mix(in oklab, var(--color-dark-blue) 90%, transparent);
    }
  }

  .bg-\[var\(--color-yellow\)\] {
    background-color: var(--color-yellow);
  }

  .bg-\[var\(--color-yellow\)\]\/5 {
    background-color: var(--color-yellow);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-\[var\(--color-yellow\)\]\/5 {
      background-color: color-mix(in oklab, var(--color-yellow) 5%, transparent);
    }
  }

  .bg-\[var\(--color-yellow\)\]\/10 {
    background-color: var(--color-yellow);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-\[var\(--color-yellow\)\]\/10 {
      background-color: color-mix(in oklab, var(--color-yellow) 10%, transparent);
    }
  }

  .bg-\[var\(--color-yellow\)\]\/20 {
    background-color: var(--color-yellow);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-\[var\(--color-yellow\)\]\/20 {
      background-color: color-mix(in oklab, var(--color-yellow) 20%, transparent);
    }
  }

  .bg-black\/50 {
    background-color: rgba(0, 0, 0, .5);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-black\/50 {
      background-color: color-mix(in oklab, var(--color-black) 50%, transparent);
    }
  }

  .bg-blue-100 {
    background-color: var(--color-blue-100);
  }

  .bg-blue-500 {
    background-color: var(--color-blue-500);
  }

  .bg-blue-600 {
    background-color: var(--color-blue-600);
  }

  .bg-cyan-300 {
    background-color: var(--color-cyan-300);
  }

  .bg-cyan-600 {
    background-color: var(--color-cyan-600);
  }

  .bg-gray-50 {
    background-color: var(--color-gray-50);
  }

  .bg-gray-100 {
    background-color: var(--color-gray-100);
  }

  .bg-gray-200 {
    background-color: var(--color-gray-200);
  }

  .bg-gray-300 {
    background-color: var(--color-gray-300);
  }

  .bg-gray-400 {
    background-color: var(--color-gray-400);
  }

  .bg-gray-600 {
    background-color: var(--color-gray-600);
  }

  .bg-gray-800 {
    background-color: var(--color-gray-800);
  }

  .bg-gray-800\/50 {
    background-color: rgba(30, 41, 57, .5);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-gray-800\/50 {
      background-color: color-mix(in oklab, var(--color-gray-800) 50%, transparent);
    }
  }

  .bg-green-50 {
    background-color: var(--color-green-50);
  }

  .bg-green-100 {
    background-color: var(--color-green-100);
  }

  .bg-green-500 {
    background-color: var(--color-green-500);
  }

  .bg-red-50 {
    background-color: var(--color-red-50);
  }

  .bg-sky-500\/\[0\.08\] {
    background-color: rgba(0, 165, 239, .08);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-sky-500\/\[0\.08\] {
      background-color: color-mix(in oklab, var(--color-sky-500) 8%, transparent);
    }
  }

  .bg-slate-800 {
    background-color: var(--color-slate-800);
  }

  .bg-transparent {
    background-color: rgba(0, 0, 0, 0);
  }

  .bg-white {
    background-color: var(--color-white);
  }

  .bg-white\/5 {
    background-color: rgba(255, 255, 255, .05);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-white\/5 {
      background-color: color-mix(in oklab, var(--color-white) 5%, transparent);
    }
  }

  .bg-white\/10 {
    background-color: rgba(255, 255, 255, .1);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-white\/10 {
      background-color: color-mix(in oklab, var(--color-white) 10%, transparent);
    }
  }

  .bg-white\/15 {
    background-color: rgba(255, 255, 255, .15);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-white\/15 {
      background-color: color-mix(in oklab, var(--color-white) 15%, transparent);
    }
  }

  .bg-white\/20 {
    background-color: rgba(255, 255, 255, .2);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-white\/20 {
      background-color: color-mix(in oklab, var(--color-white) 20%, transparent);
    }
  }

  .bg-white\/30 {
    background-color: rgba(255, 255, 255, .3);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-white\/30 {
      background-color: color-mix(in oklab, var(--color-white) 30%, transparent);
    }
  }

  .bg-white\/80 {
    background-color: rgba(255, 255, 255, .8);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-white\/80 {
      background-color: color-mix(in oklab, var(--color-white) 80%, transparent);
    }
  }

  .bg-white\/90 {
    background-color: rgba(255, 255, 255, .9);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-white\/90 {
      background-color: color-mix(in oklab, var(--color-white) 90%, transparent);
    }
  }

  .bg-yellow-500 {
    background-color: var(--color-yellow-500);
  }

  .bg-zinc-950 {
    background-color: var(--color-zinc-950);
  }

  .bg-gradient-to-b {
    --tw-gradient-position: to bottom in oklab;
    background-image: linear-gradient(var(--tw-gradient-stops));
  }

  .bg-gradient-to-bl {
    --tw-gradient-position: to bottom left in oklab;
    background-image: linear-gradient(var(--tw-gradient-stops));
  }

  .bg-gradient-to-br {
    --tw-gradient-position: to bottom right in oklab;
    background-image: linear-gradient(var(--tw-gradient-stops));
  }

  .bg-gradient-to-l {
    --tw-gradient-position: to left in oklab;
    background-image: linear-gradient(var(--tw-gradient-stops));
  }

  .bg-gradient-to-r {
    --tw-gradient-position: to right in oklab;
    background-image: linear-gradient(var(--tw-gradient-stops));
  }

  .bg-gradient-to-t {
    --tw-gradient-position: to top in oklab;
    background-image: linear-gradient(var(--tw-gradient-stops));
  }

  .bg-gradient-to-tr {
    --tw-gradient-position: to top right in oklab;
    background-image: linear-gradient(var(--tw-gradient-stops));
  }

  .bg-\[url\(\'\/images\/hexagon\.svg\'\)\] {
    background-image: url("/images/hexagon.svg");
  }

  .bg-\[url\(\'data\:image\/svg\+xml\,\%3Csvg\%20width\%3D\%2260\%22\%20height\%3D\%2260\%22\%20viewBox\%3D\%220\%200\%2060\%2060\%22\%20xmlns\%3D\%22http\%3A\/\/www\.w3\.org\/2000\/svg\%22\%3E\%3Cg\%20fill\%3D\%22none\%22\%20fill-rule\%3D\%22evenodd\%22\%3E\%3Cg\%20fill\%3D\%22\%23ffffff\%22\%20fill-opacity\%3D\%220\.03\%22\%3E\%3Ccircle\%20cx\%3D\%2230\%22\%20cy\%3D\%2230\%22\%20r\%3D\%221\%22\/\%3E\%3C\/g\%3E\%3C\/g\%3E\%3C\/svg\%3E\'\)\] {
    background-image: url("data:image/svg+xml,%3Csvg%20width%3D%2260%22%20height%3D%2260%22%20viewBox%3D%220%200%2060%2060%22%20xmlns%3D%22http%3A//www.w3.org/2000/svg%22%3E%3Cg%20fill%3D%22none%22%20fill-rule%3D%22evenodd%22%3E%3Cg%20fill%3D%22%23ffffff%22%20fill-opacity%3D%220.03%22%3E%3Ccircle%20cx%3D%2230%22%20cy%3D%2230%22%20r%3D%221%22/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
  }

  .bg-\[url\(\'data\:image\/svg\+xml\,\%3Csvg\%20width\%3D\%2260\%22\%20height\%3D\%2260\%22\%20viewBox\%3D\%220\%200\%2060\%2060\%22\%20xmlns\%3D\%22http\%3A\/\/www\.w3\.org\/2000\/svg\%22\%3E\%3Cg\%20fill\%3D\%22none\%22\%20fill-rule\%3D\%22evenodd\%22\%3E\%3Cg\%20fill\%3D\%22\%23000000\%22\%20fill-opacity\%3D\%220\.03\%22\%3E\%3Ccircle\%20cx\%3D\%2230\%22\%20cy\%3D\%2230\%22\%20r\%3D\%221\%22\/\%3E\%3C\/g\%3E\%3C\/g\%3E\%3C\/svg\%3E\'\)\] {
    background-image: url("data:image/svg+xml,%3Csvg%20width%3D%2260%22%20height%3D%2260%22%20viewBox%3D%220%200%2060%2060%22%20xmlns%3D%22http%3A//www.w3.org/2000/svg%22%3E%3Cg%20fill%3D%22none%22%20fill-rule%3D%22evenodd%22%3E%3Cg%20fill%3D%22%23000000%22%20fill-opacity%3D%220.03%22%3E%3Ccircle%20cx%3D%2230%22%20cy%3D%2230%22%20r%3D%221%22/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
  }

  .from-\[var\(--bg-light-blue\)\] {
    --tw-gradient-from: var(--bg-light-blue);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-\[var\(--color--blue\)\] {
    --tw-gradient-from: var(--color--blue);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-\[var\(--color-blue\)\] {
    --tw-gradient-from: var(--color-blue);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-\[var\(--color-blue\)\]\/5 {
    --tw-gradient-from: var(--color-blue);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .from-\[var\(--color-blue\)\]\/5 {
      --tw-gradient-from: color-mix(in oklab, var(--color-blue) 5%, transparent);
    }
  }

  .from-\[var\(--color-blue\)\]\/5 {
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-\[var\(--color-blue\)\]\/10 {
    --tw-gradient-from: var(--color-blue);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .from-\[var\(--color-blue\)\]\/10 {
      --tw-gradient-from: color-mix(in oklab, var(--color-blue) 10%, transparent);
    }
  }

  .from-\[var\(--color-blue\)\]\/10 {
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-\[var\(--color-blue\)\]\/20 {
    --tw-gradient-from: var(--color-blue);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .from-\[var\(--color-blue\)\]\/20 {
      --tw-gradient-from: color-mix(in oklab, var(--color-blue) 20%, transparent);
    }
  }

  .from-\[var\(--color-blue\)\]\/20 {
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-\[var\(--color-brand-blue\)\] {
    --tw-gradient-from: var(--color-brand-blue);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-\[var\(--color-brand-blue\)\]\/20 {
    --tw-gradient-from: var(--color-brand-blue);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .from-\[var\(--color-brand-blue\)\]\/20 {
      --tw-gradient-from: color-mix(in oklab, var(--color-brand-blue) 20%, transparent);
    }
  }

  .from-\[var\(--color-brand-blue\)\]\/20 {
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-\[var\(--color-brand-light\)\]\/20 {
    --tw-gradient-from: var(--color-brand-light);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .from-\[var\(--color-brand-light\)\]\/20 {
      --tw-gradient-from: color-mix(in oklab, var(--color-brand-light) 20%, transparent);
    }
  }

  .from-\[var\(--color-brand-light\)\]\/20 {
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-\[var\(--color-brand-navy\)\] {
    --tw-gradient-from: var(--color-brand-navy);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-\[var\(--color-dark-blue\)\] {
    --tw-gradient-from: var(--color-dark-blue);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-\[var\(--color-dark-blue\)\]\/5 {
    --tw-gradient-from: var(--color-dark-blue);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .from-\[var\(--color-dark-blue\)\]\/5 {
      --tw-gradient-from: color-mix(in oklab, var(--color-dark-blue) 5%, transparent);
    }
  }

  .from-\[var\(--color-dark-blue\)\]\/5 {
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-\[var\(--color-dark-blue\)\]\/10 {
    --tw-gradient-from: var(--color-dark-blue);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .from-\[var\(--color-dark-blue\)\]\/10 {
      --tw-gradient-from: color-mix(in oklab, var(--color-dark-blue) 10%, transparent);
    }
  }

  .from-\[var\(--color-dark-blue\)\]\/10 {
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-\[var\(--color-yellow\)\] {
    --tw-gradient-from: var(--color-yellow);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-\[var\(--color-yellow\)\]\/10 {
    --tw-gradient-from: var(--color-yellow);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .from-\[var\(--color-yellow\)\]\/10 {
      --tw-gradient-from: color-mix(in oklab, var(--color-yellow) 10%, transparent);
    }
  }

  .from-\[var\(--color-yellow\)\]\/10 {
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-\[var\(--color-yellow\)\]\/20 {
    --tw-gradient-from: var(--color-yellow);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .from-\[var\(--color-yellow\)\]\/20 {
      --tw-gradient-from: color-mix(in oklab, var(--color-yellow) 20%, transparent);
    }
  }

  .from-\[var\(--color-yellow\)\]\/20 {
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-black\/20 {
    --tw-gradient-from: rgba(0, 0, 0, .2);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .from-black\/20 {
      --tw-gradient-from: color-mix(in oklab, var(--color-black) 20%, transparent);
    }
  }

  .from-black\/20 {
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-emerald-400\/0 {
    --tw-gradient-from: rgba(0, 0, 0, 0);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .from-emerald-400\/0 {
      --tw-gradient-from: color-mix(in oklab, var(--color-emerald-400) 0%, transparent);
    }
  }

  .from-emerald-400\/0 {
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-gray-50 {
    --tw-gradient-from: var(--color-gray-50);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-gray-100 {
    --tw-gradient-from: var(--color-gray-100);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-slate-50 {
    --tw-gradient-from: var(--color-slate-50);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-slate-900 {
    --tw-gradient-from: var(--color-slate-900);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-transparent {
    --tw-gradient-from: transparent;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-white\/8 {
    --tw-gradient-from: rgba(255, 255, 255, .08);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .from-white\/8 {
      --tw-gradient-from: color-mix(in oklab, var(--color-white) 8%, transparent);
    }
  }

  .from-white\/8 {
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-white\/10 {
    --tw-gradient-from: rgba(255, 255, 255, .1);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .from-white\/10 {
      --tw-gradient-from: color-mix(in oklab, var(--color-white) 10%, transparent);
    }
  }

  .from-white\/10 {
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-white\/30 {
    --tw-gradient-from: rgba(255, 255, 255, .3);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .from-white\/30 {
      --tw-gradient-from: color-mix(in oklab, var(--color-white) 30%, transparent);
    }
  }

  .from-white\/30 {
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-white\/80 {
    --tw-gradient-from: rgba(255, 255, 255, .8);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .from-white\/80 {
      --tw-gradient-from: color-mix(in oklab, var(--color-white) 80%, transparent);
    }
  }

  .from-white\/80 {
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .via-\[var\(--color-blue\)\] {
    --tw-gradient-via: var(--color-blue);
    --tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);
    --tw-gradient-stops: var(--tw-gradient-via-stops);
  }

  .via-\[var\(--color-blue-secondary\)\] {
    --tw-gradient-via: var(--color-blue-secondary);
    --tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);
    --tw-gradient-stops: var(--tw-gradient-via-stops);
  }

  .via-\[var\(--color-dark-blue\)\] {
    --tw-gradient-via: var(--color-dark-blue);
    --tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);
    --tw-gradient-stops: var(--tw-gradient-via-stops);
  }

  .via-\[var\(--color-dark-blue-hover\)\] {
    --tw-gradient-via: var(--color-dark-blue-hover);
    --tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);
    --tw-gradient-stops: var(--tw-gradient-via-stops);
  }

  .via-emerald-400\/90 {
    --tw-gradient-via: rgba(0, 210, 148, .9);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .via-emerald-400\/90 {
      --tw-gradient-via: color-mix(in oklab, var(--color-emerald-400) 90%, transparent);
    }
  }

  .via-emerald-400\/90 {
    --tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);
    --tw-gradient-stops: var(--tw-gradient-via-stops);
  }

  .via-gray-50\/80 {
    --tw-gradient-via: rgba(249, 250, 251, .8);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .via-gray-50\/80 {
      --tw-gradient-via: color-mix(in oklab, var(--color-gray-50) 80%, transparent);
    }
  }

  .via-gray-50\/80 {
    --tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);
    --tw-gradient-stops: var(--tw-gradient-via-stops);
  }

  .via-slate-800 {
    --tw-gradient-via: var(--color-slate-800);
    --tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);
    --tw-gradient-stops: var(--tw-gradient-via-stops);
  }

  .via-white {
    --tw-gradient-via: var(--color-white);
    --tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);
    --tw-gradient-stops: var(--tw-gradient-via-stops);
  }

  .to-\[var\(--color-blue\)\] {
    --tw-gradient-to: var(--color-blue);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-\[var\(--color-blue\)\]\/5 {
    --tw-gradient-to: var(--color-blue);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .to-\[var\(--color-blue\)\]\/5 {
      --tw-gradient-to: color-mix(in oklab, var(--color-blue) 5%, transparent);
    }
  }

  .to-\[var\(--color-blue\)\]\/5 {
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-\[var\(--color-blue\)\]\/10 {
    --tw-gradient-to: var(--color-blue);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .to-\[var\(--color-blue\)\]\/10 {
      --tw-gradient-to: color-mix(in oklab, var(--color-blue) 10%, transparent);
    }
  }

  .to-\[var\(--color-blue\)\]\/10 {
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-\[var\(--color-blue-secondary\)\] {
    --tw-gradient-to: var(--color-blue-secondary);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-\[var\(--color-blue-secondary\)\]\/5 {
    --tw-gradient-to: var(--color-blue-secondary);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .to-\[var\(--color-blue-secondary\)\]\/5 {
      --tw-gradient-to: color-mix(in oklab, var(--color-blue-secondary) 5%, transparent);
    }
  }

  .to-\[var\(--color-blue-secondary\)\]\/5 {
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-\[var\(--color-blue-secondary\)\]\/10 {
    --tw-gradient-to: var(--color-blue-secondary);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .to-\[var\(--color-blue-secondary\)\]\/10 {
      --tw-gradient-to: color-mix(in oklab, var(--color-blue-secondary) 10%, transparent);
    }
  }

  .to-\[var\(--color-blue-secondary\)\]\/10 {
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-\[var\(--color-blue-secondary\)\]\/20 {
    --tw-gradient-to: var(--color-blue-secondary);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .to-\[var\(--color-blue-secondary\)\]\/20 {
      --tw-gradient-to: color-mix(in oklab, var(--color-blue-secondary) 20%, transparent);
    }
  }

  .to-\[var\(--color-blue-secondary\)\]\/20 {
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-\[var\(--color-brand-blue-alt\)\] {
    --tw-gradient-to: var(--color-brand-blue-alt);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-\[var\(--color-brand-navy\)\] {
    --tw-gradient-to: var(--color-brand-navy);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-\[var\(--color-dark-blue\)\] {
    --tw-gradient-to: var(--color-dark-blue);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-\[var\(--color-dark-blue-bg\)\] {
    --tw-gradient-to: var(--color-dark-blue-bg);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-\[var\(--color-dark-blue-hover\)\] {
    --tw-gradient-to: var(--color-dark-blue-hover);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-\[var\(--color-dark-blue-hover\)\]\/10 {
    --tw-gradient-to: var(--color-dark-blue-hover);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .to-\[var\(--color-dark-blue-hover\)\]\/10 {
      --tw-gradient-to: color-mix(in oklab, var(--color-dark-blue-hover) 10%, transparent);
    }
  }

  .to-\[var\(--color-dark-blue-hover\)\]\/10 {
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-\[var\(--color-yellow\)\]\/10 {
    --tw-gradient-to: var(--color-yellow);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .to-\[var\(--color-yellow\)\]\/10 {
      --tw-gradient-to: color-mix(in oklab, var(--color-yellow) 10%, transparent);
    }
  }

  .to-\[var\(--color-yellow\)\]\/10 {
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-\[var\(--color-yellow-hover\)\] {
    --tw-gradient-to: var(--color-yellow-hover);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-\[var\(--color-yellow-hover\)\]\/10 {
    --tw-gradient-to: var(--color-yellow-hover);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .to-\[var\(--color-yellow-hover\)\]\/10 {
      --tw-gradient-to: color-mix(in oklab, var(--color-yellow-hover) 10%, transparent);
    }
  }

  .to-\[var\(--color-yellow-hover\)\]\/10 {
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-blue-50\/30 {
    --tw-gradient-to: rgba(239, 246, 255, .3);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .to-blue-50\/30 {
      --tw-gradient-to: color-mix(in oklab, var(--color-blue-50) 30%, transparent);
    }
  }

  .to-blue-50\/30 {
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-cyan-500 {
    --tw-gradient-to: var(--color-cyan-500);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-emerald-400\/0 {
    --tw-gradient-to: rgba(0, 0, 0, 0);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .to-emerald-400\/0 {
      --tw-gradient-to: color-mix(in oklab, var(--color-emerald-400) 0%, transparent);
    }
  }

  .to-emerald-400\/0 {
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-gray-100 {
    --tw-gradient-to: var(--color-gray-100);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-gray-100\/80 {
    --tw-gradient-to: rgba(243, 244, 246, .8);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .to-gray-100\/80 {
      --tw-gradient-to: color-mix(in oklab, var(--color-gray-100) 80%, transparent);
    }
  }

  .to-gray-100\/80 {
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-gray-200 {
    --tw-gradient-to: var(--color-gray-200);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-slate-900 {
    --tw-gradient-to: var(--color-slate-900);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-transparent {
    --tw-gradient-to: transparent;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-white {
    --tw-gradient-to: var(--color-white);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-white\/3 {
    --tw-gradient-to: rgba(255, 255, 255, .03);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .to-white\/3 {
      --tw-gradient-to: color-mix(in oklab, var(--color-white) 3%, transparent);
    }
  }

  .to-white\/3 {
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .\[mask-image\:linear-gradient\(0deg\,white\,rgba\(255\,255\,255\,0\.6\)\)\] {
    -webkit-mask-image: linear-gradient(0deg, #fff, rgba(255, 255, 255, .6));
    mask-image: linear-gradient(0deg, #fff, rgba(255, 255, 255, .6));
  }

  .bg-cover {
    background-size: cover;
  }

  .bg-clip-text {
    -webkit-background-clip: text;
    background-clip: text;
  }

  .bg-center {
    background-position: center;
  }

  .bg-no-repeat {
    background-repeat: no-repeat;
  }

  .fill-current {
    fill: currentColor;
  }

  .object-contain {
    object-fit: contain;
  }

  .object-cover {
    object-fit: cover;
  }

  .object-center {
    object-position: center;
  }

  .p-1 {
    padding: calc(var(--spacing) * 1);
  }

  .p-2 {
    padding: calc(var(--spacing) * 2);
  }

  .p-3 {
    padding: calc(var(--spacing) * 3);
  }

  .p-4 {
    padding: calc(var(--spacing) * 4);
  }

  .p-5 {
    padding: calc(var(--spacing) * 5);
  }

  .p-6 {
    padding: calc(var(--spacing) * 6);
  }

  .p-8 {
    padding: calc(var(--spacing) * 8);
  }

  .p-12 {
    padding: calc(var(--spacing) * 12);
  }

  .px-0 {
    padding-inline: calc(var(--spacing) * 0);
  }

  .px-2 {
    padding-inline: calc(var(--spacing) * 2);
  }

  .px-3 {
    padding-inline: calc(var(--spacing) * 3);
  }

  .px-4 {
    padding-inline: calc(var(--spacing) * 4);
  }

  .px-5 {
    padding-inline: calc(var(--spacing) * 5);
  }

  .px-6 {
    padding-inline: calc(var(--spacing) * 6);
  }

  .px-8 {
    padding-inline: calc(var(--spacing) * 8);
  }

  .px-12 {
    padding-inline: calc(var(--spacing) * 12);
  }

  .py-0 {
    padding-block: calc(var(--spacing) * 0);
  }

  .py-0\.5 {
    padding-block: calc(var(--spacing) * .5);
  }

  .py-1 {
    padding-block: calc(var(--spacing) * 1);
  }

  .py-1\.5 {
    padding-block: calc(var(--spacing) * 1.5);
  }

  .py-2 {
    padding-block: calc(var(--spacing) * 2);
  }

  .py-2\.5 {
    padding-block: calc(var(--spacing) * 2.5);
  }

  .py-3 {
    padding-block: calc(var(--spacing) * 3);
  }

  .py-4 {
    padding-block: calc(var(--spacing) * 4);
  }

  .py-6 {
    padding-block: calc(var(--spacing) * 6);
  }

  .py-8 {
    padding-block: calc(var(--spacing) * 8);
  }

  .py-12 {
    padding-block: calc(var(--spacing) * 12);
  }

  .py-16 {
    padding-block: calc(var(--spacing) * 16);
  }

  .py-20 {
    padding-block: calc(var(--spacing) * 20);
  }

  .py-24 {
    padding-block: calc(var(--spacing) * 24);
  }

  .pt-2 {
    padding-top: calc(var(--spacing) * 2);
  }

  .pt-3 {
    padding-top: calc(var(--spacing) * 3);
  }

  .pt-4 {
    padding-top: calc(var(--spacing) * 4);
  }

  .pt-6 {
    padding-top: calc(var(--spacing) * 6);
  }

  .pt-8 {
    padding-top: calc(var(--spacing) * 8);
  }

  .pt-16 {
    padding-top: calc(var(--spacing) * 16);
  }

  .pt-24 {
    padding-top: calc(var(--spacing) * 24);
  }

  .pt-28 {
    padding-top: calc(var(--spacing) * 28);
  }

  .pt-32 {
    padding-top: calc(var(--spacing) * 32);
  }

  .pr-3 {
    padding-right: calc(var(--spacing) * 3);
  }

  .pr-4 {
    padding-right: calc(var(--spacing) * 4);
  }

  .pr-5 {
    padding-right: calc(var(--spacing) * 5);
  }

  .pr-10 {
    padding-right: calc(var(--spacing) * 10);
  }

  .pr-12 {
    padding-right: calc(var(--spacing) * 12);
  }

  .pr-14 {
    padding-right: calc(var(--spacing) * 14);
  }

  .pb-2 {
    padding-bottom: calc(var(--spacing) * 2);
  }

  .pb-3 {
    padding-bottom: calc(var(--spacing) * 3);
  }

  .pb-4 {
    padding-bottom: calc(var(--spacing) * 4);
  }

  .pb-6 {
    padding-bottom: calc(var(--spacing) * 6);
  }

  .pb-8 {
    padding-bottom: calc(var(--spacing) * 8);
  }

  .pb-16 {
    padding-bottom: calc(var(--spacing) * 16);
  }

  .pb-20 {
    padding-bottom: calc(var(--spacing) * 20);
  }

  .pl-2 {
    padding-left: calc(var(--spacing) * 2);
  }

  .pl-6 {
    padding-left: calc(var(--spacing) * 6);
  }

  .pl-10 {
    padding-left: calc(var(--spacing) * 10);
  }

  .pl-12 {
    padding-left: calc(var(--spacing) * 12);
  }

  .text-center {
    text-align: center;
  }

  .text-left {
    text-align: left;
  }

  .text-right {
    text-align: right;
  }

  .font-sans {
    font-family: var(--font-sans);
  }

  .text-2xl {
    font-size: var(--text-2xl);
    line-height: var(--tw-leading, var(--text-2xl--line-height));
  }

  .text-3xl {
    font-size: var(--text-3xl);
    line-height: var(--tw-leading, var(--text-3xl--line-height));
  }

  .text-4xl {
    font-size: var(--text-4xl);
    line-height: var(--tw-leading, var(--text-4xl--line-height));
  }

  .text-5xl {
    font-size: var(--text-5xl);
    line-height: var(--tw-leading, var(--text-5xl--line-height));
  }

  .text-base {
    font-size: var(--text-base);
    line-height: var(--tw-leading, var(--text-base--line-height));
  }

  .text-lg {
    font-size: var(--text-lg);
    line-height: var(--tw-leading, var(--text-lg--line-height));
  }

  .text-sm {
    font-size: var(--text-sm);
    line-height: var(--tw-leading, var(--text-sm--line-height));
  }

  .text-xl {
    font-size: var(--text-xl);
    line-height: var(--tw-leading, var(--text-xl--line-height));
  }

  .text-xs {
    font-size: var(--text-xs);
    line-height: var(--tw-leading, var(--text-xs--line-height));
  }

  .leading-6 {
    --tw-leading: calc(var(--spacing) * 6);
    line-height: calc(var(--spacing) * 6);
  }

  .leading-relaxed {
    --tw-leading: var(--leading-relaxed);
    line-height: var(--leading-relaxed);
  }

  .leading-snug {
    --tw-leading: var(--leading-snug);
    line-height: var(--leading-snug);
  }

  .leading-tight {
    --tw-leading: var(--leading-tight);
    line-height: var(--leading-tight);
  }

  .font-bold {
    --tw-font-weight: var(--font-weight-bold);
    font-weight: var(--font-weight-bold);
  }

  .font-medium {
    --tw-font-weight: var(--font-weight-medium);
    font-weight: var(--font-weight-medium);
  }

  .font-normal {
    --tw-font-weight: var(--font-weight-normal);
    font-weight: var(--font-weight-normal);
  }

  .font-semibold {
    --tw-font-weight: var(--font-weight-semibold);
    font-weight: var(--font-weight-semibold);
  }

  .tracking-wide {
    --tw-tracking: var(--tracking-wide);
    letter-spacing: var(--tracking-wide);
  }

  .break-all {
    word-break: break-all;
  }

  .text-\[var\(--color-blue\)\] {
    color: var(--color-blue);
  }

  .text-\[var\(--color-blue-secondary\)\] {
    color: var(--color-blue-secondary);
  }

  .text-\[var\(--color-brand-navy\)\] {
    color: var(--color-brand-navy);
  }

  .text-\[var\(--color-dark-blue\)\] {
    color: var(--color-dark-blue);
  }

  .text-\[var\(--color-dark-blue\)\]\/70 {
    color: var(--color-dark-blue);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .text-\[var\(--color-dark-blue\)\]\/70 {
      color: color-mix(in oklab, var(--color-dark-blue) 70%, transparent);
    }
  }

  .text-\[var\(--color-gray\)\] {
    color: var(--color-gray);
  }

  .text-\[var\(--color-white\)\] {
    color: var(--color-white);
  }

  .text-\[var\(--color-yellow\)\] {
    color: var(--color-yellow);
  }

  .text-\[var\(--foreground-secondary\)\] {
    color: var(--foreground-secondary);
  }

  .text-blue-100 {
    color: var(--color-blue-100);
  }

  .text-blue-400 {
    color: var(--color-blue-400);
  }

  .text-blue-600 {
    color: var(--color-blue-600);
  }

  .text-current {
    color: currentColor;
  }

  .text-gray-200 {
    color: var(--color-gray-200);
  }

  .text-gray-300 {
    color: var(--color-gray-300);
  }

  .text-gray-400 {
    color: var(--color-gray-400);
  }

  .text-gray-500 {
    color: var(--color-gray-500);
  }

  .text-gray-600 {
    color: var(--color-gray-600);
  }

  .text-gray-700 {
    color: var(--color-gray-700);
  }

  .text-gray-800 {
    color: var(--color-gray-800);
  }

  .text-gray-900 {
    color: var(--color-gray-900);
  }

  .text-green-400 {
    color: var(--color-green-400);
  }

  .text-green-500 {
    color: var(--color-green-500);
  }

  .text-green-600 {
    color: var(--color-green-600);
  }

  .text-green-700 {
    color: var(--color-green-700);
  }

  .text-green-800 {
    color: var(--color-green-800);
  }

  .text-neutral-600 {
    color: var(--color-neutral-600);
  }

  .text-orange-500 {
    color: var(--color-orange-500);
  }

  .text-red-500 {
    color: var(--color-red-500);
  }

  .text-red-700 {
    color: var(--color-red-700);
  }

  .text-red-800 {
    color: var(--color-red-800);
  }

  .text-slate-300 {
    color: var(--color-slate-300);
  }

  .text-slate-600 {
    color: var(--color-slate-600);
  }

  .text-slate-800 {
    color: var(--color-slate-800);
  }

  .text-transparent {
    color: rgba(0, 0, 0, 0);
  }

  .text-white {
    color: var(--color-white);
  }

  .text-white\/50 {
    color: rgba(255, 255, 255, .5);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .text-white\/50 {
      color: color-mix(in oklab, var(--color-white) 50%, transparent);
    }
  }

  .text-white\/60 {
    color: rgba(255, 255, 255, .6);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .text-white\/60 {
      color: color-mix(in oklab, var(--color-white) 60%, transparent);
    }
  }

  .text-white\/70 {
    color: rgba(255, 255, 255, .7);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .text-white\/70 {
      color: color-mix(in oklab, var(--color-white) 70%, transparent);
    }
  }

  .text-white\/80 {
    color: rgba(255, 255, 255, .8);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .text-white\/80 {
      color: color-mix(in oklab, var(--color-white) 80%, transparent);
    }
  }

  .text-white\/90 {
    color: rgba(255, 255, 255, .9);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .text-white\/90 {
      color: color-mix(in oklab, var(--color-white) 90%, transparent);
    }
  }

  .text-yellow-400 {
    color: var(--color-yellow-400);
  }

  .text-yellow-500 {
    color: var(--color-yellow-500);
  }

  .capitalize {
    text-transform: capitalize;
  }

  .uppercase {
    text-transform: uppercase;
  }

  .italic {
    font-style: italic;
  }

  .underline {
    -webkit-text-decoration-line: underline;
    text-decoration-line: underline;
  }

  .decoration-2 {
    text-decoration-thickness: 2px;
  }

  .underline-offset-2 {
    text-underline-offset: 2px;
  }

  .antialiased {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  .placeholder-gray-400::placeholder {
    color: var(--color-gray-400);
  }

  .placeholder-white\/60::placeholder {
    color: rgba(255, 255, 255, .6);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .placeholder-white\/60::placeholder {
      color: color-mix(in oklab, var(--color-white) 60%, transparent);
    }
  }

  .opacity-0 {
    opacity: 0;
  }

  .opacity-5 {
    opacity: .05;
  }

  .opacity-10 {
    opacity: .1;
  }

  .opacity-20 {
    opacity: .2;
  }

  .opacity-30 {
    opacity: .3;
  }

  .opacity-50 {
    opacity: .5;
  }

  .opacity-60 {
    opacity: .6;
  }

  .opacity-70 {
    opacity: .7;
  }

  .opacity-80 {
    opacity: .8;
  }

  .opacity-90 {
    opacity: .9;
  }

  .opacity-100 {
    opacity: 1;
  }

  .mix-blend-multiply {
    mix-blend-mode: multiply;
  }

  .shadow-\[var\(--color-blue\)\]\/10 {
    --tw-shadow-alpha: 10%;
    --tw-shadow: var(--color-blue);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .shadow-2xl {
    --tw-shadow: 0 25px 50px -12px var(--tw-shadow-color, rgba(0, 0, 0, .25));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .shadow-\[0_8px_16px_rgb\(0_0_0\/0\.4\)\] {
    --tw-shadow: 0 8px 16px var(--tw-shadow-color, rgba(0, 0, 0, .4));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .shadow-\[0_8px_30px_rgb\(0\,0\,0\,0\.08\)\] {
    --tw-shadow: 0 8px 30px var(--tw-shadow-color, rgba(0, 0, 0, .08));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .shadow-\[0_20px_40px_rgb\(0\,0\,0\,0\.08\)\] {
    --tw-shadow: 0 20px 40px var(--tw-shadow-color, rgba(0, 0, 0, .08));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .shadow-lg {
    --tw-shadow: 0 10px 15px -3px var(--tw-shadow-color, rgba(0, 0, 0, .1)), 0 4px 6px -4px var(--tw-shadow-color, rgba(0, 0, 0, .1));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .shadow-md {
    --tw-shadow: 0 4px 6px -1px var(--tw-shadow-color, rgba(0, 0, 0, .1)), 0 2px 4px -2px var(--tw-shadow-color, rgba(0, 0, 0, .1));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .shadow-sm {
    --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, rgba(0, 0, 0, .1)), 0 1px 2px -1px var(--tw-shadow-color, rgba(0, 0, 0, .1));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .shadow-xl {
    --tw-shadow: 0 20px 25px -5px var(--tw-shadow-color, rgba(0, 0, 0, .1)), 0 8px 10px -6px var(--tw-shadow-color, rgba(0, 0, 0, .1));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .ring-1 {
    --tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .ring-white\/10 {
    --tw-ring-color: rgba(255, 255, 255, .1);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .ring-white\/10 {
      --tw-ring-color: color-mix(in oklab, var(--color-white) 10%, transparent);
    }
  }

  .blur {
    --tw-blur: blur(8px);
    filter: var(--tw-blur, ) var(--tw-brightness, ) var(--tw-contrast, ) var(--tw-grayscale, ) var(--tw-hue-rotate, ) var(--tw-invert, ) var(--tw-saturate, ) var(--tw-sepia, ) var(--tw-drop-shadow, );
  }

  .blur-3xl {
    --tw-blur: blur(var(--blur-3xl));
    filter: var(--tw-blur, ) var(--tw-brightness, ) var(--tw-contrast, ) var(--tw-grayscale, ) var(--tw-hue-rotate, ) var(--tw-invert, ) var(--tw-saturate, ) var(--tw-sepia, ) var(--tw-drop-shadow, );
  }

  .blur-\[2px\] {
    --tw-blur: blur(2px);
    filter: var(--tw-blur, ) var(--tw-brightness, ) var(--tw-contrast, ) var(--tw-grayscale, ) var(--tw-hue-rotate, ) var(--tw-invert, ) var(--tw-saturate, ) var(--tw-sepia, ) var(--tw-drop-shadow, );
  }

  .blur-\[3px\] {
    --tw-blur: blur(3px);
    filter: var(--tw-blur, ) var(--tw-brightness, ) var(--tw-contrast, ) var(--tw-grayscale, ) var(--tw-hue-rotate, ) var(--tw-invert, ) var(--tw-saturate, ) var(--tw-sepia, ) var(--tw-drop-shadow, );
  }

  .blur-xl {
    --tw-blur: blur(var(--blur-xl));
    filter: var(--tw-blur, ) var(--tw-brightness, ) var(--tw-contrast, ) var(--tw-grayscale, ) var(--tw-hue-rotate, ) var(--tw-invert, ) var(--tw-saturate, ) var(--tw-sepia, ) var(--tw-drop-shadow, );
  }

  .drop-shadow-lg {
    --tw-drop-shadow-size: drop-shadow(0 4px 4px var(--tw-drop-shadow-color, rgba(0, 0, 0, .15)));
    --tw-drop-shadow: drop-shadow(var(--drop-shadow-lg));
    filter: var(--tw-blur, ) var(--tw-brightness, ) var(--tw-contrast, ) var(--tw-grayscale, ) var(--tw-hue-rotate, ) var(--tw-invert, ) var(--tw-saturate, ) var(--tw-sepia, ) var(--tw-drop-shadow, );
  }

  .grayscale {
    --tw-grayscale: grayscale(100%);
    filter: var(--tw-blur, ) var(--tw-brightness, ) var(--tw-contrast, ) var(--tw-grayscale, ) var(--tw-hue-rotate, ) var(--tw-invert, ) var(--tw-saturate, ) var(--tw-sepia, ) var(--tw-drop-shadow, );
  }

  .filter {
    filter: var(--tw-blur, ) var(--tw-brightness, ) var(--tw-contrast, ) var(--tw-grayscale, ) var(--tw-hue-rotate, ) var(--tw-invert, ) var(--tw-saturate, ) var(--tw-sepia, ) var(--tw-drop-shadow, );
  }

  .backdrop-blur-sm {
    --tw-backdrop-blur: blur(var(--blur-sm));
    -webkit-backdrop-filter: var(--tw-backdrop-blur, ) var(--tw-backdrop-brightness, ) var(--tw-backdrop-contrast, ) var(--tw-backdrop-grayscale, ) var(--tw-backdrop-hue-rotate, ) var(--tw-backdrop-invert, ) var(--tw-backdrop-opacity, ) var(--tw-backdrop-saturate, ) var(--tw-backdrop-sepia, );
    backdrop-filter: var(--tw-backdrop-blur, ) var(--tw-backdrop-brightness, ) var(--tw-backdrop-contrast, ) var(--tw-backdrop-grayscale, ) var(--tw-backdrop-hue-rotate, ) var(--tw-backdrop-invert, ) var(--tw-backdrop-opacity, ) var(--tw-backdrop-saturate, ) var(--tw-backdrop-sepia, );
  }

  .backdrop-blur-xl {
    --tw-backdrop-blur: blur(var(--blur-xl));
    -webkit-backdrop-filter: var(--tw-backdrop-blur, ) var(--tw-backdrop-brightness, ) var(--tw-backdrop-contrast, ) var(--tw-backdrop-grayscale, ) var(--tw-backdrop-hue-rotate, ) var(--tw-backdrop-invert, ) var(--tw-backdrop-opacity, ) var(--tw-backdrop-saturate, ) var(--tw-backdrop-sepia, );
    backdrop-filter: var(--tw-backdrop-blur, ) var(--tw-backdrop-brightness, ) var(--tw-backdrop-contrast, ) var(--tw-backdrop-grayscale, ) var(--tw-backdrop-hue-rotate, ) var(--tw-backdrop-invert, ) var(--tw-backdrop-opacity, ) var(--tw-backdrop-saturate, ) var(--tw-backdrop-sepia, );
  }

  .transition {
    transition-property: color, background-color, border-color, outline-color, -webkit-text-decoration-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to, opacity, box-shadow, transform, translate, scale, rotate, filter, -webkit-backdrop-filter, backdrop-filter, display, visibility, content-visibility, overlay, pointer-events;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .transition-all {
    transition-property: all;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .transition-colors {
    transition-property: color, background-color, border-color, outline-color, -webkit-text-decoration-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .transition-opacity {
    transition-property: opacity;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .transition-shadow {
    transition-property: box-shadow;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .transition-transform {
    transition-property: transform, translate, scale, rotate;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .duration-150 {
    --tw-duration: .15s;
    transition-duration: .15s;
  }

  .duration-200 {
    --tw-duration: .2s;
    transition-duration: .2s;
  }

  .duration-300 {
    --tw-duration: .3s;
    transition-duration: .3s;
  }

  .duration-500 {
    --tw-duration: .5s;
    transition-duration: .5s;
  }

  .duration-700 {
    --tw-duration: .7s;
    transition-duration: .7s;
  }

  .ease-in-out {
    --tw-ease: var(--ease-in-out);
    transition-timing-function: var(--ease-in-out);
  }

  .ease-out {
    --tw-ease: var(--ease-out);
    transition-timing-function: var(--ease-out);
  }

  .outline-none {
    --tw-outline-style: none;
    outline-style: none;
  }

  @media (hover: hover) {
    .group-hover\:translate-x-1:is(:where(.group):hover *) {
      --tw-translate-x: calc(var(--spacing) * 1);
      translate: var(--tw-translate-x) var(--tw-translate-y);
    }
  }

  @media (hover: hover) {
    .group-hover\:translate-x-2:is(:where(.group):hover *) {
      --tw-translate-x: calc(var(--spacing) * 2);
      translate: var(--tw-translate-x) var(--tw-translate-y);
    }
  }

  @media (hover: hover) {
    .group-hover\:scale-105:is(:where(.group):hover *) {
      --tw-scale-x: 105%;
      --tw-scale-y: 105%;
      --tw-scale-z: 105%;
      scale: var(--tw-scale-x) var(--tw-scale-y);
    }
  }

  @media (hover: hover) {
    .group-hover\:scale-110:is(:where(.group):hover *) {
      --tw-scale-x: 110%;
      --tw-scale-y: 110%;
      --tw-scale-z: 110%;
      scale: var(--tw-scale-x) var(--tw-scale-y);
    }
  }

  @media (hover: hover) {
    .group-hover\:border-\[var\(--color-blue\)\]\/20:is(:where(.group):hover *) {
      border-color: var(--color-blue);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .group-hover\:border-\[var\(--color-blue\)\]\/20:is(:where(.group):hover *) {
        border-color: color-mix(in oklab, var(--color-blue) 20%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .group-hover\:border-\[var\(--color-brand-blue\)\]\/50:is(:where(.group):hover *) {
      border-color: var(--color-brand-blue);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .group-hover\:border-\[var\(--color-brand-blue\)\]\/50:is(:where(.group):hover *) {
        border-color: color-mix(in oklab, var(--color-brand-blue) 50%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .group-hover\:text-\[var\(--color-blue\)\]:is(:where(.group):hover *) {
      color: var(--color-blue);
    }
  }

  @media (hover: hover) {
    .group-hover\:opacity-5:is(:where(.group):hover *) {
      opacity: .05;
    }
  }

  @media (hover: hover) {
    .group-hover\:opacity-100:is(:where(.group):hover *) {
      opacity: 1;
    }
  }

  @media (hover: hover) {
    .group-hover\:shadow-2xl:is(:where(.group):hover *) {
      --tw-shadow: 0 25px 50px -12px var(--tw-shadow-color, rgba(0, 0, 0, .25));
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }

  @media (hover: hover) {
    .group-hover\/bento\:translate-x-2:is(:where(.group\/bento):hover *) {
      --tw-translate-x: calc(var(--spacing) * 2);
      translate: var(--tw-translate-x) var(--tw-translate-y);
    }
  }

  @media (hover: hover) {
    .group-hover\/btn\:translate-x-1:is(:where(.group\/btn):hover *) {
      --tw-translate-x: calc(var(--spacing) * 1);
      translate: var(--tw-translate-x) var(--tw-translate-y);
    }
  }

  @media (hover: hover) {
    .group-hover\/btn\:opacity-40:is(:where(.group\/btn):hover *) {
      opacity: .4;
    }
  }

  @media (hover: hover) {
    .group-hover\/link\:translate-x-1:is(:where(.group\/link):hover *) {
      --tw-translate-x: calc(var(--spacing) * 1);
      translate: var(--tw-translate-x) var(--tw-translate-y);
    }
  }

  @media (hover: hover) {
    .group-hover\/pin\:h-40:is(:where(.group\/pin):hover *) {
      height: calc(var(--spacing) * 40);
    }
  }

  @media (hover: hover) {
    .group-hover\/pin\:border-white\/\[0\.2\]:is(:where(.group\/pin):hover *) {
      border-color: rgba(255, 255, 255, .2);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .group-hover\/pin\:border-white\/\[0\.2\]:is(:where(.group\/pin):hover *) {
        border-color: color-mix(in oklab, var(--color-white) 20%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .group-hover\/pin\:opacity-100:is(:where(.group\/pin):hover *) {
      opacity: 1;
    }
  }

  .focus-within\:ring-2:focus-within {
    --tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .focus-within\:ring-\[var\(--color-blue\)\]:focus-within {
    --tw-ring-color: var(--color-blue);
  }

  @media (hover: hover) {
    .hover\:-translate-y-0\.5:hover {
      --tw-translate-y: calc(var(--spacing) * -.5);
      translate: var(--tw-translate-x) var(--tw-translate-y);
    }
  }

  @media (hover: hover) {
    .hover\:-translate-y-1:hover {
      --tw-translate-y: calc(var(--spacing) * -1);
      translate: var(--tw-translate-x) var(--tw-translate-y);
    }
  }

  @media (hover: hover) {
    .hover\:-translate-y-2:hover {
      --tw-translate-y: calc(var(--spacing) * -2);
      translate: var(--tw-translate-x) var(--tw-translate-y);
    }
  }

  @media (hover: hover) {
    .hover\:scale-105:hover {
      --tw-scale-x: 105%;
      --tw-scale-y: 105%;
      --tw-scale-z: 105%;
      scale: var(--tw-scale-x) var(--tw-scale-y);
    }
  }

  @media (hover: hover) {
    .hover\:scale-110:hover {
      --tw-scale-x: 110%;
      --tw-scale-y: 110%;
      --tw-scale-z: 110%;
      scale: var(--tw-scale-x) var(--tw-scale-y);
    }
  }

  @media (hover: hover) {
    .hover\:border-\[var\(--color-blue\)\]:hover {
      border-color: var(--color-blue);
    }
  }

  @media (hover: hover) {
    .hover\:border-\[var\(--color-blue\)\]\/30:hover {
      border-color: var(--color-blue);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:border-\[var\(--color-blue\)\]\/30:hover {
        border-color: color-mix(in oklab, var(--color-blue) 30%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:border-\[var\(--color-blue\)\]\/50:hover {
      border-color: var(--color-blue);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:border-\[var\(--color-blue\)\]\/50:hover {
        border-color: color-mix(in oklab, var(--color-blue) 50%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:border-\[var\(--color-blue\)\]\/60:hover {
      border-color: var(--color-blue);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:border-\[var\(--color-blue\)\]\/60:hover {
        border-color: color-mix(in oklab, var(--color-blue) 60%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:border-blue-300:hover {
      border-color: var(--color-blue-300);
    }
  }

  @media (hover: hover) {
    .hover\:border-white:hover {
      border-color: var(--color-white);
    }
  }

  @media (hover: hover) {
    .hover\:border-white\/20:hover {
      border-color: rgba(255, 255, 255, .2);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:border-white\/20:hover {
        border-color: color-mix(in oklab, var(--color-white) 20%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:bg-\[var\(--color-blue\)\]:hover {
      background-color: var(--color-blue);
    }
  }

  @media (hover: hover) {
    .hover\:bg-\[var\(--color-blue\)\]\/5:hover {
      background-color: var(--color-blue);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:bg-\[var\(--color-blue\)\]\/5:hover {
        background-color: color-mix(in oklab, var(--color-blue) 5%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:bg-\[var\(--color-blue-secondary\)\]:hover {
      background-color: var(--color-blue-secondary);
    }
  }

  @media (hover: hover) {
    .hover\:bg-\[var\(--color-dark-blue\)\]:hover {
      background-color: var(--color-dark-blue);
    }
  }

  @media (hover: hover) {
    .hover\:bg-\[var\(--color-yellow-hover\)\]:hover {
      background-color: var(--color-yellow-hover);
    }
  }

  @media (hover: hover) {
    .hover\:bg-blue-50:hover {
      background-color: var(--color-blue-50);
    }
  }

  @media (hover: hover) {
    .hover\:bg-blue-700:hover {
      background-color: var(--color-blue-700);
    }
  }

  @media (hover: hover) {
    .hover\:bg-gray-50:hover {
      background-color: var(--color-gray-50);
    }
  }

  @media (hover: hover) {
    .hover\:bg-gray-100:hover {
      background-color: var(--color-gray-100);
    }
  }

  @media (hover: hover) {
    .hover\:bg-gray-200:hover {
      background-color: var(--color-gray-200);
    }
  }

  @media (hover: hover) {
    .hover\:bg-gray-500:hover {
      background-color: var(--color-gray-500);
    }
  }

  @media (hover: hover) {
    .hover\:bg-gray-700:hover {
      background-color: var(--color-gray-700);
    }
  }

  @media (hover: hover) {
    .hover\:bg-gray-800\/70:hover {
      background-color: rgba(30, 41, 57, .7);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:bg-gray-800\/70:hover {
        background-color: color-mix(in oklab, var(--color-gray-800) 70%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:bg-white:hover {
      background-color: var(--color-white);
    }
  }

  @media (hover: hover) {
    .hover\:bg-white\/10:hover {
      background-color: rgba(255, 255, 255, .1);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:bg-white\/10:hover {
        background-color: color-mix(in oklab, var(--color-white) 10%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:bg-white\/15:hover {
      background-color: rgba(255, 255, 255, .15);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:bg-white\/15:hover {
        background-color: color-mix(in oklab, var(--color-white) 15%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:bg-white\/20:hover {
      background-color: rgba(255, 255, 255, .2);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:bg-white\/20:hover {
        background-color: color-mix(in oklab, var(--color-white) 20%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:bg-white\/30:hover {
      background-color: rgba(255, 255, 255, .3);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:bg-white\/30:hover {
        background-color: color-mix(in oklab, var(--color-white) 30%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:bg-white\/50:hover {
      background-color: rgba(255, 255, 255, .5);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:bg-white\/50:hover {
        background-color: color-mix(in oklab, var(--color-white) 50%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:bg-yellow-400:hover {
      background-color: var(--color-yellow-400);
    }
  }

  @media (hover: hover) {
    .hover\:from-\[var\(--color-blue-secondary\)\]:hover {
      --tw-gradient-from: var(--color-blue-secondary);
      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    }
  }

  @media (hover: hover) {
    .hover\:from-\[var\(--color-yellow-hover\)\]:hover {
      --tw-gradient-from: var(--color-yellow-hover);
      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    }
  }

  @media (hover: hover) {
    .hover\:to-\[var\(--color-blue\)\]:hover {
      --tw-gradient-to: var(--color-blue);
      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    }
  }

  @media (hover: hover) {
    .hover\:to-\[var\(--color-yellow\)\]:hover {
      --tw-gradient-to: var(--color-yellow);
      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    }
  }

  @media (hover: hover) {
    .hover\:text-\[var\(--color-blue\)\]:hover {
      color: var(--color-blue);
    }
  }

  @media (hover: hover) {
    .hover\:text-\[var\(--color-blue-secondary\)\]:hover {
      color: var(--color-blue-secondary);
    }
  }

  @media (hover: hover) {
    .hover\:text-\[var\(--color-dark-blue\)\]:hover {
      color: var(--color-dark-blue);
    }
  }

  @media (hover: hover) {
    .hover\:text-\[var\(--color-yellow\)\]:hover {
      color: var(--color-yellow);
    }
  }

  @media (hover: hover) {
    .hover\:text-blue-800:hover {
      color: var(--color-blue-800);
    }
  }

  @media (hover: hover) {
    .hover\:text-gray-600:hover {
      color: var(--color-gray-600);
    }
  }

  @media (hover: hover) {
    .hover\:text-gray-700:hover {
      color: var(--color-gray-700);
    }
  }

  @media (hover: hover) {
    .hover\:text-gray-800:hover {
      color: var(--color-gray-800);
    }
  }

  @media (hover: hover) {
    .hover\:text-white:hover {
      color: var(--color-white);
    }
  }

  @media (hover: hover) {
    .hover\:text-white\/80:hover {
      color: rgba(255, 255, 255, .8);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:text-white\/80:hover {
        color: color-mix(in oklab, var(--color-white) 80%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:underline:hover {
      -webkit-text-decoration-line: underline;
      text-decoration-line: underline;
    }
  }

  @media (hover: hover) {
    .hover\:shadow-2xl:hover {
      --tw-shadow: 0 25px 50px -12px var(--tw-shadow-color, rgba(0, 0, 0, .25));
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }

  @media (hover: hover) {
    .hover\:shadow-\[0_20px_40px_rgb\(0\,0\,0\,0\.12\)\]:hover {
      --tw-shadow: 0 20px 40px var(--tw-shadow-color, rgba(0, 0, 0, .12));
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }

  @media (hover: hover) {
    .hover\:shadow-\[0_30px_60px_rgb\(0\,0\,0\,0\.12\)\]:hover {
      --tw-shadow: 0 30px 60px var(--tw-shadow-color, rgba(0, 0, 0, .12));
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }

  @media (hover: hover) {
    .hover\:shadow-lg:hover {
      --tw-shadow: 0 10px 15px -3px var(--tw-shadow-color, rgba(0, 0, 0, .1)), 0 4px 6px -4px var(--tw-shadow-color, rgba(0, 0, 0, .1));
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }

  @media (hover: hover) {
    .hover\:shadow-md:hover {
      --tw-shadow: 0 4px 6px -1px var(--tw-shadow-color, rgba(0, 0, 0, .1)), 0 2px 4px -2px var(--tw-shadow-color, rgba(0, 0, 0, .1));
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }

  @media (hover: hover) {
    .hover\:shadow-xl:hover {
      --tw-shadow: 0 20px 25px -5px var(--tw-shadow-color, rgba(0, 0, 0, .1)), 0 8px 10px -6px var(--tw-shadow-color, rgba(0, 0, 0, .1));
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }

  @media (hover: hover) {
    .hover\:grayscale-0:hover {
      --tw-grayscale: grayscale(0%);
      filter: var(--tw-blur, ) var(--tw-brightness, ) var(--tw-contrast, ) var(--tw-grayscale, ) var(--tw-hue-rotate, ) var(--tw-invert, ) var(--tw-saturate, ) var(--tw-sepia, ) var(--tw-drop-shadow, );
    }
  }

  .focus\:border-\[var\(--color-blue\)\]:focus {
    border-color: var(--color-blue);
  }

  .focus\:border-transparent:focus {
    border-color: rgba(0, 0, 0, 0);
  }

  .focus\:bg-white:focus {
    background-color: var(--color-white);
  }

  .focus\:underline:focus {
    -webkit-text-decoration-line: underline;
    text-decoration-line: underline;
  }

  .focus\:shadow-xl:focus {
    --tw-shadow: 0 20px 25px -5px var(--tw-shadow-color, rgba(0, 0, 0, .1)), 0 8px 10px -6px var(--tw-shadow-color, rgba(0, 0, 0, .1));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .focus\:ring-2:focus {
    --tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .focus\:ring-4:focus {
    --tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(4px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .focus\:ring-\[var\(--color-blue\)\]:focus {
    --tw-ring-color: var(--color-blue);
  }

  .focus\:ring-\[var\(--color-blue\)\]\/10:focus {
    --tw-ring-color: var(--color-blue);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .focus\:ring-\[var\(--color-blue\)\]\/10:focus {
      --tw-ring-color: color-mix(in oklab, var(--color-blue) 10%, transparent);
    }
  }

  .focus\:ring-\[var\(--color-blue\)\]\/20:focus {
    --tw-ring-color: var(--color-blue);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .focus\:ring-\[var\(--color-blue\)\]\/20:focus {
      --tw-ring-color: color-mix(in oklab, var(--color-blue) 20%, transparent);
    }
  }

  .focus\:ring-\[var\(--color-blue\)\]\/25:focus {
    --tw-ring-color: var(--color-blue);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .focus\:ring-\[var\(--color-blue\)\]\/25:focus {
      --tw-ring-color: color-mix(in oklab, var(--color-blue) 25%, transparent);
    }
  }

  .focus\:outline-none:focus {
    --tw-outline-style: none;
    outline-style: none;
  }

  .disabled\:bg-gray-400:disabled {
    background-color: var(--color-gray-400);
  }

  .disabled\:from-gray-400:disabled {
    --tw-gradient-from: var(--color-gray-400);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .disabled\:to-gray-500:disabled {
    --tw-gradient-to: var(--color-gray-500);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @media (min-width: 40rem) {
    .sm\:top-4 {
      top: calc(var(--spacing) * 4);
    }
  }

  @media (min-width: 40rem) {
    .sm\:right-4 {
      right: calc(var(--spacing) * 4);
    }
  }

  @media (min-width: 40rem) {
    .sm\:left-4 {
      left: calc(var(--spacing) * 4);
    }
  }

  @media (min-width: 40rem) {
    .sm\:mx-0 {
      margin-inline: calc(var(--spacing) * 0);
    }
  }

  @media (min-width: 40rem) {
    .sm\:mx-8 {
      margin-inline: calc(var(--spacing) * 8);
    }
  }

  @media (min-width: 40rem) {
    .sm\:-mt-16 {
      margin-top: calc(var(--spacing) * -16);
    }
  }

  @media (min-width: 40rem) {
    .sm\:mt-5 {
      margin-top: calc(var(--spacing) * 5);
    }
  }

  @media (min-width: 40rem) {
    .sm\:mt-10 {
      margin-top: calc(var(--spacing) * 10);
    }
  }

  @media (min-width: 40rem) {
    .sm\:mt-26 {
      margin-top: calc(var(--spacing) * 26);
    }
  }

  @media (min-width: 40rem) {
    .sm\:mt-28 {
      margin-top: calc(var(--spacing) * 28);
    }
  }

  @media (min-width: 40rem) {
    .sm\:mb-0 {
      margin-bottom: calc(var(--spacing) * 0);
    }
  }

  @media (min-width: 40rem) {
    .sm\:mb-4 {
      margin-bottom: calc(var(--spacing) * 4);
    }
  }

  @media (min-width: 40rem) {
    .sm\:mb-5 {
      margin-bottom: calc(var(--spacing) * 5);
    }
  }

  @media (min-width: 40rem) {
    .sm\:mb-6 {
      margin-bottom: calc(var(--spacing) * 6);
    }
  }

  @media (min-width: 40rem) {
    .sm\:mb-8 {
      margin-bottom: calc(var(--spacing) * 8);
    }
  }

  @media (min-width: 40rem) {
    .sm\:mb-12 {
      margin-bottom: calc(var(--spacing) * 12);
    }
  }

  @media (min-width: 40rem) {
    .sm\:mb-16 {
      margin-bottom: calc(var(--spacing) * 16);
    }
  }

  @media (min-width: 40rem) {
    .sm\:mb-20 {
      margin-bottom: calc(var(--spacing) * 20);
    }
  }

  @media (min-width: 40rem) {
    .sm\:hidden {
      display: none;
    }
  }

  @media (min-width: 40rem) {
    .sm\:inline {
      display: inline;
    }
  }

  @media (min-width: 40rem) {
    .sm\:h-5 {
      height: calc(var(--spacing) * 5);
    }
  }

  @media (min-width: 40rem) {
    .sm\:h-10 {
      height: calc(var(--spacing) * 10);
    }
  }

  @media (min-width: 40rem) {
    .sm\:h-12 {
      height: calc(var(--spacing) * 12);
    }
  }

  @media (min-width: 40rem) {
    .sm\:h-16 {
      height: calc(var(--spacing) * 16);
    }
  }

  @media (min-width: 40rem) {
    .sm\:h-48 {
      height: calc(var(--spacing) * 48);
    }
  }

  @media (min-width: 40rem) {
    .sm\:h-56 {
      height: calc(var(--spacing) * 56);
    }
  }

  @media (min-width: 40rem) {
    .sm\:w-4\/5 {
      width: 80%;
    }
  }

  @media (min-width: 40rem) {
    .sm\:w-5 {
      width: calc(var(--spacing) * 5);
    }
  }

  @media (min-width: 40rem) {
    .sm\:w-10 {
      width: calc(var(--spacing) * 10);
    }
  }

  @media (min-width: 40rem) {
    .sm\:w-12 {
      width: calc(var(--spacing) * 12);
    }
  }

  @media (min-width: 40rem) {
    .sm\:w-16 {
      width: calc(var(--spacing) * 16);
    }
  }

  @media (min-width: 40rem) {
    .sm\:w-96 {
      width: calc(var(--spacing) * 96);
    }
  }

  @media (min-width: 40rem) {
    .sm\:max-w-md {
      max-width: var(--container-md);
    }
  }

  @media (min-width: 40rem) {
    .sm\:grid-cols-2 {
      grid-template-columns: repeat(2, minmax(0, 1fr));
    }
  }

  @media (min-width: 40rem) {
    .sm\:flex-row {
      flex-direction: row;
    }
  }

  @media (min-width: 40rem) {
    .sm\:items-center {
      align-items: center;
    }
  }

  @media (min-width: 40rem) {
    .sm\:justify-start {
      justify-content: flex-start;
    }
  }

  @media (min-width: 40rem) {
    .sm\:gap-0 {
      gap: calc(var(--spacing) * 0);
    }
  }

  @media (min-width: 40rem) {
    .sm\:gap-4 {
      gap: calc(var(--spacing) * 4);
    }
  }

  @media (min-width: 40rem) {
    .sm\:gap-6 {
      gap: calc(var(--spacing) * 6);
    }
  }

  @media (min-width: 40rem) {
    .sm\:gap-8 {
      gap: calc(var(--spacing) * 8);
    }
  }

  @media (min-width: 40rem) {
    .sm\:gap-10 {
      gap: calc(var(--spacing) * 10);
    }
  }

  @media (min-width: 40rem) {
    .sm\:gap-12 {
      gap: calc(var(--spacing) * 12);
    }
  }

  @media (min-width: 40rem) {
    :where(.sm\:space-y-4 > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-top: calc(calc(var(--spacing) * 4) * var(--tw-space-y-reverse));
      margin-bottom: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-y-reverse)));
    }
  }

  @media (min-width: 40rem) {
    :where(.sm\:space-y-5 > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-top: calc(calc(var(--spacing) * 5) * var(--tw-space-y-reverse));
      margin-bottom: calc(calc(var(--spacing) * 5) * calc(1 - var(--tw-space-y-reverse)));
    }
  }

  @media (min-width: 40rem) {
    .sm\:p-6 {
      padding: calc(var(--spacing) * 6);
    }
  }

  @media (min-width: 40rem) {
    .sm\:px-0 {
      padding-inline: calc(var(--spacing) * 0);
    }
  }

  @media (min-width: 40rem) {
    .sm\:px-2 {
      padding-inline: calc(var(--spacing) * 2);
    }
  }

  @media (min-width: 40rem) {
    .sm\:px-3 {
      padding-inline: calc(var(--spacing) * 3);
    }
  }

  @media (min-width: 40rem) {
    .sm\:px-4 {
      padding-inline: calc(var(--spacing) * 4);
    }
  }

  @media (min-width: 40rem) {
    .sm\:px-6 {
      padding-inline: calc(var(--spacing) * 6);
    }
  }

  @media (min-width: 40rem) {
    .sm\:px-7 {
      padding-inline: calc(var(--spacing) * 7);
    }
  }

  @media (min-width: 40rem) {
    .sm\:px-8 {
      padding-inline: calc(var(--spacing) * 8);
    }
  }

  @media (min-width: 40rem) {
    .sm\:py-3 {
      padding-block: calc(var(--spacing) * 3);
    }
  }

  @media (min-width: 40rem) {
    .sm\:py-3\.5 {
      padding-block: calc(var(--spacing) * 3.5);
    }
  }

  @media (min-width: 40rem) {
    .sm\:py-8 {
      padding-block: calc(var(--spacing) * 8);
    }
  }

  @media (min-width: 40rem) {
    .sm\:py-10 {
      padding-block: calc(var(--spacing) * 10);
    }
  }

  @media (min-width: 40rem) {
    .sm\:py-12 {
      padding-block: calc(var(--spacing) * 12);
    }
  }

  @media (min-width: 40rem) {
    .sm\:py-16 {
      padding-block: calc(var(--spacing) * 16);
    }
  }

  @media (min-width: 40rem) {
    .sm\:py-24 {
      padding-block: calc(var(--spacing) * 24);
    }
  }

  @media (min-width: 40rem) {
    .sm\:pt-2 {
      padding-top: calc(var(--spacing) * 2);
    }
  }

  @media (min-width: 40rem) {
    .sm\:pt-28 {
      padding-top: calc(var(--spacing) * 28);
    }
  }

  @media (min-width: 40rem) {
    .sm\:pb-3 {
      padding-bottom: calc(var(--spacing) * 3);
    }
  }

  @media (min-width: 40rem) {
    .sm\:pb-4 {
      padding-bottom: calc(var(--spacing) * 4);
    }
  }

  @media (min-width: 40rem) {
    .sm\:text-left {
      text-align: left;
    }
  }

  @media (min-width: 40rem) {
    .sm\:text-2xl {
      font-size: var(--text-2xl);
      line-height: var(--tw-leading, var(--text-2xl--line-height));
    }
  }

  @media (min-width: 40rem) {
    .sm\:text-3xl {
      font-size: var(--text-3xl);
      line-height: var(--tw-leading, var(--text-3xl--line-height));
    }
  }

  @media (min-width: 40rem) {
    .sm\:text-4xl {
      font-size: var(--text-4xl);
      line-height: var(--tw-leading, var(--text-4xl--line-height));
    }
  }

  @media (min-width: 40rem) {
    .sm\:text-5xl {
      font-size: var(--text-5xl);
      line-height: var(--tw-leading, var(--text-5xl--line-height));
    }
  }

  @media (min-width: 40rem) {
    .sm\:text-base {
      font-size: var(--text-base);
      line-height: var(--tw-leading, var(--text-base--line-height));
    }
  }

  @media (min-width: 40rem) {
    .sm\:text-lg {
      font-size: var(--text-lg);
      line-height: var(--tw-leading, var(--text-lg--line-height));
    }
  }

  @media (min-width: 40rem) {
    .sm\:text-sm {
      font-size: var(--text-sm);
      line-height: var(--tw-leading, var(--text-sm--line-height));
    }
  }

  @media (min-width: 40rem) {
    .sm\:text-xl {
      font-size: var(--text-xl);
      line-height: var(--tw-leading, var(--text-xl--line-height));
    }
  }

  @media (min-width: 40rem) {
    .sm\:break-normal {
      overflow-wrap: normal;
      word-break: normal;
    }
  }

  @media (min-width: 48rem) {
    .md\:col-span-2 {
      grid-column: span 2 / span 2;
    }
  }

  @media (min-width: 48rem) {
    .md\:-mt-24 {
      margin-top: calc(var(--spacing) * -24);
    }
  }

  @media (min-width: 48rem) {
    .md\:mt-0 {
      margin-top: calc(var(--spacing) * 0);
    }
  }

  @media (min-width: 48rem) {
    .md\:mt-32 {
      margin-top: calc(var(--spacing) * 32);
    }
  }

  @media (min-width: 48rem) {
    .md\:mb-0 {
      margin-bottom: calc(var(--spacing) * 0);
    }
  }

  @media (min-width: 48rem) {
    .md\:h-12 {
      height: calc(var(--spacing) * 12);
    }
  }

  @media (min-width: 48rem) {
    .md\:h-16 {
      height: calc(var(--spacing) * 16);
    }
  }

  @media (min-width: 48rem) {
    .md\:h-24 {
      height: calc(var(--spacing) * 24);
    }
  }

  @media (min-width: 48rem) {
    .md\:w-16 {
      width: calc(var(--spacing) * 16);
    }
  }

  @media (min-width: 48rem) {
    .md\:w-24 {
      width: calc(var(--spacing) * 24);
    }
  }

  @media (min-width: 48rem) {
    .md\:auto-rows-\[18rem\] {
      grid-auto-rows: 18rem;
    }
  }

  @media (min-width: 48rem) {
    .md\:grid-cols-2 {
      grid-template-columns: repeat(2, minmax(0, 1fr));
    }
  }

  @media (min-width: 48rem) {
    .md\:grid-cols-3 {
      grid-template-columns: repeat(3, minmax(0, 1fr));
    }
  }

  @media (min-width: 48rem) {
    .md\:grid-cols-4 {
      grid-template-columns: repeat(4, minmax(0, 1fr));
    }
  }

  @media (min-width: 48rem) {
    .md\:gap-3 {
      gap: calc(var(--spacing) * 3);
    }
  }

  @media (min-width: 48rem) {
    .md\:gap-4 {
      gap: calc(var(--spacing) * 4);
    }
  }

  @media (min-width: 48rem) {
    .md\:gap-6 {
      gap: calc(var(--spacing) * 6);
    }
  }

  @media (min-width: 48rem) {
    .md\:gap-8 {
      gap: calc(var(--spacing) * 8);
    }
  }

  @media (min-width: 48rem) {
    .md\:gap-12 {
      gap: calc(var(--spacing) * 12);
    }
  }

  @media (min-width: 48rem) {
    .md\:gap-16 {
      gap: calc(var(--spacing) * 16);
    }
  }

  @media (min-width: 48rem) {
    :where(.md\:space-y-8 > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-top: calc(calc(var(--spacing) * 8) * var(--tw-space-y-reverse));
      margin-bottom: calc(calc(var(--spacing) * 8) * calc(1 - var(--tw-space-y-reverse)));
    }
  }

  @media (min-width: 48rem) {
    :where(.md\:space-y-12 > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-top: calc(calc(var(--spacing) * 12) * var(--tw-space-y-reverse));
      margin-bottom: calc(calc(var(--spacing) * 12) * calc(1 - var(--tw-space-y-reverse)));
    }
  }

  @media (min-width: 48rem) {
    .md\:p-6 {
      padding: calc(var(--spacing) * 6);
    }
  }

  @media (min-width: 48rem) {
    .md\:p-14 {
      padding: calc(var(--spacing) * 14);
    }
  }

  @media (min-width: 48rem) {
    .md\:px-0 {
      padding-inline: calc(var(--spacing) * 0);
    }
  }

  @media (min-width: 48rem) {
    .md\:px-3 {
      padding-inline: calc(var(--spacing) * 3);
    }
  }

  @media (min-width: 48rem) {
    .md\:px-4 {
      padding-inline: calc(var(--spacing) * 4);
    }
  }

  @media (min-width: 48rem) {
    .md\:px-5 {
      padding-inline: calc(var(--spacing) * 5);
    }
  }

  @media (min-width: 48rem) {
    .md\:px-6 {
      padding-inline: calc(var(--spacing) * 6);
    }
  }

  @media (min-width: 48rem) {
    .md\:px-8 {
      padding-inline: calc(var(--spacing) * 8);
    }
  }

  @media (min-width: 48rem) {
    .md\:px-16 {
      padding-inline: calc(var(--spacing) * 16);
    }
  }

  @media (min-width: 48rem) {
    .md\:py-1 {
      padding-block: calc(var(--spacing) * 1);
    }
  }

  @media (min-width: 48rem) {
    .md\:py-2 {
      padding-block: calc(var(--spacing) * 2);
    }
  }

  @media (min-width: 48rem) {
    .md\:py-2\.5 {
      padding-block: calc(var(--spacing) * 2.5);
    }
  }

  @media (min-width: 48rem) {
    .md\:py-16 {
      padding-block: calc(var(--spacing) * 16);
    }
  }

  @media (min-width: 48rem) {
    .md\:pt-4 {
      padding-top: calc(var(--spacing) * 4);
    }
  }

  @media (min-width: 48rem) {
    .md\:pt-5 {
      padding-top: calc(var(--spacing) * 5);
    }
  }

  @media (min-width: 48rem) {
    .md\:pt-20 {
      padding-top: calc(var(--spacing) * 20);
    }
  }

  @media (min-width: 48rem) {
    .md\:text-2xl {
      font-size: var(--text-2xl);
      line-height: var(--tw-leading, var(--text-2xl--line-height));
    }
  }

  @media (min-width: 48rem) {
    .md\:text-3xl {
      font-size: var(--text-3xl);
      line-height: var(--tw-leading, var(--text-3xl--line-height));
    }
  }

  @media (min-width: 48rem) {
    .md\:text-4xl {
      font-size: var(--text-4xl);
      line-height: var(--tw-leading, var(--text-4xl--line-height));
    }
  }

  @media (min-width: 48rem) {
    .md\:text-5xl {
      font-size: var(--text-5xl);
      line-height: var(--tw-leading, var(--text-5xl--line-height));
    }
  }

  @media (min-width: 48rem) {
    .md\:text-base {
      font-size: var(--text-base);
      line-height: var(--tw-leading, var(--text-base--line-height));
    }
  }

  @media (min-width: 48rem) {
    .md\:text-lg {
      font-size: var(--text-lg);
      line-height: var(--tw-leading, var(--text-lg--line-height));
    }
  }

  @media (min-width: 48rem) {
    .md\:text-sm {
      font-size: var(--text-sm);
      line-height: var(--tw-leading, var(--text-sm--line-height));
    }
  }

  @media (min-width: 64rem) {
    .lg\:sticky {
      position: -webkit-sticky;
      position: sticky;
    }
  }

  @media (min-width: 64rem) {
    .lg\:col-span-1 {
      grid-column: span 1 / span 1;
    }
  }

  @media (min-width: 64rem) {
    .lg\:col-span-2 {
      grid-column: span 2 / span 2;
    }
  }

  @media (min-width: 64rem) {
    .lg\:col-span-3 {
      grid-column: span 3 / span 3;
    }
  }

  @media (min-width: 64rem) {
    .lg\:col-span-4 {
      grid-column: span 4 / span 4;
    }
  }

  @media (min-width: 64rem) {
    .lg\:col-span-5 {
      grid-column: span 5 / span 5;
    }
  }

  @media (min-width: 64rem) {
    .lg\:col-span-6 {
      grid-column: span 6 / span 6;
    }
  }

  @media (min-width: 64rem) {
    .lg\:col-span-8 {
      grid-column: span 8 / span 8;
    }
  }

  @media (min-width: 64rem) {
    .lg\:mt-0 {
      margin-top: calc(var(--spacing) * 0);
    }
  }

  @media (min-width: 64rem) {
    .lg\:mt-8 {
      margin-top: calc(var(--spacing) * 8);
    }
  }

  @media (min-width: 64rem) {
    .lg\:mb-16 {
      margin-bottom: calc(var(--spacing) * 16);
    }
  }

  @media (min-width: 64rem) {
    .lg\:block {
      display: block;
    }
  }

  @media (min-width: 64rem) {
    .lg\:flex {
      display: flex;
    }
  }

  @media (min-width: 64rem) {
    .lg\:hidden {
      display: none;
    }
  }

  @media (min-width: 64rem) {
    .lg\:h-3 {
      height: calc(var(--spacing) * 3);
    }
  }

  @media (min-width: 64rem) {
    .lg\:h-14 {
      height: calc(var(--spacing) * 14);
    }
  }

  @media (min-width: 64rem) {
    .lg\:h-28 {
      height: calc(var(--spacing) * 28);
    }
  }

  @media (min-width: 64rem) {
    .lg\:h-60 {
      height: calc(var(--spacing) * 60);
    }
  }

  @media (min-width: 64rem) {
    .lg\:h-full {
      height: 100%;
    }
  }

  @media (min-width: 64rem) {
    .lg\:min-h-screen {
      min-height: 100vh;
    }
  }

  @media (min-width: 64rem) {
    .lg\:w-1\/2 {
      width: 50%;
    }
  }

  @media (min-width: 64rem) {
    .lg\:w-3 {
      width: calc(var(--spacing) * 3);
    }
  }

  @media (min-width: 64rem) {
    .lg\:w-8 {
      width: calc(var(--spacing) * 8);
    }
  }

  @media (min-width: 64rem) {
    .lg\:w-28 {
      width: calc(var(--spacing) * 28);
    }
  }

  @media (min-width: 64rem) {
    .lg\:w-\[380px\] {
      width: 380px;
    }
  }

  @media (min-width: 64rem) {
    .lg\:-translate-x-2 {
      --tw-translate-x: calc(var(--spacing) * -2);
      translate: var(--tw-translate-x) var(--tw-translate-y);
    }
  }

  @media (min-width: 64rem) {
    .lg\:grid-cols-2 {
      grid-template-columns: repeat(2, minmax(0, 1fr));
    }
  }

  @media (min-width: 64rem) {
    .lg\:grid-cols-3 {
      grid-template-columns: repeat(3, minmax(0, 1fr));
    }
  }

  @media (min-width: 64rem) {
    .lg\:grid-cols-4 {
      grid-template-columns: repeat(4, minmax(0, 1fr));
    }
  }

  @media (min-width: 64rem) {
    .lg\:grid-cols-5 {
      grid-template-columns: repeat(5, minmax(0, 1fr));
    }
  }

  @media (min-width: 64rem) {
    .lg\:grid-cols-12 {
      grid-template-columns: repeat(12, minmax(0, 1fr));
    }
  }

  @media (min-width: 64rem) {
    .lg\:flex-row {
      flex-direction: row;
    }
  }

  @media (min-width: 64rem) {
    .lg\:flex-row-reverse {
      flex-direction: row-reverse;
    }
  }

  @media (min-width: 64rem) {
    .lg\:justify-start {
      justify-content: flex-start;
    }
  }

  @media (min-width: 64rem) {
    .lg\:gap-8 {
      gap: calc(var(--spacing) * 8);
    }
  }

  @media (min-width: 64rem) {
    .lg\:gap-12 {
      gap: calc(var(--spacing) * 12);
    }
  }

  @media (min-width: 64rem) {
    .lg\:gap-16 {
      gap: calc(var(--spacing) * 16);
    }
  }

  @media (min-width: 64rem) {
    .lg\:rounded-br-\[100px\] {
      border-bottom-right-radius: 100px;
    }
  }

  @media (min-width: 64rem) {
    .lg\:border-l {
      border-left-style: var(--tw-border-style);
      border-left-width: 1px;
    }
  }

  @media (min-width: 64rem) {
    .lg\:border-gray-200 {
      border-color: var(--color-gray-200);
    }
  }

  @media (min-width: 64rem) {
    .lg\:bg-white\/0 {
      background-color: rgba(0, 0, 0, 0);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .lg\:bg-white\/0 {
        background-color: color-mix(in oklab, var(--color-white) 0%, transparent);
      }
    }
  }

  @media (min-width: 64rem) {
    .lg\:p-6 {
      padding: calc(var(--spacing) * 6);
    }
  }

  @media (min-width: 64rem) {
    .lg\:p-8 {
      padding: calc(var(--spacing) * 8);
    }
  }

  @media (min-width: 64rem) {
    .lg\:p-10 {
      padding: calc(var(--spacing) * 10);
    }
  }

  @media (min-width: 64rem) {
    .lg\:p-12 {
      padding: calc(var(--spacing) * 12);
    }
  }

  @media (min-width: 64rem) {
    .lg\:p-16 {
      padding: calc(var(--spacing) * 16);
    }
  }

  @media (min-width: 64rem) {
    .lg\:px-0 {
      padding-inline: calc(var(--spacing) * 0);
    }
  }

  @media (min-width: 64rem) {
    .lg\:px-4 {
      padding-inline: calc(var(--spacing) * 4);
    }
  }

  @media (min-width: 64rem) {
    .lg\:px-8 {
      padding-inline: calc(var(--spacing) * 8);
    }
  }

  @media (min-width: 64rem) {
    .lg\:px-10 {
      padding-inline: calc(var(--spacing) * 10);
    }
  }

  @media (min-width: 64rem) {
    .lg\:px-12 {
      padding-inline: calc(var(--spacing) * 12);
    }
  }

  @media (min-width: 64rem) {
    .lg\:px-16 {
      padding-inline: calc(var(--spacing) * 16);
    }
  }

  @media (min-width: 64rem) {
    .lg\:py-2 {
      padding-block: calc(var(--spacing) * 2);
    }
  }

  @media (min-width: 64rem) {
    .lg\:py-12 {
      padding-block: calc(var(--spacing) * 12);
    }
  }

  @media (min-width: 64rem) {
    .lg\:py-16 {
      padding-block: calc(var(--spacing) * 16);
    }
  }

  @media (min-width: 64rem) {
    .lg\:py-20 {
      padding-block: calc(var(--spacing) * 20);
    }
  }

  @media (min-width: 64rem) {
    .lg\:py-24 {
      padding-block: calc(var(--spacing) * 24);
    }
  }

  @media (min-width: 64rem) {
    .lg\:py-26 {
      padding-block: calc(var(--spacing) * 26);
    }
  }

  @media (min-width: 64rem) {
    .lg\:py-28 {
      padding-block: calc(var(--spacing) * 28);
    }
  }

  @media (min-width: 64rem) {
    .lg\:pt-20 {
      padding-top: calc(var(--spacing) * 20);
    }
  }

  @media (min-width: 64rem) {
    .lg\:pt-28 {
      padding-top: calc(var(--spacing) * 28);
    }
  }

  @media (min-width: 64rem) {
    .lg\:pt-32 {
      padding-top: calc(var(--spacing) * 32);
    }
  }

  @media (min-width: 64rem) {
    .lg\:pb-8 {
      padding-bottom: calc(var(--spacing) * 8);
    }
  }

  @media (min-width: 64rem) {
    .lg\:pb-16 {
      padding-bottom: calc(var(--spacing) * 16);
    }
  }

  @media (min-width: 64rem) {
    .lg\:pb-24 {
      padding-bottom: calc(var(--spacing) * 24);
    }
  }

  @media (min-width: 64rem) {
    .lg\:pl-6 {
      padding-left: calc(var(--spacing) * 6);
    }
  }

  @media (min-width: 64rem) {
    .lg\:pl-8 {
      padding-left: calc(var(--spacing) * 8);
    }
  }

  @media (min-width: 64rem) {
    .lg\:text-left {
      text-align: left;
    }
  }

  @media (min-width: 64rem) {
    .lg\:text-2xl {
      font-size: var(--text-2xl);
      line-height: var(--tw-leading, var(--text-2xl--line-height));
    }
  }

  @media (min-width: 64rem) {
    .lg\:text-3xl {
      font-size: var(--text-3xl);
      line-height: var(--tw-leading, var(--text-3xl--line-height));
    }
  }

  @media (min-width: 64rem) {
    .lg\:text-4xl {
      font-size: var(--text-4xl);
      line-height: var(--tw-leading, var(--text-4xl--line-height));
    }
  }

  @media (min-width: 64rem) {
    .lg\:text-5xl {
      font-size: var(--text-5xl);
      line-height: var(--tw-leading, var(--text-5xl--line-height));
    }
  }

  @media (min-width: 64rem) {
    .lg\:text-6xl {
      font-size: var(--text-6xl);
      line-height: var(--tw-leading, var(--text-6xl--line-height));
    }
  }

  @media (min-width: 64rem) {
    .lg\:text-base {
      font-size: var(--text-base);
      line-height: var(--tw-leading, var(--text-base--line-height));
    }
  }

  @media (min-width: 64rem) {
    .lg\:text-lg {
      font-size: var(--text-lg);
      line-height: var(--tw-leading, var(--text-lg--line-height));
    }
  }

  @media (min-width: 64rem) {
    .lg\:text-xl {
      font-size: var(--text-xl);
      line-height: var(--tw-leading, var(--text-xl--line-height));
    }
  }

  @media (min-width: 80rem) {
    .xl\:col-span-5 {
      grid-column: span 5 / span 5;
    }
  }

  @media (min-width: 80rem) {
    .xl\:col-span-7 {
      grid-column: span 7 / span 7;
    }
  }

  @media (min-width: 80rem) {
    .xl\:mt-28 {
      margin-top: calc(var(--spacing) * 28);
    }
  }

  @media (min-width: 80rem) {
    .xl\:ml-10 {
      margin-left: calc(var(--spacing) * 10);
    }
  }

  @media (min-width: 80rem) {
    .xl\:h-48 {
      height: calc(var(--spacing) * 48);
    }
  }

  @media (min-width: 80rem) {
    .xl\:grid-cols-3 {
      grid-template-columns: repeat(3, minmax(0, 1fr));
    }
  }

  @media (min-width: 80rem) {
    .xl\:grid-cols-4 {
      grid-template-columns: repeat(4, minmax(0, 1fr));
    }
  }

  @media (min-width: 80rem) {
    .xl\:grid-cols-12 {
      grid-template-columns: repeat(12, minmax(0, 1fr));
    }
  }

  @media (min-width: 80rem) {
    .xl\:gap-8 {
      gap: calc(var(--spacing) * 8);
    }
  }

  @media (min-width: 80rem) {
    .xl\:pt-14 {
      padding-top: calc(var(--spacing) * 14);
    }
  }

  @media (min-width: 80rem) {
    .xl\:text-3xl {
      font-size: var(--text-3xl);
      line-height: var(--tw-leading, var(--text-3xl--line-height));
    }
  }

  @media (min-width: 80rem) {
    .xl\:text-5xl {
      font-size: var(--text-5xl);
      line-height: var(--tw-leading, var(--text-5xl--line-height));
    }
  }

  @media (min-width: 80rem) {
    .xl\:text-base {
      font-size: var(--text-base);
      line-height: var(--tw-leading, var(--text-base--line-height));
    }
  }

  @media (min-width: 80rem) {
    .xl\:text-xl {
      font-size: var(--text-xl);
      line-height: var(--tw-leading, var(--text-xl--line-height));
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:text-neutral-200 {
      color: var(--color-neutral-200);
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:text-neutral-300 {
      color: var(--color-neutral-300);
    }
  }

  .cyber-grid {
    background-image: linear-gradient(to right, #3b82f6 1px, rgba(0, 0, 0, 0) 1px), linear-gradient(#3b82f6 1px, rgba(0, 0, 0, 0) 1px);
    background-size: 40px 40px;
  }

  .text-brand-light-blue {
    color: var(--color-blue);
  }

  .bg-brand-light-blue {
    background-color: var(--color-blue);
  }

  .text-brand-navy {
    color: var(--color-dark-blue);
  }
}

:root {
  --color-blue: #36f;
  --color-blue-secondary: #6188ff;
  --color-yellow: #fbc02d;
  --color-yellow-hover: #f9a825;
  --color-yellow-bg: #fff9c4;
  --color-dark-blue: #0d1a63;
  --color-dark-blue-hover: #1b263b;
  --color-dark-blue-bg: #274472;
  --color-dark-blue-subtle: #4a6fa5;
  --color-gray: #212121;
  --color-gray-light: #e0e0e0;
  --background: #fff;
  --foreground: var(--color-dark-blue);
  --foreground-secondary: #4a5568;
  --bg-light-blue: #f3f5fa;
}

html {
  scroll-behavior: smooth;
}

body {
  background-color: var(--background);
  color: var(--foreground);
  font-family: var(--font-roboto), "Roboto", sans-serif;
  margin: 0;
  padding: 0;
}

h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-poppins), "Poppins", sans-serif;
}

p, span, div, a, li, td, th, label, input, textarea, select {
  font-family: var(--font-roboto), "Roboto", sans-serif;
}

.font-poppins {
  font-family: var(--font-poppins), "Poppins", sans-serif !important;
}

.font-roboto {
  font-family: var(--font-roboto), "Roboto", sans-serif !important;
}

.font-heading {
  font-family: var(--font-poppins), "Poppins", sans-serif !important;
}

.font-body {
  font-family: var(--font-roboto), "Roboto", sans-serif !important;
}

.container {
  width: 100% !important;
  margin-left: auto !important;
  margin-right: auto !important;
  padding-left: 1rem !important;
  padding-right: 1rem !important;
}

@media (min-width: 640px) {
  .container {
    padding-left: 1.5rem !important;
    padding-right: 1.5rem !important;
  }
}

@media (min-width: 1024px) {
  .container {
    padding-left: 2rem !important;
    padding-right: 2rem !important;
  }
}

*, :before, :after, * {
  box-sizing: border-box;
}

html, body {
  width: 100%;
  overflow-x: hidden;
}

.navbar-glass {
  -webkit-backdrop-filter: blur(10px);
  background: rgba(13, 26, 99, .9);
  border: 1px solid rgba(255, 255, 255, .1);
}

.max-w-7xl {
  max-width: 80rem !important;
}

.btn-primary {
  background: linear-gradient(135deg, var(--color-blue) 0%, var(--color-blue-secondary) 100%);
  border: none;
  transition: all .3s cubic-bezier(.4, 0, .2, 1);
  position: relative;
  overflow: hidden;
  transform: translateY(0);
}

.btn-primary:before {
  content: "";
  background: linear-gradient(90deg, rgba(0, 0, 0, 0), rgba(255, 255, 255, .2), rgba(0, 0, 0, 0));
  width: 100%;
  height: 100%;
  transition: left .5s;
  position: absolute;
  top: 0;
  left: -100%;
}

.btn-primary:hover:before {
  left: 100%;
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(51, 102, 255, .3);
}

.btn-primary:active {
  transition: transform .1s;
  transform: translateY(0);
}

.btn-secondary {
  background: linear-gradient(135deg, var(--color-dark-blue) 0%, var(--color-dark-blue-hover) 100%);
  border: none;
  transition: all .3s cubic-bezier(.4, 0, .2, 1);
  position: relative;
  overflow: hidden;
  transform: translateY(0);
}

.btn-secondary:before {
  content: "";
  background: linear-gradient(90deg, rgba(0, 0, 0, 0), rgba(255, 255, 255, .1), rgba(0, 0, 0, 0));
  width: 100%;
  height: 100%;
  transition: left .5s;
  position: absolute;
  top: 0;
  left: -100%;
}

.btn-secondary:hover:before {
  left: 100%;
}

.btn-secondary:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(13, 26, 99, .3);
}

.btn-secondary:active {
  transition: transform .1s;
  transform: translateY(0);
}

.btn-outlined {
  border: 2px solid var(--color-dark-blue);
  background: none;
  transition: all .3s cubic-bezier(.4, 0, .2, 1);
  position: relative;
  overflow: hidden;
  transform: translateY(0);
}

.btn-outlined:before {
  content: "";
  background: var(--color-dark-blue);
  z-index: -1;
  width: 0;
  height: 100%;
  transition: width .3s cubic-bezier(.4, 0, .2, 1);
  position: absolute;
  top: 0;
  left: 0;
}

.btn-outlined:hover:before {
  width: 100%;
}

.btn-outlined:hover {
  color: #fff;
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(13, 26, 99, .2);
}

.btn-outlined:active {
  transition: transform .1s;
  transform: translateY(0);
}

.btn-accent {
  background: linear-gradient(135deg, var(--color-yellow) 0%, var(--color-yellow-hover) 100%);
  border: none;
  transition: all .3s cubic-bezier(.4, 0, .2, 1);
  position: relative;
  overflow: hidden;
  transform: translateY(0);
}

.btn-accent:before {
  content: "";
  background: linear-gradient(90deg, rgba(0, 0, 0, 0), rgba(255, 255, 255, .3), rgba(0, 0, 0, 0));
  width: 100%;
  height: 100%;
  transition: left .5s;
  position: absolute;
  top: 0;
  left: -100%;
}

.btn-accent:hover:before {
  left: 100%;
}

.btn-accent:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(251, 192, 45, .4);
}

.btn-accent:active {
  transition: transform .1s;
  transform: translateY(0);
}

.navbar-glass {
  -webkit-backdrop-filter: blur(20px);
  background: rgba(75, 85, 99, .8);
  border: 1px solid rgba(255, 255, 255, .1);
  transition: all .3s cubic-bezier(.4, 0, .2, 1);
}

.navbar-glass.scrolled {
  -webkit-backdrop-filter: blur(25px);
  background: rgba(75, 85, 99, .95);
  box-shadow: 0 8px 32px rgba(0, 0, 0, .1);
}

button:focus-visible {
  outline: 2px solid var(--color-blue);
  outline-offset: 2px;
}

a:focus-visible {
  outline: 2px solid var(--color-blue);
  outline-offset: 2px;
}

input:focus-visible {
  outline: 2px solid var(--color-blue);
  outline-offset: 2px;
}

textarea:focus-visible {
  outline: 2px solid var(--color-blue);
  outline-offset: 2px;
}

.btn-loading {
  pointer-events: none;
  position: relative;
}

.btn-loading:after {
  content: "";
  border: 2px solid rgba(0, 0, 0, 0);
  border-top-color: currentColor;
  border-radius: 50%;
  width: 16px;
  height: 16px;
  margin-top: -8px;
  margin-left: -8px;
  animation: 1s linear infinite spin;
  position: absolute;
  top: 50%;
  left: 50%;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in-up {
  animation: .6s ease-out fadeInUp;
}

@keyframes blob {
  0% {
    transform: translate(0)scale(1);
  }

  33% {
    transform: translate(30px, -50px)scale(1.1);
  }

  66% {
    transform: translate(-20px, 20px)scale(.9);
  }

  100% {
    transform: translate(0)scale(1);
  }
}

.animate-blob {
  animation: 7s infinite blob;
}

.animation-delay-2000 {
  animation-delay: 2s;
}

.animation-delay-4000 {
  animation-delay: 4s;
}

.scrollbar-hide {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;
}

.custom-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: var(--color-blue) transparent;
}

.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: none;
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: var(--color-blue);
  border-radius: 3px;
  transition: background-color .2s;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: var(--color-dark-blue);
}

.custom-scrollbar::-webkit-scrollbar-thumb:active {
  background: var(--color-dark-blue);
}

@property --tw-translate-x {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-translate-y {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-translate-z {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-scale-x {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}

@property --tw-scale-y {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}

@property --tw-scale-z {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}

@property --tw-rotate-x {
  syntax: "*";
  inherits: false
}

@property --tw-rotate-y {
  syntax: "*";
  inherits: false
}

@property --tw-rotate-z {
  syntax: "*";
  inherits: false
}

@property --tw-skew-x {
  syntax: "*";
  inherits: false
}

@property --tw-skew-y {
  syntax: "*";
  inherits: false
}

@property --tw-scroll-snap-strictness {
  syntax: "*";
  inherits: false;
  initial-value: proximity;
}

@property --tw-space-y-reverse {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-space-x-reverse {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-border-style {
  syntax: "*";
  inherits: false;
  initial-value: solid;
}

@property --tw-gradient-position {
  syntax: "*";
  inherits: false
}

@property --tw-gradient-from {
  syntax: "<color>";
  inherits: false;
  initial-value: rgba(0, 0, 0, 0);
}

@property --tw-gradient-via {
  syntax: "<color>";
  inherits: false;
  initial-value: rgba(0, 0, 0, 0);
}

@property --tw-gradient-to {
  syntax: "<color>";
  inherits: false;
  initial-value: rgba(0, 0, 0, 0);
}

@property --tw-gradient-stops {
  syntax: "*";
  inherits: false
}

@property --tw-gradient-via-stops {
  syntax: "*";
  inherits: false
}

@property --tw-gradient-from-position {
  syntax: "<length-percentage>";
  inherits: false;
  initial-value: 0%;
}

@property --tw-gradient-via-position {
  syntax: "<length-percentage>";
  inherits: false;
  initial-value: 50%;
}

@property --tw-gradient-to-position {
  syntax: "<length-percentage>";
  inherits: false;
  initial-value: 100%;
}

@property --tw-leading {
  syntax: "*";
  inherits: false
}

@property --tw-font-weight {
  syntax: "*";
  inherits: false
}

@property --tw-tracking {
  syntax: "*";
  inherits: false
}

@property --tw-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 rgba(0, 0, 0, 0);
}

@property --tw-shadow-color {
  syntax: "*";
  inherits: false
}

@property --tw-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}

@property --tw-inset-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 rgba(0, 0, 0, 0);
}

@property --tw-inset-shadow-color {
  syntax: "*";
  inherits: false
}

@property --tw-inset-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}

@property --tw-ring-color {
  syntax: "*";
  inherits: false
}

@property --tw-ring-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 rgba(0, 0, 0, 0);
}

@property --tw-inset-ring-color {
  syntax: "*";
  inherits: false
}

@property --tw-inset-ring-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 rgba(0, 0, 0, 0);
}

@property --tw-ring-inset {
  syntax: "*";
  inherits: false
}

@property --tw-ring-offset-width {
  syntax: "<length>";
  inherits: false;
  initial-value: 0;
}

@property --tw-ring-offset-color {
  syntax: "*";
  inherits: false;
  initial-value: #fff;
}

@property --tw-ring-offset-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 rgba(0, 0, 0, 0);
}

@property --tw-blur {
  syntax: "*";
  inherits: false
}

@property --tw-brightness {
  syntax: "*";
  inherits: false
}

@property --tw-contrast {
  syntax: "*";
  inherits: false
}

@property --tw-grayscale {
  syntax: "*";
  inherits: false
}

@property --tw-hue-rotate {
  syntax: "*";
  inherits: false
}

@property --tw-invert {
  syntax: "*";
  inherits: false
}

@property --tw-opacity {
  syntax: "*";
  inherits: false
}

@property --tw-saturate {
  syntax: "*";
  inherits: false
}

@property --tw-sepia {
  syntax: "*";
  inherits: false
}

@property --tw-drop-shadow {
  syntax: "*";
  inherits: false
}

@property --tw-drop-shadow-color {
  syntax: "*";
  inherits: false
}

@property --tw-drop-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}

@property --tw-drop-shadow-size {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-blur {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-brightness {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-contrast {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-grayscale {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-hue-rotate {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-invert {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-opacity {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-saturate {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-sepia {
  syntax: "*";
  inherits: false
}

@property --tw-duration {
  syntax: "*";
  inherits: false
}

@property --tw-ease {
  syntax: "*";
  inherits: false
}

/*# sourceMappingURL=%5Broot-of-the-server%5D__7666c318._.css.map*/