import { NextResponse } from 'next/server';
import fs from 'fs/promises';
import path from 'path';
import { isBusinessEmail, isValidEmail } from '../../utils/emailValidator';
import { sendEmail } from '../../utils/send-email';

// Constants
const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB max file size
const MAX_SUBMISSIONS = 10000; // Maximum number of submissions to keep
const SUBMISSIONS_PATH = path.join(process.cwd(), 'data', 'submissions.json');

// Helper function to ensure data directory exists
async function ensureDataDirectory() {
  const dataDir = path.join(process.cwd(), 'data');
  try {
    await fs.access(dataDir);
  } catch {
    await fs.mkdir(dataDir, { recursive: true });
  }
}

// Helper function to read submissions
async function readSubmissions() {
  try {
    const data = await fs.readFile(SUBMISSIONS_PATH, 'utf8');
    return JSON.parse(data);
  } catch (error) {
    // File doesn't exist or is invalid, return empty array
    return [];
  }
}

// Helper function to write submissions
async function writeSubmissions(submissions) {
  // Keep only the most recent submissions if we exceed the limit
  if (submissions.length > MAX_SUBMISSIONS) {
    submissions = submissions.slice(-MAX_SUBMISSIONS);
  }
  
  await fs.writeFile(SUBMISSIONS_PATH, JSON.stringify(submissions, null, 2));
}

export async function POST(request) {
  try {
    // Ensure data directory exists
    await ensureDataDirectory();

    // Parse and validate request data
    const data = await request.json();
    if (!data || typeof data !== 'object') {
      return NextResponse.json(
        { error: 'Invalid submission data' },
        { status: 400 }
      );
    }

    // Required fields per form type
    const requiredFieldsMap = {
      'contact-form': ['name', 'email', 'message'],
      'training-enrollment': ['fullName', 'email', 'phone'],
      'newsletter-signup': ['name', 'email'],
      'premium-training-inquiry': ['name', 'email', 'message'],
      'general-inquiry': ['name', 'email', 'message']
    };

    const formType = data.formType;
    if (!formType || !requiredFieldsMap[formType]) {
      return NextResponse.json(
        { error: 'Missing or unknown formType.' },
        { status: 400 }
      );
    }

    // Validate required fields for this formType
    const requiredFields = requiredFieldsMap[formType];
    const missingFields = requiredFields.filter(field => !data[field] || String(data[field]).trim() === '');
    if (missingFields.length > 0) {
      return NextResponse.json(
        { error: `Missing required field(s): ${missingFields.join(', ')}` },
        { status: 400 }
      );
    }

    // Email validation - use business email validation for contact forms, regular validation for others
    const emailToValidate = data.email || data.businessEmail || null;
    if (!emailToValidate) {
      return NextResponse.json(
        { error: 'Email address is required.' },
        { status: 400 }
      );
    }

    // For contact forms and training enrollment, require business email
    // For newsletter signup, allow any valid email
    if (formType === 'contact-form' || formType === 'training-enrollment') {
      if (!isBusinessEmail(emailToValidate)) {
        return NextResponse.json(
          { error: 'Please use a valid business email address.' },
          { status: 400 }
        );
      }
    } else {
      if (!isValidEmail(emailToValidate)) {
        return NextResponse.json(
          { error: 'Please use a valid email address.' },
          { status: 400 }
        );
      }
    }

    // Add timestamp if not present
    const submissionWithTimestamp = {
      ...data,
      timestamp: data.timestamp || new Date().toISOString()
    };

    // Read existing submissions
    const submissions = await readSubmissions();

    // Add new submission
    submissions.push(submissionWithTimestamp);

    // Write back to file
    await writeSubmissions(submissions);

    // Send email with form data
    const subject = data.subject || `New ${formType.replace('-', ' ')} Submission`;
    const body = Object.entries(data).map(([key, value]) => `${key}: ${value}`).join('\n');
    await sendEmail(subject, body);

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error storing submission or sending email:', error);
    return NextResponse.json(
      { error: 'Failed to store submission or send email' },
      { status: 500 }
    );
  }
}
