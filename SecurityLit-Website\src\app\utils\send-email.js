import { EmailClient, KnownEmailSendStatus } from "@azure/communication-email";

// Check if connection string is available
const connectionString = process.env.NEXT_PUBLIC_COMMUNICATION_SERVICES_CONNECTION_STRING;
let emailClient;

// Only initialize email client if connection string is available
if (connectionString) {
  try {
    emailClient = new EmailClient(connectionString);
  } catch (error) {
    console.error("Failed to initialize email client:", error);
  }
} else {
  console.warn("Communication Services connection string is not defined");
}

// Constants for polling
const POLLER_WAIT_TIME = 10; // seconds
const MAX_POLL_TIME = 120; // seconds

// Helper function to parse form data from body
function parseFormData(body) {
  const lines = body.split('\n');
  const formData = {};
  
  lines.forEach(line => {
    const [key, ...valueParts] = line.split(': ');
    if (key && valueParts.length > 0) {
      formData[key] = valueParts.join(': ');
    }
  });
  
  return formData;
}

// Helper function to determine form type from subject
function getFormType(subject) {
  if (subject.includes('Contact')) return 'contact-form';
  if (subject.includes('Training')) return 'training-enrollment';
  if (subject.includes('Newsletter')) return 'newsletter-signup';
  if (subject.includes('Premium')) return 'premium-training-inquiry';
  return 'general-inquiry';
}

// Helper function to store submission with retry logic
const MAX_RETRIES = 3;
const storeSubmission = async (submissionData) => {
  let retries = 0;
  while (retries < MAX_RETRIES) {
    try {
      // Use absolute URL with window.location.origin
      const baseUrl = typeof window !== 'undefined' ? window.location.origin : 'http://localhost:3000';
      const response = await fetch(`${baseUrl}/api/submissions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(submissionData),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return true;
    } catch (error) {
      retries++;
      if (retries === MAX_RETRIES) {
        console.error('Failed to store submission after retries:', error);
        return false;
      }
      // Wait before retrying (exponential backoff)
      await new Promise(resolve => setTimeout(resolve, Math.pow(2, retries) * 1000));
    }
  }
  return false;
};

export async function sendEmail(subject, body, attachments = []) {
  try {
    // Parse form data and prepare submission
    const formData = parseFormData(body);
    const submissionData = {
      subject,
      body,
      attachments: attachments.length > 0 ? 'Has attachments' : 'No attachments',
      formType: getFormType(subject),
      formData,
      timestamp: new Date().toISOString()
    };

    // Store submission (non-blocking)
    const storePromise = storeSubmission(submissionData);

    // Check if email client is initialized
    if (!emailClient) {
      console.warn("Email client not initialized, skipping email send");
      await storePromise; // Still wait for storage to complete
      return true; // Return success for development environments
    }

    // Prepare email message
    const message = {
      senderAddress: process.env.NEXT_PUBLIC_EMAIL_USER,
      content: {
        subject,
        plainText: body,
        attachments,
      },
      recipients: {
        to: [
          {
            address: process.env.NEXT_PUBLIC_RECIEVER_EMAIL || '<EMAIL>',
            displayName: "SecurityLit Team",
          },
        ],
      },
    };

    // Send email
    const poller = await emailClient.beginSend(message);

    if (!poller.getOperationState().isStarted) {
      throw new Error("Email sending failed to start");
    }

    // Poll for completion with timeout
    let timeElapsed = 0;
    while (!poller.isDone()) {
      await poller.poll();
      await new Promise(resolve => setTimeout(resolve, POLLER_WAIT_TIME * 1000));
      timeElapsed += POLLER_WAIT_TIME;

      if (timeElapsed > MAX_POLL_TIME) {
        throw new Error("Email sending timed out");
      }
    }

    const result = poller.getResult();
    if (result.status !== KnownEmailSendStatus.Succeeded) {
      throw new Error(result.error || "Email sending failed");
    }

    // Wait for storage to complete
    await storePromise;

    console.log(`Successfully sent the email (operation id: ${result.id})`);
    return true;
  } catch (error) {
    console.error("Failed to send email:", error);
    throw error;
  }
}
