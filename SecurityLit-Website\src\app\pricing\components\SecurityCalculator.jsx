"use client";

import React, { useState, useMemo } from "react";

// SecurityLit Cybersecurity Services Pricing Data
const securityServicesData = [
  // Web Application Security Testing
  {
    target: "Web Application",
    userRole: "1 user role",
    pages: "40 pages",
    hours: 40,
    comprehensive: 6500,
    standard: 4800,
  },
  {
    target: "Web Application",
    userRole: "1 user role", 
    pages: "50 pages",
    hours: 56,
    comprehensive: 9200,
    standard: 6900,
  },
  {
    target: "Web Application",
    userRole: "2 user roles",
    pages: "60 pages",
    hours: 80,
    comprehensive: 13500,
    standard: 10200,
  },
  {
    target: "Web Application",
    userRole: "3 user roles",
    pages: "70 pages",
    hours: 96,
    comprehensive: 16800,
    standard: 12600,
  },
  {
    target: "Web Application",
    userRole: "4 user roles",
    pages: "80 pages",
    hours: 112,
    comprehensive: 20100,
    standard: 15000,
  },
  {
    target: "Web Application",
    userRole: "5+ user roles",
    pages: "100+ pages",
    hours: 128,
    comprehensive: 23400,
    standard: 17500,
  },

  // API Security Testing
  {
    target: "API Security",
    userRole: "1 service",
    endpoints: "75 endpoints",
    hours: 40,
    comprehensive: 6500,
    standard: 4800,
  },
  {
    target: "API Security",
    userRole: "1 service",
    endpoints: "90 endpoints", 
    hours: 56,
    comprehensive: 9200,
    standard: 6900,
  },
  {
    target: "API Security",
    userRole: "2 services",
    endpoints: "120 endpoints",
    hours: 80,
    comprehensive: 13500,
    standard: 10200,
  },
  {
    target: "API Security",
    userRole: "3 services",
    endpoints: "150 endpoints",
    hours: 96,
    comprehensive: 16800,
    standard: 12600,
  },
  {
    target: "API Security",
    userRole: "4+ services",
    endpoints: "180+ endpoints",
    hours: 112,
    comprehensive: 20100,
    standard: 15000,
  },

  // Mobile Application Security
  {
    target: "Mobile Application",
    userRole: "iOS or Android",
    screens: "25 screens",
    hours: 40,
    comprehensive: 6500,
    standard: 4800,
  },
  {
    target: "Mobile Application",
    userRole: "iOS and Android",
    screens: "35 screens",
    hours: 56,
    comprehensive: 9200,
    standard: 6900,
  },
  {
    target: "Mobile Application",
    userRole: "Cross-platform + Backend",
    screens: "50 screens",
    hours: 80,
    comprehensive: 13500,
    standard: 10200,
  },
  {
    target: "Mobile Application",
    userRole: "Enterprise Mobile Suite",
    screens: "75+ screens",
    hours: 96,
    comprehensive: 16800,
    standard: 12600,
  },

  // Network Security Assessment
  {
    target: "Network Security",
    userRole: "Small Network",
    scope: "50 IPs",
    hours: 40,
    comprehensive: 6500,
    standard: 4800,
  },
  {
    target: "Network Security",
    userRole: "Medium Network",
    scope: "100 IPs",
    hours: 56,
    comprehensive: 9200,
    standard: 6900,
  },
  {
    target: "Network Security",
    userRole: "Large Network",
    scope: "200 IPs",
    hours: 80,
    comprehensive: 13500,
    standard: 10200,
  },
  {
    target: "Network Security",
    userRole: "Enterprise Network",
    scope: "300+ IPs",
    hours: 96,
    comprehensive: 16800,
    standard: 12600,
  },

  // Cloud Security Assessment
  {
    target: "Cloud Security",
    userRole: "Single Cloud Provider",
    scope: "Basic Configuration",
    hours: 40,
    comprehensive: 6500,
    standard: 4800,
  },
  {
    target: "Cloud Security",
    userRole: "Multi-Cloud Setup",
    scope: "Standard Configuration",
    hours: 56,
    comprehensive: 9200,
    standard: 6900,
  },
  {
    target: "Cloud Security",
    userRole: "Enterprise Cloud",
    scope: "Complex Configuration",
    hours: 80,
    comprehensive: 13500,
    standard: 10200,
  },
  {
    target: "Cloud Security",
    userRole: "Global Cloud Infrastructure",
    scope: "Enterprise+ Configuration",
    hours: 96,
    comprehensive: 16800,
    standard: 12600,
  },
];

const serviceDescriptions = {
  "Web Application": "Comprehensive security testing of your web applications to identify vulnerabilities, secure coding issues, and potential attack vectors that could compromise your business data.",
  "API Security": "In-depth security assessment of your APIs, including authentication, authorization, data validation, and protection against common API vulnerabilities like injection attacks.",
  "Mobile Application": "Complete mobile security testing covering both client-side and server-side components, including data storage, communication security, and platform-specific vulnerabilities.",
  "Network Security": "Thorough network penetration testing to identify security weaknesses in your network infrastructure, including firewalls, routers, and network segmentation.",
  "Cloud Security": "Comprehensive cloud security assessment covering configuration reviews, access controls, data protection, and compliance with cloud security best practices.",
};

const SecurityDropdown = ({ label, value, onChange, options, placeholder, icon }) => (
  <div className="space-y-3">
    <div className="flex items-center gap-3">
      {icon && (
        <div className="w-8 h-8 bg-gradient-to-br from-[var(--color-blue)] to-[var(--color-blue-secondary)] rounded-lg flex items-center justify-center">
          <div className="text-white text-sm">
            {icon}
          </div>
        </div>
      )}
      <label className="text-[var(--color-dark-blue)] font-semibold text-lg">
        {label}
      </label>
    </div>
    <div className="relative">
      <select
        value={value}
        onChange={onChange}
        className={`w-full p-5 pr-14 border-2 rounded-2xl appearance-none transition-all duration-300 font-medium text-lg
          ${value
            ? 'border-[var(--color-blue)] bg-white text-[var(--color-dark-blue)] shadow-xl shadow-[var(--color-blue)]/10'
            : 'border-gray-300 bg-white text-gray-500 hover:border-[var(--color-blue)]/60 hover:shadow-lg'
          }
          focus:outline-none focus:ring-4 focus:ring-[var(--color-blue)]/25 focus:border-[var(--color-blue)] focus:shadow-xl`}
      >
        <option value="" className="text-gray-500">{placeholder}</option>
        {options.map((option) => (
          <option key={option} value={option} className="text-[var(--color-dark-blue)] font-medium py-2">
            {option}
          </option>
        ))}
      </select>
      <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-5">
        <div className={`w-8 h-8 rounded-full flex items-center justify-center transition-all duration-300 ${
          value ? 'bg-[var(--color-blue)]' : 'bg-gray-300'
        }`}>
          <svg
            className="h-4 w-4 text-white"
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 20 20"
            fill="currentColor"
          >
            <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
          </svg>
        </div>
      </div>
    </div>
  </div>
);

export default function SecurityCalculator() {
  const [assessmentType, setAssessmentType] = useState("");
  const [target, setTarget] = useState("");
  const [userRole, setUserRole] = useState("");
  const [scope, setScope] = useState("");
  const [competitorQuote, setCompetitorQuote] = useState("");

  const targets = useMemo(
    () => [...new Set(securityServicesData.map((item) => item.target))],
    []
  );

  const userRoles = useMemo(() => {
    if (!target) return [];
    return [
      ...new Set(
        securityServicesData
          .filter((item) => item.target === target)
          .map((item) => item.userRole)
      ),
    ];
  }, [target]);

  const scopes = useMemo(() => {
    if (!target || !userRole) return [];
    return [
      ...new Set(
        securityServicesData
          .filter(
            (item) => item.target === target && item.userRole === userRole
          )
          .map((item) => item.pages || item.endpoints || item.screens || item.scope)
      ),
    ].filter(Boolean);
  }, [target, userRole]);

  const result = useMemo(() => {
    if (!assessmentType || !target || !userRole) return null;

    let item;
    if (["Web Application", "API Security", "Mobile Application"].includes(target)) {
      if (!scope) return null;
      item = securityServicesData.find(
        (item) =>
          item.target === target &&
          item.userRole === userRole &&
          (item.pages === scope ||
            item.endpoints === scope ||
            item.screens === scope ||
            item.scope === scope)
      );
    } else {
      item = securityServicesData.find(
        (item) => item.target === target && item.userRole === userRole
      );
    }

    if (!item) return null;
    const cost = assessmentType === "Comprehensive Assessment" ? item.comprehensive : item.standard;
    const savings =
      competitorQuote && competitorQuote !== ""
        ? parseFloat(competitorQuote) - cost
        : 0;
    const percentage =
      savings && savings !== 0
        ? ((savings / parseFloat(competitorQuote)) * 100).toFixed(1)
        : 0;
    return { hours: item.hours, cost, savings, percentage };
  }, [assessmentType, target, userRole, scope, competitorQuote]);

  return (
    <div className="w-full max-w-7xl mx-auto p-6 lg:p-12">
      {/* Header Section */}
      <div className="text-center mb-12">
        <h2 className="text-4xl lg:text-5xl font-bold text-[var(--color-dark-blue)] mb-6">
          Security Assessment Calculator
        </h2>
        <p className="text-xl text-gray-600 max-w-4xl mx-auto leading-relaxed">
          Configure your security assessment requirements to get an instant pricing estimate.
          Our transparent pricing helps you plan your cybersecurity investments effectively.
        </p>
      </div>

      {/* Main Calculator Grid */}
      <div className="grid grid-cols-1 xl:grid-cols-12 gap-8">
        {/* Left Panel - Configuration */}
        <div className="xl:col-span-5 space-y-8">
          <div className="bg-gradient-to-br from-[var(--color-dark-blue)] to-[var(--color-blue)] rounded-3xl p-8 text-white">
            <div className="flex items-center gap-4 mb-6">
              <div className="w-12 h-12 bg-[var(--color-yellow)] rounded-xl flex items-center justify-center">
                <svg className="w-6 h-6 text-[var(--color-dark-blue)]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                </svg>
              </div>
              <h3 className="text-2xl font-bold">Configure Your Assessment</h3>
            </div>
            <p className="text-white/90 text-lg leading-relaxed">
              Select your requirements below to receive an accurate pricing estimate for your cybersecurity assessment.
            </p>
          </div>

          <div className="bg-white rounded-3xl p-8 shadow-xl border border-gray-100 space-y-8">
            <SecurityDropdown
              label="Assessment Type"
              value={assessmentType}
              onChange={(e) => {
                setAssessmentType(e.target.value);
                setTarget("");
                setUserRole("");
                setScope("");
                setCompetitorQuote("");
              }}
              options={["Standard Assessment", "Comprehensive Assessment"]}
              placeholder="Select Assessment Type"
              icon="🔍"
            />

            <SecurityDropdown
              label="Security Service"
              value={target}
              onChange={(e) => {
                setTarget(e.target.value);
                setUserRole("");
                setScope("");
                setCompetitorQuote("");
              }}
              options={targets}
              placeholder="Choose Security Service"
              icon="🛡️"
            />

            {target && (
              <SecurityDropdown
                label="Project Scope"
                value={userRole}
                onChange={(e) => {
                  setUserRole(e.target.value);
                  setScope("");
                  setCompetitorQuote("");
                }}
                options={userRoles}
                placeholder="Select Project Scope"
                icon="📊"
              />
            )}
          </div>
        </div>

        {/* Right Panel - Results and Details */}
        <div className="xl:col-span-7 space-y-8">
          {target ? (
            <div className="bg-white rounded-3xl p-8 shadow-xl border border-gray-100">
              <div className="flex items-start gap-6 mb-8">
                <div className="w-16 h-16 bg-gradient-to-br from-[var(--color-blue)] to-[var(--color-blue-secondary)] rounded-2xl flex items-center justify-center flex-shrink-0">
                  <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                  </svg>
                </div>
                <div className="flex-1">
                  <h3 className="text-3xl font-bold text-[var(--color-dark-blue)] mb-4">{target}</h3>
                  <p className="text-gray-700 text-lg leading-relaxed">{serviceDescriptions[target]}</p>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                {assessmentType && (
                  <div className="bg-gradient-to-br from-[var(--color-blue)]/10 to-[var(--color-blue-secondary)]/10 rounded-2xl p-6 border border-[var(--color-blue)]/20">
                    <div className="flex items-center gap-3 mb-3">
                      <div className="w-8 h-8 bg-[var(--color-blue)] rounded-lg flex items-center justify-center">
                        <span className="text-white text-sm">🔍</span>
                      </div>
                      <span className="text-sm font-semibold text-[var(--color-dark-blue)] uppercase tracking-wide">Assessment Type</span>
                    </div>
                    <div className="text-[var(--color-blue)] font-bold text-lg">{assessmentType}</div>
                  </div>
                )}

                {userRole && (
                  <div className="bg-gradient-to-br from-[var(--color-yellow)]/10 to-[var(--color-yellow-hover)]/10 rounded-2xl p-6 border border-[var(--color-yellow)]/30">
                    <div className="flex items-center gap-3 mb-3">
                      <div className="w-8 h-8 bg-[var(--color-yellow)] rounded-lg flex items-center justify-center">
                        <span className="text-[var(--color-dark-blue)] text-sm">📊</span>
                      </div>
                      <span className="text-sm font-semibold text-[var(--color-dark-blue)] uppercase tracking-wide">Project Scope</span>
                    </div>
                    <div className="text-[var(--color-dark-blue)] font-bold text-lg">{userRole}</div>
                  </div>
                )}

                {scope && (
                  <div className="bg-gradient-to-br from-gray-100 to-gray-200 rounded-2xl p-6 border border-gray-300">
                    <div className="flex items-center gap-3 mb-3">
                      <div className="w-8 h-8 bg-gray-600 rounded-lg flex items-center justify-center">
                        <span className="text-white text-sm">⚙️</span>
                      </div>
                      <span className="text-sm font-semibold text-gray-700 uppercase tracking-wide">Technical Scope</span>
                    </div>
                    <div className="text-gray-800 font-bold text-lg">{scope}</div>
                  </div>
                )}
              </div>
            </div>
          ) : (
            <div className="bg-gradient-to-br from-gray-50 to-gray-100 rounded-3xl p-12 text-center border-2 border-dashed border-gray-300">
              <div className="w-20 h-20 bg-gray-200 rounded-full flex items-center justify-center mx-auto mb-6">
                <svg className="w-10 h-10 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                </svg>
              </div>
              <h3 className="text-2xl font-bold text-gray-600 mb-4">Select Your Security Service</h3>
              <p className="text-gray-500 text-lg">Choose an assessment type and security service to see detailed information and pricing.</p>
            </div>
          )}

          {userRole &&
            ["Web Application", "API Security", "Mobile Application"].includes(target) &&
            scopes.length > 0 && (
              <div className="bg-white rounded-3xl p-8 shadow-xl border border-gray-100">
                <SecurityDropdown
                  label="Technical Scope"
                  value={scope}
                  onChange={(e) => {
                    setScope(e.target.value);
                    setCompetitorQuote("");
                  }}
                  options={scopes}
                  placeholder="Select Technical Scope"
                  icon="⚙️"
                />
              </div>
            )}

          <div className="bg-white rounded-3xl p-8 shadow-xl border border-gray-100">
            <div className="flex items-center gap-4 mb-6">
              <div className="w-12 h-12 bg-gradient-to-br from-[var(--color-yellow)] to-[var(--color-yellow-hover)] rounded-xl flex items-center justify-center">
                <svg className="w-6 h-6 text-[var(--color-dark-blue)]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                </svg>
              </div>
              <label className="text-[var(--color-dark-blue)] font-semibold text-lg">
                Competitor Quote (Optional)
              </label>
            </div>
            <input
              type="number"
              value={competitorQuote}
              onChange={(e) => setCompetitorQuote(e.target.value)}
              className="w-full p-5 border-2 border-gray-300 rounded-2xl focus:outline-none focus:ring-4 focus:ring-[var(--color-blue)]/25 focus:border-[var(--color-blue)] transition-all duration-300 font-medium text-lg hover:border-[var(--color-blue)]/60 hover:shadow-lg"
              placeholder="Enter competitor quote in USD (e.g., 15000)"
            />
          </div>

          {/* Pricing Results */}
          <div className="bg-gradient-to-br from-[var(--color-dark-blue)] via-[var(--color-blue)] to-[var(--color-blue-secondary)] rounded-3xl p-8 text-white shadow-2xl">
            <div className="flex items-center gap-4 mb-8">
              <div className="w-16 h-16 bg-[var(--color-yellow)] rounded-2xl flex items-center justify-center">
                <svg className="w-8 h-8 text-[var(--color-dark-blue)]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
                </svg>
              </div>
              <h3 className="text-3xl font-bold">Investment Summary</h3>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              <div className="bg-white/15 backdrop-blur-sm rounded-2xl p-8 border border-white/20">
                <div className="flex items-center gap-3 mb-4">
                  <div className="w-8 h-8 bg-[var(--color-yellow)] rounded-lg flex items-center justify-center">
                    <span className="text-[var(--color-dark-blue)] text-sm font-bold">$</span>
                  </div>
                  <p className="text-white/90 font-semibold text-lg">Estimated Investment</p>
                </div>
                <p className="text-5xl font-bold text-white mb-3">
                  ${result ? result.cost.toLocaleString() : '0'}
                </p>
                {result && (
                  <p className="text-white/80 text-lg">
                    ~{result.hours} hours of expert assessment
                  </p>
                )}
              </div>

              {result && result.savings > 0 && (
                <div className="bg-[var(--color-yellow)]/20 backdrop-blur-sm rounded-2xl p-8 border border-[var(--color-yellow)]/30">
                  <div className="flex items-center gap-3 mb-4">
                    <div className="w-8 h-8 bg-[var(--color-yellow)] rounded-lg flex items-center justify-center">
                      <span className="text-[var(--color-dark-blue)] text-sm font-bold">%</span>
                    </div>
                    <p className="text-white/90 font-semibold text-lg">Your Savings</p>
                  </div>
                  <p className="text-5xl font-bold text-[var(--color-yellow)] mb-3">
                    ${result.savings.toLocaleString()}
                  </p>
                  <p className="text-white/80 text-lg">
                    {result.percentage}% less than competitor
                  </p>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Disclaimer */}
      <div className="mt-12 bg-gradient-to-r from-[var(--color-dark-blue)]/5 to-[var(--color-blue)]/5 rounded-3xl p-8 border border-[var(--color-blue)]/20">
        <div className="flex items-start gap-6">
          <div className="w-12 h-12 bg-[var(--color-yellow)] rounded-xl flex items-center justify-center flex-shrink-0">
            <svg className="w-6 h-6 text-[var(--color-dark-blue)]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <div className="flex-1">
            <h4 className="text-xl font-bold text-[var(--color-dark-blue)] mb-3">Important Pricing Information</h4>
            <p className="text-gray-700 leading-relaxed text-lg">
              These estimates are for planning purposes and may vary based on specific requirements, complexity, and timeline.
              SecurityLit provides customized cybersecurity solutions tailored to your unique environment.
              For a detailed proposal, please{" "}
              <a href="/contact" className="text-[var(--color-blue)] hover:text-[var(--color-blue-secondary)] font-bold underline decoration-2 underline-offset-2">
                contact our security experts
              </a>{" "}
              for a comprehensive consultation and accurate quote.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
