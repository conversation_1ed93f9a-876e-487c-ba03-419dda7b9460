"use client";

import React, { useState, useMemo } from "react";

// SecurityLit Cybersecurity Services Pricing Data
const securityServicesData = [
  // Web Application Security Testing
  {
    target: "Web Application",
    userRole: "1 user role",
    pages: "40 pages",
    hours: 40,
    comprehensive: 6500,
    standard: 4800,
  },
  {
    target: "Web Application",
    userRole: "1 user role", 
    pages: "50 pages",
    hours: 56,
    comprehensive: 9200,
    standard: 6900,
  },
  {
    target: "Web Application",
    userRole: "2 user roles",
    pages: "60 pages",
    hours: 80,
    comprehensive: 13500,
    standard: 10200,
  },
  {
    target: "Web Application",
    userRole: "3 user roles",
    pages: "70 pages",
    hours: 96,
    comprehensive: 16800,
    standard: 12600,
  },
  {
    target: "Web Application",
    userRole: "4 user roles",
    pages: "80 pages",
    hours: 112,
    comprehensive: 20100,
    standard: 15000,
  },
  {
    target: "Web Application",
    userRole: "5+ user roles",
    pages: "100+ pages",
    hours: 128,
    comprehensive: 23400,
    standard: 17500,
  },

  // API Security Testing
  {
    target: "API Security",
    userRole: "1 service",
    endpoints: "75 endpoints",
    hours: 40,
    comprehensive: 6500,
    standard: 4800,
  },
  {
    target: "API Security",
    userRole: "1 service",
    endpoints: "90 endpoints", 
    hours: 56,
    comprehensive: 9200,
    standard: 6900,
  },
  {
    target: "API Security",
    userRole: "2 services",
    endpoints: "120 endpoints",
    hours: 80,
    comprehensive: 13500,
    standard: 10200,
  },
  {
    target: "API Security",
    userRole: "3 services",
    endpoints: "150 endpoints",
    hours: 96,
    comprehensive: 16800,
    standard: 12600,
  },
  {
    target: "API Security",
    userRole: "4+ services",
    endpoints: "180+ endpoints",
    hours: 112,
    comprehensive: 20100,
    standard: 15000,
  },

  // Mobile Application Security
  {
    target: "Mobile Application",
    userRole: "iOS or Android",
    screens: "25 screens",
    hours: 40,
    comprehensive: 6500,
    standard: 4800,
  },
  {
    target: "Mobile Application",
    userRole: "iOS and Android",
    screens: "35 screens",
    hours: 56,
    comprehensive: 9200,
    standard: 6900,
  },
  {
    target: "Mobile Application",
    userRole: "Cross-platform + Backend",
    screens: "50 screens",
    hours: 80,
    comprehensive: 13500,
    standard: 10200,
  },
  {
    target: "Mobile Application",
    userRole: "Enterprise Mobile Suite",
    screens: "75+ screens",
    hours: 96,
    comprehensive: 16800,
    standard: 12600,
  },

  // Network Security Assessment
  {
    target: "Network Security",
    userRole: "Small Network",
    scope: "50 IPs",
    hours: 40,
    comprehensive: 6500,
    standard: 4800,
  },
  {
    target: "Network Security",
    userRole: "Medium Network",
    scope: "100 IPs",
    hours: 56,
    comprehensive: 9200,
    standard: 6900,
  },
  {
    target: "Network Security",
    userRole: "Large Network",
    scope: "200 IPs",
    hours: 80,
    comprehensive: 13500,
    standard: 10200,
  },
  {
    target: "Network Security",
    userRole: "Enterprise Network",
    scope: "300+ IPs",
    hours: 96,
    comprehensive: 16800,
    standard: 12600,
  },

  // Cloud Security Assessment
  {
    target: "Cloud Security",
    userRole: "Single Cloud Provider",
    scope: "Basic Configuration",
    hours: 40,
    comprehensive: 6500,
    standard: 4800,
  },
  {
    target: "Cloud Security",
    userRole: "Multi-Cloud Setup",
    scope: "Standard Configuration",
    hours: 56,
    comprehensive: 9200,
    standard: 6900,
  },
  {
    target: "Cloud Security",
    userRole: "Enterprise Cloud",
    scope: "Complex Configuration",
    hours: 80,
    comprehensive: 13500,
    standard: 10200,
  },
  {
    target: "Cloud Security",
    userRole: "Global Cloud Infrastructure",
    scope: "Enterprise+ Configuration",
    hours: 96,
    comprehensive: 16800,
    standard: 12600,
  },
];

const serviceDescriptions = {
  "Web Application": "Comprehensive security testing of your web applications to identify vulnerabilities, secure coding issues, and potential attack vectors that could compromise your business data.",
  "API Security": "In-depth security assessment of your APIs, including authentication, authorization, data validation, and protection against common API vulnerabilities like injection attacks.",
  "Mobile Application": "Complete mobile security testing covering both client-side and server-side components, including data storage, communication security, and platform-specific vulnerabilities.",
  "Network Security": "Thorough network penetration testing to identify security weaknesses in your network infrastructure, including firewalls, routers, and network segmentation.",
  "Cloud Security": "Comprehensive cloud security assessment covering configuration reviews, access controls, data protection, and compliance with cloud security best practices.",
};

const SecurityDropdown = ({ label, value, onChange, options, placeholder, icon }) => (
  <div className="space-y-3">
    <div className="flex items-center gap-3">
      {icon && (
        <div className="w-8 h-8 bg-gradient-to-br from-[var(--color-blue)] to-[var(--color-blue-secondary)] rounded-lg flex items-center justify-center">
          <div className="text-white text-sm">
            {icon}
          </div>
        </div>
      )}
      <label className="text-[var(--color-dark-blue)] font-semibold text-lg">
        {label}
      </label>
    </div>
    <div className="relative">
      <select
        value={value}
        onChange={onChange}
        className={`w-full p-5 pr-14 border-2 rounded-2xl appearance-none transition-all duration-300 font-medium text-lg
          ${value
            ? 'border-[var(--color-blue)] bg-white text-[var(--color-dark-blue)] shadow-xl shadow-[var(--color-blue)]/10'
            : 'border-gray-300 bg-white text-gray-500 hover:border-[var(--color-blue)]/60 hover:shadow-lg'
          }
          focus:outline-none focus:ring-4 focus:ring-[var(--color-blue)]/25 focus:border-[var(--color-blue)] focus:shadow-xl`}
      >
        <option value="" className="text-gray-500">{placeholder}</option>
        {options.map((option) => (
          <option key={option} value={option} className="text-[var(--color-dark-blue)] font-medium py-2">
            {option}
          </option>
        ))}
      </select>
      <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-5">
        <div className={`w-8 h-8 rounded-full flex items-center justify-center transition-all duration-300 ${
          value ? 'bg-[var(--color-blue)]' : 'bg-gray-300'
        }`}>
          <svg
            className="h-4 w-4 text-white"
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 20 20"
            fill="currentColor"
          >
            <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
          </svg>
        </div>
      </div>
    </div>
  </div>
);

const StyledDropdown = ({ label, value, onChange, options, placeholder }) => (
  <div className="relative group">
    <label className="block mb-2 font-medium text-[var(--color-dark-blue)] pl-2">{label}:</label>
    <div className="relative flex items-center pl-2">
      <select
        value={value}
        onChange={onChange}
        className={`w-full p-3 pr-10 border rounded-lg appearance-none transition-all duration-200 ease-in-out
          ${value ? 'border-[var(--color-blue)] bg-white' : 'border-gray-300 bg-gray-50'}
          focus:outline-none focus:ring-2 focus:ring-[var(--color-blue)] focus:border-transparent
          hover:border-[var(--color-blue)]`}
      >
        <option value="">{placeholder}</option>
        {options.map((option) => (
          <option key={option} value={option}>
            {option}
          </option>
        ))}
      </select>
      <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-3">
        <div className="h-6 border-r border-gray-300 mr-2"></div>
        <svg
          className="fill-current text-gray-500 h-4 w-4 transition-transform duration-200 group-hover:scale-110"
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 20 20"
        >
          <path d="M9.293 12.95l.707.707L15.657 8l-1.414-1.414L10 10.828 5.757 6.586 4.343 8z" />
        </svg>
      </div>
    </div>
    {value && (
      <div className="w-1 h-full bg-[var(--color-blue)] absolute -left-1 top-0 bottom-0 rounded-full"></div>
    )}
  </div>
);

const TargetDescription = ({ target, assessmentType, userRole, scope, competitorQuote, result }) => {
  const serviceDescriptions = {
    "Web Application": "Comprehensive security testing of web applications including authentication, authorization, input validation, and business logic vulnerabilities.",
    "API Security": "Thorough testing of REST and GraphQL APIs for security vulnerabilities, authentication flaws, and data exposure risks.",
    "Mobile Application": "Complete security assessment of mobile applications covering both client-side and server-side vulnerabilities.",
    "Network Infrastructure": "In-depth penetration testing of network infrastructure, including firewalls, routers, and network segmentation.",
    "Cloud Security": "Comprehensive security assessment of cloud environments, configurations, and access controls.",
    "Social Engineering": "Simulated social engineering attacks to test human security awareness and organizational vulnerabilities."
  };

  return (
    <div className="bg-gradient-to-br from-[var(--color-blue)]/5 to-[var(--color-blue-secondary)]/5 rounded-2xl p-6 border border-[var(--color-blue)]/20">
      <h3 className="text-2xl font-bold text-[var(--color-dark-blue)] mb-4">{target}</h3>
      <p className="text-gray-700 mb-6 leading-relaxed">{serviceDescriptions[target]}</p>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {assessmentType && (
          <div className="bg-white rounded-xl p-4 border border-[var(--color-blue)]/10">
            <span className="text-sm font-medium text-gray-600">Assessment Type</span>
            <div className="text-[var(--color-blue)] font-semibold mt-1">{assessmentType}</div>
          </div>
        )}

        {userRole && (
          <div className="bg-white rounded-xl p-4 border border-[var(--color-blue)]/10">
            <span className="text-sm font-medium text-gray-600">Project Scope</span>
            <div className="text-[var(--color-dark-blue)] font-semibold mt-1">{userRole}</div>
          </div>
        )}

        {scope && (
          <div className="bg-white rounded-xl p-4 border border-[var(--color-blue)]/10">
            <span className="text-sm font-medium text-gray-600">Technical Scope</span>
            <div className="text-[var(--color-dark-blue)] font-semibold mt-1">{scope}</div>
          </div>
        )}

        {result && (
          <div className="bg-white rounded-xl p-4 border border-[var(--color-blue)]/10">
            <span className="text-sm font-medium text-gray-600">Estimated Hours</span>
            <div className="text-[var(--color-dark-blue)] font-semibold mt-1">{result.hours} hours</div>
          </div>
        )}
      </div>
    </div>
  );
};

export default function SecurityCalculator() {
  const [assessmentType, setAssessmentType] = useState("");
  const [target, setTarget] = useState("");
  const [userRole, setUserRole] = useState("");
  const [scope, setScope] = useState("");
  const [competitorQuote, setCompetitorQuote] = useState("");

  const targets = useMemo(
    () => [...new Set(securityServicesData.map((item) => item.target))],
    []
  );

  const userRoles = useMemo(() => {
    if (!target) return [];
    return [
      ...new Set(
        securityServicesData
          .filter((item) => item.target === target)
          .map((item) => item.userRole)
      ),
    ];
  }, [target]);

  const scopes = useMemo(() => {
    if (!target || !userRole) return [];
    return [
      ...new Set(
        securityServicesData
          .filter(
            (item) => item.target === target && item.userRole === userRole
          )
          .map((item) => item.pages || item.endpoints || item.screens || item.scope)
      ),
    ].filter(Boolean);
  }, [target, userRole]);

  const getUserRoleLabel = () => {
    switch (target) {
      case "Web Application":
      case "API Security":
        return "User Roles";
      case "Mobile Application":
        return "Mobile Suite";
      case "Network Infrastructure":
        return "Network Scope";
      case "Cloud Security":
        return "Cloud Environment";
      default:
        return "User Role / Scope";
    }
  };

  const getScopeLabel = () => {
    switch (target) {
      case "Web Application":
        return "Pages";
      case "API Security":
        return "Endpoints";
      case "Mobile Application":
        return "Screens";
      case "Network Infrastructure":
        return "IP Addresses";
      case "Cloud Security":
        return "Resources";
      default:
        return "Scope";
    }
  };

  const result = useMemo(() => {
    if (!assessmentType || !target || !userRole) return null;

    let item;
    if (["Web Application", "API Security", "Mobile Application"].includes(target)) {
      if (!scope) return null;
      item = securityServicesData.find(
        (item) =>
          item.target === target &&
          item.userRole === userRole &&
          (item.pages === scope ||
            item.endpoints === scope ||
            item.screens === scope ||
            item.scope === scope)
      );
    } else {
      item = securityServicesData.find(
        (item) => item.target === target && item.userRole === userRole
      );
    }

    if (!item) return null;
    const cost = assessmentType === "Comprehensive Assessment" ? item.comprehensive : item.standard;
    const savings =
      competitorQuote && competitorQuote !== ""
        ? parseFloat(competitorQuote) - cost
        : 0;
    const percentage =
      savings && savings !== 0
        ? ((savings / parseFloat(competitorQuote)) * 100).toFixed(1)
        : 0;
    return { hours: item.hours, cost, savings, percentage };
  }, [assessmentType, target, userRole, scope, competitorQuote]);

  return (
    <div className="w-full max-w-8xl mx-auto p-4 lg:p-8">
      {/* Header Section */}
      <div className="text-center mb-8">
        <h2 className="text-3xl lg:text-4xl font-bold text-[var(--color-dark-blue)] mb-4">
          Security Assessment Calculator
        </h2>
        <p className="text-lg text-gray-600 max-w-3xl mx-auto leading-relaxed">
          Configure your security assessment requirements to get an instant pricing estimate.
          Our transparent pricing helps you plan your cybersecurity investments effectively.
        </p>
      </div>

      {/* Main Calculator Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Left Panel - Configuration */}
        <div className="lg:col-span-1 space-y-6">
          <div className="bg-gradient-to-br from-[var(--color-dark-blue)] to-[var(--color-blue)] rounded-2xl p-6 text-white">
            <div className="flex items-center gap-3 mb-4">
              <div className="w-10 h-10 bg-[var(--color-yellow)] rounded-lg flex items-center justify-center">
                <svg className="w-5 h-5 text-[var(--color-dark-blue)]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                </svg>
              </div>
              <h3 className="text-xl font-bold">Configure Your Assessment</h3>
            </div>
            <p className="text-white/90 leading-relaxed">
              Select your requirements below to receive an accurate pricing estimate for your cybersecurity assessment.
            </p>
          </div>

          <div className="bg-white rounded-2xl p-6 shadow-lg border border-gray-100 space-y-6">
            <StyledDropdown
              label="Assessment Type"
              value={assessmentType}
              onChange={(e) => {
                setAssessmentType(e.target.value);
                setTarget("");
                setUserRole("");
                setScope("");
                setCompetitorQuote("");
              }}
              options={["Grey Box", "Black Box"]}
              placeholder="Choose Type"
            />

            <StyledDropdown
              label="Target Security Testing"
              value={target}
              onChange={(e) => {
                setTarget(e.target.value);
                setUserRole("");
                setScope("");
                setCompetitorQuote("");
              }}
              options={targets}
              placeholder="Choose Target"
            />

            {/* Always reserve space for user role dropdown */}
            <div className={`transition-opacity duration-300 ${target ? 'opacity-100' : 'opacity-50 pointer-events-none'}`}>
              <StyledDropdown
                label={getUserRoleLabel()}
                value={userRole}
                onChange={(e) => {
                  setUserRole(e.target.value);
                  setScope("");
                  setCompetitorQuote("");
                }}
                options={userRoles}
                placeholder={`Select ${getUserRoleLabel()}`}
              />
            </div>
          </div>
        </div>

        {/* Right Panel - Results and Details */}
        <div className="lg:col-span-2 space-y-6">
          {/* Always reserve space for target description */}
          <div className={`transition-opacity duration-300 ${target ? 'opacity-100' : 'opacity-50'}`}>
            {target ? (
              <TargetDescription
                target={target}
                assessmentType={assessmentType}
                userRole={userRole}
                scope={scope}
                competitorQuote={competitorQuote}
                result={result}
              />
            ) : (
              <div className="bg-gray-50 rounded-2xl p-6 border border-gray-200 min-h-[200px] flex items-center justify-center">
                <div className="text-center">
                  <div className="w-16 h-16 bg-gray-200 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                    </svg>
                  </div>
                  <h3 className="text-lg font-semibold text-gray-600 mb-2">Select Your Security Service</h3>
                  <p className="text-gray-500">Choose an assessment type and security service to see detailed information.</p>
                </div>
              </div>
            )}
          </div>

          {/* Always reserve space for scope dropdown */}
          <div className={`transition-opacity duration-300 ${
            userRole && ["Web Application", "API Security", "Mobile Application"].includes(target) && scopes.length > 0
              ? 'opacity-100'
              : 'opacity-0 pointer-events-none'
          }`}>
            <StyledDropdown
              label={getScopeLabel()}
              value={scope}
              onChange={(e) => {
                setScope(e.target.value);
                setCompetitorQuote("");
              }}
              options={scopes}
              placeholder={`Select ${getScopeLabel()}`}
            />
          </div>

          {/* Always show quote input */}
          <div className="bg-gray-50 rounded-2xl p-6 border border-gray-200">
            <label className="block mb-3 font-semibold text-[var(--color-dark-blue)] text-sm uppercase tracking-wide">
              Other Vendor Quote:
            </label>
            <input
              type="number"
              value={competitorQuote}
              onChange={(e) => setCompetitorQuote(e.target.value)}
              className="w-full p-4 border-2 border-gray-200 rounded-xl focus:outline-none focus:ring-4 focus:ring-[var(--color-blue)]/20 focus:border-[var(--color-blue)] transition-all duration-300 font-medium"
              placeholder="Enter other vendor quote in USD"
            />
          </div>

          {/* Always show pricing results */}
          <div className="bg-gradient-to-br from-[var(--color-dark-blue)] to-[var(--color-blue)] rounded-2xl p-6 text-white min-h-[200px]">
            <h3 className="text-2xl font-bold mb-6">Investment Summary</h3>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6">
                <p className="text-white/80 font-medium mb-2">Estimated Investment</p>
                <p className="text-4xl font-bold text-white">
                  ${result ? result.cost.toLocaleString() : '0'}
                </p>
                {result && (
                  <p className="text-white/80 text-sm mt-2">
                    ~{result.hours} hours of expert assessment
                  </p>
                )}
              </div>

              <div className={`bg-[var(--color-yellow)]/20 backdrop-blur-sm rounded-xl p-6 transition-opacity duration-300 ${
                result && result.savings > 0 ? 'opacity-100' : 'opacity-50'
              }`}>
                <p className="text-white/80 font-medium mb-2">Your Savings</p>
                <p className="text-4xl font-bold text-[var(--color-yellow)]">
                  ${result && result.savings > 0 ? result.savings.toLocaleString() : '0'}
                </p>
                <p className="text-white/80 text-sm mt-2">
                  {result && result.savings > 0 ? `${result.percentage}% less than competitor` : 'Enter competitor quote to see savings'}
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Disclaimer */}
      <div className="mt-8 p-6 bg-gray-50 rounded-2xl border border-gray-200">
        <p className="text-sm text-gray-600 leading-relaxed">
          <strong className="text-[var(--color-dark-blue)]">Important:</strong>
          These estimates are for planning purposes and may vary based on specific requirements, complexity, and timeline.
          SecurityLit provides customized cybersecurity solutions tailored to your unique environment.
          For a detailed proposal, please{" "}
          <a href="/contact" className="text-[var(--color-blue)] hover:text-[var(--color-blue-secondary)] font-semibold underline">
            contact our security experts
          </a>{" "}
          for a comprehensive consultation and accurate quote.
        </p>
      </div>
    </div>
  );
}
