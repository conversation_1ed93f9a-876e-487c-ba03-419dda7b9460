"use client";

import React from 'react';
import { Shield, Play, ArrowRight, GraduationCap, CheckCircle, Users, Star, Clock, Award } from 'lucide-react';
import BreadcrumbNavigation from '../../common/components/BreadcrumbNavigation';

export default function HeroBento() {
  const breadcrumbItems = [
    {
      name: "Home",
      url: "/",
      iconKey: "home",
      description: "Return to homepage"
    },
    {
      name: "Training",
      url: "/training",
      current: true,
      iconKey: "graduation-cap",
      description: "Explore SecurityLit's cybersecurity training programs"
    }
  ];

  const keyBenefits = [
    "Complete pentesting skills with real-time live projects",
    "Latest tools and topics in cybersecurity"
  ];

  const trustStats = [
    { value: "500+", label: "Students Trained", icon: Users },
    { value: "98%", label: "Success Rate", icon: Star },
    { value: "83", label: "Total Lessons", icon: Clock },
    { value: "11", label: "Course Sections", icon: Award }
  ];

  const handleProgramDetails = () => {
    // Navigate to program details page
    window.location.href = '/CyberSecTraining';
  };

  return (
    <div className="min-h-screen bg-white">
      <div className="flex flex-col lg:flex-row min-h-screen relative">

        {/* Left Section - Optimized Content with Rounded Corner */}
        <div className="w-full lg:w-1/2 bg-gradient-to-br from-[var(--color-dark-blue)] to-[var(--color-dark-blue-hover)] relative overflow-hidden lg:rounded-br-[100px]">
          {/* Background Pattern */}
          <div className="absolute inset-0 bg-cover bg-center bg-no-repeat opacity-10"
               style={{ backgroundImage: 'url(/images/hexagon.svg)' }}>
          </div>
          <div className="absolute inset-0 bg-[var(--color-dark-blue)]/90 lg:rounded-br-[100px]"></div>
          
          <div className="relative z-10 flex justify-center px-4 sm:px-6 lg:px-12 py-8 sm:py-12 lg:pt-20 lg:pb-8 min-h-screen lg:h-full">
            <div className="max-w-lg w-full flex flex-col justify-center">
              {/* Breadcrumb */}
              <div className="mb-4 mt-2 lg:mt-0">
                <BreadcrumbNavigation items={breadcrumbItems} className="text-white" />
              </div>

              {/* Main Heading - Enhanced */}
              <h1 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold text-white mb-4 leading-tight">
                Elite Security
                <span className="block bg-gradient-to-r from-[var(--color-blue)] to-[var(--color-blue-secondary)] bg-clip-text text-transparent">
                  Training
                </span>
              </h1>
              
              {/* Streamlined Subtitle */}
              <h2 className="text-lg sm:text-xl lg:text-2xl font-bold text-white mb-3 leading-tight">
                Launch Your Cyber Security Career
              </h2>

              <h3 className="text-base sm:text-lg text-[var(--color-yellow)] mb-4 font-semibold">
                Free and Premium Pathways
              </h3>
              
              {/* Compact Description - Reduced content */}
              <p className="text-sm sm:text-base text-white/90 mb-6 leading-relaxed">
                Dive into penetration testing with our comprehensive program designed for aspiring security professionals.
              </p>

              {/* Streamlined Key Benefits - 3 items only */}
              <div className="space-y-3 mb-8">
                {keyBenefits.map((benefit, index) => (
                  <div key={index} className="flex items-center gap-3">
                    <div className="w-5 h-5 bg-[var(--color-yellow)] rounded-full flex items-center justify-center flex-shrink-0">
                      <CheckCircle className="w-3 h-3 text-[var(--color-dark-blue)]" />
                    </div>
                    <span className="text-white/90 text-sm sm:text-base">{benefit}</span>
                  </div>
                ))}
              </div>

              {/* Enhanced CTA Buttons */}
              <div className="flex flex-col gap-3">
                <a
                  href="#training-form"
                  className="group bg-gradient-to-r from-[var(--color-blue)] to-[var(--color-blue-secondary)] text-white px-4 sm:px-6 py-2.5 sm:py-3 rounded-lg font-semibold text-sm sm:text-base transition-all duration-300 shadow-lg hover:shadow-xl flex items-center justify-center gap-2 cursor-pointer"
                >
                  <Play className="w-4 h-4" />
                  Enroll Now
                  <ArrowRight className="w-4 h-4 group-hover:translate-x-1 transition-transform" />
                </a>

                <button
                  onClick={handleProgramDetails}
                  className="group bg-white/10 backdrop-blur-sm text-white px-4 sm:px-6 py-2.5 sm:py-3 rounded-lg font-semibold text-sm sm:text-base transition-all duration-300 border-2 border-white/20 hover:bg-white/20 flex items-center justify-center gap-2"
                >
                  <GraduationCap className="w-4 h-4" />
                  Program Details
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Right Section - Enhanced Visual Focus */}
        <div className="w-full lg:w-1/2 bg-white">
          <div className="max-w-2xl mx-auto px-4 sm:px-6 lg:px-12 py-8 sm:py-12 lg:pt-20 lg:pb-8 min-h-screen lg:h-full flex flex-col justify-center">
            
            {/* Enhanced Hero Visual */}
            <div className="relative mb-6">
              <div className="relative bg-gradient-to-br from-[var(--color-blue)]/10 to-[var(--color-blue-secondary)]/10 rounded-2xl p-4 sm:p-6 lg:p-8 border-2 border-[var(--color-blue)]/20">
                <div className="absolute inset-0 bg-gradient-to-br from-[var(--color-blue)]/5 to-[var(--color-blue-secondary)]/5 rounded-2xl"></div>
                
                <div className="relative z-10 text-center">
                  <div className="w-12 h-12 sm:w-16 sm:h-16 bg-gradient-to-br from-[var(--color-blue)] to-[var(--color-blue-secondary)] rounded-xl flex items-center justify-center mx-auto mb-4 shadow-lg">
                    <img
                      src="/SecurityLit_Icon_White.png"
                      alt="SecurityLit Logo"
                      className="w-8 h-8 sm:w-12 sm:h-12 object-contain"
                    />
                  </div>

                  <h3 className="text-lg sm:text-xl font-bold text-[var(--color-dark-blue)] mb-2">
                    SecurityLit Presents
                  </h3>
                  <p className="text-[var(--foreground-secondary)] text-sm sm:text-base">
                    Professional cybersecurity training designed by industry experts
                  </p>
                </div>
              </div>
            </div>

            {/* Enhanced Trust Indicators */}
            <div className="grid grid-cols-2 gap-3 sm:gap-4 mb-6">
              {trustStats.map((stat, index) => (
                <div 
                  key={index}
                  className="text-center group"
                >
                  <div className="w-8 h-8 sm:w-10 sm:h-10 bg-gradient-to-br from-[var(--color-blue)]/10 to-[var(--color-blue-secondary)]/10 rounded-lg flex items-center justify-center mx-auto mb-2 group-hover:scale-110 transition-transform">
                    <stat.icon className="w-4 h-4 sm:w-5 sm:h-5 text-[var(--color-blue)]" />
                  </div>
                  <div className="text-xl sm:text-2xl lg:text-3xl font-bold text-[var(--color-dark-blue)] mb-1">
                    {stat.value}
                  </div>
                  <div className="text-[var(--foreground-secondary)] text-xs font-medium">
                    {stat.label}
                  </div>
                </div>
              ))}
            </div>

            
          </div>
        </div>
      </div>
    </div>
  );
} 