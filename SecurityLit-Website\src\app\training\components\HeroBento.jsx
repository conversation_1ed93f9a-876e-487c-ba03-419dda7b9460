"use client";

import React from 'react';
import { Shield, Play, ArrowRight, Mail } from 'lucide-react';
import BreadcrumbNavigation from '../../common/components/BreadcrumbNavigation';

export default function HeroBento() {
  const breadcrumbItems = [
    {
      name: "Home",
      url: "/",
      iconKey: "home",
      description: "Return to homepage"
    },
    {
      name: "Training",
      url: "/training",
      current: true,
      iconKey: "graduation-cap",
      description: "Explore SecurityLit's cybersecurity training programs"
    }
  ];

  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section - Light Color */}
      <section className="py-12 sm:py-16 lg:py-24 bg-gradient-to-br from-[var(--color-blue)]/10 to-[var(--color-blue-secondary)]/10 relative overflow-hidden">
        <div className="absolute inset-0 bg-cover bg-center bg-no-repeat opacity-5"
             style={{ backgroundImage: 'url(/images/hexagon.svg)' }}>
        </div>

        <div className="relative z-10 container mx-auto px-4 max-w-7xl">
          <div className="flex flex-col lg:flex-row items-center gap-12">
            <div className="flex-1 text-center lg:text-left">
              {/* Breadcrumb */}
              <div className="mb-6">
                <BreadcrumbNavigation items={breadcrumbItems} className="text-[var(--color-dark-blue)]" />
              </div>

              <div className="inline-flex items-center bg-gradient-to-r from-[var(--color-blue)]/10 to-[var(--color-blue-secondary)]/10 px-6 py-3 rounded-full mb-8 border border-[var(--color-blue)]/20">
                <Shield className="w-5 h-5 text-[var(--color-blue)] mr-3" />
                <span className="text-sm font-semibold text-[var(--color-blue)]">Security Lit Presents</span>
              </div>

              <h1 className="text-4xl lg:text-5xl font-bold text-[var(--color-dark-blue)] mb-6 leading-tight">
                Are you searching for training in
                <span className="block bg-gradient-to-r from-[var(--color-blue)] to-[var(--color-blue-secondary)] bg-clip-text text-transparent">
                  Cyber Security field?
                </span>
              </h1>

              <p className="text-lg text-[var(--foreground-secondary)] mb-8 leading-relaxed max-w-3xl">
                We are among the few companies in India offering internships across different sectors of Cyber Security. Check out real-life Cyber Security projects, get awesome experience to kickstart your career in cyber security and totally change your life!
              </p>

              <div className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start">
                <button className="group bg-gradient-to-r from-[var(--color-blue)] to-[var(--color-blue-secondary)] text-white px-6 py-3 rounded-lg font-semibold text-lg transition-all duration-300 shadow-lg hover:shadow-xl flex items-center justify-center gap-2">
                  <Play className="w-5 h-5" />
                  Enroll Now
                  <ArrowRight className="w-5 h-5 group-hover:translate-x-1 transition-transform" />
                </button>

                <button className="group bg-white text-[var(--color-dark-blue)] px-6 py-3 rounded-lg font-semibold text-lg transition-all duration-300 border-2 border-[var(--color-blue)]/20 hover:bg-[var(--color-blue)]/5 flex items-center justify-center gap-2">
                  <Mail className="w-5 h-5" />
                  Contact Us
                </button>
              </div>
            </div>

            <div className="flex-1">
              <div className="relative">
                <img
                  src="https://securitylit.com/images/p1s1.png"
                  alt="Cybersecurity Training"
                  className="w-full h-[500px] rounded-2xl shadow-2xl"
                />
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
} 