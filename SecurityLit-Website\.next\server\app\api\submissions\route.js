const CHUNK_PUBLIC_PATH = "server/app/api/submissions/route.js";
const runtime = require("../../../chunks/[turbopack]_runtime.js");
runtime.loadChunk("server/chunks/node_modules_next_da14e5e3._.js");
runtime.loadChunk("server/chunks/node_modules_@typespec_ts-http-runtime_dist_esm_c8cb7fcc._.js");
runtime.loadChunk("server/chunks/node_modules_b2b8eadd._.js");
runtime.loadChunk("server/chunks/[root-of-the-server]__cdc4f36a._.js");
runtime.getOrInstantiateRuntimeModule("[project]/.next-internal/server/app/api/submissions/route/actions.js [app-rsc] (server actions loader, ecmascript)", CHUNK_PUBLIC_PATH);
runtime.getOrInstantiateRuntimeModule("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/src/app/api/submissions/route.js [app-route] (ecmascript)\" } [app-route] (ecmascript)", CHUNK_PUBLIC_PATH);
module.exports = runtime.getOrInstantiateRuntimeModule("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/src/app/api/submissions/route.js [app-route] (ecmascript)\" } [app-route] (ecmascript)", CHUNK_PUBLIC_PATH).exports;
