module.exports = {

"[project]/.next-internal/server/app/api/submissions/route/actions.js [app-rsc] (server actions loader, ecmascript)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/shared/lib/no-fallback-error.external.js [external] (next/dist/shared/lib/no-fallback-error.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/shared/lib/no-fallback-error.external.js", () => require("next/dist/shared/lib/no-fallback-error.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/fs/promises [external] (fs/promises, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("fs/promises", () => require("fs/promises"));

module.exports = mod;
}}),
"[externals]/path [external] (path, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("path", () => require("path"));

module.exports = mod;
}}),
"[project]/src/app/utils/emailValidator.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "isBusinessEmail": ()=>isBusinessEmail,
    "isValidEmail": ()=>isValidEmail
});
const freeEmailDomains = [
    "gmail.com",
    "yahoo.com",
    "hotmail.com",
    "outlook.com",
    "aol.com",
    "icloud.com",
    "zoho.com",
    "protonmail.com",
    "gmx.com",
    "yandex.com",
    "mail.com",
    "live.com",
    "me.com",
    "msn.com",
    "inbox.com"
];
const disposableEmailDomains = [
    "temp-mail.org",
    "1secmail.com",
    "1secmail.org",
    "1secmail.net",
    "disposableemailaddresses.com",
    "mailinator.com",
    "guerrillamail.com",
    "10minutemail.com",
    "getnada.com",
    "throwawaymail.com",
    "yopmail.com",
    "trashmail.com",
    "maildrop.cc",
    "fakeinbox.com",
    "moakt.com",
    "meltmail.com",
    "spambog.com",
    "mailcatch.com",
    "spamex.com",
    "spammotel.com",
    "spamgourmet.com",
    "discard.email",
    "airmail.cc",
    "sharklasers.com",
    "mytemp.email",
    "dispostable.com",
    "tempmailo.com",
    "emailondeck.com",
    "mintemail.com",
    "eyepaste.com",
    "33mail.com",
    "tmail.com",
    "instant-mail.com",
    "opayq.com",
    "tmpbox.net",
    "kasmail.com",
    "mohmal.com",
    "anonbox.net",
    "disbox.org",
    "easytrashmail.com",
    "fakemailgenerator.com",
    "getairmail.com",
    "guerillamail.org",
    "guerillamailblock.com",
    "hot-mail.gq",
    "inboxkitten.com",
    "mailsac.com",
    "mail.tm",
    "mytrashmail.com",
    "tutanota.com",
    "privatemail.com",
    "jetable.org",
    "temporary-mail.net",
    "crazymailing.com",
    "heweek.com",
    "example.com",
    "test.com"
];
const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
// Known spam traps
const spamTraps = [
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>"
];
const isBusinessEmail = (email)=>{
    // Check if email is undefined or null
    if (!email) {
        console.log("Email is undefined or null");
        return false;
    }
    if (!emailRegex.test(email)) {
        console.log("Invalid email format");
        return false;
    }
    const domain = email.split("@")[1];
    if (freeEmailDomains.includes(domain) || disposableEmailDomains.includes(domain)) {
        console.log("Free or disposable domain detected");
        return false;
    }
    if (spamTraps.includes(email.toLowerCase())) {
        console.log("Spam trap detected");
        return false;
    }
    return true;
};
const isValidEmail = (email)=>{
    if (!email) {
        return false;
    }
    return emailRegex.test(email);
};
}),
"[externals]/node:crypto [external] (node:crypto, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:crypto", () => require("node:crypto"));

module.exports = mod;
}}),
"[externals]/node:os [external] (node:os, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:os", () => require("node:os"));

module.exports = mod;
}}),
"[externals]/node:util [external] (node:util, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:util", () => require("node:util"));

module.exports = mod;
}}),
"[externals]/node:process [external] (node:process, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:process", () => require("node:process"));

module.exports = mod;
}}),
"[externals]/node:http [external] (node:http, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:http", () => require("node:http"));

module.exports = mod;
}}),
"[externals]/node:https [external] (node:https, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:https", () => require("node:https"));

module.exports = mod;
}}),
"[externals]/node:zlib [external] (node:zlib, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:zlib", () => require("node:zlib"));

module.exports = mod;
}}),
"[externals]/node:stream [external] (node:stream, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:stream", () => require("node:stream"));

module.exports = mod;
}}),
"[externals]/net [external] (net, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("net", () => require("net"));

module.exports = mod;
}}),
"[externals]/tls [external] (tls, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("tls", () => require("tls"));

module.exports = mod;
}}),
"[externals]/assert [external] (assert, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("assert", () => require("assert"));

module.exports = mod;
}}),
"[externals]/tty [external] (tty, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("tty", () => require("tty"));

module.exports = mod;
}}),
"[externals]/util [external] (util, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("util", () => require("util"));

module.exports = mod;
}}),
"[externals]/os [external] (os, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("os", () => require("os"));

module.exports = mod;
}}),
"[externals]/http [external] (http, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("http", () => require("http"));

module.exports = mod;
}}),
"[externals]/https [external] (https, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("https", () => require("https"));

module.exports = mod;
}}),
"[externals]/url [external] (url, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("url", () => require("url"));

module.exports = mod;
}}),
"[externals]/events [external] (events, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("events", () => require("events"));

module.exports = mod;
}}),
"[externals]/stream [external] (stream, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("stream", () => require("stream"));

module.exports = mod;
}}),
"[project]/src/app/utils/send-email.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "sendEmail": ()=>sendEmail
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$azure$2f$communication$2d$email$2f$dist$2d$esm$2f$src$2f$emailClient$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@azure/communication-email/dist-esm/src/emailClient.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$azure$2f$communication$2d$email$2f$dist$2d$esm$2f$src$2f$generated$2f$src$2f$models$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@azure/communication-email/dist-esm/src/generated/src/models/index.js [app-route] (ecmascript)");
;
// Check if connection string is available
const connectionString = process.env.NEXT_PUBLIC_COMMUNICATION_SERVICES_CONNECTION_STRING;
let emailClient;
// Only initialize email client if connection string is available
if (connectionString) {
    try {
        emailClient = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$azure$2f$communication$2d$email$2f$dist$2d$esm$2f$src$2f$emailClient$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["EmailClient"](connectionString);
    } catch (error) {
        console.error("Failed to initialize email client:", error);
    }
} else {
    console.warn("Communication Services connection string is not defined");
}
// Constants for polling
const POLLER_WAIT_TIME = 10; // seconds
const MAX_POLL_TIME = 120; // seconds
// Helper function to parse form data from body
function parseFormData(body) {
    const lines = body.split('\n');
    const formData = {};
    lines.forEach((line)=>{
        const [key, ...valueParts] = line.split(': ');
        if (key && valueParts.length > 0) {
            formData[key] = valueParts.join(': ');
        }
    });
    return formData;
}
// Helper function to determine form type from subject
function getFormType(subject) {
    if (subject.includes('Contact')) return 'contact-form';
    if (subject.includes('Training')) return 'training-enrollment';
    if (subject.includes('Newsletter')) return 'newsletter-signup';
    if (subject.includes('Premium')) return 'premium-training-inquiry';
    return 'general-inquiry';
}
// Helper function to store submission with retry logic
const MAX_RETRIES = 3;
const storeSubmission = async (submissionData)=>{
    let retries = 0;
    while(retries < MAX_RETRIES){
        try {
            // Use absolute URL with window.location.origin
            const baseUrl = ("TURBOPACK compile-time falsy", 0) ? "TURBOPACK unreachable" : 'http://localhost:3000';
            const response = await fetch(`${baseUrl}/api/submissions`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(submissionData)
            });
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return true;
        } catch (error) {
            retries++;
            if (retries === MAX_RETRIES) {
                console.error('Failed to store submission after retries:', error);
                return false;
            }
            // Wait before retrying (exponential backoff)
            await new Promise((resolve)=>setTimeout(resolve, Math.pow(2, retries) * 1000));
        }
    }
    return false;
};
async function sendEmail(subject, body, attachments = []) {
    try {
        // Parse form data and prepare submission
        const formData = parseFormData(body);
        const submissionData = {
            subject,
            body,
            attachments: attachments.length > 0 ? 'Has attachments' : 'No attachments',
            formType: getFormType(subject),
            formData,
            timestamp: new Date().toISOString()
        };
        // Store submission (non-blocking)
        const storePromise = storeSubmission(submissionData);
        // Check if email client is initialized
        if (!emailClient) {
            console.warn("Email client not initialized, skipping email send");
            await storePromise; // Still wait for storage to complete
            return true; // Return success for development environments
        }
        // Prepare email message
        const message = {
            senderAddress: process.env.NEXT_PUBLIC_EMAIL_USER,
            content: {
                subject,
                plainText: body,
                attachments
            },
            recipients: {
                to: [
                    {
                        address: process.env.NEXT_PUBLIC_RECIEVER_EMAIL || '<EMAIL>',
                        displayName: "SecurityLit Team"
                    }
                ]
            }
        };
        // Send email
        const poller = await emailClient.beginSend(message);
        if (!poller.getOperationState().isStarted) {
            throw new Error("Email sending failed to start");
        }
        // Poll for completion with timeout
        let timeElapsed = 0;
        while(!poller.isDone()){
            await poller.poll();
            await new Promise((resolve)=>setTimeout(resolve, POLLER_WAIT_TIME * 1000));
            timeElapsed += POLLER_WAIT_TIME;
            if (timeElapsed > MAX_POLL_TIME) {
                throw new Error("Email sending timed out");
            }
        }
        const result = poller.getResult();
        if (result.status !== __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$azure$2f$communication$2d$email$2f$dist$2d$esm$2f$src$2f$generated$2f$src$2f$models$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["KnownEmailSendStatus"].Succeeded) {
            throw new Error(result.error || "Email sending failed");
        }
        // Wait for storage to complete
        await storePromise;
        console.log(`Successfully sent the email (operation id: ${result.id})`);
        return true;
    } catch (error) {
        console.error("Failed to send email:", error);
        throw error;
    }
}
}),
"[project]/src/app/api/submissions/route.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "POST": ()=>POST
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$externals$5d2f$fs$2f$promises__$5b$external$5d$__$28$fs$2f$promises$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/fs/promises [external] (fs/promises, cjs)");
var __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/path [external] (path, cjs)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$utils$2f$emailValidator$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/app/utils/emailValidator.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$utils$2f$send$2d$email$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/app/utils/send-email.js [app-route] (ecmascript)");
;
;
;
;
;
// Constants
const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB max file size
const MAX_SUBMISSIONS = 10000; // Maximum number of submissions to keep
const SUBMISSIONS_PATH = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(process.cwd(), 'data', 'submissions.json');
// Helper function to ensure data directory exists
async function ensureDataDirectory() {
    const dataDir = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(process.cwd(), 'data');
    try {
        await __TURBOPACK__imported__module__$5b$externals$5d2f$fs$2f$promises__$5b$external$5d$__$28$fs$2f$promises$2c$__cjs$29$__["default"].access(dataDir);
    } catch  {
        await __TURBOPACK__imported__module__$5b$externals$5d2f$fs$2f$promises__$5b$external$5d$__$28$fs$2f$promises$2c$__cjs$29$__["default"].mkdir(dataDir, {
            recursive: true
        });
    }
}
// Helper function to read submissions
async function readSubmissions() {
    try {
        const data = await __TURBOPACK__imported__module__$5b$externals$5d2f$fs$2f$promises__$5b$external$5d$__$28$fs$2f$promises$2c$__cjs$29$__["default"].readFile(SUBMISSIONS_PATH, 'utf8');
        return JSON.parse(data);
    } catch (error) {
        // File doesn't exist or is invalid, return empty array
        return [];
    }
}
// Helper function to write submissions
async function writeSubmissions(submissions) {
    // Keep only the most recent submissions if we exceed the limit
    if (submissions.length > MAX_SUBMISSIONS) {
        submissions = submissions.slice(-MAX_SUBMISSIONS);
    }
    await __TURBOPACK__imported__module__$5b$externals$5d2f$fs$2f$promises__$5b$external$5d$__$28$fs$2f$promises$2c$__cjs$29$__["default"].writeFile(SUBMISSIONS_PATH, JSON.stringify(submissions, null, 2));
}
async function POST(request) {
    try {
        // Ensure data directory exists
        await ensureDataDirectory();
        // Parse and validate request data
        const data = await request.json();
        if (!data || typeof data !== 'object') {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Invalid submission data'
            }, {
                status: 400
            });
        }
        // Required fields per form type
        const requiredFieldsMap = {
            'contact-form': [
                'name',
                'email',
                'message'
            ],
            'training-enrollment': [
                'fullName',
                'email',
                'phone'
            ],
            'newsletter-signup': [
                'name',
                'email'
            ],
            'premium-training-inquiry': [
                'name',
                'email',
                'message'
            ],
            'general-inquiry': [
                'name',
                'email',
                'message'
            ]
        };
        const formType = data.formType;
        if (!formType || !requiredFieldsMap[formType]) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Missing or unknown formType.'
            }, {
                status: 400
            });
        }
        // Validate required fields for this formType
        const requiredFields = requiredFieldsMap[formType];
        const missingFields = requiredFields.filter((field)=>!data[field] || String(data[field]).trim() === '');
        if (missingFields.length > 0) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: `Missing required field(s): ${missingFields.join(', ')}`
            }, {
                status: 400
            });
        }
        // Email validation - use business email validation for contact forms, regular validation for others
        const emailToValidate = data.email || data.businessEmail || null;
        if (!emailToValidate) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Email address is required.'
            }, {
                status: 400
            });
        }
        // For contact forms and training enrollment, require business email
        // For newsletter signup, allow any valid email
        if (formType === 'contact-form' || formType === 'training-enrollment') {
            if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$utils$2f$emailValidator$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isBusinessEmail"])(emailToValidate)) {
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                    error: 'Please use a valid business email address.'
                }, {
                    status: 400
                });
            }
        } else {
            if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$utils$2f$emailValidator$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isValidEmail"])(emailToValidate)) {
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                    error: 'Please use a valid email address.'
                }, {
                    status: 400
                });
            }
        }
        // Add timestamp if not present
        const submissionWithTimestamp = {
            ...data,
            timestamp: data.timestamp || new Date().toISOString()
        };
        // Read existing submissions
        const submissions = await readSubmissions();
        // Add new submission
        submissions.push(submissionWithTimestamp);
        // Write back to file
        await writeSubmissions(submissions);
        // Send email with form data
        const subject = data.subject || `New ${formType.replace('-', ' ')} Submission`;
        const body = Object.entries(data).map(([key, value])=>`${key}: ${value}`).join('\n');
        await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$utils$2f$send$2d$email$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["sendEmail"])(subject, body);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: true
        });
    } catch (error) {
        console.error('Error storing submission or sending email:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: 'Failed to store submission or send email'
        }, {
            status: 500
        });
    }
}
}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__cdc4f36a._.js.map